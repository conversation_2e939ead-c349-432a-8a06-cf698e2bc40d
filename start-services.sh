#!/bin/bash

# Swiss Budget Pro (Fire or Retire) - Service Startup Script
# This script handles the complete setup and startup of the application

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}================================${NC}"
}

# Main execution
main() {
    print_header "🔥 Fire or Retire - Swiss Budget Pro 🔥"
    print_status "Starting service deployment..."
    
    # Check if Docker is running
    if ! docker info >/dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    
    # Check if Traefik network exists
    if ! docker network ls | grep -q "traefik_network"; then
        print_warning "Traefik network not found. Creating traefik_network..."
        docker network create traefik_network
        print_success "Created traefik_network"
    else
        print_status "Traefik network already exists"
    fi
    
    # Clean up any existing containers
    print_status "Cleaning up existing containers..."
    docker compose down --remove-orphans 2>/dev/null || true

    # Build and start services
    print_status "Building and starting services..."
    docker compose up --build -d

    # Wait for services to be healthy
    print_status "Waiting for services to be ready..."
    sleep 10

    # Check service health
    if docker compose ps | grep -q "Up (healthy)"; then
        print_success "✅ Services are running and healthy!"
        echo ""
        print_header "🌐 Access Information"
        echo -e "${CYAN}Application URL:${NC} http://fire.docker.localhost"
        echo -e "${CYAN}Container Name:${NC} fire-or-retire-app"
        echo -e "${CYAN}Internal Port:${NC} 4173"
        echo ""
        print_status "📊 Swiss Budget Pro is now available!"
        print_status "🔥 Fire or Retire Calculator is ready to use!"
        
        # Show container status
        echo ""
        print_header "📋 Container Status"
        docker compose ps

    else
        print_error "❌ Services failed to start properly"
        print_status "Checking logs..."
        docker compose logs --tail=20
        exit 1
    fi
}

# Cleanup function
cleanup() {
    print_header "🧹 Cleanup Services"
    print_status "Stopping and removing containers..."
    docker compose down --remove-orphans
    print_success "Cleanup completed"
}

# Show logs function
show_logs() {
    print_header "📋 Service Logs"
    docker compose logs -f
}

# Show status function
show_status() {
    print_header "📊 Service Status"
    docker compose ps
    echo ""
    print_status "Health checks:"
    docker compose exec fire-or-retire wget --spider -q http://localhost:4173/ && \
        print_success "✅ Application is responding" || \
        print_error "❌ Application is not responding"
}

# Handle command line arguments
case "${1:-start}" in
    "start")
        main
        ;;
    "stop"|"cleanup")
        cleanup
        ;;
    "logs")
        show_logs
        ;;
    "status")
        show_status
        ;;
    "restart")
        cleanup
        sleep 2
        main
        ;;
    *)
        echo "Usage: $0 {start|stop|cleanup|logs|status|restart}"
        echo ""
        echo "Commands:"
        echo "  start    - Start the services (default)"
        echo "  stop     - Stop and cleanup services"
        echo "  cleanup  - Same as stop"
        echo "  logs     - Show service logs"
        echo "  status   - Show service status"
        echo "  restart  - Restart services"
        exit 1
        ;;
esac
