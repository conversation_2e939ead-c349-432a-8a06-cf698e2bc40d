#!/usr/bin/env node

/**
 * Swiss Budget Pro E2E Test Runner
 * Comprehensive test execution script with reporting and analysis
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

class SwissE2ETestRunner {
  constructor() {
    this.testSuites = {
      critical: [
        'tests/e2e/critical-flows/fire-calculation.spec.ts',
        'tests/e2e/critical-flows/tax-optimization.spec.ts',
        'tests/e2e/critical-flows/healthcare-optimization.spec.ts',
      ],
      mobile: [
        'tests/e2e/mobile/responsive-design.spec.ts',
      ],
      accessibility: [
        'tests/e2e/accessibility/wcag-compliance.spec.ts',
      ],
      performance: [
        'tests/e2e/performance/load-performance.spec.ts',
      ],
      all: [
        'tests/e2e/**/*.spec.ts',
      ],
    };
    
    this.browsers = ['chromium', 'firefox', 'webkit'];
    this.mobileDevices = ['mobile-chrome', 'mobile-safari'];
    this.swissLocales = ['swiss-german', 'swiss-french'];
  }

  async run() {
    console.log('🇨🇭 Swiss Budget Pro E2E Test Runner');
    console.log('=====================================');
    
    const args = process.argv.slice(2);
    const suite = args[0] || 'critical';
    const browser = args[1] || 'chromium';
    const options = this.parseOptions(args);
    
    try {
      await this.setupEnvironment();
      await this.runTestSuite(suite, browser, options);
      await this.generateReports();
      await this.analyzeResults();
    } catch (error) {
      console.error('❌ Test execution failed:', error.message);
      process.exit(1);
    }
  }

  parseOptions(args) {
    const options = {
      headed: args.includes('--headed'),
      debug: args.includes('--debug'),
      trace: args.includes('--trace'),
      video: args.includes('--video'),
      parallel: !args.includes('--serial'),
      retries: args.includes('--no-retry') ? 0 : 2,
      timeout: this.getArgValue(args, '--timeout') || 60000,
      workers: this.getArgValue(args, '--workers') || 2,
      reporter: this.getArgValue(args, '--reporter') || 'html',
    };
    
    console.log('Test options:', options);
    return options;
  }

  getArgValue(args, flag) {
    const index = args.indexOf(flag);
    return index !== -1 && index + 1 < args.length ? args[index + 1] : null;
  }

  async setupEnvironment() {
    console.log('🔧 Setting up test environment...');
    
    // Create test results directories
    const dirs = [
      'test-results',
      'test-results/screenshots',
      'test-results/videos',
      'test-results/traces',
      'test-results/reports',
    ];
    
    dirs.forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        console.log(`📁 Created directory: ${dir}`);
      }
    });
    
    // Verify Playwright is installed
    try {
      execSync('npx playwright --version', { stdio: 'pipe' });
      console.log('✅ Playwright is installed');
    } catch (error) {
      console.log('📦 Installing Playwright...');
      execSync('npx playwright install', { stdio: 'inherit' });
    }
    
    // Check if application is running
    try {
      const response = await fetch('http://localhost:5173');
      if (response.ok) {
        console.log('✅ Application is running on localhost:5173');
      }
    } catch (error) {
      console.log('⚠️  Application not running - tests will start dev server');
    }
  }

  async runTestSuite(suite, browser, options) {
    console.log(`🧪 Running ${suite} test suite on ${browser}...`);
    
    const testFiles = this.testSuites[suite] || [suite];
    const playwrightArgs = this.buildPlaywrightArgs(browser, options);
    
    console.log('Test files:', testFiles);
    console.log('Playwright args:', playwrightArgs.join(' '));
    
    return new Promise((resolve, reject) => {
      const child = spawn('npx', ['playwright', 'test', ...playwrightArgs, ...testFiles], {
        stdio: 'inherit',
        cwd: process.cwd(),
      });
      
      child.on('close', (code) => {
        if (code === 0) {
          console.log('✅ Test suite completed successfully');
          resolve();
        } else {
          console.log(`❌ Test suite failed with exit code ${code}`);
          reject(new Error(`Tests failed with exit code ${code}`));
        }
      });
      
      child.on('error', (error) => {
        console.error('❌ Failed to start test process:', error);
        reject(error);
      });
    });
  }

  buildPlaywrightArgs(browser, options) {
    const args = [];
    
    // Browser selection
    if (browser !== 'all') {
      args.push('--project', browser);
    }
    
    // Test execution options
    if (options.headed) args.push('--headed');
    if (options.debug) args.push('--debug');
    if (options.trace) args.push('--trace', 'on');
    if (options.video) args.push('--video', 'on');
    if (!options.parallel) args.push('--workers', '1');
    if (options.workers) args.push('--workers', options.workers.toString());
    if (options.retries) args.push('--retries', options.retries.toString());
    if (options.timeout) args.push('--timeout', options.timeout.toString());
    
    // Reporter options
    args.push('--reporter', options.reporter);
    
    return args;
  }

  async generateReports() {
    console.log('📊 Generating test reports...');
    
    try {
      // Generate HTML report
      if (fs.existsSync('test-results/results.json')) {
        console.log('✅ HTML report available at test-results/html-report/index.html');
      }
      
      // Generate custom Swiss Budget Pro report
      await this.generateSwissReport();
      
      // Generate performance report
      await this.generatePerformanceReport();
      
    } catch (error) {
      console.error('⚠️  Failed to generate some reports:', error.message);
    }
  }

  async generateSwissReport() {
    console.log('🇨🇭 Generating Swiss Budget Pro specific report...');
    
    try {
      const resultsPath = 'test-results/results.json';
      if (!fs.existsSync(resultsPath)) {
        console.log('⚠️  No test results found for Swiss report');
        return;
      }
      
      const results = JSON.parse(fs.readFileSync(resultsPath, 'utf8'));
      
      const swissReport = {
        timestamp: new Date().toISOString(),
        summary: {
          total: results.stats?.total || 0,
          passed: results.stats?.passed || 0,
          failed: results.stats?.failed || 0,
          duration: results.stats?.duration || 0,
        },
        swissFeatures: {
          fireCalculation: this.analyzeTestCategory(results, 'fire-calculation'),
          taxOptimization: this.analyzeTestCategory(results, 'tax-optimization'),
          healthcareOptimization: this.analyzeTestCategory(results, 'healthcare-optimization'),
          swissLocalization: this.analyzeTestCategory(results, 'swiss-german'),
        },
        performance: {
          loadTime: this.extractPerformanceMetrics(results, 'load-performance'),
          calculationSpeed: this.extractPerformanceMetrics(results, 'calculation'),
          mobilePerformance: this.extractPerformanceMetrics(results, 'mobile'),
        },
        accessibility: {
          wcagCompliance: this.analyzeTestCategory(results, 'wcag-compliance'),
          keyboardNavigation: this.analyzeTestCategory(results, 'keyboard'),
          screenReader: this.analyzeTestCategory(results, 'screen-reader'),
        },
        recommendations: this.generateRecommendations(results),
      };
      
      const reportPath = 'test-results/reports/swiss-budget-pro-report.json';
      fs.writeFileSync(reportPath, JSON.stringify(swissReport, null, 2));
      
      // Generate markdown report
      const markdownReport = this.generateMarkdownReport(swissReport);
      fs.writeFileSync('test-results/reports/swiss-budget-pro-report.md', markdownReport);
      
      console.log('✅ Swiss Budget Pro report generated');
      
    } catch (error) {
      console.error('❌ Failed to generate Swiss report:', error.message);
    }
  }

  analyzeTestCategory(results, category) {
    const categoryTests = results.suites?.filter(suite => 
      suite.title?.toLowerCase().includes(category) ||
      suite.file?.toLowerCase().includes(category)
    ) || [];
    
    const total = categoryTests.length;
    const passed = categoryTests.filter(test => test.outcome === 'passed').length;
    
    return {
      total,
      passed,
      failed: total - passed,
      passRate: total > 0 ? (passed / total) * 100 : 0,
    };
  }

  extractPerformanceMetrics(results, metricType) {
    // This would extract performance metrics from test results
    // Implementation depends on how performance data is stored
    return {
      average: 'N/A',
      min: 'N/A',
      max: 'N/A',
      threshold: 'N/A',
    };
  }

  generateRecommendations(results) {
    const recommendations = [];
    
    const failureRate = (results.stats?.failed || 0) / (results.stats?.total || 1);
    if (failureRate > 0.1) {
      recommendations.push('High failure rate detected - review test stability');
    }
    
    const avgDuration = (results.stats?.duration || 0) / (results.stats?.total || 1);
    if (avgDuration > 30000) {
      recommendations.push('Tests are running slowly - optimize test scenarios');
    }
    
    return recommendations;
  }

  generateMarkdownReport(report) {
    return `# Swiss Budget Pro E2E Test Report

## Summary
- **Total Tests**: ${report.summary.total}
- **Passed**: ${report.summary.passed}
- **Failed**: ${report.summary.failed}
- **Duration**: ${(report.summary.duration / 1000).toFixed(2)}s
- **Pass Rate**: ${((report.summary.passed / report.summary.total) * 100).toFixed(1)}%

## Swiss Features Testing
### FIRE Calculation
- Tests: ${report.swissFeatures.fireCalculation.total}
- Pass Rate: ${report.swissFeatures.fireCalculation.passRate.toFixed(1)}%

### Tax Optimization
- Tests: ${report.swissFeatures.taxOptimization.total}
- Pass Rate: ${report.swissFeatures.taxOptimization.passRate.toFixed(1)}%

### Healthcare Optimization
- Tests: ${report.swissFeatures.healthcareOptimization.total}
- Pass Rate: ${report.swissFeatures.healthcareOptimization.passRate.toFixed(1)}%

## Accessibility Compliance
### WCAG 2.1 AA
- Tests: ${report.accessibility.wcagCompliance.total}
- Pass Rate: ${report.accessibility.wcagCompliance.passRate.toFixed(1)}%

## Recommendations
${report.recommendations.map(rec => `- ${rec}`).join('\n')}

---
Generated on: ${report.timestamp}
`;
  }

  async generatePerformanceReport() {
    console.log('⚡ Generating performance report...');
    
    const performanceReport = {
      timestamp: new Date().toISOString(),
      benchmarks: {
        pageLoad: { target: '< 3s', status: 'unknown' },
        fireCalculation: { target: '< 500ms', status: 'unknown' },
        taxCalculation: { target: '< 300ms', status: 'unknown' },
        mobileLoad: { target: '< 5s', status: 'unknown' },
      },
      recommendations: [
        'Monitor FIRE calculation performance for complex scenarios',
        'Optimize bundle size for faster mobile loading',
        'Implement progressive loading for large datasets',
      ],
    };
    
    const reportPath = 'test-results/reports/performance-report.json';
    fs.writeFileSync(reportPath, JSON.stringify(performanceReport, null, 2));
    
    console.log('✅ Performance report generated');
  }

  async analyzeResults() {
    console.log('🔍 Analyzing test results...');
    
    try {
      const resultsPath = 'test-results/results.json';
      if (!fs.existsSync(resultsPath)) {
        console.log('⚠️  No test results found for analysis');
        return;
      }
      
      const results = JSON.parse(fs.readFileSync(resultsPath, 'utf8'));
      
      console.log('\n📈 Test Results Summary:');
      console.log(`Total Tests: ${results.stats?.total || 0}`);
      console.log(`Passed: ${results.stats?.passed || 0}`);
      console.log(`Failed: ${results.stats?.failed || 0}`);
      console.log(`Skipped: ${results.stats?.skipped || 0}`);
      console.log(`Duration: ${((results.stats?.duration || 0) / 1000).toFixed(2)}s`);
      
      if (results.stats?.failed > 0) {
        console.log('\n❌ Failed Tests:');
        // List failed tests (implementation depends on results structure)
      }
      
      console.log('\n📊 Reports Generated:');
      console.log('- HTML Report: test-results/html-report/index.html');
      console.log('- Swiss Report: test-results/reports/swiss-budget-pro-report.md');
      console.log('- Performance Report: test-results/reports/performance-report.json');
      
    } catch (error) {
      console.error('❌ Failed to analyze results:', error.message);
    }
  }
}

// CLI Usage
if (require.main === module) {
  const runner = new SwissE2ETestRunner();
  runner.run().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

module.exports = SwissE2ETestRunner;
