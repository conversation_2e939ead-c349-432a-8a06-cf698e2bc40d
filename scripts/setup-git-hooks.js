#!/usr/bin/env node

/**
 * Git Hooks Setup for Swiss Budget Pro
 * Installs pre-commit and pre-push hooks for automatic validation
 */

import { writeFileSync, chmodSync, mkdirSync, existsSync } from 'fs';
import { join } from 'path';

const colors = {
  green: '\x1b[32m',
  blue: '\x1b[34m',
  yellow: '\x1b[33m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Ensure .git/hooks directory exists
const hooksDir = '.git/hooks';
if (!existsSync(hooksDir)) {
  mkdirSync(hooksDir, { recursive: true });
}

// Pre-commit hook
const preCommitHook = `#!/bin/sh
#
# Swiss Budget Pro - Pre-commit Hook
# Runs validation before allowing commits
#

echo "🔍 Running pre-commit validation..."

# Run quick validation
npm run validate 2>/dev/null
VALIDATE_EXIT_CODE=$?

if [ $VALIDATE_EXIT_CODE -ne 0 ]; then
  echo "❌ Pre-commit validation failed!"
  echo "🔧 Run 'npm run lint:fix && npm run format' to auto-fix issues"
  echo "📋 Or run 'npm run validate' to see detailed errors"
  exit 1
fi

# Run issue detection
node scripts/detect-issues.js
ISSUES_EXIT_CODE=$?

if [ $ISSUES_EXIT_CODE -ne 0 ]; then
  echo "❌ Critical issues detected!"
  echo "🔧 Please fix the issues above before committing"
  exit 1
fi

echo "✅ Pre-commit validation passed!"
exit 0
`;

// Pre-push hook
const prePushHook = `#!/bin/sh
#
# Swiss Budget Pro - Pre-push Hook
# Runs comprehensive tests before pushing
#

echo "🚀 Running pre-push validation..."

# Run full test suite
npm run test:run 2>/dev/null
TEST_EXIT_CODE=$?

if [ $TEST_EXIT_CODE -ne 0 ]; then
  echo "❌ Tests failed!"
  echo "🧪 Run 'npm test' to see detailed test results"
  exit 1
fi

# Verify build works
npm run build 2>/dev/null
BUILD_EXIT_CODE=$?

if [ $BUILD_EXIT_CODE -ne 0 ]; then
  echo "❌ Build failed!"
  echo "🏗️  Run 'npm run build' to see detailed build errors"
  exit 1
fi

echo "✅ Pre-push validation passed!"
exit 0
`;

// Commit message hook
const commitMsgHook = `#!/bin/sh
#
# Swiss Budget Pro - Commit Message Hook
# Validates commit message format
#

commit_regex='^(feat|fix|docs|style|refactor|test|chore|perf|ci|build|revert)(\\(.+\\))?: .{1,50}'

error_msg="❌ Invalid commit message format!
📋 Use: type(scope): description
📝 Types: feat, fix, docs, style, refactor, test, chore, perf, ci, build, revert
📏 Keep description under 50 characters

Examples:
  feat(ui): add dark mode toggle
  fix(calc): correct tax calculation
  docs: update README with setup instructions"

if ! grep -qE "$commit_regex" "$1"; then
  echo "$error_msg"
  exit 1
fi
`;

// Write hooks
const hooks = [
  { name: 'pre-commit', content: preCommitHook },
  { name: 'pre-push', content: prePushHook },
  { name: 'commit-msg', content: commitMsgHook }
];

log('🔧 Setting up Git hooks...', 'blue');

hooks.forEach(({ name, content }) => {
  const hookPath = join(hooksDir, name);
  writeFileSync(hookPath, content);
  chmodSync(hookPath, 0o755); // Make executable
  log(`✅ Installed ${name} hook`, 'green');
});

log('\n🎉 Git hooks installed successfully!', 'bold');
log('\n📋 Hooks installed:', 'blue');
log('  • pre-commit: Runs linting and issue detection', 'green');
log('  • pre-push: Runs tests and build verification', 'green');
log('  • commit-msg: Validates commit message format', 'green');

log('\n💡 To bypass hooks (not recommended):', 'yellow');
log('  git commit --no-verify', 'yellow');
log('  git push --no-verify', 'yellow');

log('\n🔧 To manually run validations:', 'blue');
log('  npm run validate     # Quick validation', 'blue');
log('  npm run pre-commit   # Full pre-commit check', 'blue');
log('  node scripts/local-ci.js run  # Complete pipeline', 'blue');
