#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to fix duplicate component definitions in Swiss Budget Pro
 * This addresses the SwissBudgetProWithErrorBoundary reference error
 */

const fs = require('fs');
const path = require('path');

class ComponentFixer {
  constructor() {
    this.mainFile = path.join(__dirname, '..', 'retire.tsx');
    this.backupFile = path.join(__dirname, '..', 'retire.tsx.backup');
    this.issues = [];
  }

  run() {
    console.log('🔧 Swiss Budget Pro Component Fixer');
    console.log('=====================================');
    
    try {
      this.createBackup();
      this.analyzeFile();
      this.fixDuplicates();
      this.validateFix();
      this.reportResults();
    } catch (error) {
      console.error('❌ Error during fix process:', error.message);
      this.restoreBackup();
    }
  }

  createBackup() {
    console.log('📋 Creating backup of retire.tsx...');
    
    if (fs.existsSync(this.mainFile)) {
      fs.copyFileSync(this.mainFile, this.backupFile);
      console.log('✅ Backup created successfully');
    } else {
      throw new Error('retire.tsx not found');
    }
  }

  analyzeFile() {
    console.log('🔍 Analyzing file for duplicate components...');
    
    const content = fs.readFileSync(this.mainFile, 'utf8');
    
    // Check for duplicate SwissBudgetProWithErrorBoundary definitions
    const componentMatches = content.match(/const SwissBudgetProWithErrorBoundary/g) || [];
    if (componentMatches.length > 1) {
      this.issues.push({
        type: 'duplicate-component',
        count: componentMatches.length,
        message: `Found ${componentMatches.length} SwissBudgetProWithErrorBoundary definitions`
      });
    }

    // Check for duplicate default exports
    const exportMatches = content.match(/^export default SwissBudgetProWithErrorBoundary/gm) || [];
    if (exportMatches.length > 1) {
      this.issues.push({
        type: 'duplicate-export',
        count: exportMatches.length,
        message: `Found ${exportMatches.length} default export statements`
      });
    }

    // Check for orphaned closing braces
    const lines = content.split('\n');
    const orphanedBraces = lines.filter((line, index) => {
      const trimmed = line.trim();
      return trimmed === '};' && lines[index - 1] && lines[index - 1].trim() === '';
    });

    if (orphanedBraces.length > 0) {
      this.issues.push({
        type: 'orphaned-braces',
        count: orphanedBraces.length,
        message: `Found ${orphanedBraces.length} potentially orphaned closing braces`
      });
    }

    console.log(`📊 Analysis complete: ${this.issues.length} issues found`);
    this.issues.forEach(issue => {
      console.log(`  ⚠️  ${issue.message}`);
    });
  }

  fixDuplicates() {
    if (this.issues.length === 0) {
      console.log('✅ No issues found to fix');
      return;
    }

    console.log('🔧 Applying fixes...');
    
    let content = fs.readFileSync(this.mainFile, 'utf8');
    let fixesApplied = 0;

    // Fix 1: Remove duplicate component definitions
    const duplicateComponentIssue = this.issues.find(i => i.type === 'duplicate-component');
    if (duplicateComponentIssue) {
      console.log('  🔧 Fixing duplicate component definitions...');
      
      // Find all SwissBudgetProWithErrorBoundary definitions
      const componentRegex = /\/\*\*[\s\S]*?\*\/\s*const SwissBudgetProWithErrorBoundary[\s\S]*?(?=\n\n|\/\*|const |export |$)/g;
      const matches = content.match(componentRegex) || [];
      
      if (matches.length > 1) {
        // Keep only the first definition, remove others
        let firstMatch = true;
        content = content.replace(componentRegex, (match) => {
          if (firstMatch) {
            firstMatch = false;
            return match;
          }
          console.log('    ❌ Removed duplicate component definition');
          fixesApplied++;
          return '';
        });
      }
    }

    // Fix 2: Remove duplicate exports
    const duplicateExportIssue = this.issues.find(i => i.type === 'duplicate-export');
    if (duplicateExportIssue) {
      console.log('  🔧 Fixing duplicate export statements...');
      
      const exportLines = content.split('\n');
      let exportCount = 0;
      
      for (let i = 0; i < exportLines.length; i++) {
        if (exportLines[i].trim() === 'export default SwissBudgetProWithErrorBoundary;') {
          exportCount++;
          if (exportCount > 1) {
            exportLines[i] = '// Duplicate export removed';
            console.log(`    ❌ Removed duplicate export at line ${i + 1}`);
            fixesApplied++;
          }
        }
      }
      
      content = exportLines.join('\n');
    }

    // Fix 3: Clean up orphaned braces and empty sections
    console.log('  🔧 Cleaning up orphaned braces...');
    
    // Remove sections that look like orphaned code
    const orphanedSectionRegex = /\/\/ ORPHANED[\s\S]*?(?=\n\n|\/\*|const |export |$)/g;
    content = content.replace(orphanedSectionRegex, (match) => {
      console.log('    ❌ Removed orphaned code section');
      fixesApplied++;
      return '';
    });

    // Remove duplicate component sections
    const duplicateSectionRegex = /\/\*\*\s*\* DUPLICATE COMPONENTS[\s\S]*?(?=\n\n|\/\*|const |export |$)/g;
    content = content.replace(duplicateSectionRegex, (match) => {
      console.log('    ❌ Removed duplicate component section');
      fixesApplied++;
      return '';
    });

    // Clean up multiple empty lines
    content = content.replace(/\n\n\n+/g, '\n\n');

    // Write the fixed content
    fs.writeFileSync(this.mainFile, content, 'utf8');
    
    console.log(`✅ Applied ${fixesApplied} fixes`);
  }

  validateFix() {
    console.log('🔍 Validating fixes...');
    
    const content = fs.readFileSync(this.mainFile, 'utf8');
    
    // Re-check for issues
    const componentMatches = content.match(/const SwissBudgetProWithErrorBoundary/g) || [];
    const exportMatches = content.match(/^export default SwissBudgetProWithErrorBoundary/gm) || [];
    
    const validationResults = {
      componentDefinitions: componentMatches.length,
      exportStatements: exportMatches.length,
      isValid: componentMatches.length === 1 && exportMatches.length === 1
    };

    if (validationResults.isValid) {
      console.log('✅ Validation passed - file structure is now correct');
    } else {
      console.log('⚠️  Validation warnings:');
      if (validationResults.componentDefinitions !== 1) {
        console.log(`  - Expected 1 component definition, found ${validationResults.componentDefinitions}`);
      }
      if (validationResults.exportStatements !== 1) {
        console.log(`  - Expected 1 export statement, found ${validationResults.exportStatements}`);
      }
    }

    return validationResults;
  }

  restoreBackup() {
    console.log('🔄 Restoring backup due to error...');
    
    if (fs.existsSync(this.backupFile)) {
      fs.copyFileSync(this.backupFile, this.mainFile);
      console.log('✅ Backup restored successfully');
    }
  }

  reportResults() {
    console.log('\n📊 Fix Summary');
    console.log('================');
    console.log(`Issues found: ${this.issues.length}`);
    console.log(`Backup created: ${fs.existsSync(this.backupFile) ? 'Yes' : 'No'}`);
    
    if (this.issues.length > 0) {
      console.log('\nIssues addressed:');
      this.issues.forEach(issue => {
        console.log(`  ✅ ${issue.message}`);
      });
    }

    console.log('\n🎯 Next Steps:');
    console.log('1. Test the application to ensure it loads correctly');
    console.log('2. Check the browser console for any remaining errors');
    console.log('3. If issues persist, restore backup with: cp retire.tsx.backup retire.tsx');
    console.log('4. Open fix-runtime-errors.html for additional diagnostic tools');
    
    console.log('\n✅ Component fix process completed!');
  }
}

// Run the fixer
if (require.main === module) {
  const fixer = new ComponentFixer();
  fixer.run();
}

module.exports = ComponentFixer;
