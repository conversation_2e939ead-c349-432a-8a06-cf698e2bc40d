#!/bin/bash

# Fire or Retire Calculator - Development Script
# This script manages the development environment with volume mapping

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}🔥 Fire or Retire Calculator - Development Mode${NC}"
    echo -e "${CYAN}================================================${NC}"
}

# Function to check if <PERSON><PERSON> is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Function to start development environment
start_dev() {
    print_header
    print_status "Starting development environment with volume mapping..."
    
    # Stop any existing containers
    docker compose -f docker-compose.dev.yml down 2>/dev/null || true
    
    # Build and start development container
    print_status "Building development container..."
    docker compose -f docker-compose.dev.yml up --build -d
    
    print_success "Development environment started!"
    print_status "🌐 Application URLs:"
    echo -e "  ${CYAN}Development Server:${NC} http://localhost:5173"
    echo -e "  ${CYAN}Traefik URL:${NC}        http://fire-dev.docker.localhost"
    echo -e "  ${CYAN}Preview Server:${NC}     http://localhost:4173"
    
    print_status "📝 Useful commands:"
    echo -e "  ${YELLOW}View logs:${NC}     docker compose -f docker-compose.dev.yml logs -f"
    echo -e "  ${YELLOW}Stop dev:${NC}      docker compose -f docker-compose.dev.yml down"
    echo -e "  ${YELLOW}Restart:${NC}       docker compose -f docker-compose.dev.yml restart"
    echo -e "  ${YELLOW}Shell access:${NC}  docker compose -f docker-compose.dev.yml exec fire-or-retire bash"
}

# Function to stop development environment
stop_dev() {
    print_status "Stopping development environment..."
    docker compose -f docker-compose.dev.yml down
    print_success "Development environment stopped!"
}

# Function to show logs
show_logs() {
    print_status "Showing development logs (Ctrl+C to exit)..."
    docker compose -f docker-compose.dev.yml logs -f
}

# Function to restart development environment
restart_dev() {
    print_status "Restarting development environment..."
    docker compose -f docker-compose.dev.yml restart
    print_success "Development environment restarted!"
}

# Function to open shell in container
shell_access() {
    print_status "Opening shell in development container..."
    docker compose -f docker-compose.dev.yml exec fire-or-retire bash
}

# Function to build for production
build_prod() {
    print_status "Building production version..."
    docker compose -f docker-compose.dev.yml exec fire-or-retire npm run build
    print_success "Production build completed!"
}

# Function to show status
show_status() {
    print_header
    print_status "Development Environment Status:"
    docker compose -f docker-compose.dev.yml ps
    echo ""
    print_status "Container Health:"
    docker compose -f docker-compose.dev.yml exec fire-or-retire wget --spider -q http://localhost:5173 && \
        print_success "Development server is healthy" || \
        print_warning "Development server is not responding"
}

# Main script logic
case "${1:-start}" in
    "start")
        check_docker
        start_dev
        ;;
    "stop")
        stop_dev
        ;;
    "restart")
        check_docker
        restart_dev
        ;;
    "logs")
        show_logs
        ;;
    "shell")
        shell_access
        ;;
    "build")
        build_prod
        ;;
    "status")
        show_status
        ;;
    "help"|"-h"|"--help")
        print_header
        echo -e "${CYAN}Usage:${NC} $0 [command]"
        echo ""
        echo -e "${CYAN}Commands:${NC}"
        echo -e "  ${YELLOW}start${NC}    Start development environment (default)"
        echo -e "  ${YELLOW}stop${NC}     Stop development environment"
        echo -e "  ${YELLOW}restart${NC}  Restart development environment"
        echo -e "  ${YELLOW}logs${NC}     Show development logs"
        echo -e "  ${YELLOW}shell${NC}    Open shell in development container"
        echo -e "  ${YELLOW}build${NC}    Build production version"
        echo -e "  ${YELLOW}status${NC}   Show development environment status"
        echo -e "  ${YELLOW}help${NC}     Show this help message"
        ;;
    *)
        print_error "Unknown command: $1"
        print_status "Use '$0 help' to see available commands"
        exit 1
        ;;
esac
