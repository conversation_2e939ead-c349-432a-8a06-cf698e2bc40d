# Complete Debug Integration Guide

This comprehensive guide shows you how to integrate the advanced debugging and error logging system into your Swiss Budget Pro application to capture, analyze, and debug runtime errors effectively.

## 🚀 Complete Debugging System Overview

The debugging system includes:

1. **Debug Logger** - Comprehensive logging with categories and severity levels
2. **Performance Monitor** - Real-time performance tracking and optimization
3. **Error Reporter** - Automatic error capture with context and reproduction steps
4. **Error Diagnostics** - Intelligent error analysis with suggested fixes
5. **Enhanced Debug Panel** - Multi-tab interface for monitoring all aspects
6. **React Hooks** - Easy integration with React components
7. **Error Boundary** - Enhanced error catching with detailed reporting

## 📋 Quick Integration Checklist

- [ ] Wrap app with ErrorBoundary and RuntimeErrorMonitor
- [ ] Initialize debug systems in main App component
- [ ] Add performance monitoring to critical components
- [ ] Integrate Swiss-specific calculation tracking
- [ ] Test keyboard shortcuts and debug panel
- [ ] Verify error capture and reporting

## 🛠️ Step-by-Step Integration

### 1. Main App Integration

Update your main App component to include all monitoring systems:

```tsx
// src/App.tsx
import React, { useEffect } from 'react';
import ErrorBoundary from './components/ErrorBoundary';
import RuntimeErrorMonitor from './components/RuntimeErrorMonitor';
import { debugLogger } from './utils/debug-logger';
import { performanceMonitor } from './utils/performance-monitor';
import { errorReporter } from './utils/error-reporter';

// Your existing app component
import SwissBudgetPro from './SwissBudgetPro';

const App: React.FC = () => {
  useEffect(() => {
    // Initialize all debug systems
    debugLogger.log('info', 'app', 'Swiss Budget Pro starting', {
      version: '4.1.0',
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight,
      },
    });

    // Start performance monitoring
    performanceMonitor.startMonitoring();

    // Start error reporting
    errorReporter.startTracking();

    // Log system capabilities
    debugLogger.log('info', 'system', 'Debug systems initialized', {
      localStorage: !!window.localStorage,
      performance: !!window.performance,
      memory: !!(performance as any).memory,
      observer: !!window.PerformanceObserver,
    });

    return () => {
      debugLogger.log('info', 'app', 'Swiss Budget Pro shutting down');
      performanceMonitor.stopMonitoring();
      errorReporter.stopTracking();
    };
  }, []);

  return (
    <ErrorBoundary
      onError={(error, errorInfo) => {
        debugLogger.log(
          'error',
          'app',
          'Application error boundary triggered',
          {
            error: error.message,
            componentStack: errorInfo.componentStack,
          }
        );
      }}
    >
      <RuntimeErrorMonitor
        enableAutoCapture={true}
        showErrorToast={true}
        onError={error => {
          debugLogger.log('warn', 'runtime', 'Runtime error captured', {
            type: error.type,
            message: error.message,
            severity: error.severity,
          });
        }}
      >
        <SwissBudgetPro />
      </RuntimeErrorMonitor>
    </ErrorBoundary>
  );
};

export default App;
```

### 2. Component Integration with Performance Monitoring

Integrate performance monitoring into your existing components:

```tsx
// Example: Swiss Budget Component with Full Monitoring
import React, { useState, useEffect, useCallback } from 'react';
import {
  usePerformanceMonitoring,
  useSwissCalculationPerformance,
  useUserInteractionTracking,
} from '../hooks/usePerformanceMonitoring';
import { debugLogger } from '../utils/debug-logger';

const SwissBudgetComponent: React.FC = () => {
  const [data, setData] = useState({ income: 0, expenses: 0, canton: 'ZH' });
  const [results, setResults] = useState(null);

  // Performance monitoring hooks
  const { trackCalculation, trackAsyncOperation } = usePerformanceMonitoring({
    componentName: 'SwissBudgetComponent',
    trackRenders: true,
    trackCalculations: true,
  });

  const { trackTaxCalculation, trackFIRECalculation } =
    useSwissCalculationPerformance('SwissBudgetComponent');
  const { trackFormSubmission, trackCalculationTrigger } =
    useUserInteractionTracking('SwissBudgetComponent');

  // Tax calculation with monitoring
  const calculateTax = useCallback(
    (income: number, canton: string) => {
      return trackTaxCalculation(canton, income, () => {
        debugLogger.log('debug', 'tax-calc', 'Starting tax calculation', {
          income,
          canton,
        });

        // Your existing tax calculation logic
        const federalTax = income * 0.08;
        const cantonalTax = income * 0.12;
        const totalTax = federalTax + cantonalTax;

        debugLogger.log('info', 'tax-calc', 'Tax calculation completed', {
          income,
          canton,
          totalTax,
          effectiveRate: totalTax / income,
        });

        return { federalTax, cantonalTax, totalTax };
      });
    },
    [trackTaxCalculation]
  );

  // FIRE calculation with monitoring
  const calculateFIRE = useCallback(
    scenario => {
      return trackFIRECalculation(scenario, () => {
        debugLogger.log(
          'debug',
          'fire-calc',
          'Starting FIRE calculation',
          scenario
        );

        // Your existing FIRE calculation logic
        const annualExpenses = scenario.expenses * 12;
        const fireNumber = annualExpenses * 25;
        const yearsToFIRE =
          fireNumber / (scenario.income * 12 - annualExpenses);

        const result = { fireNumber, yearsToFIRE };

        debugLogger.log(
          'info',
          'fire-calc',
          'FIRE calculation completed',
          result
        );
        return result;
      });
    },
    [trackFIRECalculation]
  );

  // Form submission with tracking
  const handleSubmit = useCallback(
    e => {
      e.preventDefault();
      const endTracking = trackFormSubmission('budget_form', data);

      try {
        const taxResult = calculateTax(data.income, data.canton);
        const fireResult = calculateFIRE(data);
        setResults({ ...taxResult, ...fireResult });
      } finally {
        endTracking();
      }
    },
    [data, calculateTax, calculateFIRE, trackFormSubmission]
  );

  return <form onSubmit={handleSubmit}>{/* Your existing form JSX */}</form>;
};
```

### 3. Add Error Diagnostics for Specific Issues

For the specific errors you're seeing, add targeted diagnostics:

```tsx
// src/utils/specific-error-fixes.ts
import { debugLogger } from './debug-logger';
import { errorDiagnostics } from './error-diagnostics';

export const diagnoseCurrentErrors = () => {
  // Check for the specific errors you're seeing

  // 1. Tailwind CSS production warning
  if (document.querySelector('link[href*="tailwindcss"]')) {
    debugLogger.log('warn', 'tailwind', 'Tailwind CSS detected in production', {
      recommendation: 'Use Tailwind CLI for production builds',
      links: ['https://tailwindcss.com/docs/installation'],
    });
  }

  // 2. SwissBudgetProWithErrorBoundary reference error
  try {
    // Check if the component exists
    if (typeof SwissBudgetProWithErrorBoundary === 'undefined') {
      debugLogger.log(
        'error',
        'component',
        'SwissBudgetProWithErrorBoundary is not defined',
        {
          possibleCauses: [
            'Component not imported correctly',
            'Typo in component name',
            'Component not exported from module',
          ],
          suggestedFixes: [
            'Check import statement',
            'Verify component export',
            'Check file path',
          ],
        }
      );
    }
  } catch (error) {
    debugLogger.log(
      'error',
      'component',
      'Error checking component reference',
      { error }
    );
  }

  // Run comprehensive diagnostics
  const diagnostics = errorDiagnostics.runSystemDiagnostics();
  diagnostics.forEach(diagnostic => {
    debugLogger.log(
      diagnostic.severity === 'critical' || diagnostic.severity === 'high'
        ? 'error'
        : 'warn',
      'diagnostics',
      diagnostic.issue,
      diagnostic
    );
  });
};

// Call this function on app startup
export const initializeErrorDiagnostics = () => {
  // Run diagnostics after a short delay to let the app initialize
  setTimeout(diagnoseCurrentErrors, 2000);
};
```

## Keyboard Shortcuts

Once integrated, you'll have these keyboard shortcuts available:

- **Ctrl+Shift+D**: Toggle debug panel
- **Ctrl+Shift+L**: Download debug logs
- **Ctrl+Shift+C**: Clear debug logs

## 🎛️ Enhanced Debug Panel Features

The multi-tab debug panel provides comprehensive monitoring:

### 📝 Logs Tab

- **Real-time Error Monitoring**: See errors as they happen with timestamps
- **Advanced Filtering**: Filter by severity (error, warn, info, debug) and category
- **Stack Trace Analysis**: Expandable stack traces for detailed error investigation
- **Structured Data**: JSON data viewer for complex error contexts

### ⚡ Performance Tab

- **Performance Metrics Dashboard**: Real-time performance statistics
- **Slow Operation Detection**: Automatic identification of performance bottlenecks
- **Memory Usage Monitoring**: Track memory consumption and detect leaks
- **Component Render Tracking**: Monitor React component performance

### 🚨 Errors Tab

- **Error Report Management**: Comprehensive error reports with reproduction steps
- **Severity Classification**: Automatic error severity assessment
- **Context Capture**: Full application context at time of error
- **Export Functionality**: Download detailed error reports for analysis

### 🖥️ System Tab

- **Browser Information**: Complete browser and system details
- **Session Statistics**: User session duration and interaction tracking
- **Memory Analysis**: Real-time memory usage and performance metrics
- **Network Status**: Connection status and request monitoring

## Fixing Specific Errors

### 1. Tailwind CSS Production Warning

```bash
# Instead of using CDN in production, use Tailwind CLI
npm install -D tailwindcss
npx tailwindcss init

# Build CSS for production
npx tailwindcss -i ./src/input.css -o ./dist/output.css --watch
```

### 2. Component Reference Errors

Check your component imports and exports:

```tsx
// Make sure your component is properly exported
export default SwissBudgetPro;

// And properly imported
import SwissBudgetPro from './SwissBudgetPro';
```

### 3. Module Resolution Issues

```bash
# Clear node modules and reinstall
rm -rf node_modules package-lock.json
npm install

# Or use npm ci for clean install
npm ci
```

## Development vs Production

The debug tools automatically adjust based on environment:

- **Development**: Full debugging features, error toasts, debug panel
- **Production**: Silent error logging, minimal UI impact

## Error Reporting

When errors occur, you can:

1. **View in Debug Panel**: Real-time error monitoring
2. **Export Error Logs**: Download comprehensive error reports
3. **Console Logging**: Structured console output for development

## Integration Checklist

- [ ] Wrap app with `ErrorBoundary`
- [ ] Add `RuntimeErrorMonitor` component
- [ ] Import and initialize `debugLogger`
- [ ] Add debug logging to critical components
- [ ] Test keyboard shortcuts work
- [ ] Verify error capture in console
- [ ] Test error export functionality

## Testing the Integration

1. **Trigger a Test Error**:

```tsx
const TestErrorButton = () => (
  <button
    onClick={() => {
      throw new Error('Test error for debugging');
    }}
  >
    Trigger Test Error
  </button>
);
```

2. **Check Debug Panel**: Press Ctrl+Shift+D to open
3. **Verify Error Capture**: Error should appear in panel and console
4. **Test Export**: Use Ctrl+Shift+L to download logs

## 🔧 Advanced Usage and Troubleshooting

### Performance Optimization

Monitor and optimize your Swiss Budget Pro performance:

```tsx
// Track specific Swiss calculations
const { trackTaxCalculation, trackFIRECalculation } =
  useSwissCalculationPerformance('MyComponent');

// Monitor slow operations
useEffect(() => {
  const slowOps = performanceMonitor.getSlowOperations();
  if (slowOps.length > 0) {
    debugLogger.log('warn', 'performance', 'Slow operations detected', {
      count: slowOps.length,
      operations: slowOps.map(op => ({ name: op.name, duration: op.duration })),
    });
  }
}, []);
```

### Custom Error Handling

Implement custom error handling for specific scenarios:

```tsx
// Custom error handler for Swiss tax calculations
const handleTaxCalculationError = async (error: Error, context: any) => {
  // Generate detailed error report
  const report = await errorReporter.generateErrorReport(error, {
    componentName: 'TaxCalculator',
    type: 'calculation',
    context,
  });

  // Run diagnostics
  const diagnostics = errorDiagnostics.diagnoseError(error);

  // Log comprehensive error information
  debugLogger.log('error', 'tax-calculation', 'Tax calculation failed', {
    error: error.message,
    reportId: report.id,
    diagnostics: diagnostics.map(d => d.issue),
    context,
  });
};
```

### Memory Leak Detection

Monitor for memory leaks in long-running sessions:

```tsx
useEffect(() => {
  const checkMemoryUsage = () => {
    const snapshots = performanceMonitor.getMemorySnapshots();
    const latest = snapshots[snapshots.length - 1];

    if (latest && latest.percentage > 80) {
      debugLogger.log('warn', 'memory', 'High memory usage detected', {
        percentage: latest.percentage,
        used: `${(latest.usedJSHeapSize / 1024 / 1024).toFixed(1)}MB`,
        recommendation: 'Consider refreshing the page or clearing data',
      });
    }
  };

  const interval = setInterval(checkMemoryUsage, 30000); // Check every 30 seconds
  return () => clearInterval(interval);
}, []);
```

### Debugging Production Issues

For production debugging (with minimal performance impact):

```tsx
// Production-safe error reporting
if (process.env.NODE_ENV === 'production') {
  // Only capture critical errors in production
  window.addEventListener('error', async event => {
    if (event.error && event.error.message.includes('critical')) {
      const report = await errorReporter.generateErrorReport(event.error);
      // Send to error tracking service
      console.error('Critical error reported:', report.id);
    }
  });
}
```

## 🚨 Troubleshooting Common Issues

### Debug Panel Not Opening

- Verify keyboard shortcuts are working (Ctrl+Shift+D)
- Check console for JavaScript errors
- Ensure `useDebugPanel` hook is properly integrated

### Performance Monitoring Not Working

- Verify `performanceMonitor.startMonitoring()` is called
- Check browser support for Performance API
- Ensure components are using performance hooks correctly

### Error Reports Not Generating

- Verify `errorReporter.startTracking()` is called
- Check localStorage availability
- Ensure error boundary is properly wrapped around components

### Memory Usage Issues

- Monitor memory snapshots in debug panel
- Check for event listener cleanup in useEffect
- Verify large objects are being garbage collected

## 📊 Monitoring and Analytics

### Key Metrics to Track

1. **Error Rate**: Number of errors per session
2. **Performance**: Average render times and slow operations
3. **Memory Usage**: Peak memory consumption
4. **User Actions**: Interaction patterns before errors
5. **Browser Compatibility**: Error patterns across different browsers

### Setting Up Alerts

```tsx
// Set up automatic alerts for critical issues
const setupAlerts = () => {
  // Alert on high error rate
  const errorCount = debugLogger.getLogsByLevel('error').length;
  if (errorCount > 10) {
    debugLogger.log('critical', 'alerts', 'High error rate detected', {
      errorCount,
      recommendation: 'Investigate error patterns immediately',
    });
  }

  // Alert on memory issues
  const memorySnapshots = performanceMonitor.getMemorySnapshots();
  const latestMemory = memorySnapshots[memorySnapshots.length - 1];
  if (latestMemory && latestMemory.percentage > 90) {
    debugLogger.log('critical', 'alerts', 'Critical memory usage', {
      percentage: latestMemory.percentage,
      recommendation: 'Immediate action required',
    });
  }
};
```

## 🎯 Next Steps

1. **Integrate the components** into your existing app using the step-by-step guide
2. **Test with your current errors** to see detailed diagnostics and solutions
3. **Use the enhanced debug panel** to monitor real-time issues across all tabs
4. **Set up performance monitoring** for Swiss-specific calculations
5. **Export comprehensive error reports** when you encounter problems
6. **Monitor memory usage** and performance metrics over time
7. **Share error reports** for faster debugging and collaborative problem-solving

## 🎉 Benefits You'll Get

- **Faster Debugging**: Identify issues quickly with comprehensive error context
- **Performance Optimization**: Monitor and optimize Swiss calculation performance
- **Better User Experience**: Catch and fix issues before users encounter them
- **Comprehensive Monitoring**: Full visibility into application health and performance
- **Swiss-Specific Insights**: Specialized monitoring for Swiss financial calculations
- **Production-Ready**: Robust error handling suitable for production environments

The enhanced debugging system will transform how you identify, analyze, and fix runtime errors in Swiss Budget Pro, providing the tools and insights needed to maintain a high-quality financial planning application.
