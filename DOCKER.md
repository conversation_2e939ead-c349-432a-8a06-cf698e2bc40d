# 🔥 Fire or Retire Calculator - Docker Development Guide

This guide explains how to use the Docker-based development and production environments with volume mapping for live development.

## 🚀 Quick Start

### Development Mode (Recommended for Development)
```bash
# Start development environment with volume mapping
./fire.sh dev

# Or using npm
npm run docker:dev
```

### Production Mode
```bash
# Start production environment
./fire.sh prod

# Or using npm
npm run docker:prod
```

## 📁 Project Structure

```
fire_or_retire/
├── docker-compose.yml          # Production configuration
├── docker-compose.dev.yml      # Development configuration with volume mapping
├── Dockerfile                  # Production Dockerfile
├── Dockerfile.dev              # Development Dockerfile
├── fire.sh                     # Main management script
├── scripts/
│   ├── dev.sh                  # Development environment script
│   └── prod.sh                 # Production environment script
└── src/                        # Your source code (volume mapped in dev mode)
```

## 🛠️ Development Environment

The development environment uses **volume mapping** to enable live development:

### Features
- ✅ **Live Reload**: Changes to source files are immediately reflected
- ✅ **Volume Mapping**: Your local files are mapped into the container
- ✅ **Hot Module Replacement**: Instant updates without page refresh
- ✅ **Development Tools**: Full access to dev dependencies
- ✅ **Port 5173**: Vite development server
- ✅ **Port 4173**: Preview server for production builds

### URLs
- **Development Server**: http://localhost:5173
- **Traefik URL**: http://fire-dev.docker.localhost
- **Preview Server**: http://localhost:4173

### Commands
```bash
# Start development environment
./fire.sh dev start

# View logs
./fire.sh dev logs

# Open shell in container
./fire.sh dev shell

# Build production version in dev container
./fire.sh dev build

# Stop development environment
./fire.sh dev stop

# Check status
./fire.sh dev status
```

### NPM Shortcuts
```bash
npm run docker:dev          # Start dev environment
npm run docker:dev:logs     # View dev logs
npm run docker:dev:shell    # Open dev shell
npm run docker:dev:stop     # Stop dev environment
```

## 🏭 Production Environment

The production environment creates optimized builds:

### Features
- ✅ **Optimized Build**: Minified and optimized for production
- ✅ **Static File Serving**: Efficient static file serving
- ✅ **Health Checks**: Automatic health monitoring
- ✅ **Port 4173**: Production preview server

### URLs
- **Production Server**: http://localhost:4173
- **Traefik URL**: http://fire.docker.localhost

### Commands
```bash
# Start production environment
./fire.sh prod start

# Deploy latest changes (rebuild and restart)
./fire.sh prod deploy

# View logs
./fire.sh prod logs

# Open shell in container
./fire.sh prod shell

# Stop production environment
./fire.sh prod stop

# Check status
./fire.sh prod status
```

### NPM Shortcuts
```bash
npm run docker:prod         # Start prod environment
npm run docker:prod:deploy  # Deploy latest changes
npm run docker:prod:logs    # View prod logs
npm run docker:prod:shell   # Open prod shell
npm run docker:prod:stop    # Stop prod environment
```

## 🔧 Development Workflow

### 1. Start Development Environment
```bash
./fire.sh dev
```

### 2. Edit Files Locally
- Edit files in your local `src/` directory
- Changes are automatically reflected in the browser
- No need to rebuild the container

### 3. View Changes
- Open http://localhost:5173 in your browser
- Changes appear instantly with hot reload

### 4. Build for Production Testing
```bash
./fire.sh dev build
```

### 5. Deploy to Production
```bash
./fire.sh prod deploy
```

## 📊 Volume Mapping Details

### Development Mode Volumes
```yaml
volumes:
  - .:/app                    # Map entire project directory
  - /app/node_modules         # Exclude node_modules (use container's version)
  - /app/dist                 # Exclude dist (generated files)
```

### Benefits
- **Instant Updates**: No container rebuilds needed
- **Local Development**: Use your preferred editor/IDE
- **Persistent Changes**: All changes are saved locally
- **Fast Iteration**: Immediate feedback loop

## 🌐 Traefik Integration

Both environments integrate with Traefik reverse proxy:

### Development
- **URL**: http://fire-dev.docker.localhost
- **Port**: 5173 (Vite dev server)

### Production
- **URL**: http://fire.docker.localhost
- **Port**: 4173 (Static file server)

## 🔍 Troubleshooting

### Container Won't Start
```bash
# Check Docker is running
docker info

# Check for port conflicts
docker ps
netstat -tulpn | grep :5173
netstat -tulpn | grep :4173
```

### Volume Mapping Issues
```bash
# Check volume mounts
docker compose -f docker-compose.dev.yml exec fire-or-retire ls -la /app

# Restart with fresh volumes
./fire.sh dev stop
docker volume prune
./fire.sh dev start
```

### File Permission Issues
```bash
# Fix file permissions (if needed)
sudo chown -R $USER:$USER .
```

### Hot Reload Not Working
```bash
# Check if polling is enabled
./fire.sh dev shell
echo $CHOKIDAR_USEPOLLING
echo $WATCHPACK_POLLING
```

## 📝 Environment Variables

### Development
- `NODE_ENV=development`
- `CHOKIDAR_USEPOLLING=true`
- `WATCHPACK_POLLING=true`

### Production
- `NODE_ENV=production`
- `VITE_APP_NAME=Swiss Budget Pro`
- `VITE_APP_VERSION=1.0.0`

## 🎯 Best Practices

1. **Use Development Mode for Coding**: Always use `./fire.sh dev` for development
2. **Test Production Builds**: Regularly test with `./fire.sh dev build`
3. **Deploy Frequently**: Use `./fire.sh prod deploy` for production updates
4. **Monitor Logs**: Use `logs` command to monitor application health
5. **Clean Up**: Stop environments when not in use to save resources

## 🆘 Getting Help

```bash
# Show help for main script
./fire.sh help

# Show help for development
./fire.sh dev help

# Show help for production
./fire.sh prod help
```

## 🔄 Migration from Old Setup

If you were using the old Docker setup:

1. **Stop old containers**: `docker compose down`
2. **Start new development**: `./fire.sh dev`
3. **Enjoy live development**: Edit files and see instant changes!

---

**Happy Coding! 🔥**
