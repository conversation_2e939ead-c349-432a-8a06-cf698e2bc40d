{"fileNames": ["./node_modules/typescript/lib/lib.es5.d.ts", "./node_modules/typescript/lib/lib.es2015.d.ts", "./node_modules/typescript/lib/lib.es2016.d.ts", "./node_modules/typescript/lib/lib.es2017.d.ts", "./node_modules/typescript/lib/lib.es2018.d.ts", "./node_modules/typescript/lib/lib.es2019.d.ts", "./node_modules/typescript/lib/lib.es2020.d.ts", "./node_modules/typescript/lib/lib.dom.d.ts", "./node_modules/typescript/lib/lib.dom.iterable.d.ts", "./node_modules/typescript/lib/lib.es2015.core.d.ts", "./node_modules/typescript/lib/lib.es2015.collection.d.ts", "./node_modules/typescript/lib/lib.es2015.generator.d.ts", "./node_modules/typescript/lib/lib.es2015.iterable.d.ts", "./node_modules/typescript/lib/lib.es2015.promise.d.ts", "./node_modules/typescript/lib/lib.es2015.proxy.d.ts", "./node_modules/typescript/lib/lib.es2015.reflect.d.ts", "./node_modules/typescript/lib/lib.es2015.symbol.d.ts", "./node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "./node_modules/typescript/lib/lib.es2016.array.include.d.ts", "./node_modules/typescript/lib/lib.es2016.intl.d.ts", "./node_modules/typescript/lib/lib.es2017.arraybuffer.d.ts", "./node_modules/typescript/lib/lib.es2017.date.d.ts", "./node_modules/typescript/lib/lib.es2017.object.d.ts", "./node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "./node_modules/typescript/lib/lib.es2017.string.d.ts", "./node_modules/typescript/lib/lib.es2017.intl.d.ts", "./node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "./node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "./node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "./node_modules/typescript/lib/lib.es2018.intl.d.ts", "./node_modules/typescript/lib/lib.es2018.promise.d.ts", "./node_modules/typescript/lib/lib.es2018.regexp.d.ts", "./node_modules/typescript/lib/lib.es2019.array.d.ts", "./node_modules/typescript/lib/lib.es2019.object.d.ts", "./node_modules/typescript/lib/lib.es2019.string.d.ts", "./node_modules/typescript/lib/lib.es2019.symbol.d.ts", "./node_modules/typescript/lib/lib.es2019.intl.d.ts", "./node_modules/typescript/lib/lib.es2020.bigint.d.ts", "./node_modules/typescript/lib/lib.es2020.date.d.ts", "./node_modules/typescript/lib/lib.es2020.promise.d.ts", "./node_modules/typescript/lib/lib.es2020.sharedmemory.d.ts", "./node_modules/typescript/lib/lib.es2020.string.d.ts", "./node_modules/typescript/lib/lib.es2020.symbol.wellknown.d.ts", "./node_modules/typescript/lib/lib.es2020.intl.d.ts", "./node_modules/typescript/lib/lib.es2020.number.d.ts", "./node_modules/typescript/lib/lib.decorators.d.ts", "./node_modules/typescript/lib/lib.decorators.legacy.d.ts", "./node_modules/@types/react/global.d.ts", "./node_modules/csstype/index.d.ts", "./node_modules/@types/prop-types/index.d.ts", "./node_modules/@types/react/index.d.ts", "./node_modules/@types/react/jsx-runtime.d.ts", "./node_modules/@remix-run/router/dist/history.d.ts", "./node_modules/@remix-run/router/dist/utils.d.ts", "./node_modules/@remix-run/router/dist/router.d.ts", "./node_modules/@remix-run/router/dist/index.d.ts", "./node_modules/react-router/dist/lib/context.d.ts", "./node_modules/react-router/dist/lib/components.d.ts", "./node_modules/react-router/dist/lib/hooks.d.ts", "./node_modules/react-router/dist/lib/deprecations.d.ts", "./node_modules/react-router/dist/index.d.ts", "./node_modules/react-router-dom/dist/dom.d.ts", "./node_modules/react-router-dom/dist/index.d.ts", "./node_modules/react-i18next/helpers.d.ts", "./node_modules/i18next/typescript/helpers.d.ts", "./node_modules/i18next/typescript/options.d.ts", "./node_modules/i18next/typescript/t.d.ts", "./node_modules/i18next/index.d.ts", "./node_modules/i18next/index.d.mts", "./node_modules/react-i18next/TransWithoutContext.d.ts", "./node_modules/react-i18next/initReactI18next.d.ts", "./node_modules/react-i18next/index.d.ts", "./node_modules/react-i18next/index.d.mts", "./src/contexts/AdminConfigContext.tsx", "./src/components/admin/InflationConfigPanel.tsx", "./src/components/admin/MarketDataConfigPanel.tsx", "./src/components/admin/TaxConfigPanel.tsx", "./src/components/admin/HealthcareConfigPanel.tsx", "./src/components/admin/SystemConfigPanel.tsx", "./src/components/admin/ConfigImportExport.tsx", "./src/components/admin/ConfigAuditLog.tsx", "./src/components/admin/AdminAuthGuard.tsx", "./src/components/admin/AdminPage.tsx", "./src/types/expenses.ts", "./src/hooks/useLocalStorage.ts", "./src/components/ExpenseManager.tsx", "./src/types/investments.ts", "./src/components/InvestmentPortfolioManager.tsx", "./src/types/savings.ts", "./src/components/SavingsGoalsManager.tsx", "./src/components/FireOrRetireCalculator.tsx", "./src/App.tsx", "./node_modules/@types/react-dom/client.d.ts", "./node_modules/i18next-browser-languagedetector/index.d.ts", "./node_modules/i18next-browser-languagedetector/index.d.mts", "./src/i18n/config.ts", "./src/main.tsx", "./src/components/AIFinancialAdvisor.tsx", "./src/types/income.ts", "./src/components/AdditionalIncomeManager.tsx", "./src/components/AdvancedPortfolioRebalancing.tsx", "./src/components/AdvancedTaxCalculator.tsx", "./src/components/AutoSaveManager.tsx", "./src/components/CantonRelocationAnalysis.tsx", "./src/components/CryptocurrencyPortfolioManager.tsx", "./src/components/DataExportImport.tsx", "./src/utils/debug-logger.ts", "./src/components/DebugPanel.tsx", "./src/components/ErrorBoundary.tsx", "./src/components/ErrorRecovery.tsx", "./node_modules/decimal.js/decimal.d.ts", "./src/utils/swiss-tax-calculations.ts", "./src/utils/healthcare-calculations.ts", "./src/utils/healthcare-fire-integration.ts", "./src/data/swiss-health-insurance-data.ts", "./src/components/HealthcareCostOptimizer.tsx", "./src/components/HistoricalTrackingCharts.tsx", "./src/components/MonteCarloSimulation.tsx", "./src/components/OnboardingWizard.tsx", "./src/components/PerformanceOptimizer.tsx", "./src/components/ProgressTracker.tsx", "./src/components/RealEstateInvestmentTracker.tsx", "./src/components/RiskAssessmentMetrics.tsx", "./src/hooks/useDebugPanel.ts", "./src/components/RuntimeErrorMonitor.tsx", "./src/components/SafeWithdrawalRateAnalysis.tsx", "./src/components/SmartDashboard.tsx", "./src/types/swiss-tax.ts", "./src/utils/swiss-tax-calculator.ts", "./src/components/SwissTaxCalculator.tsx", "./src/components/SwissTaxDeductionOptimizer.tsx", "./src/components/SwissTaxOptimizationEngine.tsx", "./src/utils/swiss-social-insurance.ts", "./src/components/SwissTaxPlanningDashboard.tsx", "./src/components/LanguageSwitcher/LanguageSwitcher.tsx", "./src/components/LanguageSwitcher/index.ts", "./src/components/improved/OnboardingWizard.tsx", "./src/components/improved/SmartDashboard.tsx", "./src/security/security-monitor.ts", "./src/security/privacy-controls.ts", "./src/security/encryption.ts", "./src/components/security/SecurityDashboard.tsx", "./src/utils/performance-monitor.ts", "./src/utils/error-diagnostics.ts", "./src/utils/error-reporter.ts", "./src/hooks/usePerformanceMonitoring.ts", "./src/examples/DebuggedSwissBudgetComponent.tsx", "./src/hooks/useDateFormat.ts", "./src/hooks/useNumberFormat.ts", "./src/locales/en/common.json", "./src/locales/en/financial.json", "./src/locales/en/swiss.json", "./src/locales/en/forms.json", "./src/locales/en/reports.json", "./src/locales/en/errors.json", "./src/locales/de/common.json", "./src/locales/de/financial.json", "./src/locales/de/swiss.json", "./src/locales/de/forms.json", "./src/locales/de/reports.json", "./src/locales/de/errors.json", "./src/i18n/index.ts", "./src/services/MonteCarloEngine.ts", "./src/utils/financial-calculations.ts", "./src/utils/fix-current-errors.ts", "./node_modules/playwright-core/types/protocol.d.ts", "./node_modules/playwright-core/types/structs.d.ts", "./node_modules/playwright-core/types/types.d.ts", "./node_modules/playwright-core/index.d.ts", "./node_modules/playwright/types/test.d.ts", "./node_modules/playwright/test.d.ts", "./node_modules/@playwright/test/index.d.ts", "./tests/e2e/debug-fire-projection.spec.ts", "./tests/e2e/debug-goals-tab.spec.ts", "./tests/e2e/debug-page-load.spec.ts", "./tests/e2e/financial-calculations-e2e.spec.ts", "./tests/e2e/security-workflow.spec.ts", "./tests/page-objects/pages/SwissBudgetProPage.ts", "./tests/e2e/accessibility/wcag-compliance.spec.ts", "./tests/e2e/config/global-setup.ts", "./tests/e2e/config/global-teardown.ts", "./tests/e2e/config/test-config.ts", "./tests/fixtures/swiss-test-data.json", "./tests/e2e/critical-flows/fire-calculation.spec.ts", "./tests/e2e/critical-flows/healthcare-optimization.spec.ts", "./tests/fixtures/swiss-financial-data.json", "./tests/e2e/critical-flows/tax-optimization.spec.ts", "./tests/e2e/utils/test-helpers.ts", "./tests/e2e/pages/base-page.ts", "./tests/e2e/pages/healthcare-optimizer-page.ts", "./tests/e2e/fixtures/healthcare-scenarios.ts", "./tests/e2e/fixtures/swiss-scenarios.ts", "./tests/e2e/mobile/responsive-design.spec.ts", "./tests/e2e/pages/dashboard-page.ts", "./tests/e2e/performance/load-performance.spec.ts", "./tests/e2e/scripts/run-tests.ts", "./tests/e2e/tests/accessibility/accessibility-compliance.spec.ts", "./tests/e2e/tests/accessibility/healthcare-accessibility.spec.ts", "./tests/e2e/tests/advanced-features/risk-assessment-metrics.spec.ts", "./tests/e2e/tests/advanced-features/safe-withdrawal-rate-analysis.spec.ts", "./tests/e2e/tests/analytics/historical-tracking-charts.spec.ts", "./tests/e2e/tests/comprehensive/premium-advanced-features.spec.ts", "./tests/e2e/tests/critical-path/financial-planning-journey.spec.ts", "./tests/e2e/tests/data-management/import-export.spec.ts", "./tests/e2e/tests/data-persistence/localStorage-edge-cases.spec.ts", "./tests/e2e/tests/demo/comprehensive-demo.spec.ts", "./tests/e2e/tests/error-handling/edge-cases.spec.ts", "./tests/e2e/tests/form-validation/input-validation.spec.ts", "./tests/e2e/tests/internationalization/language-switching.spec.ts", "./tests/e2e/tests/mobile/responsive-design.spec.ts", "./tests/e2e/tests/performance/advanced-performance.spec.ts", "./tests/e2e/tests/performance/healthcare-performance.spec.ts", "./tests/e2e/tests/performance/performance-benchmarks.spec.ts", "./tests/e2e/tests/premium-features/ai-financial-advisor.spec.ts", "./tests/e2e/tests/smoke/basic-functionality.spec.ts", "./tests/e2e/tests/smoke/simple-test.spec.ts", "./tests/e2e/tests/swiss-features/healthcare-cost-optimizer.spec.ts", "./tests/e2e/tests/swiss-features/tax-optimization.spec.ts", "./tests/e2e/tests/user-journeys/complete-workflows.spec.ts", "./tests/e2e/tests/user-journeys/healthcare-user-journeys.spec.ts", "./tests/e2e/tests/visual/visual-regression.spec.ts", "./tests/setup/global-setup.ts", "./tests/setup/global-teardown.ts", "./node_modules/@vitest/pretty-format/dist/index.d.ts", "./node_modules/@vitest/utils/dist/types.d.ts", "./node_modules/@vitest/utils/dist/helpers.d.ts", "./node_modules/tinyrainbow/dist/index-8b61d5bc.d.ts", "./node_modules/tinyrainbow/dist/node.d.ts", "./node_modules/@vitest/utils/dist/index.d.ts", "./node_modules/@vitest/runner/dist/tasks.d-hsdzc98-.d.ts", "./node_modules/@vitest/utils/dist/types.d-BCElaP-c.d.ts", "./node_modules/@vitest/utils/dist/diff.d.ts", "./node_modules/@vitest/runner/dist/types.d.ts", "./node_modules/@vitest/utils/dist/error.d.ts", "./node_modules/@vitest/runner/dist/index.d.ts", "./node_modules/vitest/optional-types.d.ts", "./node_modules/vitest/dist/chunks/environment.d.Dmw5ulng.d.ts", "./node_modules/@types/node/compatibility/disposable.d.ts", "./node_modules/@types/node/compatibility/indexable.d.ts", "./node_modules/@types/node/compatibility/iterators.d.ts", "./node_modules/@types/node/compatibility/index.d.ts", "./node_modules/@types/node/globals.typedarray.d.ts", "./node_modules/@types/node/buffer.buffer.d.ts", "./node_modules/undici-types/header.d.ts", "./node_modules/undici-types/readable.d.ts", "./node_modules/undici-types/file.d.ts", "./node_modules/undici-types/fetch.d.ts", "./node_modules/undici-types/formdata.d.ts", "./node_modules/undici-types/connector.d.ts", "./node_modules/undici-types/client.d.ts", "./node_modules/undici-types/errors.d.ts", "./node_modules/undici-types/dispatcher.d.ts", "./node_modules/undici-types/global-dispatcher.d.ts", "./node_modules/undici-types/global-origin.d.ts", "./node_modules/undici-types/pool-stats.d.ts", "./node_modules/undici-types/pool.d.ts", "./node_modules/undici-types/handlers.d.ts", "./node_modules/undici-types/balanced-pool.d.ts", "./node_modules/undici-types/agent.d.ts", "./node_modules/undici-types/mock-interceptor.d.ts", "./node_modules/undici-types/mock-agent.d.ts", "./node_modules/undici-types/mock-client.d.ts", "./node_modules/undici-types/mock-pool.d.ts", "./node_modules/undici-types/mock-errors.d.ts", "./node_modules/undici-types/proxy-agent.d.ts", "./node_modules/undici-types/env-http-proxy-agent.d.ts", "./node_modules/undici-types/retry-handler.d.ts", "./node_modules/undici-types/retry-agent.d.ts", "./node_modules/undici-types/api.d.ts", "./node_modules/undici-types/interceptors.d.ts", "./node_modules/undici-types/util.d.ts", "./node_modules/undici-types/cookies.d.ts", "./node_modules/undici-types/patch.d.ts", "./node_modules/undici-types/websocket.d.ts", "./node_modules/undici-types/eventsource.d.ts", "./node_modules/undici-types/filereader.d.ts", "./node_modules/undici-types/diagnostics-channel.d.ts", "./node_modules/undici-types/content-type.d.ts", "./node_modules/undici-types/cache.d.ts", "./node_modules/undici-types/index.d.ts", "./node_modules/@types/node/globals.d.ts", "./node_modules/@types/node/assert.d.ts", "./node_modules/@types/node/assert/strict.d.ts", "./node_modules/@types/node/async_hooks.d.ts", "./node_modules/@types/node/buffer.d.ts", "./node_modules/@types/node/child_process.d.ts", "./node_modules/@types/node/cluster.d.ts", "./node_modules/@types/node/console.d.ts", "./node_modules/@types/node/constants.d.ts", "./node_modules/@types/node/crypto.d.ts", "./node_modules/@types/node/dgram.d.ts", "./node_modules/@types/node/diagnostics_channel.d.ts", "./node_modules/@types/node/dns.d.ts", "./node_modules/@types/node/dns/promises.d.ts", "./node_modules/@types/node/domain.d.ts", "./node_modules/@types/node/dom-events.d.ts", "./node_modules/@types/node/events.d.ts", "./node_modules/@types/node/fs.d.ts", "./node_modules/@types/node/fs/promises.d.ts", "./node_modules/@types/node/http.d.ts", "./node_modules/@types/node/http2.d.ts", "./node_modules/@types/node/https.d.ts", "./node_modules/@types/node/inspector.d.ts", "./node_modules/@types/node/module.d.ts", "./node_modules/@types/node/net.d.ts", "./node_modules/@types/node/os.d.ts", "./node_modules/@types/node/path.d.ts", "./node_modules/@types/node/perf_hooks.d.ts", "./node_modules/@types/node/process.d.ts", "./node_modules/@types/node/punycode.d.ts", "./node_modules/@types/node/querystring.d.ts", "./node_modules/@types/node/readline.d.ts", "./node_modules/@types/node/readline/promises.d.ts", "./node_modules/@types/node/repl.d.ts", "./node_modules/@types/node/sea.d.ts", "./node_modules/@types/node/stream.d.ts", "./node_modules/@types/node/stream/promises.d.ts", "./node_modules/@types/node/stream/consumers.d.ts", "./node_modules/@types/node/stream/web.d.ts", "./node_modules/@types/node/string_decoder.d.ts", "./node_modules/@types/node/test.d.ts", "./node_modules/@types/node/timers.d.ts", "./node_modules/@types/node/timers/promises.d.ts", "./node_modules/@types/node/tls.d.ts", "./node_modules/@types/node/trace_events.d.ts", "./node_modules/@types/node/tty.d.ts", "./node_modules/@types/node/url.d.ts", "./node_modules/@types/node/util.d.ts", "./node_modules/@types/node/v8.d.ts", "./node_modules/@types/node/vm.d.ts", "./node_modules/@types/node/wasi.d.ts", "./node_modules/@types/node/worker_threads.d.ts", "./node_modules/@types/node/zlib.d.ts", "./node_modules/@types/node/index.d.ts", "./node_modules/@types/estree/index.d.ts", "./node_modules/rollup/dist/rollup.d.ts", "./node_modules/rollup/dist/parseAst.d.ts", "./node_modules/vite/types/hmrPayload.d.ts", "./node_modules/vite/types/customEvent.d.ts", "./node_modules/vite/types/hot.d.ts", "./node_modules/vite/dist/node/moduleRunnerTransport.d-DJ_mE5sf.d.ts", "./node_modules/vite/dist/node/module-runner.d.ts", "./node_modules/esbuild/lib/main.d.ts", "./node_modules/source-map-js/source-map.d.ts", "./node_modules/postcss/lib/previous-map.d.ts", "./node_modules/postcss/lib/input.d.ts", "./node_modules/postcss/lib/css-syntax-error.d.ts", "./node_modules/postcss/lib/declaration.d.ts", "./node_modules/postcss/lib/root.d.ts", "./node_modules/postcss/lib/warning.d.ts", "./node_modules/postcss/lib/lazy-result.d.ts", "./node_modules/postcss/lib/no-work-result.d.ts", "./node_modules/postcss/lib/processor.d.ts", "./node_modules/postcss/lib/result.d.ts", "./node_modules/postcss/lib/document.d.ts", "./node_modules/postcss/lib/rule.d.ts", "./node_modules/postcss/lib/node.d.ts", "./node_modules/postcss/lib/comment.d.ts", "./node_modules/postcss/lib/container.d.ts", "./node_modules/postcss/lib/at-rule.d.ts", "./node_modules/postcss/lib/list.d.ts", "./node_modules/postcss/lib/postcss.d.ts", "./node_modules/postcss/lib/postcss.d.mts", "./node_modules/lightningcss/node/ast.d.ts", "./node_modules/lightningcss/node/targets.d.ts", "./node_modules/lightningcss/node/index.d.ts", "./node_modules/vite/types/internal/lightningcssOptions.d.ts", "./node_modules/vite/types/internal/cssPreprocessorOptions.d.ts", "./node_modules/vite/types/importGlob.d.ts", "./node_modules/vite/types/metadata.d.ts", "./node_modules/vite/dist/node/index.d.ts", "./node_modules/@vitest/mocker/dist/registry.d-D765pazg.d.ts", "./node_modules/@vitest/mocker/dist/types.d-D_aRZRdy.d.ts", "./node_modules/@vitest/mocker/dist/index.d.ts", "./node_modules/@vitest/utils/dist/source-map.d.ts", "./node_modules/vite-node/dist/trace-mapping.d-DLVdEqOp.d.ts", "./node_modules/vite-node/dist/index.d-CWZbpOcv.d.ts", "./node_modules/vite-node/dist/index.d.ts", "./node_modules/@vitest/snapshot/dist/environment.d-DHdQ1Csl.d.ts", "./node_modules/@vitest/snapshot/dist/rawSnapshot.d-lFsMJFUd.d.ts", "./node_modules/@vitest/snapshot/dist/index.d.ts", "./node_modules/@vitest/snapshot/dist/environment.d.ts", "./node_modules/vitest/dist/chunks/config.d.UqE-KR0o.d.ts", "./node_modules/vitest/dist/chunks/worker.d.CHGSOG0s.d.ts", "./node_modules/@vitest/runner/dist/utils.d.ts", "./node_modules/tinybench/dist/index.d.ts", "./node_modules/vitest/dist/chunks/benchmark.d.BwvBVTda.d.ts", "./node_modules/vite-node/dist/client.d.ts", "./node_modules/vitest/dist/chunks/coverage.d.S9RMNXIe.d.ts", "./node_modules/@vitest/snapshot/dist/manager.d.ts", "./node_modules/vitest/dist/chunks/reporters.d.C-cu31ET.d.ts", "./node_modules/vitest/dist/chunks/worker.d.C-KN07Ls.d.ts", "./node_modules/@vitest/expect/dist/chai.d.cts", "./node_modules/@vitest/spy/dist/index.d.ts", "./node_modules/@vitest/expect/dist/index.d.ts", "./node_modules/@vitest/expect/index.d.ts", "./node_modules/vitest/dist/chunks/global.d.CXRAxnWc.d.ts", "./node_modules/vitest/dist/chunks/vite.d.iXCEVtFP.d.ts", "./node_modules/vitest/dist/chunks/mocker.d.BE_2ls6u.d.ts", "./node_modules/vitest/dist/chunks/suite.d.FvehnV49.d.ts", "./node_modules/expect-type/dist/utils.d.ts", "./node_modules/expect-type/dist/overloads.d.ts", "./node_modules/expect-type/dist/branding.d.ts", "./node_modules/expect-type/dist/messages.d.ts", "./node_modules/expect-type/dist/index.d.ts", "./node_modules/vitest/dist/index.d.ts", "./node_modules/@types/aria-query/index.d.ts", "./node_modules/@testing-library/dom/types/matches.d.ts", "./node_modules/@testing-library/dom/types/wait-for.d.ts", "./node_modules/@testing-library/dom/types/query-helpers.d.ts", "./node_modules/@testing-library/dom/types/queries.d.ts", "./node_modules/@testing-library/dom/types/get-queries-for-element.d.ts", "./node_modules/@testing-library/dom/node_modules/pretty-format/build/types.d.ts", "./node_modules/@testing-library/dom/node_modules/pretty-format/build/index.d.ts", "./node_modules/@testing-library/dom/types/screen.d.ts", "./node_modules/@testing-library/dom/types/wait-for-element-to-be-removed.d.ts", "./node_modules/@testing-library/dom/types/get-node-text.d.ts", "./node_modules/@testing-library/dom/types/events.d.ts", "./node_modules/@testing-library/dom/types/pretty-dom.d.ts", "./node_modules/@testing-library/dom/types/role-helpers.d.ts", "./node_modules/@testing-library/dom/types/config.d.ts", "./node_modules/@testing-library/dom/types/suggestions.d.ts", "./node_modules/@testing-library/dom/types/index.d.ts", "./node_modules/@types/react-dom/test-utils/index.d.ts", "./node_modules/@testing-library/react/types/index.d.ts", "./node_modules/@testing-library/user-event/dist/types/event/eventMap.d.ts", "./node_modules/@testing-library/user-event/dist/types/event/types.d.ts", "./node_modules/@testing-library/user-event/dist/types/event/dispatchEvent.d.ts", "./node_modules/@testing-library/user-event/dist/types/event/focus.d.ts", "./node_modules/@testing-library/user-event/dist/types/event/input.d.ts", "./node_modules/@testing-library/user-event/dist/types/utils/click/isClickableInput.d.ts", "./node_modules/@testing-library/user-event/dist/types/utils/dataTransfer/Blob.d.ts", "./node_modules/@testing-library/user-event/dist/types/utils/dataTransfer/DataTransfer.d.ts", "./node_modules/@testing-library/user-event/dist/types/utils/dataTransfer/FileList.d.ts", "./node_modules/@testing-library/user-event/dist/types/utils/dataTransfer/Clipboard.d.ts", "./node_modules/@testing-library/user-event/dist/types/utils/edit/timeValue.d.ts", "./node_modules/@testing-library/user-event/dist/types/utils/edit/isContentEditable.d.ts", "./node_modules/@testing-library/user-event/dist/types/utils/edit/isEditable.d.ts", "./node_modules/@testing-library/user-event/dist/types/utils/edit/maxLength.d.ts", "./node_modules/@testing-library/user-event/dist/types/utils/edit/setFiles.d.ts", "./node_modules/@testing-library/user-event/dist/types/utils/focus/cursor.d.ts", "./node_modules/@testing-library/user-event/dist/types/utils/focus/getActiveElement.d.ts", "./node_modules/@testing-library/user-event/dist/types/utils/focus/getTabDestination.d.ts", "./node_modules/@testing-library/user-event/dist/types/utils/focus/isFocusable.d.ts", "./node_modules/@testing-library/user-event/dist/types/utils/focus/selection.d.ts", "./node_modules/@testing-library/user-event/dist/types/utils/focus/selector.d.ts", "./node_modules/@testing-library/user-event/dist/types/utils/keyDef/readNextDescriptor.d.ts", "./node_modules/@testing-library/user-event/dist/types/utils/misc/cloneEvent.d.ts", "./node_modules/@testing-library/user-event/dist/types/utils/misc/findClosest.d.ts", "./node_modules/@testing-library/user-event/dist/types/utils/misc/getDocumentFromNode.d.ts", "./node_modules/@testing-library/user-event/dist/types/utils/misc/getTreeDiff.d.ts", "./node_modules/@testing-library/user-event/dist/types/utils/misc/getWindow.d.ts", "./node_modules/@testing-library/user-event/dist/types/utils/misc/isDescendantOrSelf.d.ts", "./node_modules/@testing-library/user-event/dist/types/utils/misc/isElementType.d.ts", "./node_modules/@testing-library/user-event/dist/types/utils/misc/isVisible.d.ts", "./node_modules/@testing-library/user-event/dist/types/utils/misc/isDisabled.d.ts", "./node_modules/@testing-library/user-event/dist/types/utils/misc/level.d.ts", "./node_modules/@testing-library/user-event/dist/types/utils/misc/wait.d.ts", "./node_modules/@testing-library/user-event/dist/types/utils/pointer/cssPointerEvents.d.ts", "./node_modules/@testing-library/user-event/dist/types/utils/index.d.ts", "./node_modules/@testing-library/user-event/dist/types/document/UI.d.ts", "./node_modules/@testing-library/user-event/dist/types/document/getValueOrTextContent.d.ts", "./node_modules/@testing-library/user-event/dist/types/document/copySelection.d.ts", "./node_modules/@testing-library/user-event/dist/types/document/trackValue.d.ts", "./node_modules/@testing-library/user-event/dist/types/document/index.d.ts", "./node_modules/@testing-library/user-event/dist/types/event/selection/getInputRange.d.ts", "./node_modules/@testing-library/user-event/dist/types/event/selection/modifySelection.d.ts", "./node_modules/@testing-library/user-event/dist/types/event/selection/moveSelection.d.ts", "./node_modules/@testing-library/user-event/dist/types/event/selection/setSelectionPerMouse.d.ts", "./node_modules/@testing-library/user-event/dist/types/event/selection/modifySelectionPerMouse.d.ts", "./node_modules/@testing-library/user-event/dist/types/event/selection/selectAll.d.ts", "./node_modules/@testing-library/user-event/dist/types/event/selection/setSelectionRange.d.ts", "./node_modules/@testing-library/user-event/dist/types/event/selection/setSelection.d.ts", "./node_modules/@testing-library/user-event/dist/types/event/selection/updateSelectionOnFocus.d.ts", "./node_modules/@testing-library/user-event/dist/types/event/selection/index.d.ts", "./node_modules/@testing-library/user-event/dist/types/event/index.d.ts", "./node_modules/@testing-library/user-event/dist/types/system/pointer/buttons.d.ts", "./node_modules/@testing-library/user-event/dist/types/system/pointer/shared.d.ts", "./node_modules/@testing-library/user-event/dist/types/system/pointer/index.d.ts", "./node_modules/@testing-library/user-event/dist/types/system/index.d.ts", "./node_modules/@testing-library/user-event/dist/types/system/keyboard.d.ts", "./node_modules/@testing-library/user-event/dist/types/options.d.ts", "./node_modules/@testing-library/user-event/dist/types/convenience/click.d.ts", "./node_modules/@testing-library/user-event/dist/types/convenience/hover.d.ts", "./node_modules/@testing-library/user-event/dist/types/convenience/tab.d.ts", "./node_modules/@testing-library/user-event/dist/types/convenience/index.d.ts", "./node_modules/@testing-library/user-event/dist/types/keyboard/index.d.ts", "./node_modules/@testing-library/user-event/dist/types/clipboard/copy.d.ts", "./node_modules/@testing-library/user-event/dist/types/clipboard/cut.d.ts", "./node_modules/@testing-library/user-event/dist/types/clipboard/paste.d.ts", "./node_modules/@testing-library/user-event/dist/types/clipboard/index.d.ts", "./node_modules/@testing-library/user-event/dist/types/pointer/index.d.ts", "./node_modules/@testing-library/user-event/dist/types/utility/clear.d.ts", "./node_modules/@testing-library/user-event/dist/types/utility/selectOptions.d.ts", "./node_modules/@testing-library/user-event/dist/types/utility/type.d.ts", "./node_modules/@testing-library/user-event/dist/types/utility/upload.d.ts", "./node_modules/@testing-library/user-event/dist/types/utility/index.d.ts", "./node_modules/@testing-library/user-event/dist/types/setup/api.d.ts", "./node_modules/@testing-library/user-event/dist/types/setup/directApi.d.ts", "./node_modules/@testing-library/user-event/dist/types/setup/setup.d.ts", "./node_modules/@testing-library/user-event/dist/types/setup/index.d.ts", "./node_modules/@testing-library/user-event/dist/types/index.d.ts", "./tests/ui/components/OnboardingWizard.test.tsx", "./tests/ui/components/SecurityDashboard.test.tsx", "./tests/ui/components/SmartDashboard.test.tsx", "./tests/unit/calculations/financial-calculations.test.ts", "./tests/unit/calculations/swiss-tax-calculations.test.ts", "./node_modules/@testing-library/jest-dom/types/matchers.d.ts", "./node_modules/@testing-library/jest-dom/types/jest.d.ts", "./node_modules/@testing-library/jest-dom/types/index.d.ts", "./tests/unit/components/AIFinancialAdvisor.test.tsx", "./tests/unit/components/CantonRelocationAnalysis.test.tsx", "./tests/unit/components/ExpenseManager.test.tsx", "./tests/unit/components/HistoricalTrackingCharts.test.tsx", "./tests/unit/components/RiskAssessmentMetrics.test.tsx", "./tests/unit/components/SafeWithdrawalRateAnalysis.test.tsx", "./tests/unit/components/SavingsGoalsManager.test.tsx", "./tests/unit/hooks/useLocalStorage.test.ts", "./tests/unit/security/encryption.test.ts", "./tests/unit/security/privacy-controls.test.ts", "./tests/unit/security/security-monitor.test.ts", "./tests/unit/types/expenses.test.ts", "./tests/unit/types/savings.test.ts", "./tests/unit/utils/swiss-social-insurance.test.ts", "./node_modules/@babel/types/lib/index.d.ts", "./node_modules/@types/babel__generator/index.d.ts", "./node_modules/@babel/parser/typings/babel-parser.d.ts", "./node_modules/@types/babel__template/index.d.ts", "./node_modules/@types/babel__traverse/index.d.ts", "./node_modules/@types/babel__core/index.d.ts", "./node_modules/@types/d3-array/index.d.ts", "./node_modules/@types/d3-selection/index.d.ts", "./node_modules/@types/d3-axis/index.d.ts", "./node_modules/@types/d3-brush/index.d.ts", "./node_modules/@types/d3-chord/index.d.ts", "./node_modules/@types/d3-color/index.d.ts", "./node_modules/@types/geojson/index.d.ts", "./node_modules/@types/d3-contour/index.d.ts", "./node_modules/@types/d3-delaunay/index.d.ts", "./node_modules/@types/d3-dispatch/index.d.ts", "./node_modules/@types/d3-drag/index.d.ts", "./node_modules/@types/d3-dsv/index.d.ts", "./node_modules/@types/d3-ease/index.d.ts", "./node_modules/@types/d3-fetch/index.d.ts", "./node_modules/@types/d3-force/index.d.ts", "./node_modules/@types/d3-format/index.d.ts", "./node_modules/@types/d3-geo/index.d.ts", "./node_modules/@types/d3-hierarchy/index.d.ts", "./node_modules/@types/d3-interpolate/index.d.ts", "./node_modules/@types/d3-path/index.d.ts", "./node_modules/@types/d3-polygon/index.d.ts", "./node_modules/@types/d3-quadtree/index.d.ts", "./node_modules/@types/d3-random/index.d.ts", "./node_modules/@types/d3-time/index.d.ts", "./node_modules/@types/d3-scale/index.d.ts", "./node_modules/@types/d3-scale-chromatic/index.d.ts", "./node_modules/@types/d3-shape/index.d.ts", "./node_modules/@types/d3-time-format/index.d.ts", "./node_modules/@types/d3-timer/index.d.ts", "./node_modules/@types/d3-transition/index.d.ts", "./node_modules/@types/d3-zoom/index.d.ts", "./node_modules/@types/d3/index.d.ts", "./node_modules/@types/decimal.js/index.d.ts", "./node_modules/@types/history/DOMUtils.d.ts", "./node_modules/@types/history/createBrowserHistory.d.ts", "./node_modules/@types/history/createHashHistory.d.ts", "./node_modules/@types/history/createMemoryHistory.d.ts", "./node_modules/@types/history/LocationUtils.d.ts", "./node_modules/@types/history/PathUtils.d.ts", "./node_modules/@types/history/index.d.ts", "./node_modules/@types/i18next/index.d.ts", "./node_modules/@types/json-schema/index.d.ts", "./node_modules/@types/react-dom/index.d.ts", "./node_modules/@types/react-i18next/src/context.d.ts", "./node_modules/@types/react-i18next/src/I18n.d.ts", "./node_modules/@types/react-i18next/src/I18nextProvider.d.ts", "./node_modules/@types/react-i18next/src/interpolate.d.ts", "./node_modules/@types/react-i18next/src/loadNamespaces.d.ts", "./node_modules/@types/react-i18next/src/trans.d.ts", "./node_modules/@types/react-i18next/src/props.d.ts", "./node_modules/@types/react-i18next/src/translate.d.ts", "./node_modules/@types/react-i18next/index.d.ts", "./node_modules/@types/react-router/index.d.ts", "./node_modules/@types/react-router-dom/index.d.ts"], "fileIdsList": [[243, 285, 525], [243, 285], [171, 243, 285], [53, 54, 55, 243, 285], [53, 54, 243, 285], [53, 243, 285], [243, 285, 413], [243, 285, 411], [243, 285, 408, 409, 410, 411, 412, 415, 416, 417, 418, 419, 420, 421, 422], [243, 285, 407], [243, 285, 414], [243, 285, 408, 409, 410], [243, 285, 408, 409], [243, 285, 411, 412, 414], [243, 285, 409], [243, 285, 509], [243, 285, 508], [243, 285, 423, 424], [243, 285, 501], [243, 285, 488, 489, 490], [243, 285, 483, 484, 485], [243, 285, 461, 462, 463, 464], [243, 285, 427, 501], [243, 285, 427], [243, 285, 427, 428, 429, 430, 475], [243, 285, 465], [243, 285, 460, 466, 467, 468, 469, 470, 471, 472, 473, 474], [243, 285, 475], [243, 285, 426], [243, 285, 479, 481, 482, 500, 501], [243, 285, 479, 481], [243, 285, 476, 479, 501], [243, 285, 486, 487, 491, 492, 497], [243, 285, 480, 482, 492, 500], [243, 285, 499, 500], [243, 285, 476, 480, 482, 498, 499], [243, 285, 480, 501], [243, 285, 478], [243, 285, 478, 480, 501], [243, 285, 476, 477], [243, 285, 493, 494, 495, 496], [243, 285, 482, 501], [243, 285, 437], [243, 285, 431, 438], [243, 285, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459], [243, 285, 457, 501], [243, 285, 525, 526, 527, 528, 529], [243, 285, 525, 527], [243, 285, 532, 560], [243, 285, 531, 537], [243, 285, 542], [243, 285, 537], [243, 285, 536], [243, 285, 554], [243, 285, 550], [243, 285, 532, 549, 560], [243, 285, 531, 532, 533, 534, 535, 536, 538, 539, 540, 541, 542, 543, 544, 545, 546, 547, 548, 549, 550, 551, 552, 553, 554, 555, 556, 557, 558, 559, 560, 561], [243, 285, 570], [243, 285, 564, 570], [243, 285, 565, 566, 567, 568, 569], [243, 282, 285], [243, 284, 285], [285], [243, 285, 290, 319], [243, 285, 286, 291, 297, 298, 305, 316, 327], [243, 285, 286, 287, 297, 305], [238, 239, 240, 243, 285], [243, 285, 288, 328], [243, 285, 289, 290, 298, 306], [243, 285, 290, 316, 324], [243, 285, 291, 293, 297, 305], [243, 284, 285, 292], [243, 285, 293, 294], [243, 285, 295, 297], [243, 284, 285, 297], [243, 285, 297, 298, 299, 316, 327], [243, 285, 297, 298, 299, 312, 316, 319], [243, 280, 285], [243, 285, 293, 297, 300, 305, 316, 327], [243, 285, 297, 298, 300, 301, 305, 316, 324, 327], [243, 285, 300, 302, 316, 324, 327], [241, 242, 243, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333], [243, 285, 297, 303], [243, 285, 304, 327, 332], [243, 285, 293, 297, 305, 316], [243, 285, 306], [243, 285, 307], [243, 284, 285, 308], [243, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333], [243, 285, 310], [243, 285, 311], [243, 285, 297, 312, 313], [243, 285, 312, 314, 328, 330], [243, 285, 297, 316, 317, 319], [243, 285, 318, 319], [243, 285, 316, 317], [243, 285, 319], [243, 285, 320], [243, 282, 285, 316], [243, 285, 297, 322, 323], [243, 285, 322, 323], [243, 285, 290, 305, 316, 324], [243, 285, 325], [243, 285, 305, 326], [243, 285, 300, 311, 327], [243, 285, 290, 328], [243, 285, 316, 329], [243, 285, 304, 330], [243, 285, 331], [243, 285, 297, 299, 308, 316, 319, 327, 330, 332], [243, 285, 316, 333], [51, 243, 285], [51, 243, 285, 424], [69, 72, 94, 243, 285, 574, 575, 576, 577, 578, 579, 580, 581], [51, 69, 72, 94, 243, 285], [69, 72, 94, 243, 285], [51, 69, 72, 94, 243, 285, 574, 580], [51, 61, 243, 285, 570], [51, 243, 285, 570], [48, 49, 50, 243, 285], [228, 229, 232, 243, 285, 394], [243, 285, 395], [243, 285, 372, 373], [229, 230, 232, 233, 234, 243, 285], [229, 243, 285], [229, 230, 232, 243, 285], [229, 230, 243, 285], [243, 285, 379], [224, 243, 285, 379, 380], [224, 243, 285, 379], [224, 231, 243, 285], [225, 243, 285], [224, 225, 226, 228, 243, 285], [224, 243, 285], [243, 285, 401, 402], [243, 285, 401, 402, 403, 404], [243, 285, 401, 403], [243, 285, 401], [94, 243, 285], [65, 66, 67, 68, 243, 285], [65, 66, 67, 243, 285], [65, 243, 285], [65, 66, 243, 285], [243, 285, 364, 365], [168, 243, 285], [166, 167, 243, 285, 286, 298, 316], [170, 243, 285], [169, 243, 285], [243, 285, 359], [243, 285, 357, 359], [243, 285, 348, 356, 357, 358, 360], [243, 285, 346], [243, 285, 349, 354, 359, 362], [243, 285, 345, 362], [243, 285, 349, 350, 353, 354, 355, 362], [243, 285, 349, 350, 351, 353, 354, 362], [243, 285, 346, 347, 348, 349, 350, 354, 355, 356, 358, 359, 360, 362], [243, 285, 362], [243, 285, 344, 346, 347, 348, 349, 350, 351, 353, 354, 355, 356, 357, 358, 359, 360, 361], [243, 285, 344, 362], [243, 285, 349, 351, 352, 354, 355, 362], [243, 285, 353, 362], [243, 285, 354, 355, 359, 362], [243, 285, 347, 357], [72, 243, 285], [51, 64, 69, 70, 71, 72, 94, 243, 285], [56, 243, 285], [51, 56, 61, 62, 243, 285], [56, 57, 58, 59, 60, 243, 285], [51, 56, 57, 243, 285], [51, 56, 243, 285], [56, 58, 243, 285], [243, 285, 336, 370, 371], [243, 285, 335, 336], [227, 243, 285], [243, 252, 256, 285, 327], [243, 252, 285, 316, 327], [243, 247, 285], [243, 249, 252, 285, 324, 327], [243, 285, 305, 324], [243, 285, 334], [243, 247, 285, 334], [243, 249, 252, 285, 305, 327], [243, 244, 245, 248, 251, 285, 297, 316, 327], [243, 252, 259, 285], [243, 244, 250, 285], [243, 252, 273, 274, 285], [243, 248, 252, 285, 319, 327, 334], [243, 273, 285, 334], [243, 246, 247, 285, 334], [243, 252, 285], [243, 246, 247, 248, 249, 250, 251, 252, 253, 254, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 274, 275, 276, 277, 278, 279, 285], [243, 252, 267, 285], [243, 252, 259, 260, 285], [243, 250, 252, 260, 261, 285], [243, 251, 285], [243, 244, 247, 252, 285], [243, 252, 256, 260, 261, 285], [243, 256, 285], [243, 250, 252, 255, 285, 327], [243, 244, 249, 252, 259, 285], [243, 285, 316], [243, 247, 252, 273, 285, 332, 334], [243, 285, 376, 377], [243, 285, 376], [243, 285, 297, 298, 300, 301, 302, 305, 316, 324, 327, 333, 334, 336, 337, 338, 339, 341, 342, 343, 363, 367, 368, 369, 370, 371], [243, 285, 338, 339, 340, 341], [243, 285, 338], [243, 285, 339], [243, 285, 366], [243, 285, 336, 371], [235, 243, 285, 385, 386, 397], [224, 232, 235, 243, 285, 381, 382, 397], [243, 285, 388], [236, 243, 285], [224, 235, 237, 243, 285, 381, 387, 396, 397], [243, 285, 374], [224, 229, 232, 235, 237, 243, 285, 288, 298, 316, 371, 374, 375, 378, 381, 383, 384, 387, 389, 390, 393, 397, 398], [235, 243, 285, 385, 386, 387, 397], [243, 285, 371, 391, 398], [243, 285, 332, 384], [235, 237, 243, 285, 378, 381, 383, 397], [224, 229, 232, 235, 236, 237, 243, 285, 288, 298, 316, 332, 371, 374, 375, 378, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 396, 397, 398, 399, 400, 405], [51, 52, 63, 83, 91, 243, 285], [51, 52, 73, 85, 243, 285], [51, 52, 73, 85, 99, 243, 285], [51, 52, 73, 243, 285], [51, 52, 107, 243, 285], [51, 52, 243, 285], [51, 52, 73, 84, 85, 243, 285], [51, 52, 73, 86, 88, 90, 243, 285], [51, 52, 73, 112, 113, 114, 115, 243, 285], [51, 52, 73, 85, 87, 243, 285], [52, 135, 243, 285], [51, 52, 107, 108, 124, 243, 285], [51, 52, 73, 85, 89, 243, 285], [51, 52, 73, 85, 128, 129, 243, 285], [51, 52, 73, 112, 243, 285], [51, 52, 73, 112, 131, 132, 133, 243, 285], [51, 52, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 243, 285], [51, 52, 73, 74, 243, 285], [51, 52, 139, 140, 141, 243, 285], [52, 112, 113, 243, 285], [51, 52, 107, 143, 144, 145, 146, 243, 285], [51, 52, 107, 143, 243, 285], [52, 69, 72, 73, 94, 95, 243, 285], [52, 69, 72, 73, 94, 95, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 243, 285], [52, 243, 285], [51, 52, 73, 74, 92, 93, 96, 243, 285], [52, 107, 243, 285], [52, 107, 143, 144, 243, 285], [52, 243, 285, 563], [52, 107, 144, 243, 285], [52, 112, 243, 285], [52, 128, 243, 285], [52, 172, 178, 243, 285], [52, 172, 243, 285], [52, 172, 178, 183, 243, 285], [52, 172, 178, 183, 186, 243, 285], [52, 190, 243, 285], [52, 172, 188, 243, 285], [52, 172, 189, 192, 243, 285], [52, 172, 189, 243, 285], [52, 182, 243, 285, 286, 298, 307], [52, 172, 192, 194, 243, 285], [52, 172, 190, 191, 194, 243, 285], [52, 172, 192, 194, 243, 285, 298, 307], [52, 172, 194, 243, 285], [52, 172, 188, 192, 194, 243, 285], [52, 172, 190, 192, 194, 243, 285], [52, 172, 243, 285, 298, 307], [52, 137, 243, 285, 406, 425, 502], [51, 52, 139, 140, 142, 243, 285, 406, 425], [52, 138, 243, 285, 406, 425], [52, 164, 243, 285, 406], [52, 112, 243, 285, 406], [51, 52, 98, 243, 285, 425], [51, 52, 104, 243, 285, 425], [52, 73, 84, 86, 96, 243, 285, 406, 425, 502], [51, 52, 117, 243, 285, 425], [51, 52, 123, 243, 285, 425], [51, 52, 126, 243, 285, 425], [52, 73, 89, 90, 96, 243, 285, 406, 425, 502], [52, 85, 243, 285, 406, 425], [52, 141, 243, 285, 406], [52, 140, 243, 285, 406], [52, 139, 243, 285, 406], [52, 84, 243, 285, 406], [52, 89, 243, 285, 406], [52, 133, 243, 285, 406]], "fileInfos": [{"version": "69684132aeb9b5642cbcd9e22dff7818ff0ee1aa831728af0ecf97d3364d5546", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "45b7ab580deca34ae9729e97c13cfd999df04416a79116c3bfb483804f85ded4", "impliedFormat": 1}, {"version": "3facaf05f0c5fc569c5649dd359892c98a85557e3e0c847964caeb67076f4d75", "impliedFormat": 1}, {"version": "e44bb8bbac7f10ecc786703fe0a6a4b952189f908707980ba8f3c8975a760962", "impliedFormat": 1}, {"version": "5e1c4c362065a6b95ff952c0eab010f04dcd2c3494e813b493ecfd4fcb9fc0d8", "impliedFormat": 1}, {"version": "68d73b4a11549f9c0b7d352d10e91e5dca8faa3322bfb77b661839c42b1ddec7", "impliedFormat": 1}, {"version": "5efce4fc3c29ea84e8928f97adec086e3dc876365e0982cc8479a07954a3efd4", "impliedFormat": 1}, {"version": "092c2bfe125ce69dbb1223c85d68d4d2397d7d8411867b5cc03cec902c233763", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "07f073f19d67f74d732b1adea08e1dc66b1b58d77cb5b43931dee3d798a2fd53", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "c57796738e7f83dbc4b8e65132f11a377649c00dd3eee333f672b8f0a6bea671", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "dc2df20b1bcdc8c2d34af4926e2c3ab15ffe1160a63e58b7e09833f616efff44", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "515d0b7b9bea2e31ea4ec968e9edd2c39d3eebf4a2d5cbd04e88639819ae3b71", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0559b1f683ac7505ae451f9a96ce4c3c92bdc71411651ca6ddb0e88baaaad6a3", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0dc1e7ceda9b8b9b455c3a2d67b0412feab00bd2f66656cd8850e8831b08b537", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ce691fb9e5c64efb9547083e4a34091bcbe5bdb41027e310ebba8f7d96a98671", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8d697a2a929a5fcb38b7a65594020fcef05ec1630804a33748829c5ff53640d0", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4ff2a353abf8a80ee399af572debb8faab2d33ad38c4b4474cff7f26e7653b8d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "936e80ad36a2ee83fc3caf008e7c4c5afe45b3cf3d5c24408f039c1d47bdc1df", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d15bea3d62cbbdb9797079416b8ac375ae99162a7fba5de2c6c505446486ac0a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "68d18b664c9d32a7336a70235958b8997ebc1c3b8505f4f1ae2b7e7753b87618", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "eb3d66c8327153d8fa7dd03f9c58d351107fe824c79e9b56b462935176cdf12a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "38f0219c9e23c915ef9790ab1d680440d95419ad264816fa15009a8851e79119", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "69ab18c3b76cd9b1be3d188eaf8bba06112ebbe2f47f6c322b5105a6fbc45a2e", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "fef8cfad2e2dc5f5b3d97a6f4f2e92848eb1b88e897bb7318cef0e2820bceaab", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "2f11ff796926e0832f9ae148008138ad583bd181899ab7dd768a2666700b1893", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4de680d5bb41c17f7f68e0419412ca23c98d5749dcaaea1896172f06435891fc", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "954296b30da6d508a104a3a0b5d96b76495c709785c1d11610908e63481ee667", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ac9538681b19688c8eae65811b329d3744af679e0bdfa5d842d0e32524c73e1c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0a969edff4bd52585473d24995c5ef223f6652d6ef46193309b3921d65dd4376", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "9e9fbd7030c440b33d021da145d3232984c8bb7916f277e8ffd3dc2e3eae2bdb", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "811ec78f7fefcabbda4bfa93b3eb67d9ae166ef95f9bff989d964061cbf81a0c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "717937616a17072082152a2ef351cb51f98802fb4b2fdabd32399843875974ca", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d7e7d9b7b50e5f22c915b525acc5a49a7a6584cf8f62d0569e557c5cfc4b2ac2", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "71c37f4c9543f31dfced6c7840e068c5a5aacb7b89111a4364b1d5276b852557", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "576711e016cf4f1804676043e6a0a5414252560eb57de9faceee34d79798c850", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "89c1b1281ba7b8a96efc676b11b264de7a8374c5ea1e6617f11880a13fc56dc6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "74f7fa2d027d5b33eb0471c8e82a6c87216223181ec31247c357a3e8e2fddc5b", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d6d7ae4d1f1f3772e2a3cde568ed08991a8ae34a080ff1151af28b7f798e22ca", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "063600664504610fe3e99b717a1223f8b1900087fab0b4cad1496a114744f8df", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "934019d7e3c81950f9a8426d093458b65d5aff2c7c1511233c0fd5b941e608ab", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "52ada8e0b6e0482b728070b7639ee42e83a9b1c22d205992756fe020fd9f4a47", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3bdefe1bfd4d6dee0e26f928f93ccc128f1b64d5d501ff4a8cf3c6371200e5e6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "59fb2c069260b4ba00b5643b907ef5d5341b167e7d1dbf58dfd895658bda2867", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "639e512c0dfc3fad96a84caad71b8834d66329a1f28dc95e3946c9b58176c73a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "368af93f74c9c932edd84c58883e736c9e3d53cec1fe24c0b0ff451f529ceab1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8e7f8264d0fb4c5339605a15daadb037bf238c10b654bb3eee14208f860a32ea", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "782dec38049b92d4e85c1585fbea5474a219c6984a35b004963b00beb1aab538", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "eb5b19b86227ace1d29ea4cf81387279d04bb34051e944bc53df69f58914b788", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8a8eb4ebffd85e589a1cc7c178e291626c359543403d58c9cd22b81fab5b1fb9", "impliedFormat": 1}, {"version": "65ff5a0aefd7817a03c1ad04fee85c9cdd3ec415cc3c9efec85d8008d4d5e4ee", "impliedFormat": 1}, {"version": "69b76e74a56b52e89f3400cbd99a9e2a67f4a4f7b6d0b07dff2c637ac514b3e6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "42c169fb8c2d42f4f668c624a9a11e719d5d07dacbebb63cbcf7ef365b0a75b3", "impliedFormat": 1}, {"version": "5af7c35a9c5c4760fd084fedb6ba4c7059ac9598b410093e5312ac10616bf17b", "impliedFormat": 1}, {"version": "71e1687de45bc0cc54791abb165e153ce97a963fb4ece6054648988f586c187f", "impliedFormat": 1}, {"version": "3c8c1edb7ed8a842cb14d9f2ba6863183168a9fc8d6aa15dec221ebf8b946393", "impliedFormat": 1}, {"version": "0a6e1a3f199d6cc3df0410b4df05a914987b1e152c4beacfd0c5142e15302cac", "impliedFormat": 1}, {"version": "ec3c1376b4f34b271b1815674546fe09a2f449b0773afd381bbce7eb2f96a731", "impliedFormat": 1}, {"version": "c2a262a3157e868d279327daf428dd629bd485f825800c8e62b001e6c879aff6", "impliedFormat": 1}, {"version": "f7e84f314f9276b44b707289446303db8ef34a6c2c6d6b08d03a76e168367072", "impliedFormat": 1}, {"version": "2b493706eb7879d42a8e8009379292b59827d612ab3a275bc476f87e60259c79", "impliedFormat": 1}, {"version": "5542628b04d7bd4e0cd4871b6791b3b936a917ac6b819dcd20487f040acb01f1", "impliedFormat": 1}, {"version": "f07b335f87cfac999fc1da00dfbc616837ced55be67bcaa41524d475b7394554", "impliedFormat": 1}, {"version": "938326adb4731184e14e17fc57fca2a3b69c337ef8d6c00c65d73472fe8feca4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "890bdcec61a6fe8e39e35a1a9e4e0cad8c99b371646077bed13724862c4ab711", "impliedFormat": 1}, {"version": "e30accdbef6f904f20354b6f598d7f2f7ff29094fc5410c33f63b29b4832172a", "impliedFormat": 1}, {"version": "d46f6e5d2f8ffc47b103d452bda041eb1b714ceff051079ac96f1057e2ba1e02", "impliedFormat": 1}, {"version": "17e5b4890145a3adf7eafd52b35981cef7aaf385d073c027e37a1126305970b5", "impliedFormat": 1}, {"version": "4d39c150715cb238da715e5c3fbeac2e2b2d0ef3f23c632996dd59ae35ba1f45", "impliedFormat": 1}, {"version": "e9f80c5934982b97886eadab6684c073344a588d1758b12fba2d0184e6f450a2", "impliedFormat": 99}, {"version": "9d3ebddb2b7d90a93d19ddae7fc3fe28bf2d3233f16c97f89b880fd045b068e4", "impliedFormat": 1}, {"version": "b3e571e9f098c30db463d30d16d395ad8dd2457ee6e8d1561e2e1527bc2b6ce0", "impliedFormat": 1}, {"version": "cd141139eea89351f56a0acb87705eb8b2ff951bb252a3dfe759de75c6bfed87", "impliedFormat": 1}, {"version": "a7661d2413b60a722b7d7ff8e52dd3efad86600553eba1c88c990a0b2c11870b", "impliedFormat": 99}, {"version": "6832c4fa7376d7e5bee1b83aa54e740307966eb64702a21ecf6ca6834788af36", "signature": "1bf8d47e9b9aba83e130eecc6921425326ea3c86943945a79e5c63fc15308a90"}, {"version": "c265bac527a9fd275b6b7215b26b538c252743c8a159171829db3064ec01190b", "signature": "f78ab533123e9b4b58831bbd2f6404f72086319a01846392c119b569ff9968db"}, {"version": "ce17883c34a1618e1020656c72f3bb93f08d21c8cb8364a2bec0b8207cbe78a2", "signature": "f25d330c62d393381f3d825ea0cfb289d8b6d77d998d7474adad09cc842531a2"}, {"version": "f6b8d81940179c5305460a4d132d18e1ed0361f40452db4069fe646118872ce1", "signature": "f6525e87b2cc16124045e6ddcb1f2e9d9c1b4a45e103734d49678651a77700ed"}, {"version": "b6c11e28961a3ae0a2b45d7ab42b13e83efd3744f1fec302ab215156eb2bdd00", "signature": "fa2ff85cdd93a99a12b882fa5a499e6f3787f96c783effd4e06b11c1d927b356"}, {"version": "1f97d639db92ffb6c4dd6ebe58858fb5d00e0ebce44a6901dca53300ffe9d6a5", "signature": "6ca05afa6a990a36e285223e41aa62df1a28dfd634bff1c4105ec0f8625373a2"}, {"version": "4f66f360a311d3704e701c916c4b0a315b1c6a5cfdf80b192b4ad4ad4c9d98ce", "signature": "628e2bb060cbd557e0c3341a3e832ca5f11c8ef12efcb9c66ed4401b052b6de6"}, {"version": "f955b145ddc5faa178a9ce7883dee3ecb8dc29eb0c82a8392d8ce9a2135db687", "signature": "4835426f8d9ed6bf3b25463086635ed42c7021716068ca726d17d06a6821bfd8"}, {"version": "6cc7ab12950f23913e3c8c9cfa7b07a097334e05abbc035cf26004ce85e48827", "signature": "2576e17228ce5163b2c8238a1143a329411d4594f7a8bf0946d48ad85edde3d1"}, {"version": "dd16b82c929d39988febe482f773a9e744610127093ccbe15f07f985eb9d9704", "signature": "3fda2035618fd7e5c118946a3f401d9bb055ce16adec01eb27bdfa0e108742a4"}, {"version": "e6a68ba5f83c1a9b5b4536195a1a5b551cd995accf5406f7bfdc0f10b7301fcc", "signature": "b5138e0a4fec39bb4802c2f9f231dcf966cf833d98ba42a49687ff3fa2a8c4b7"}, {"version": "8b5962b0cef03d20d93bef6089725ee9aee3e42f9011b54caba58a6fdd80f1ce", "signature": "3038e9c991b1c63aceb7bfaaf01ba3003c6340887bb85f5418142c45180a3816"}, {"version": "9a52f86c13489dd9b33fce3075d6588ddbcc16349e63ca2175d5654ec9602bb4", "signature": "f27a456550d4176bdb106e392aa73ef5448db1a381e0bebd96d47e446aff9a29"}, {"version": "804c22d130172d1edf364ec7599a51824aef7b4eace4a1f15fb4510360277653", "signature": "1d4fac2c65e3c4b3fbaa466e869f11320a9f395badb704823a5ab0bc1ba759ea"}, {"version": "60ffcfe0f6d8a9c015bb1c169d82fa4f13adb2b5b074ccc1d366d8b624da5cb6", "signature": "37fd2bbfbcf24a918732ae00a20e73824445e972f9a69eee01e84935b10ea45f"}, {"version": "9dcfc17a433dc710fffaea3d7af0cef7f8009a0cffa005dc2eafbdb79417c696", "signature": "9282d62e145ad36a1439372fe0b608202dba678de4dc8cbb1bddd87c8060e621"}, {"version": "0f74044f3843cef5bf890bcc96010fd33c461ef97343968ab118c37113960ff1", "signature": "f6c0ca67804256eb590f1874c5a5ab030850a63c26255195c8c12a703e45a44b"}, {"version": "5c716c77b4bab9ca18bc4a2fa95279eddcd1739900e96a764f77ea8f958d9deb", "signature": "5d3c3f25b6304d8a98f9782c30aba43f5ac0eca69656431c42bcab18727f1fc0"}, {"version": "4a364279c8b1ff8b170cc0aa9034d1687529b8af0b914be0b875837860927ae1", "signature": "46028ecd6ee362a7f84654749b4143531b7ec6ce352670ee73387e70128d2d12"}, {"version": "05321b823dd3781d0b6aac8700bfdc0c9181d56479fe52ba6a40c9196fd661a8", "impliedFormat": 1}, {"version": "055d2177aa4ec7e23415119ed27410bd2f7458b75886ed222835d6cb5264061c", "impliedFormat": 1}, {"version": "bc9a11927ced39797acd3fa50bee8163685417d846800abbdbdec42824986108", "impliedFormat": 99}, {"version": "3bb9af2ce2ba9efbb17e2ff5f8939315f10afaf098c6d04843d08fb23e920312", "signature": "ecca93219b5690c7099cab5e99545509059911f54eaa862ce2c5acb943c8bd8d"}, {"version": "ba0011231ceee9d38f83df8afd3e9e8f66c009d919bcba79c2d474e155164448", "signature": "5e27e58e235a551ef56cb47dbd7c7f4686e88886a9cf1d8b6226833f84013fff"}, {"version": "3587ac51e9bf9983fe5ae6abdd3b726bf72afa7b9cdb982ee4694b1847cf7a71", "signature": "ecf867d6889fc1ea93469b71ba4449b16d2dd4bb7f106be6cea111f8f7d1e4e3"}, {"version": "6b1938291fcfb8f1a21db0c402c09a0930a65d4f97eda6eb98908a21d1f0e6bc", "signature": "40561ab86e3f60a4b8542389f441cabbccce725afef72783813ceaa5720ef31f"}, {"version": "ae7711ed23582cb9dd0f7c4c05ef68f7b804113a38b37aa042ed1c4cd7fcc75c", "signature": "218488f60f4b4b0ebe99081851ad10f55043fd0e1df7540f0b0aca72ca52393b"}, {"version": "633ffbcbe1f14925bcf43c753113f0bd38f498e05fd21e9832165c4c8ed425c5", "signature": "d536e5d694c20354e88ae6b5005070ef42787f12fc9c25830b370f7c54bf50b4"}, {"version": "34651a46fb7dbf25cf63cf83ceb2dd8941b4e362dfd1afa14a273a6a523a2f59", "signature": "b4601d8bde2d0fa2bd1c3b9d092206b9614eda27bb6995c0cb637dadebff0c1f"}, {"version": "af4a9592142866658279c8d15c83bf4e613eba75be010116f9496e55ddcddbe6", "signature": "dbdede2ac7876abe9c633fc414206da3db9447b7b10e46ede9a759d2a36a4dba"}, {"version": "9205a1ee4a7aafa36fd04f4ec72188cadd957179c1202baafaf68e416de7ae82", "signature": "1aff87e07d1e9f998794fcf44af9d326b92b62d8fd2acbf02d753a01c301d6fc"}, {"version": "8c84aac7a043dcf6e907bea9705109d4598dd13a72a66f004b82f3f492f13997", "signature": "c0f4da22ff21e1e8da55e1cbf2eb95f0eee6aa527e140061f235d8be042f1fbf"}, {"version": "b9fe5c5e2cf48eaa7b2e62e22b20b84627f93447bcee3b2daffc0c76fa9afd1d", "signature": "1cd26170ca8dd6e8214399351ddffc4f9fb03e096973b1870ada869be86151e6"}, {"version": "8df27b078ce1ea62b0939273fbc430c0c1585675137ad086cb2c29fad5ece41c", "signature": "12ff8c7e5b871a2adaeac1fd3a5350db7334c4edbc7b87784f5913f7b303237d"}, {"version": "a92a4105edb2dd687319a3d815b6c5cc95bedfd8c2ecf5238f0a347dc739c655", "signature": "94c7dfb1dcf27ae1c22f349c3f160c2e31288c450a66f00638cf7d6e0e8dae86"}, {"version": "d50a13c605174eb25a564b4701c51ffab74a5ee3fa9c9b2d9c0923b89b2c35b2", "signature": "52a4366ebda544a78d4fd60be13183f4c44df16b589804adf0001c093fae809a"}, {"version": "2c2e1dd803f7e57d5e1fc3f3910c7a376e5d581ea24edfccb615a154652b0b5c", "signature": "13b37e57377a03b4b19f24498c30e61770fb8755d848333f8c5ae4271d3e8754"}, {"version": "fc58167d7e18853b1e8a390066d23fe85d92778f2aa6bcd8aae01fd0887a66ad", "impliedFormat": 1}, {"version": "93ca39d8c500f88ebb95b7cef7ec5094cb23277183fbc60fe4eb2a2d30c894d6", "signature": "817b2000ff989940811a1a7ad95b0b05a674e9d49e90fc59c0a4b0221e3685a6"}, {"version": "637129580097f9302156e22c56f2b4e141e2bac062337b480c63a880041ccd60", "signature": "3f3f787719fb9f44a91c86c06e4a407de4c003a949c3ecc8e028b33ed5a616fb"}, {"version": "96315f41774c7cf7040b08d697820a3a1ea245bf2cfe8235e67b270aa2aa5f7a", "signature": "60355b9be513a38988e92c9ea1a51777b89e96c84426542381b95dd2c9f807b0"}, {"version": "6f7b03e070da176cfffa6314e5d7849bdcdfc52d757d994224eec0037be429b2", "signature": "cf361715cfbc76752d643a69c59711a141298b3f9a98d5b45903be0de8afe005"}, {"version": "dff3e394ec4be0257d399870be70cf8a18012ad190fba09a0b88c34e3bfeba53", "signature": "02f5787921d0b26b550d45239a01f42a7d2888e8c7a08c0afa25422d22a42cac"}, {"version": "c30364c1143b860e7630f048a9062adfd4991dd579988a735524d136992a2c7e", "signature": "fd934da4d2e3a8acc973eab0978bd214492e6a6d19ccc11be84a23f98f2486e9"}, {"version": "bdf745b6573f84782707243e47820df47ad5380ccc743600935de88c3ca0a479", "signature": "53e447743a037a445cb16732ec4ee15d1890e5c83a52735523e264c9979cb852"}, {"version": "1393e6488b0d1f1f9bcaa38d9425ce02b06ee1d1a4a9451ed6069153e562d86f", "signature": "435fd30eda93706b82b0b869b9d88adfa2bb0daf490c6e6287ab612c8a133109"}, {"version": "860ad9cde73e73439af0a1cc5727129cc5431b0a7034dee5270c7263a9dfcccc", "signature": "24447f9a2e7a246a96b906e2f60b15762f7e15e4f74e9911c9a03423e4081bc4"}, {"version": "88c2f258dc4bba3b653c2a6935a7fcb8521cad5cbe3c46287ad660e9c27d5e19", "signature": "a8868500cb408cd6fe1e3bd2a70d09515feda08c12ee96b4bab335937d57b941"}, {"version": "50ad2e1fa3c13712db8c5d1576d9d3c95d4ab506ff144fd87404e2f71530151e", "signature": "a716a3c2edb7102a5cca5053e22afebcda50e47698f5c38d98dfe78bcdcfd02c"}, {"version": "12ea1b5d12ed72ff33f5a79d9991b8b59d0b6be2c7bf93eb2ac10fb43b511916", "signature": "3046901eadccb2aef1be3ee4104acc68b6a754e00216cb8b04f243e477b722b7"}, {"version": "15806aaf6deedce8c0398fc77bfaab46e05c22f432802f3f6188fc7f2da08f85", "signature": "ac8097fa077710b0b904892151961bfb94b8b77e04b8a52c8d7ee4c76e584937"}, {"version": "c15d4d8c788df32ea8f46472a6f3bd17933fcac600cb56fc178c0ab34e084b86", "signature": "111737e82d67bbb0786e28a2a9fd3f78ba0b7d0b229412f6fa4c4a9978c1e467"}, {"version": "b7260fabe3a5ac130343954083d9d354d40356b7947cf2cc1d1c0579a699e252", "signature": "7bd4932fe77ce73b64d666c8012cb108533213f1a38036a282965edd44580049"}, {"version": "8869a68fc2226b89b7543964135b81099488073e67650806040d090714d1e9f4", "signature": "ceabea1f0d4a734bca23c69d95fccf1951ff8a8a602fdf7a3855fe1a77bdac58"}, {"version": "982389b21069e882c4e4aff18a590a03ea9d353a265ca724048fbc372349d2d1", "signature": "062f35615cf6c7ba91aa1a8f66b94d3837372d59098567770251741f11a93c6d"}, {"version": "05cf8daa570919cbfa3d4d9dfc839086c016be27199cc0508c9836ce9de37327", "signature": "cd908a234c73e31eca032e07611c83a750a6fc663d64aa4f63bba39c1f091a3b"}, {"version": "60466787944dad49d3557a8c80df423e0de8511f6da239ffa428daa2a4d01fdc", "signature": "f9eaabe6ebf9adbf6f2b6c97e3b0b88c791d899c7ead326785a73af8d18de642"}, {"version": "3c928a122ef4a9218cc99ada00e85b71f07a8ca48b6aaf4530ffdea0ca15bb89", "signature": "046f1484706729877e9c12cebb40ae82b01dfdd1b225ddad6a882495387a7f35"}, {"version": "c5fd647b9c50a4b6e0a995f600655f182d25dd491253ceff756222c793b7244e", "signature": "44ebe680e067a5801147f70f24e98a4e0c84b73a359b93c6674f1d5de75fa981"}, {"version": "685669a92d584769e9066c710217f00a7bdcdbfb4e979da4c60ef171019ef840", "signature": "6deac4aa99b25a3113f9af28cba485abca3b208f93b039515f2868473a5c49c7"}, {"version": "cd86d8401cbb4ffbde565e8d50fcdc1e44a3b5a8ff981729d1c831f7a03c60c2", "signature": "30a9403442f115e4a5bcabeea2fd9cb168e2f30451cd3974180c7ca56f9e261e"}, {"version": "af5dbc5d0d55913a42078737c62262d9fc78562d39d34d22ad080b3f9d2db79d", "signature": "78c29d0a5284d0161a89a341a9a982150d28e9f3f10cd7d2dbfe43c8ff3aff53"}, "d0fd1945e3304020d476032837180a117324dc527978dd69d44cbaf3b4359e85", {"version": "0e33ad47076490a4ac8f0a36d163fe842209935bfc26f0ffaa576c742562aa8d", "signature": "2669eb19b6aefa783d845cd6a755a6fa264387e930fd71107794364e806511f9"}, {"version": "15334272a3f53ec9b07e1602250982c038ae227136dde3e75a8d91988a9492ee", "signature": "216a88678239d3fe61b1695ca5667c8aa355c33d8310673faf7a6d0ccc213107"}, {"version": "81077832a1a27adf25141d42cde95dc6b1fc7e8b64678d5905a241084135e1fd", "signature": "a305588f5f1b947ea9da08fdd451873f81cdab5736e8456210804e371f193eaf"}, {"version": "676f52ef3e4621492527c6b7e5e291391529f9a2559c1b240cdb71e984230967", "signature": "227df66f7bca1f52e8c1ac26777338371362c458539e28e95d66da7438016f6c"}, {"version": "2c7ce1608da249d6d303c85d9a2ddba4e7b22633646ed505c86a5516f6894c51", "signature": "aef1e0e570bde8a3f6b2d9c73058df7cf7c31eda849b5aae7c2edee0f8585153"}, {"version": "ab19b2ae1d52240ff53e4efbbe89589c43c5be8977103ded849d039f2546e1d7", "signature": "d217ba9efb90edf0771bbe2e6e3178870e499db816b4b5a49ebb744c880725f8"}, {"version": "027e901eaa4267d70b2a92ce979a2336070a434705d5bfa41cc950b552eab141", "signature": "cb2ef0b18e44b50c9db6a5c9907f829fdd846b362eae462fab6a9105f1a8c5de"}, {"version": "4921b7d3907e139ad19850b0fef8fb31815951b03ff1b6232ed891b16bf92fba", "signature": "0024fabf9e61ae8590c723217e78520877d88c03cf7818cef9411da790428a90"}, {"version": "8ce0032e55458675c8bdb66928deefd8e255b61437ed450f759a451b02db1d25", "signature": "38f33066df23728888fd5209c746a42e1aca9e040972ab80f0582fcb650d528b"}, {"version": "0c2d0a296e3b89c7844d70182fcf326b60c9a6833a21d3b8d0d160f04d0f61ee", "signature": "b12ef77768f9b2dffb60f179fb8c5a5c2d1052c57162ff84c07e770edc9b5994"}, {"version": "6288178e05a8ba652afd5a0bfb33b681a2664324d12fdcf4039c27f12f6555d1", "signature": "ab1eabc0b4fbef9ef6ce1afa683e60a497a96b0557522cdac9c97e907241ea6b"}, {"version": "974673862856e9cb3712da669d0e5eca77c28b4e8ff9dc03cd2aba8bf81ca87f", "signature": "c53e11fadcd9cc7aa05b3e1c6d709a6779e9a88470946375ff9c7b6d98198814"}, {"version": "3077ce394e1187b55e39bed122a9d7ad4fa117d8dcaf9e35835a91d777989ce9", "signature": "8c91d2813bd25657a53451ff04f908c12f36c7cc133c037767de8d3c17f5238e"}, "2d357b9bb3f1dbdae6bf1a1574eb20b5623fd14442f637a0cb32af1ea85116de", "60cd0845e420c66902d5fa357cceae89dfb66f6104c67d85c9d81c8fd397d680", {"version": "f6d6e120202338e0f3800c0250e7c80140671399a704db0f75aadfcd8729410d", "signature": "531d78b32855650de0d57bd091116c481d5263e38c2b0924bb5b57fc570215cf"}, "52aa6927402d3401f9af3239c8e2ee4dc6f73f0fda32b445d05ada311f1826b3", {"version": "f2624c3cd2d6f9df37be732f6d446b8fd9c08659310ef578faf8bc143870163a", "signature": "0a51dd6758bf97b76bd8dc30103a329aee346de4846a61e0601181a3113eab5b"}, "d2f433c1df5f2aeaf0f17d59e00f2702ef9c865026594e054f36709d52049332", "0d4423e48053699720af9e3d78cdf9f71f49cd91b2e79b184cb597a1a61a6699", "75e1efd9763cc1447ce04bbf24e0b7baafee57761a347a3658fab0f2a1862f45", "9dd546faa953ec83bfa50cad339addf58e7a92ed275a9163a28502464c9d6b32", "b087e7b99f243c4bc967affeb771ee23e1ea540789d20d797ec0a77fb6b959fa", "c9379f7d284f4140432487f6474fa29c2c384618b762f8a19b0f6100d095a0cf", "9780fef594e552ea8879e79da083763c89d35007442b1d8ed1a7490780664654", {"version": "bfb4051ea9876d3e5417d72ed944179d0fc7d6b3d7f3a0fe8cf1a99aa73ff9af", "signature": "e0888d89cead5a48758f5e8eb8f736ac6956c4dc0f0bb53501ea1b00d0467066"}, {"version": "8241bcdd49292fcb72a24cba53aac193ca2c44673edab1c3563327ba1135f716", "signature": "a8dabd0e0bc042e4149d61b1b1afff1e21f0d7cb9993398bb843d52e309e07de"}, {"version": "a77368e084836d080a2e7bb082610c282e635a883530b313357f61614d83decd", "signature": "7509f3e9b5e543ac9cd091d2e9fc857f26f660ee1cba798edaabbd7b6d593141"}, {"version": "ce0bcfb3e77181d5c2fc819f80ace85fe1f45cbbbf3e578051ddf3a4d06dde96", "signature": "5d179b1fb6ad9411f2876fc16f8e0bf59c04f493f4d07168cb4044233a928a7a"}, {"version": "0ef72620b8ed8555b1a571fba0573059b427c72c0f8fe0af12117c01deaa62aa", "impliedFormat": 1}, {"version": "32727845ab5bd8a9ef3e4844c567c09f6d418fcf0f90d381c00652a6f23e7f6e", "impliedFormat": 1}, {"version": "317522008ac4f9a976630cb65fd5b072d7aea6da0a93ec0cfe0c0b0336337ee2", "impliedFormat": 1}, {"version": "7a8ec10b0834eb7183e4bfcd929838ac77583828e343211bb73676d1e47f6f01", "impliedFormat": 1}, {"version": "e241a236efd22fd53f0cad74d812bece9bc1691bf3890e95705a6e3b73e2f98e", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3f00324f263189b385c3a9383b1f4dae6237697bcf0801f96aa35c340512d79c", "impliedFormat": 1}, {"version": "ec8997c2e5cea26befc76e7bf990750e96babb16977673a9ff3b5c0575d01e48", "impliedFormat": 1}, {"version": "3d83c8923a9019a7888700fcd49ba5eb652dd9a72c94a79a0db43d0d4e898bd1", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, {"version": "05c6ea42f711ad7880dde29d405cc18d719a8af386d4be1237d167e7d679a7e7", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, "17f67c05dfa59d26a086bb1a9adf21ea9b4e7f319d229430f129675ea44b3463", {"version": "1fd0647013ea02d4ae7dbad783f91e5d96d560f3ce7b302a8a5f928627255889", "signature": "62e97c2c631babc333e53a6e9efc3df9db7604300119590e37713e9478782ca3"}, "6f37305601cc06ba3ac48be835cc4814333444eb7e1b2d425d676f850cb30b0d", {"version": "49b247180cc671321a706350fc3ffbe7784d42187e2d7c5204903279cd1aae4d", "signature": "1759b1203db6fcdecfd9dc298935f6e695c045bcec5af9e8464173e63288dbe7"}, {"version": "3e0129918f59fc00fb072321ca17dc94531a83511d0c228f0963eeffa2487032", "signature": "24836165aba37d3443e11058bc7597735ff5074699097fea37faddc159e22637"}, "d536b1726aac8bdf0b3a4680d096b506b890532a202ca74d1b6302d0fe8f29eb", "7eb990f89e07c3dc59eb283612469b75994e275bd6f1af66e221182b64fbcfe1", {"version": "10d9ff42c492a58b43ab07d7cab37dd63336d07ef7769de12d6b1d4eda71def7", "signature": "866186006c8219847514abc27381168a98b05f566973ee32948579235fe9cd7e"}, {"version": "72296e7a83f597852bd8ae99495c515b86a10a7e5dcc09429b112cf2486301ac", "signature": "1a7a5ef44341ac47ae814ffbe0ed99490f51c1fdb54352df674183b70bfec30d"}, {"version": "da1fcb4b8985f6879a06d26d0b5b1bb60040fc706317c49067fba9a9ffe39987", "signature": "b85d707de445aa90cf426ea9c16715b94becbcfd677b5ba3eab86b2955b7d87d"}, {"version": "2c8dd4d11db2d638d74df5aa2c774f1bf6dcf6f6a1292b7d946aebb74dc48905", "signature": "c57a05ab92ae71ac77bd8ec4a495988375222bbc040385f45709796eb85ba7c1"}, {"version": "d80539e713d181e72d57ada9a9ca179f334478f1dc41bfacf38fc4565ad17d43", "signature": "4c7ce20959c04df4067591467b57f2e8a9c264e2641d3c42a7ba969162c61ca4"}, {"version": "708e8c2c826cf7628a18a26c11bf67c510e9a5fde7f950071e55a4a8f1190ef7", "signature": "5b27f98a64c786b36e5eb7ab39724e655dabf5770981fb71d6c8c0edffdab4c6"}, {"version": "0365abdb7e87668d51fa667a7332f9dcfe14d5e910d92643381e38cf74f76020", "signature": "ff75b3215ce49bc9e246d2a33cdd46c84055aea138944bc521da09e6226b51d8"}, {"version": "0d2f606453af32cdf3fa705bdbfeb48ca3c4a9ffbaead7a3e50a19396308df0c", "signature": "90c3a9cfa4526fe319595c54f448d9d8bab0caedd36d2fa5453f33f87a2dd8e3"}, {"version": "7dd348bc61bd2245aae1ee5d6843f9be6134f57870562d4adeb4ed3c277672ca", "signature": "46793e70c44902da4c9150e8d658576ed913f7c9eef4509975508c4a093454ab"}, {"version": "eb9c9194bb2c931d862eaa00a11f5294e07543496ca82a61f515d748e457b2e4", "signature": "4b5109f86e65d2d049caaf79004da61047ba5de3fd6e457ad979b1312dbf99e3"}, {"version": "b8b4952aaa5616af9f0f4dd8ffa0216d454be9f2b9d68bfe2d4059b1cece7e59", "signature": "80bc5b04ecf7d33d35ff9ebf130676ad6691696577b56669bb0611076c18d4ff"}, {"version": "a64e2a236e8d93717dfa73f48d69186089c3f34f6568aac1338a82746ed72314", "signature": "04fb396f44b61a2ca73b0d9304fd1381fdd854c7d002b6c0d91dc5a1aabed83d"}, {"version": "9f1884f343ccc19a07893b95a44d48ebcc403e72a38f9157eb11bbe2043fc369", "signature": "ea1661e944a289ee56013f34db56f62c1e0a4fd558f59a45b1cb0e6323a8adbe"}, {"version": "e96b8f4aea68a17b43f0f13081f6b7b901d38ec477bcdadb97bf24120356ce2d", "signature": "623ec4690ad14c06ae60aa8a2aef9df133742e6a994895b452eceb7015d9d762"}, {"version": "d142d22319e66fbc2bccfa3b41964448ffac36196940797caf47ac147903955c", "signature": "3abd1aaa25404120483776e303f2a65af8c7d775806bf34ce2213c94f9566024"}, {"version": "e19084ebdc3617432769d0335913384268bf64720949ac3f642b7feb212cf257", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, {"version": "59145413a65c92bde49bbb413dacfb2e943616d947e3095f8be1379e3186a0cd", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, {"version": "a58155832923842be71d27bf7b7817d0d183d4a64ea2d2a0bacdb3c71eaf90a9", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, {"version": "082117f257daab4d605cb68e8a9e5b3b48d83be6c902e71b9a04b738cca90ca4", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, {"version": "167cb5905351d8730ebf4b544b7f213bb32176041cd89d3253c17dfe85356508", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, {"version": "a52f38b15aa3b03f74e0b678311d6a8e8552ac3dece3f6321e5163f13c3312d6", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, {"version": "46a3998898a452a48f4341f016a342f9f926363463d098aba714a76a8250d088", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, {"version": "72530879555b23d2ac369d859598a8d91744a41a13ab0e393f6633cb94302842", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, {"version": "cbd1869ba54ebf74f8117d5b1968810875686381580dfc9b4f392c2d530f0bec", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, "8a961360923db00340cde6ddd01c32413af9bccfb88429c362c44426adf8a176", {"version": "232b537753da0caf760b45d4daa7038b2cd6569923765f44069db8e0e42449a7", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, "18c802c553c33ca3c2fa57e652b612011066c25eb88c2abe606dfd5e2c03f873", {"version": "2235cb3b0394764ca0a80cbf3d22f483cd4b10288bd7a8de0b5626fdb5436acb", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, {"version": "a6025e0d40a0f99377d49d79859c7e419e5c81df895036fc6ab4228e01bda2b1", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, {"version": "bf477df605d30eb815c5888fa980f6869d25c7fdd187ad9da4aec0d38552fa02", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, {"version": "18cf25b816a7b0d1a99bec370fd4d6534434d3a089a5b690be4378f2cc326787", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, {"version": "5bc7c52f73c7d71521172255d4b83bfbd24c3d468931f7a150ee6cf5c13efc69", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, {"version": "72fbd4a993ea8b80d528f8774516a747c5d731d2bdb5d431c2dc47b9befd278b", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, {"version": "eb995e598a6c16cc50f5e0986fb43475d44496b5088e729c4b242bab79a2df81", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, "b880d41702636669956ca034561552e7a515cf1ba3ad52291b9159fb2628b5e9", {"version": "72c242824c0bbc50cff72e36dedd4168e79bb1c2d4232d3d195af3a7db82a4ea", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, {"version": "0a752a74c18df13d8e726dc9bdd071e6138f3744d51e20ca98729b9e900df8e6", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, "d4e2759dbf60e3149c9b67cc0a4d42eb3c6ce0f6152b15083149938f11f1bcf6", {"version": "3706de392572cfb227867a3f7486e4d5302ebf69481e5350ba119089dca7b13c", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, {"version": "6f6539d3901a4d06eb54fb7922be0c0e6e85beaabd5da4f9eab9051b113570ec", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, {"version": "7ce433414459453d991b9c9b25b77577df9404a991aab3601b32bf9561804983", "signature": "4e1d8d10cc71e7a39879192bdf9f1cab51157fe0551f428edcf2f1063677e5a4"}, {"version": "81484ff35de06e50cf3434851ce56d5cc1a9dcc84deb377ab1e9052c128aab12", "signature": "91c87bed1508ddd38cc9959617240db8684777cc8914d6e572745acbeef4ac81"}, {"version": "5c54a34e3d91727f7ae840bfe4d5d1c9a2f93c54cb7b6063d06ee4a6c3322656", "impliedFormat": 99}, {"version": "07c0547e91d0c35c3d1bff1d2b7ffac3334b315e9eb5744a8440940e819ab13a", "impliedFormat": 99}, {"version": "a6f223e9ef29edb1dc1ffa1a8507b9247589077081be99883ec5ac84d74e61d6", "impliedFormat": 99}, {"version": "f9b028d3c3891dd817e24d53102132b8f696269309605e6ed4f0db2c113bbd82", "impliedFormat": 99}, {"version": "fb7c8d90e52e2884509166f96f3d591020c7b7977ab473b746954b0c8d100960", "impliedFormat": 99}, {"version": "373e16d44e57937558478c586396210e4eeac6c895787863381a6588185528e4", "impliedFormat": 99}, {"version": "7c45fbd736e81fd9899cf4d75b242326ccda37eafdd9555e5b64a0ed59e8f6e9", "impliedFormat": 99}, {"version": "ef13c73d6157a32933c612d476c1524dd674cf5b9a88571d7d6a0d147544d529", "impliedFormat": 99}, {"version": "daf54402364627db51d8ccdcf98620ca7bd88dbd0036053bff51b87714a299b4", "impliedFormat": 99}, {"version": "9c2fe4e4ddf257e9b40d4d9fca28f86a8653a98492239a5ba27790019570cb71", "impliedFormat": 99}, {"version": "f8433f2a07ccab79429b2fd66d12731a13f18061d4e7f8dc8559796086b22bc4", "impliedFormat": 99}, {"version": "e64b03ee2d4d53929ea13a1e2b52aaba0685c86185b0f6f3346fc548b75a2245", "impliedFormat": 99}, {"version": "8124828a11be7db984fcdab052fd4ff756b18edcfa8d71118b55388176210923", "impliedFormat": 99}, {"version": "d06f9f2ba52c62a1d6cc63f4a015bc7ccd155f3bac2c07fbb979aec6013d966f", "impliedFormat": 99}, {"version": "70521b6ab0dcba37539e5303104f29b721bfb2940b2776da4cc818c07e1fefc1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ab41ef1f2cdafb8df48be20cd969d875602483859dc194e9c97c8a576892c052", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d153a11543fd884b596587ccd97aebbeed950b26933ee000f94009f1ab142848", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "21d819c173c0cf7cc3ce57c3276e77fd9a8a01d35a06ad87158781515c9a438a", "impliedFormat": 1}, {"version": "a79e62f1e20467e11a904399b8b18b18c0c6eea6b50c1168bf215356d5bebfaf", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "49a5a44f2e68241a1d2bd9ec894535797998841c09729e506a7cbfcaa40f2180", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "5929864ce17fba74232584d90cb721a89b7ad277220627cc97054ba15a98ea8f", "impliedFormat": 1}, {"version": "24bd580b5743dc56402c440dc7f9a4f5d592ad7a419f25414d37a7bfe11e342b", "impliedFormat": 1}, {"version": "25c8056edf4314820382a5fdb4bb7816999acdcb929c8f75e3f39473b87e85bc", "impliedFormat": 1}, {"version": "c464d66b20788266e5353b48dc4aa6bc0dc4a707276df1e7152ab0c9ae21fad8", "impliedFormat": 1}, {"version": "78d0d27c130d35c60b5e5566c9f1e5be77caf39804636bc1a40133919a949f21", "impliedFormat": 1}, {"version": "c6fd2c5a395f2432786c9cb8deb870b9b0e8ff7e22c029954fabdd692bff6195", "impliedFormat": 1}, {"version": "1d6e127068ea8e104a912e42fc0a110e2aa5a66a356a917a163e8cf9a65e4a75", "impliedFormat": 1}, {"version": "5ded6427296cdf3b9542de4471d2aa8d3983671d4cac0f4bf9c637208d1ced43", "impliedFormat": 1}, {"version": "6bdc71028db658243775263e93a7db2fd2abfce3ca569c3cca5aee6ed5eb186d", "impliedFormat": 1}, {"version": "cadc8aced301244057c4e7e73fbcae534b0f5b12a37b150d80e5a45aa4bebcbd", "impliedFormat": 1}, {"version": "385aab901643aa54e1c36f5ef3107913b10d1b5bb8cbcd933d4263b80a0d7f20", "impliedFormat": 1}, {"version": "9670d44354bab9d9982eca21945686b5c24a3f893db73c0dae0fd74217a4c219", "impliedFormat": 1}, {"version": "0b8a9268adaf4da35e7fa830c8981cfa22adbbe5b3f6f5ab91f6658899e657a7", "impliedFormat": 1}, {"version": "11396ed8a44c02ab9798b7dca436009f866e8dae3c9c25e8c1fbc396880bf1bb", "impliedFormat": 1}, {"version": "ba7bc87d01492633cb5a0e5da8a4a42a1c86270e7b3d2dea5d156828a84e4882", "impliedFormat": 1}, {"version": "4893a895ea92c85345017a04ed427cbd6a1710453338df26881a6019432febdd", "impliedFormat": 1}, {"version": "c21dc52e277bcfc75fac0436ccb75c204f9e1b3fa5e12729670910639f27343e", "impliedFormat": 1}, {"version": "13f6f39e12b1518c6650bbb220c8985999020fe0f21d818e28f512b7771d00f9", "impliedFormat": 1}, {"version": "9b5369969f6e7175740bf51223112ff209f94ba43ecd3bb09eefff9fd675624a", "impliedFormat": 1}, {"version": "4fe9e626e7164748e8769bbf74b538e09607f07ed17c2f20af8d680ee49fc1da", "impliedFormat": 1}, {"version": "24515859bc0b836719105bb6cc3d68255042a9f02a6022b3187948b204946bd2", "impliedFormat": 1}, {"version": "ea0148f897b45a76544ae179784c95af1bd6721b8610af9ffa467a518a086a43", "impliedFormat": 1}, {"version": "24c6a117721e606c9984335f71711877293a9651e44f59f3d21c1ea0856f9cc9", "impliedFormat": 1}, {"version": "dd3273ead9fbde62a72949c97dbec2247ea08e0c6952e701a483d74ef92d6a17", "impliedFormat": 1}, {"version": "405822be75ad3e4d162e07439bac80c6bcc6dbae1929e179cf467ec0b9ee4e2e", "impliedFormat": 1}, {"version": "0db18c6e78ea846316c012478888f33c11ffadab9efd1cc8bcc12daded7a60b6", "impliedFormat": 1}, {"version": "4d2b0eb911816f66abe4970898f97a2cfc902bcd743cbfa5017fad79f7ef90d8", "impliedFormat": 1}, {"version": "bd0532fd6556073727d28da0edfd1736417a3f9f394877b6d5ef6ad88fba1d1a", "impliedFormat": 1}, {"version": "89167d696a849fce5ca508032aabfe901c0868f833a8625d5a9c6e861ef935d2", "impliedFormat": 1}, {"version": "e53a3c2a9f624d90f24bf4588aacd223e7bec1b9d0d479b68d2f4a9e6011147f", "impliedFormat": 1}, {"version": "24b8685c62562f5d98615c5a0c1d05f297cf5065f15246edfe99e81ec4c0e011", "impliedFormat": 1}, {"version": "93507c745e8f29090efb99399c3f77bec07db17acd75634249dc92f961573387", "impliedFormat": 1}, {"version": "339dc5265ee5ed92e536a93a04c4ebbc2128f45eeec6ed29f379e0085283542c", "impliedFormat": 1}, {"version": "4732aec92b20fb28c5fe9ad99521fb59974289ed1e45aecb282616202184064f", "impliedFormat": 1}, {"version": "2e85db9e6fd73cfa3d7f28e0ab6b55417ea18931423bd47b409a96e4a169e8e6", "impliedFormat": 1}, {"version": "c46e079fe54c76f95c67fb89081b3e399da2c7d109e7dca8e4b58d83e332e605", "impliedFormat": 1}, {"version": "bf67d53d168abc1298888693338cb82854bdb2e69ef83f8a0092093c2d562107", "impliedFormat": 1}, {"version": "b8582f8bf95b9b901bf6cf47b9ee3560c7f340be0bd39cb432f21e9e136c36a7", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "6b042aa5d277ad6963e2837179fd2f8fbb01968ac67115b0833c0244e93d1d50", "impliedFormat": 1}, {"version": "7394959e5a741b185456e1ef5d64599c36c60a323207450991e7a42e08911419", "impliedFormat": 1}, {"version": "8c0bcd6c6b67b4b503c11e91a1fb91522ed585900eab2ab1f61bba7d7caa9d6f", "impliedFormat": 1}, {"version": "9e025aa38cad40827cc30aca974fe33fe2c4652fe8c88f48dadbbbd6300c8b07", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "f3e58c4c18a031cbb17abec7a4ad0bd5ae9fc70c1f4ba1e7fb921ad87c504aca", "impliedFormat": 1}, {"version": "84c1930e33d1bb12ad01bcbe11d656f9646bd21b2fb2afd96e8e10615a021aef", "impliedFormat": 1}, {"version": "35ec8b6760fd7138bbf5809b84551e31028fb2ba7b6dc91d95d098bf212ca8b4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "5524481e56c48ff486f42926778c0a3cce1cc85dc46683b92b1271865bcf015a", "impliedFormat": 1}, {"version": "4b87f767c7bc841511113c876a6b8bf1fd0cb0b718c888ad84478b372ec486b1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8d04e3640dd9eb67f7f1e5bd3d0bf96c784666f7aefc8ac1537af6f2d38d4c29", "impliedFormat": 1}, {"version": "9d19808c8c291a9010a6c788e8532a2da70f811adb431c97520803e0ec649991", "impliedFormat": 1}, {"version": "5a369483ac4cfbdf0331c248deeb36140e6907db5e1daed241546b4a2055f82c", "impliedFormat": 1}, {"version": "e8f5b5cc36615c17d330eaf8eebbc0d6bdd942c25991f96ef122f246f4ff722f", "impliedFormat": 1}, {"version": "f0bd7e6d931657b59605c44112eaf8b980ba7f957a5051ed21cb93d978cf2f45", "impliedFormat": 1}, {"version": "71450bbc2d82821d24ca05699a533e72758964e9852062c53b30f31c36978ab8", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0ada07543808f3b967624645a8e1ccd446f8b01ade47842acf1328aec899fed0", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4c21aaa8257d7950a5b75a251d9075b6a371208fc948c9c8402f6690ef3b5b55", "impliedFormat": 1}, {"version": "b5895e6353a5d708f55d8685c38a235c3a6d8138e374dee8ceb8ffde5aa8002a", "impliedFormat": 1}, {"version": "5b75ca915164e4a7ad94a60729fe45b8a62e7750ab232d0122f8ccdd768f5314", "impliedFormat": 1}, {"version": "93bd413918fa921c8729cef45302b24d8b6c7855d72d5bf82d3972595ae8dcbf", "impliedFormat": 1}, {"version": "4ff41188773cbf465807dd2f7059c7494cbee5115608efc297383832a1150c43", "impliedFormat": 1}, {"version": "dccdf1677e531e33f8ac961a68bc537418c9a414797c1ea7e91307501cdc3f5e", "impliedFormat": 1}, {"version": "1354ca5c38bd3fd3836a68e0f7c9f91f172582ba30ab15bb8c075891b91502b7", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "5155da3047ef977944d791a2188ff6e6c225f6975cc1910ab7bb6838ab84cede", "impliedFormat": 1}, {"version": "93f437e1398a4f06a984f441f7fa7a9f0535c04399619b5c22e0b87bdee182cb", "impliedFormat": 1}, {"version": "afbe24ab0d74694372baa632ecb28bb375be53f3be53f9b07ecd7fc994907de5", "impliedFormat": 1}, {"version": "3e5b3163e34f3dc24cba59db4bb90bcc33555cccac06b707501439bdcf3d4df4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "6b19db3600a17af69d4f33d08cc7076a7d19fb65bb36e442cac58929ec7c9482", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "9e043a1bc8fbf2a255bccf9bf27e0f1caf916c3b0518ea34aa72357c0afd42ec", "impliedFormat": 1}, {"version": "b4f70ec656a11d570e1a9edce07d118cd58d9760239e2ece99306ee9dfe61d02", "impliedFormat": 1}, {"version": "3bc2f1e2c95c04048212c569ed38e338873f6a8593930cf5a7ef24ffb38fc3b6", "impliedFormat": 1}, {"version": "8145e07aad6da5f23f2fcd8c8e4c5c13fb26ee986a79d03b0829b8fce152d8b2", "impliedFormat": 1}, {"version": "f9d9d753d430ed050dc1bf2667a1bab711ccbb1c1507183d794cc195a5b085cc", "impliedFormat": 1}, {"version": "9eece5e586312581ccd106d4853e861aaaa1a39f8e3ea672b8c3847eedd12f6e", "impliedFormat": 1}, {"version": "5b6844ad931dcc1d3aca53268f4bd671428421464b1286746027aede398094f2", "impliedFormat": 1}, {"version": "37ba7b45141a45ce6e80e66f2a96c8a5ab1bcef0fc2d0f56bb58df96ec67e972", "impliedFormat": 1}, {"version": "125d792ec6c0c0f657d758055c494301cc5fdb327d9d9d5960b3f129aff76093", "impliedFormat": 1}, {"version": "0dbcebe2126d03936c70545e96a6e41007cf065be38a1ce4d32a39fcedefead4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "1851a3b4db78664f83901bb9cac9e45e03a37bb5933cc5bf37e10bb7e91ab4eb", "impliedFormat": 1}, {"version": "09d479208911ac3ac6a7c2fe86217fc1abe6c4f04e2d52e4890e500699eeab32", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "12ed4559eba17cd977aa0db658d25c4047067444b51acfdcbf38470630642b23", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "f3ffabc95802521e1e4bcba4c88d8615176dc6e09111d920c7a213bdda6e1d65", "impliedFormat": 1}, {"version": "c40b3d3cfbb1227c8935f681c2480a32b560e387dd771d329cdbd1641f2d6da5", "impliedFormat": 1}, {"version": "ae56f65caf3be91108707bd8dfbccc2a57a91feb5daabf7165a06a945545ed26", "impliedFormat": 1}, {"version": "a136d5de521da20f31631a0a96bf712370779d1c05b7015d7019a9b2a0446ca9", "impliedFormat": 1}, {"version": "5b566927cad2ed2139655d55d690ffa87df378b956e7fe1c96024c4d9f75c4cf", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "c4a3720550d1787c8d6284040853c0781ff1e2cd8d842f2cb44547525ee34c36", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d3dffd70e6375b872f0b4e152de4ae682d762c61a24881ecc5eb9f04c5caf76f", "impliedFormat": 1}, {"version": "f008d63ce0077f533e39df44b82d660707b15b0f8e31fbc153a62bb00b99bfe5", "impliedFormat": 1}, {"version": "d91a7d8b5655c42986f1bdfe2105c4408f472831c8f20cf11a8c3345b6b56c8c", "impliedFormat": 1}, {"version": "6bdb3144f8bf020f513651a6ea1cb8a378a612c0791042e0436fd9adf7372a17", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "e8a979b8af001c9fc2e774e7809d233c8ca955a28756f52ee5dee88ccb0611d2", "impliedFormat": 1}, {"version": "cac793cc47c29e26e4ac3601dcb00b4435ebed26203485790e44f2ad8b6ad847", "impliedFormat": 1}, {"version": "e2b48abff5a8adc6bb1cd13a702b9ef05e6045a98e7cfa95a8779b53b6d0e69d", "impliedFormat": 1}, {"version": "a02d26c056491b1ddfa53a671ad60ce852969b369f0e71993dbac8ddcf0d038b", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "a660aa95476042d3fdcc1343cf6bb8fdf24772d31712b1db321c5a4dcc325434", "impliedFormat": 1}, {"version": "a7ca8df4f2931bef2aa4118078584d84a0b16539598eaadf7dce9104dfaa381c", "impliedFormat": 1}, {"version": "11443a1dcfaaa404c68d53368b5b818712b95dd19f188cab1669c39bee8b84b3", "impliedFormat": 1}, {"version": "36977c14a7f7bfc8c0426ae4343875689949fb699f3f84ecbe5b300ebf9a2c55", "impliedFormat": 1}, {"version": "035d0934d304483f07148427a5bd5b98ac265dae914a6b49749fe23fbd893ec7", "impliedFormat": 99}, {"version": "e2ed5b81cbed3a511b21a18ab2539e79ac1f4bc1d1d28f8d35d8104caa3b429f", "impliedFormat": 99}, {"version": "dd7ca4f0ef3661dac7043fb2cdf1b99e008d2b6bc5cd998dd1fa5a2968034984", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "402e5c534fb2b85fa771170595db3ac0dd532112c8fa44fc23f233bc6967488b", "impliedFormat": 1}, {"version": "8885cf05f3e2abf117590bbb951dcf6359e3e5ac462af1c901cfd24c6a6472e2", "impliedFormat": 1}, {"version": "33f3718dababfc26dfd9832c150149ea4e934f255130f8c118a59ae69e5ed441", "impliedFormat": 1}, {"version": "e61df3640a38d535fd4bc9f4a53aef17c296b58dc4b6394fd576b808dd2fe5e6", "impliedFormat": 1}, {"version": "459920181700cec8cbdf2a5faca127f3f17fd8dd9d9e577ed3f5f3af5d12a2e4", "impliedFormat": 1}, {"version": "4719c209b9c00b579553859407a7e5dcfaa1c472994bd62aa5dd3cc0757eb077", "impliedFormat": 1}, {"version": "7ec359bbc29b69d4063fe7dad0baaf35f1856f914db16b3f4f6e3e1bca4099fa", "impliedFormat": 1}, {"version": "70790a7f0040993ca66ab8a07a059a0f8256e7bb57d968ae945f696cbff4ac7a", "impliedFormat": 1}, {"version": "d1b9a81e99a0050ca7f2d98d7eedc6cda768f0eb9fa90b602e7107433e64c04c", "impliedFormat": 1}, {"version": "a022503e75d6953d0e82c2c564508a5c7f8556fad5d7f971372d2d40479e4034", "impliedFormat": 1}, {"version": "b215c4f0096f108020f666ffcc1f072c81e9f2f95464e894a5d5f34c5ea2a8b1", "impliedFormat": 1}, {"version": "644491cde678bd462bb922c1d0cfab8f17d626b195ccb7f008612dc31f445d2d", "impliedFormat": 1}, {"version": "dfe54dab1fa4961a6bcfba68c4ca955f8b5bbeb5f2ab3c915aa7adaa2eabc03a", "impliedFormat": 1}, {"version": "1bb61aa2f08ab4506d41dbe16c5f3f5010f014bbf46fa3d715c0cbe3b00f4e1c", "impliedFormat": 1}, {"version": "47865c5e695a382a916b1eedda1b6523145426e48a2eae4647e96b3b5e52024f", "impliedFormat": 1}, {"version": "e42820cd611b15910c204cd133f692dcd602532b39317d4f2a19389b27e6f03d", "impliedFormat": 1}, {"version": "331b8f71bfae1df25d564f5ea9ee65a0d847c4a94baa45925b6f38c55c7039bf", "impliedFormat": 1}, {"version": "2a771d907aebf9391ac1f50e4ad37952943515eeea0dcc7e78aa08f508294668", "impliedFormat": 1}, {"version": "0146fd6262c3fd3da51cb0254bb6b9a4e42931eb2f56329edd4c199cb9aaf804", "impliedFormat": 1}, {"version": "183f480885db5caa5a8acb833c2be04f98056bdcc5fb29e969ff86e07efe57ab", "impliedFormat": 99}, {"version": "f7eebe1b25040d805aefe8971310b805cd49b8602ec206d25b38dc48c542f165", "impliedFormat": 1}, {"version": "a18642ddf216f162052a16cba0944892c4c4c977d3306a87cb673d46abbb0cbf", "impliedFormat": 1}, {"version": "509f8efdfc5f9f6b52284170e8d7413552f02d79518d1db691ee15acc0088676", "impliedFormat": 1}, {"version": "4ec16d7a4e366c06a4573d299e15fe6207fc080f41beac5da06f4af33ea9761e", "impliedFormat": 1}, {"version": "7870becb94cbc11d2d01b77c4422589adcba4d8e59f726246d40cd0d129784d8", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "7f698624bbbb060ece7c0e51b7236520ebada74b747d7523c7df376453ed6fea", "impliedFormat": 1}, {"version": "f70b8328a15ca1d10b1436b691e134a49bc30dcf3183a69bfaa7ba77e1b78ecd", "impliedFormat": 1}, {"version": "ff3660e2664e6096196280deb4e176633b1bb1e58a7dcc9b021ec0e913a6f96f", "impliedFormat": 99}, {"version": "b34b5f6b506abb206b1ea73c6a332b9ee9c8c98be0f6d17cdbda9430ecc1efab", "impliedFormat": 99}, {"version": "75d4c746c3d16af0df61e7b0afe9606475a23335d9f34fcc525d388c21e9058b", "impliedFormat": 99}, {"version": "fa959bf357232201c32566f45d97e70538c75a093c940af594865d12f31d4912", "impliedFormat": 99}, {"version": "7d0eecfbb8fd85a40b3f1218d7b53f193d4194543a4053d0b007fcc869bd2594", "impliedFormat": 99}, {"version": "e6233e1c976265e85aa8ad76c3881febe6264cb06ae3136f0257e1eab4a6cc5a", "impliedFormat": 99}, {"version": "cc99b45397f724c65ab5b16dd2b9add8e2f49513621ccba4e3a564b939bfe706", "impliedFormat": 99}, {"version": "4734f2650122fed32bf168723cbc2e7b64f0c281fec9fc7c37a23d68ee4d4033", "impliedFormat": 99}, {"version": "324ac98294dab54fbd580c7d0e707d94506d7b2c3d5efe981a8495f02cf9ad96", "impliedFormat": 99}, {"version": "9ec72eb493ff209b470467e24264116b6a8616484bca438091433a545dfba17e", "impliedFormat": 99}, {"version": "dd1e40affaae1edc4beefe3d9832e86a683dcfc66fdf8c93c851a47298b04276", "impliedFormat": 99}, {"version": "49747416f08b3ba50500a215e7a55d75268b84e31e896a40313c8053e8dec908", "impliedFormat": 99}, {"version": "bb14e4b17394d59bd9f08459ce36d460dca08bd885c1347cf4fa7166c5af80a3", "impliedFormat": 99}, {"version": "b07c8a8ea750da9dea2d813f9d4f65d14c0090bb00c6dde9372ec1d38b74992e", "impliedFormat": 99}, {"version": "77217723774e80cf137592086cb40cd7607e106155a4c4071773574057863635", "impliedFormat": 99}, {"version": "69bf2422313487956e4dacf049f30cb91b34968912058d244cb19e4baa24da97", "impliedFormat": 99}, {"version": "971a2c327ff166c770c5fb35699575ba2d13bba1f6d2757309c9be4b30036c8e", "impliedFormat": 99}, {"version": "3dc60aac181aa635ad323906cdb76d723376299f0f7a4264f2f3e2ae9b8ecc1b", "impliedFormat": 99}, {"version": "7bd51996fb7717941cbe094b05adc0d80b9503b350a77b789bbb0fc786f28053", "impliedFormat": 99}, {"version": "b62006bbc815fe8190c7aee262aad6bff993e3f9ade70d7057dfceab6de79d2f", "impliedFormat": 99}, {"version": "33c8acef655f35aa0f44e13a1573488662755a99884292702262b97df6b9b473", "impliedFormat": 99}, {"version": "cb0ec5ea8c0bb861262b62e0fbb899a85d86796de4caaadb53d747706fda82e3", "impliedFormat": 99}, {"version": "3c1291fa957007538097ce38f7f0d65bf4c6ba6c2fad80ab806b71264fd296f4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3b8d3c5051687a7454c8f54746ba62b86b91c1e77bce27fea8f86ffc2d0a1325", "impliedFormat": 99}, {"version": "171c0308da0fc6251ea4184989d62c33dff3f277695ab1d556c421c0af59ddd3", "affectsGlobalScope": true, "impliedFormat": 99}, {"version": "fe2d63fcfdde197391b6b70daf7be8c02a60afa90754a5f4a04bdc367f62793d", "impliedFormat": 99}, {"version": "8f86cb232f12a7261a16b4afcd8222327255daac1620b00a734119baf2862fa5", "affectsGlobalScope": true, "impliedFormat": 99}, {"version": "1cc7fab9dcb518f2081a84caf4802c99db543fddad7227bceb3c3c97ea04688b", "impliedFormat": 99}, {"version": "5a88655bf852c8cc007d6bc874ab61d1d63fba97063020458177173c454e9b4a", "impliedFormat": 99}, {"version": "7e4dfae2da12ec71ffd9f55f4641a6e05610ce0d6784838659490e259e4eb13c", "impliedFormat": 99}, {"version": "c30a41267fc04c6518b17e55dcb2b810f267af4314b0b6d7df1c33a76ce1b330", "impliedFormat": 1}, {"version": "72422d0bac4076912385d0c10911b82e4694fc106e2d70added091f88f0824ba", "impliedFormat": 1}, {"version": "da251b82c25bee1d93f9fd80c5a61d945da4f708ca21285541d7aff83ecb8200", "impliedFormat": 1}, {"version": "4c8ca51077f382498f47074cf304d654aba5d362416d4f809dfdd5d4f6b3aaca", "impliedFormat": 1}, {"version": "c6bddf16578495abc8b5546850b047f30c4b5a2a2b7fecefc0e11a44a6e91399", "impliedFormat": 1}, {"version": "58c3c65b66f856365ceaf4f7a8f311f298ea8e1c20080ee852e91be53c98ab1d", "impliedFormat": 99}, {"version": "ae77d81a5541a8abb938a0efedf9ac4bea36fb3a24cc28cfa11c598863aba571", "impliedFormat": 1}, {"version": "3cfb7c0c642b19fb75132154040bb7cd840f0002f9955b14154e69611b9b3f81", "impliedFormat": 1}, {"version": "8387ec1601cf6b8948672537cf8d430431ba0d87b1f9537b4597c1ab8d3ade5b", "impliedFormat": 1}, {"version": "d16f1c460b1ca9158e030fdf3641e1de11135e0c7169d3e8cf17cc4cc35d5e64", "impliedFormat": 1}, {"version": "a934063af84f8117b8ce51851c1af2b76efe960aa4c7b48d0343a1b15c01aedf", "impliedFormat": 1}, {"version": "e3c5ad476eb2fca8505aee5bdfdf9bf11760df5d0f9545db23f12a5c4d72a718", "impliedFormat": 1}, {"version": "462bccdf75fcafc1ae8c30400c9425e1a4681db5d605d1a0edb4f990a54d8094", "impliedFormat": 1}, {"version": "5923d8facbac6ecf7c84739a5c701a57af94a6f6648d6229a6c768cf28f0f8cb", "impliedFormat": 1}, {"version": "d0570ce419fb38287e7b39c910b468becb5b2278cf33b1000a3d3e82a46ecae2", "impliedFormat": 1}, {"version": "3aca7f4260dad9dcc0a0333654cb3cde6664d34a553ec06c953bce11151764d7", "impliedFormat": 1}, {"version": "a0a6f0095f25f08a7129bc4d7cb8438039ec422dc341218d274e1e5131115988", "impliedFormat": 1}, {"version": "1d2699a343a347a830be26eb17ab340d7875c6f549c8d7477efb1773060cc7e5", "impliedFormat": 1}, {"version": "45785e608b3d380c79e21957a6d1467e1206ac0281644e43e8ed6498808ace72", "impliedFormat": 1}, {"version": "bece27602416508ba946868ad34d09997911016dbd6893fb884633017f74e2c5", "impliedFormat": 1}, {"version": "2a90177ebaef25de89351de964c2c601ab54d6e3a157cba60d9cd3eaf5a5ee1a", "impliedFormat": 1}, {"version": "82200e963d3c767976a5a9f41ecf8c65eca14a6b33dcbe00214fcbe959698c46", "impliedFormat": 1}, {"version": "b4966c503c08bbd9e834037a8ab60e5f53c5fd1092e8873c4a1c344806acdab2", "impliedFormat": 1}, {"version": "b598deb1da203a2b58c76cf8d91cfc2ca172d785dacd8466c0a11e400ff6ab2d", "impliedFormat": 1}, {"version": "f3ded47c50efa3fbc7105c933490fa0cf48df063248a5b27bca5849d5d126f9b", "impliedFormat": 1}, {"version": "da0f84fcd93700b4a5fbf9c6f166a6cc19fc798231bff56dd1e3875bfc6966eb", "impliedFormat": 1}, {"version": "634ff08e0143bec98401c737de7bfc6883bfec09200bd3806d2a4cfc79c62aaa", "impliedFormat": 1}, {"version": "90a86863e3a57143c50fec5129d844ec12cef8fe44d120e56650ed51a6ce9867", "impliedFormat": 1}, {"version": "472c0a98c5de98b8f5206132c941b052f5cc1ae78860cb8712ac4f1ebf4550ca", "impliedFormat": 1}, {"version": "538c4903ef9f8df7d84c6cf2e065d589a2532d152fa44105c7093a606393b814", "impliedFormat": 1}, {"version": "cfcb6acbb793a78b20899e6537c010bfbbf939c77471abcdc2a41faf9682ca1a", "impliedFormat": 1}, {"version": "a7798e86de8e76844f774f8e0e338149893789cdc08970381f0ae78c86e8667f", "impliedFormat": 1}, {"version": "eebc21bb922816f92302a1f9dcefc938e74d4af8c0a111b2a52519d7e25d4868", "impliedFormat": 1}, {"version": "6b359d3c3138a9f4d3a9c9a8fda24be6fd15bd789e692252b53e68ce99db8edc", "impliedFormat": 1}, {"version": "9488b648a6a4146b26c0fd4e85984f617056293092a89861f5259a69be16ca5c", "impliedFormat": 1}, {"version": "e156513655462b5811a8f980e32ccd204c19042f8c9756430fe4e8d6f7c1326e", "impliedFormat": 1}, {"version": "5679b694d138b8c4b3d56c9b1210f903c6b0ca2b5e7f1682a2dd41a6c955f094", "impliedFormat": 1}, {"version": "ca8da035b76fb0136d2c1390dda650b7979202dbe0f5dc7eaefcde1c76dee4f4", "impliedFormat": 1}, {"version": "4b1022a607444684abeee6537e4cace97263d1ef047c31b012c41fdc15838a79", "impliedFormat": 1}, {"version": "dd0271250f1e4314e52d7e0da9f3b25a708827f8a43ceff847a2a5e3fd3283e8", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "47971d8a8639a2a2dd684091c6e7660ec5909fed540c4479ca24e22ac237194e", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "e1075312b07671ef1cbf46409a0fa2eb2b90bb59c6215c94f0e530113013eeda", "impliedFormat": 1}, {"version": "1bfd63c3f3749c5dc925bb0c05f229f9a376b8d3f8173d0e01901c08202caf6f", "impliedFormat": 1}, {"version": "da850b4fdbabdd528f8b9c2784c5ba3b3bedc4e2e1e34dcd08b6407f9ec61a25", "impliedFormat": 1}, {"version": "e61c918bb5f4a39b795a06e22bc4d44befcefd22f6a5c8a732c9ed0b565a6128", "impliedFormat": 1}, {"version": "ee56351989b0e6f31fd35c9048e222146ced0aac68c64ce2e034f7c881327d6d", "impliedFormat": 1}, {"version": "f58b2f1c8f4bcf519377d39f9555631b6507977ad2f4d8b73ac04622716dc925", "impliedFormat": 1}, {"version": "4c805d3d1228c73877e7550afd8b881d89d9bc0c6b73c88940cffcdd2931b1f6", "impliedFormat": 1}, {"version": "4aa74b4bc57c535815ae004550c59a953c8f8c3c61418ac47a7dcfefba76d1ba", "impliedFormat": 1}, {"version": "78b17ceb133d95df989a1e073891259b54c968f71f416cd76185308af4f9a185", "impliedFormat": 1}, {"version": "d76e5d04d111581b97e0aa35de3063022d20d572f22f388d3846a73f6ce0b788", "impliedFormat": 1}, {"version": "0a53bb48eba6e9f5a56e3b85529fbbe786d96e84871579d10593d4f3ae0f9dba", "impliedFormat": 1}, {"version": "d34fb8b0a66f0a406c7ce63a36f16dda7ff4500b11b0bd30a491aa0d59336d1f", "impliedFormat": 1}, {"version": "282b31893b18a06114e5173f775dd085597ca220d183b8bd474d21846c048334", "impliedFormat": 1}, {"version": "ed27d5ce258f069acf0036471d1fbb56b4cb3c16d7401b52a51297eca651db62", "impliedFormat": 1}, {"version": "ec203a515afd88589bf1d384535024f5b90ebe6b5c416fb3dcca0abd428a8ba4", "impliedFormat": 1}, {"version": "32a2a1374b57f0744d284ca93b477bd97825922513a24dfe262cbf3497377d96", "impliedFormat": 1}, {"version": "a8b60d24dc1eb26c0e987f9461c893744339a7f48e4496f8077f258a644cffab", "impliedFormat": 1}, {"version": "3f9df27a77a23d69088e369b42af5f95bcb3e605e6b5c2395f0bfcd82045e051", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "9fd080a9458c6d6f3eb6d4e2b12a3ec498d7d219863e9dca0646bdee9acce875", "impliedFormat": 1}, {"version": "e5d31928bee2ba0e72aeb858881891f8948326e4f91823028d0aea5c6f9e7564", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "9a9ba9f6fd097bb2f57d68da8a39403bbe4dc818b8ccd155a780e4e23fa556f2", "impliedFormat": 1}, {"version": "e50c4cd1f5cbce3e74c19a5bbf503c460e6ae86597e6d648a98c7f6c90b596dd", "impliedFormat": 1}, {"version": "fa140f881e20591ce163039a7968b54c5e51c11228708b4f9147473d06471cf5", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "295eca0c47be1191690fd2fe588195fff9d4dc43852aceb8b4cab2aa634579f0", "impliedFormat": 1}, {"version": "59ee7346e19b0050508a592702871dc943083c6dcb69a47d52e888115d840781", "impliedFormat": 1}, {"version": "067712491fb2094c212c733dd8e2d56e74c309a9ce9dac9e919286b7245a1eb4", "impliedFormat": 1}, {"version": "a5eae58ac55bd30c42359e4b01fb2be5eddac336869d3f04ffb4daa54b58f009", "impliedFormat": 1}, {"version": "d12d691ef8933e8db39f2ca81d6973940ff5e37bb421752f5b6e7bc15dea3abf", "impliedFormat": 1}, {"version": "4c5f8bd9b3a1aae4e4fddfee41667e495a045f73ed603993038fa6a8ba92fa14", "impliedFormat": 1}, {"version": "dfb274ab0f319cf18ce7152067c25f984c7fd1924fc72b3f66734588444c934a", "impliedFormat": 1}, {"version": "108c8c05cbc3fbbbd4ff4fc0779c9bef55655c28528eb0f77829795dc9f0b484", "impliedFormat": 1}, {"version": "a7e5444d24cdec45f113f4fb8a687e1c83a5d30c55d2da19a04be71108ad77bd", "impliedFormat": 1}, {"version": "41ec17e218b7358fcff25c719bc419fec8ec98f13e561b9a33b07392d4fec24c", "impliedFormat": 1}, {"version": "23c204326746e981e02d7f0a15ab6f8015f9035998cb3766c9ddbf8ea247aea2", "impliedFormat": 1}, {"version": "25f994b5d76ce6a3186a3319555bbba79706dac2174019915c39ac6080e98c7e", "impliedFormat": 1}, {"version": "dfa4e2c6a612d43851ccbc499598cb006a3a78bc8c7f972c52078f862fa84e47", "impliedFormat": 1}, {"version": "02c1705fa902f172be6e9020d74bcd92ce5db8d2ef3e1b03aabc2ac8eb46c3db", "impliedFormat": 1}, {"version": "99d2d8a0c7bb3dd77459552269a7b5865fa912cedab69db686d40d2586b551f7", "impliedFormat": 1}, {"version": "b47abe58626d76d258472b1d5f76752dd29efe681545f32698db84e7f83517df", "impliedFormat": 1}, {"version": "3a99bbbbbf42e45c3d203e7c74f1319b79f9821c5e5f3cdd03249184d3e003ce", "impliedFormat": 1}, {"version": "aaacc0e12ab4de27bdf131f666e315d8e60abec26c7f87501e0a7806fc824ae6", "impliedFormat": 1}, {"version": "3b4195afd41a9215afc7be0820f8083f6bd2e85e5e0b45bb0061fb041944711e", "impliedFormat": 1}, {"version": "108df8095f5e25d7189dd0d1433ac2df75ec40c779d8faf7d2670f1485beb643", "impliedFormat": 1}, {"version": "ddd3c1d3c9ff67140191a3cf49b09875e20f28f2fc5535ae5ea16e14293a989b", "impliedFormat": 1}, {"version": "7b496e53d5f7e1737adcb5610516476ee055bf547918797348f245c68e7418fe", "impliedFormat": 1}, {"version": "577f44389d7faedd7fc9c0330caf73140e5d0d5f6c968210bff78be569f398a7", "impliedFormat": 1}, {"version": "3046c57724587a59bceefadd30040d418e9df81b9f3cfd680618a3511302ed7a", "impliedFormat": 1}, {"version": "15ccc911ed15397e838471bfe6d476c28deffe976c05cb057e6b1ea7491242c2", "impliedFormat": 1}, {"version": "64b5a5ebdaead77a9a564aa938f4fb7a45e27cda7441d3bee8c9de8a4df5a04f", "impliedFormat": 1}, {"version": "a48037f7af5f80df8973db5e562e17566407541de284b8dadf1879ea3aed8a2f", "impliedFormat": 1}, {"version": "dab97d96ce986857150db03f0d435b44c060d126b4a387c7807f4e9f6c92e531", "impliedFormat": 1}, {"version": "85f39366ea7bc5e34b596fc97de18a7e377856755e789d8e931054f2191d9b8b", "impliedFormat": 1}, {"version": "daf3ea3d49f6e8a2fa70b7ca1f21bd97f1b65021b31fbfccb73dd55f86abb792", "impliedFormat": 1}, {"version": "b15bd260805f9dd06cd4b2b741057209994823942c5696fd835e8a04fb4aab6b", "impliedFormat": 1}, {"version": "6635a824edf99ed52dbd3502d5bce35990c3ed5e2ec5cef88229df8ac0c52b06", "impliedFormat": 1}, {"version": "d6577effa37aae713c34363b7cc4c84851cbabe399882c60e2b70bcbb02bfa01", "impliedFormat": 1}, {"version": "8eaf80ad438890fe5880c39a7bbf2c998ce7d29d4c14dd56d82db63bd871eefb", "impliedFormat": 1}, {"version": "9b3e7f776f312c76ac67e1060e5398d7ac2c69d6a3a928a9daaae2eb05b15f56", "impliedFormat": 1}, {"version": "202042eccb4789b7dee51ba9ecab0b854834ea5c1d6a3946504bfc733d4468c3", "impliedFormat": 1}, {"version": "2b2ef76a9f36094b07ee6f76a5ac6903f2f65c0a20283201814a8d1e752cb592", "impliedFormat": 1}, {"version": "8882e4e087d0bc8cc713cb3d8090c45d33e373e6f5c83e0f8d00fe6a950ef875", "impliedFormat": 1}, {"version": "5e0a5e914a06b87914061b407eef45305245fa2e2f613eede72b75dcf103caae", "signature": "6123211d71c20ff86a4f6b6ab73a33ffd24ffd9a0ccb57f0acc00ff3a6bfa4f9"}, {"version": "937af76ec320f7ca0e4bf462498905645cc233572684756798c975e9b42cabd9", "signature": "2fc86c34a5d13779d60ea12df0961ba558e9d3b7f2928882c4b96d35cdbbce40"}, {"version": "895551e861b09a0d2f42efdd6da473a5f66acea716f7d23f626e3b08b9fa864f", "signature": "7f65ded41b30ebd207ec5f46b1a6d80e7b46a334522107f10c7aa46575c84863"}, {"version": "b64adbd4c82fb7a32577692a43b052aff5928c1d8177046ecfeab0e5ac3239dd", "signature": "27dc49c05fa2169420352119fe2b79d1b58fe12f0a171956fa5cb607feff6111"}, {"version": "4dc26842fc3c75da729d9b466c93620fb29a4fbc44cb7e7513d56a5c41a06162", "signature": "669a325c4095850c1ac79fa7153aafa00a4c044fe0b699006a3597e5ad7425e7"}, {"version": "72e9425f1ba1eb7fd8122d08f48848a0d56de1cd4c7b51f26dc2612bd26c7241", "impliedFormat": 1}, {"version": "841784cfa9046a2b3e453d638ea5c3e53680eb8225a45db1c13813f6ea4095e5", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "646ef1cff0ec3cf8e96adb1848357788f244b217345944c2be2942a62764b771", "impliedFormat": 1}, {"version": "3e9f28800727ecf20ef38cdce9e13a8be418fb90db6f45bc7d08afff4465774d", "signature": "a46d66851af2c056e805fdd574bf5ec3adb1181c43c5e41f0a1c592e338afe64"}, {"version": "bcc84d625dbe17d8db84e300ca6fac5095bd792a076db712435042f58c47ae15", "signature": "a46d66851af2c056e805fdd574bf5ec3adb1181c43c5e41f0a1c592e338afe64"}, {"version": "1e65ddbd5abbb031b6d507564784cc005eb730cf2875fc764fb7d9baa654feee", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, {"version": "2717894801a9a3c0700b7f15811a145e587cb10765b6a7ff6229d84e134d834c", "signature": "a46d66851af2c056e805fdd574bf5ec3adb1181c43c5e41f0a1c592e338afe64"}, {"version": "d1dae7670c6bfe24a97f2a0609dec8311e5e92e365c3c79342e1495101afc754", "signature": "a46d66851af2c056e805fdd574bf5ec3adb1181c43c5e41f0a1c592e338afe64"}, {"version": "207f0f87e1d28b1fb3437895e9d99682aa63e78b352b7f1bf37223fcfba735d6", "signature": "a46d66851af2c056e805fdd574bf5ec3adb1181c43c5e41f0a1c592e338afe64"}, {"version": "95f2cbb0c270177036a5d724f308d34309a9d1e28857360b6cd471ee155c6b14", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, {"version": "014a3d116ccddfa04a1f63f1a47687d9d75e60d37d7178b216fd7618193ea641", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, {"version": "b0ff37d203dddd8ab6a792e196bb8147aa79decb078b1e68e8b2cc7f5e3b836d", "signature": "fe0ce9eb7981a8da19f673a070b422fc51622e7d9ef42b26514d407680fc96cf"}, {"version": "6b6ab58f42228b4c2b7302d37aec7bc93307f1cd872cb056e586698e877c232c", "signature": "6083b27df43ed294040e1cb8c0e49871172a906d9035886e971242b8f378f228"}, {"version": "c2b3703495f76abae0c26af2c3d387c385cfa13b7b268b9289d4199307f8fce9", "signature": "87e8f541ac151f8c88e90cf75540d6befc8396b5b6726d22ed3136294f2e382a"}, {"version": "bb2ca2502f843e0686350d36578ffbbc74fa07b894099aebb84481b16247457e", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, {"version": "7e3fc6301e1c5422e8d2493e6f50c632288179eaf30de0e6cba13c73ba5fca8a", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, {"version": "fa4afc2f856ae8f83aa0011430eeff3eb144a4e927263fffefb0dd6a6b78b39c", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, {"version": "d88b3dc8b7055665059ea06ffafce9467fc4bdfa7cb2d7a6f4262556bb482b0d", "impliedFormat": 1}, {"version": "b6d03c9cfe2cf0ba4c673c209fcd7c46c815b2619fd2aad59fc4229aaef2ed43", "impliedFormat": 1}, {"version": "32ddc6ad753ae79571bbf28cebff7a383bf7f562ac5ef5d25c94ef7f71609d49", "impliedFormat": 1}, {"version": "670a76db379b27c8ff42f1ba927828a22862e2ab0b0908e38b671f0e912cc5ed", "impliedFormat": 1}, {"version": "81df92841a7a12d551fcbc7e4e83dbb7d54e0c73f33a82162d13e9ae89700079", "impliedFormat": 1}, {"version": "069bebfee29864e3955378107e243508b163e77ab10de6a5ee03ae06939f0bb9", "impliedFormat": 1}, {"version": "e0c868a08451c879984ccf4d4e3c1240b3be15af8988d230214977a3a3dad4ce", "impliedFormat": 1}, {"version": "469532350a366536390c6eb3bde6839ec5c81fe1227a6b7b6a70202954d70c40", "impliedFormat": 1}, {"version": "17c9f569be89b4c3c17dc17a9fb7909b6bab34f73da5a9a02d160f502624e2e8", "impliedFormat": 1}, {"version": "003df7b9a77eaeb7a524b795caeeb0576e624e78dea5e362b053cb96ae89132a", "impliedFormat": 1}, {"version": "7ba17571f91993b87c12b5e4ecafe66b1a1e2467ac26fcb5b8cee900f6cf8ff4", "impliedFormat": 1}, {"version": "6fc1a4f64372593767a9b7b774e9b3b92bf04e8785c3f9ea98973aa9f4bbe490", "impliedFormat": 1}, {"version": "d30e67059f5c545c5f8f0cc328a36d2e03b8c4a091b4301bc1d6afb2b1491a3a", "impliedFormat": 1}, {"version": "8b219399c6a743b7c526d4267800bd7c84cf8e27f51884c86ad032d662218a9d", "impliedFormat": 1}, {"version": "bad6d83a581dbd97677b96ee3270a5e7d91b692d220b87aab53d63649e47b9ad", "impliedFormat": 1}, {"version": "7f15c8d21ca2c062f4760ff3408e1e0ec235bad2ca4e2842d1da7fc76bb0b12f", "impliedFormat": 1}, {"version": "54e79224429e911b5d6aeb3cf9097ec9fd0f140d5a1461bbdece3066b17c232c", "impliedFormat": 1}, {"version": "e1b666b145865bc8d0d843134b21cf589c13beba05d333c7568e7c30309d933a", "impliedFormat": 1}, {"version": "ff09b6fbdcf74d8af4e131b8866925c5e18d225540b9b19ce9485ca93e574d84", "impliedFormat": 1}, {"version": "c836b5d8d84d990419548574fc037c923284df05803b098fe5ddaa49f88b898a", "impliedFormat": 1}, {"version": "3a2b8ed9d6b687ab3e1eac3350c40b1624632f9e837afe8a4b5da295acf491cb", "impliedFormat": 1}, {"version": "189266dd5f90a981910c70d7dfa05e2bca901a4f8a2680d7030c3abbfb5b1e23", "impliedFormat": 1}, {"version": "5ec8dcf94c99d8f1ed7bb042cdfa4ef6a9810ca2f61d959be33bcaf3f309debe", "impliedFormat": 1}, {"version": "a80e02af710bdac31f2d8308890ac4de4b6a221aafcbce808123bfc2903c5dc2", "impliedFormat": 1}, {"version": "d5895252efa27a50f134a9b580aa61f7def5ab73d0a8071f9b5bf9a317c01c2d", "impliedFormat": 1}, {"version": "2c378d9368abcd2eba8c29b294d40909845f68557bc0b38117e4f04fc56e5f9c", "impliedFormat": 1}, {"version": "0f345151cece7be8d10df068b58983ea8bcbfead1b216f0734037a6c63d8af87", "impliedFormat": 1}, {"version": "37fd7bde9c88aa142756d15aeba872498f45ad149e0d1e56f3bccc1af405c520", "impliedFormat": 1}, {"version": "2a920fd01157f819cf0213edfb801c3fb970549228c316ce0a4b1885020bad35", "impliedFormat": 1}, {"version": "56208c500dcb5f42be7e18e8cb578f257a1a89b94b3280c506818fed06391805", "impliedFormat": 1}, {"version": "0c94c2e497e1b9bcfda66aea239d5d36cd980d12a6d9d59e66f4be1fa3da5d5a", "impliedFormat": 1}, {"version": "a67774ceb500c681e1129b50a631fa210872bd4438fae55e5e8698bac7036b19", "impliedFormat": 1}, {"version": "bb220eaac1677e2ad82ac4e7fd3e609a0c7b6f2d6d9c673a35068c97f9fcd5cd", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "dd8936160e41420264a9d5fade0ff95cc92cab56032a84c74a46b4c38e43121e", "impliedFormat": 1}, {"version": "1f366bde16e0513fa7b64f87f86689c4d36efd85afce7eb24753e9c99b91c319", "impliedFormat": 1}, {"version": "421c3f008f6ef4a5db2194d58a7b960ef6f33e94b033415649cd557be09ef619", "impliedFormat": 1}, {"version": "57568ff84b8ba1a4f8c817141644b49252cc39ec7b899e4bfba0ec0557c910a0", "impliedFormat": 1}, {"version": "e6f10f9a770dedf552ca0946eef3a3386b9bfb41509233a30fc8ca47c49db71c", "impliedFormat": 1}, {"version": "64ef412b8346db43b82d6ad96aa5edf342c635346e41185cd3796937df2b11ad", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "271cde49dfd9b398ccc91bb3aaa43854cf76f4d14e10fed91cbac649aa6cbc63", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "2bcecd31f1b4281710c666843fc55133a0ee25b143e59f35f49c62e168123f4b", "impliedFormat": 1}, {"version": "a6273756fa05f794b64fe1aff45f4371d444f51ed0257f9364a8b25f3501915d", "impliedFormat": 1}, {"version": "9c4e644fe9bf08d93c93bd892705842189fe345163f8896849d5964d21b56b78", "impliedFormat": 1}, {"version": "25d91fb9ed77a828cc6c7a863236fb712dafcd52f816eec481bd0c1f589f4404", "impliedFormat": 1}, {"version": "4cd14cea22eed1bfb0dc76183e56989f897ac5b14c0e2a819e5162eafdcfe243", "impliedFormat": 1}, {"version": "8d32432f68ca4ce93ad717823976f2db2add94c70c19602bf87ee67fe51df48b", "impliedFormat": 1}, {"version": "ac690de1f4703b17019f51085c231ae628886a88b7b862a6e3d456a5000793fc", "impliedFormat": 1}, {"version": "f3d8c757e148ad968f0d98697987db363070abada5f503da3c06aefd9d4248c1", "impliedFormat": 1}, {"version": "17ed71200119e86ccef2d96b73b02ce8854b76ad6bd21b5021d4269bec527b5f", "impliedFormat": 1}, {"version": "1cf99f57eacf64bb7420a2c39b8b867436d91d390cb9ed9a403e57082bbfa4bc", "impliedFormat": 1}, {"version": "782894d3c1ff0fb62c67ab698b6dc857e3739f2c6c350467bc3b8386a435c692", "impliedFormat": 1}, {"version": "62a727085d87282d32f820edbf50149601e966dc4b15ffce501ec50bc800026c", "impliedFormat": 1}, {"version": "ccbdd8ec3c825275c5bd9c5c386973f99248ac6b0c8606b6860d25036a33b33b", "impliedFormat": 1}, {"version": "98945b6f3bb883c9df2bc3d65cff7a80203b54489df3ff11413f60c04946aa17", "impliedFormat": 1}, {"version": "7e71e33e590a767ec511b2c97bf246f780511fd39ffd4a95410a6b9c4abf90a8", "impliedFormat": 1}, {"version": "af42b240739dd8069986674029b9c339f3f8a353a0053ac9e15dc9bb76c9af66", "impliedFormat": 1}, {"version": "dc26ceed4fbda4cfbae8ab6249e628d6f4adc126cf387972f47280232ddc353a", "impliedFormat": 1}, {"version": "92a47e838628d2540034088620f5ae2ac2b2f62de6865899aed68d57c4e39f2d", "impliedFormat": 1}, {"version": "1d4bc73751d6ec6285331d1ca378904f55d9e5e8aeaa69bc45b675c3df83e778", "impliedFormat": 1}, {"version": "8017277c3843df85296d8730f9edf097d68d7d5f9bc9d8124fcacf17ecfd487e", "impliedFormat": 1}], "root": [[74, 92], [96, 110], [112, 149], [162, 165], [173, 182], 184, 185, [187, 223], [503, 507], [511, 524]], "options": {"allowImportingTsExtensions": true, "alwaysStrict": true, "exactOptionalPropertyTypes": false, "jsx": 4, "module": 99, "noFallthroughCasesInSwitch": true, "noImplicitAny": true, "noImplicitOverride": true, "noImplicitReturns": true, "noImplicitThis": true, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false, "noUnusedLocals": true, "noUnusedParameters": true, "skipLibCheck": true, "strict": true, "strictBindCallApply": true, "strictFunctionTypes": true, "strictNullChecks": true, "strictPropertyInitialization": true, "target": 7, "tsBuildInfoFile": "./.tsbuildinfo", "useDefineForClassFields": true}, "referencedMap": [[527, 1], [525, 2], [172, 3], [53, 2], [56, 4], [55, 5], [54, 6], [414, 7], [413, 2], [421, 2], [418, 2], [417, 2], [412, 8], [423, 9], [408, 10], [419, 11], [411, 12], [410, 13], [420, 2], [415, 14], [422, 2], [416, 15], [409, 2], [510, 16], [509, 17], [508, 10], [425, 18], [488, 19], [489, 19], [491, 20], [490, 19], [483, 19], [484, 19], [486, 21], [485, 19], [461, 2], [463, 2], [462, 2], [465, 22], [464, 2], [428, 23], [426, 24], [429, 2], [476, 25], [430, 19], [466, 26], [475, 27], [467, 2], [470, 28], [468, 2], [471, 2], [473, 2], [469, 28], [472, 2], [474, 2], [427, 29], [502, 30], [487, 19], [482, 31], [492, 32], [498, 33], [499, 34], [501, 35], [500, 36], [480, 31], [481, 37], [477, 38], [479, 39], [478, 40], [493, 19], [497, 41], [494, 19], [495, 42], [496, 19], [431, 2], [432, 2], [435, 2], [433, 2], [434, 2], [437, 2], [438, 43], [439, 2], [440, 2], [436, 2], [441, 2], [442, 2], [443, 2], [444, 2], [445, 44], [446, 2], [460, 45], [447, 2], [448, 2], [449, 2], [450, 2], [451, 2], [452, 2], [453, 2], [456, 2], [454, 2], [455, 2], [457, 19], [458, 19], [459, 46], [407, 2], [530, 47], [526, 1], [528, 48], [529, 1], [531, 2], [533, 49], [534, 49], [535, 2], [536, 2], [538, 50], [539, 2], [540, 2], [541, 49], [542, 2], [543, 2], [544, 51], [545, 2], [546, 2], [547, 52], [548, 2], [549, 53], [550, 2], [551, 2], [552, 2], [553, 2], [556, 2], [555, 54], [532, 2], [557, 55], [558, 2], [554, 2], [559, 2], [560, 49], [561, 56], [562, 57], [563, 2], [335, 2], [537, 2], [564, 2], [568, 58], [569, 58], [565, 59], [566, 59], [567, 59], [570, 60], [571, 2], [572, 2], [282, 61], [283, 61], [284, 62], [243, 63], [285, 64], [286, 65], [287, 66], [238, 2], [241, 67], [239, 2], [240, 2], [288, 68], [289, 69], [290, 70], [291, 71], [292, 72], [293, 73], [294, 73], [296, 2], [295, 74], [297, 75], [298, 76], [299, 77], [281, 78], [242, 2], [300, 79], [301, 80], [302, 81], [334, 82], [303, 83], [304, 84], [305, 85], [306, 86], [307, 87], [308, 88], [309, 89], [310, 90], [311, 91], [312, 92], [313, 92], [314, 93], [315, 2], [316, 94], [318, 95], [317, 96], [319, 97], [320, 98], [321, 99], [322, 100], [323, 101], [324, 102], [325, 103], [326, 104], [327, 105], [328, 106], [329, 107], [330, 108], [331, 109], [332, 110], [333, 111], [50, 2], [93, 112], [573, 112], [424, 113], [582, 114], [575, 115], [576, 115], [574, 116], [577, 115], [578, 115], [580, 116], [579, 115], [581, 117], [584, 118], [583, 119], [48, 2], [51, 120], [52, 112], [393, 2], [395, 121], [396, 122], [374, 123], [372, 2], [373, 2], [224, 2], [235, 124], [230, 125], [233, 126], [385, 127], [379, 2], [382, 128], [381, 129], [390, 129], [380, 130], [394, 2], [232, 131], [234, 131], [226, 132], [229, 133], [375, 132], [231, 134], [225, 2], [49, 2], [111, 2], [343, 2], [403, 135], [405, 136], [404, 137], [402, 138], [401, 2], [95, 139], [94, 116], [69, 140], [68, 141], [65, 2], [66, 142], [67, 143], [364, 2], [366, 144], [365, 2], [169, 145], [166, 2], [167, 145], [168, 146], [171, 147], [170, 148], [360, 149], [358, 150], [359, 151], [347, 152], [348, 150], [355, 153], [346, 154], [351, 155], [361, 2], [352, 156], [357, 157], [363, 158], [362, 159], [345, 160], [353, 161], [354, 162], [349, 163], [356, 149], [350, 164], [70, 115], [64, 2], [73, 165], [72, 166], [71, 116], [62, 167], [63, 168], [61, 169], [58, 170], [57, 171], [60, 172], [59, 170], [337, 173], [336, 174], [344, 2], [386, 2], [227, 2], [228, 175], [46, 2], [47, 2], [8, 2], [9, 2], [11, 2], [10, 2], [2, 2], [12, 2], [13, 2], [14, 2], [15, 2], [16, 2], [17, 2], [18, 2], [19, 2], [3, 2], [20, 2], [21, 2], [4, 2], [22, 2], [26, 2], [23, 2], [24, 2], [25, 2], [27, 2], [28, 2], [29, 2], [5, 2], [30, 2], [31, 2], [32, 2], [33, 2], [6, 2], [37, 2], [34, 2], [35, 2], [36, 2], [38, 2], [7, 2], [39, 2], [44, 2], [45, 2], [40, 2], [41, 2], [42, 2], [43, 2], [1, 2], [259, 176], [269, 177], [258, 176], [279, 178], [250, 179], [249, 180], [278, 181], [272, 182], [277, 183], [252, 184], [266, 185], [251, 186], [275, 187], [247, 188], [246, 181], [276, 189], [248, 190], [253, 191], [254, 2], [257, 191], [244, 2], [280, 192], [270, 193], [261, 194], [262, 195], [264, 196], [260, 197], [263, 198], [273, 181], [255, 199], [256, 200], [265, 201], [245, 202], [268, 193], [267, 191], [271, 2], [274, 203], [388, 204], [377, 205], [378, 204], [376, 2], [371, 206], [342, 207], [341, 208], [339, 208], [338, 2], [340, 209], [369, 2], [368, 2], [367, 210], [370, 211], [387, 212], [383, 213], [389, 214], [237, 215], [397, 216], [399, 217], [391, 218], [400, 219], [398, 220], [392, 221], [384, 222], [406, 223], [236, 2], [92, 224], [98, 225], [100, 226], [101, 225], [102, 227], [103, 225], [104, 227], [105, 225], [106, 227], [108, 228], [109, 228], [110, 229], [86, 230], [91, 231], [116, 232], [117, 225], [88, 233], [135, 227], [136, 234], [118, 227], [119, 225], [120, 227], [121, 225], [122, 225], [123, 227], [125, 235], [126, 227], [90, 236], [127, 227], [130, 237], [131, 227], [132, 238], [134, 239], [82, 227], [83, 240], [81, 241], [80, 241], [78, 241], [75, 241], [76, 241], [79, 241], [77, 241], [137, 227], [138, 227], [142, 242], [74, 229], [115, 243], [147, 244], [148, 227], [124, 228], [85, 229], [149, 227], [146, 245], [96, 246], [162, 247], [156, 248], [161, 248], [157, 248], [159, 248], [160, 248], [158, 248], [150, 248], [155, 248], [151, 248], [153, 248], [154, 248], [152, 248], [97, 249], [141, 248], [140, 248], [139, 248], [163, 248], [84, 248], [99, 248], [87, 248], [89, 248], [128, 248], [107, 248], [144, 250], [145, 251], [164, 252], [165, 253], [113, 254], [114, 243], [143, 250], [133, 248], [112, 252], [129, 255], [179, 256], [180, 257], [181, 257], [182, 248], [184, 258], [185, 258], [187, 259], [173, 257], [174, 257], [175, 257], [176, 257], [191, 260], [192, 248], [193, 256], [189, 261], [194, 262], [190, 263], [195, 256], [196, 264], [177, 257], [197, 265], [198, 266], [199, 257], [200, 257], [201, 257], [202, 257], [203, 265], [204, 267], [205, 265], [206, 257], [207, 268], [208, 268], [209, 269], [210, 268], [211, 265], [212, 266], [213, 265], [214, 257], [215, 257], [216, 257], [217, 270], [218, 265], [219, 265], [220, 266], [221, 265], [188, 257], [186, 248], [183, 248], [178, 257], [222, 271], [223, 271], [503, 272], [504, 273], [505, 274], [506, 275], [507, 276], [511, 277], [512, 278], [513, 279], [514, 280], [515, 281], [516, 282], [517, 283], [518, 284], [519, 285], [520, 286], [521, 287], [522, 288], [523, 289], [524, 290]], "semanticDiagnosticsPerFile": [[74, [{"start": 11785, "length": 18, "messageText": "Spread types may only be created from object types.", "category": 1, "code": 2698}]], [76, [{"start": 2668, "length": 51, "messageText": "Spread types may only be created from object types.", "category": 1, "code": 2698}, {"start": 4825, "length": 88, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type 'string | number | boolean | { stocks: number; bonds: number; cash: number; realEstate: number; commodities: number; } | { equity: number; credit: number; liquidity: number; }'."}]], [77, [{"start": 2768, "length": 101, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ rate: number; maxIncome: number; } | { rate: number; maxIncome: number; additionalRate: number; additionalThreshold: number; } | { rate: number; }' is not assignable to type '{ rate: number; maxIncome: number; } & { rate: number; maxIncome: number; additionalRate: number; additionalThreshold: number; } & { rate: number; }'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ rate: number; maxIncome: number; }' is not assignable to type '{ rate: number; maxIncome: number; } & { rate: number; maxIncome: number; additionalRate: number; additionalThreshold: number; } & { rate: number; }'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ rate: number; maxIncome: number; }' is missing the following properties from type '{ rate: number; maxIncome: number; additionalRate: number; additionalThreshold: number; }': additionalRate, additionalThreshold", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type '{ rate: number; maxIncome: number; }' is not assignable to type '{ rate: number; maxIncome: number; additionalRate: number; additionalThreshold: number; }'."}}]}]}}]], [79, [{"start": 5502, "length": 62, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type 'string | { withdrawalRate: number; safeWithdrawalRate: number; conservativeWithdrawalRate: number; investmentReturn: number; bondReturn: number; cashReturn: number; } | { defaultLanguage: string; defaultCurrency: string; numberFormat: { ...; }; dateFormat: string; } | { ...; } | { ...; }'."}]], [81, [{"start": 241, "length": 13, "messageText": "'AuditLogEntry' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [83, [{"start": 1062, "length": 12, "messageText": "'exportConfig' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 1081, "length": 12, "messageText": "'importConfig' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 1099, "length": 11, "messageText": "'getAuditLog' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [86, [{"start": 574, "length": 5, "messageText": "'t' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [88, [{"start": 2601, "length": 5, "messageText": "'t' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [90, [{"start": 753, "length": 5, "messageText": "'t' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 7610, "length": 24, "messageText": "'handleContributionUpdate' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [91, [{"start": 2039, "length": 13, "messageText": "'ErrorBoundary' is declared but never used.", "category": 1, "code": 6196, "reportsUnnecessary": true}, {"start": 2404, "length": 17, "messageText": "This member must have an 'override' modifier because it overrides a member in the base class 'Component<{ children: ReactNode; darkMode?: boolean | undefined; }, { hasError: boolean; error?: Error | undefined; }, any>'.", "category": 1, "code": 4114}, {"start": 2622, "length": 6, "messageText": "This member must have an 'override' modifier because it overrides a member in the base class 'Component<{ children: ReactNode; darkMode?: boolean | undefined; }, { hasError: boolean; error?: Error | undefined; }, any>'.", "category": 1, "code": 4114}, {"start": 4301, "length": 15, "messageText": "'setSecondaryTab' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 4755, "length": 14, "messageText": "'showOnboarding' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 4818, "length": 18, "messageText": "'showSmartDashboard' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 4838, "length": 21, "messageText": "'setShowSmartDashboard' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 21515, "length": 1, "messageText": "'e' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 25187, "length": 2, "messageText": "'id' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 37503, "length": 1, "messageText": "Parameter 'e' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 38232, "length": 1, "messageText": "Parameter 'e' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 39135, "length": 1, "messageText": "Parameter 'e' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 40116, "length": 1, "messageText": "Parameter 'e' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 41090, "length": 1, "messageText": "Parameter 'e' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 42134, "length": 1, "messageText": "Parameter 'e' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 43092, "length": 1, "messageText": "Parameter 'e' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 50195, "length": 1, "messageText": "Parameter 'e' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 51175, "length": 1, "messageText": "Parameter 'e' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 59850, "length": 18, "messageText": "Cannot find name 'safeWithdrawalRate'. Did you mean 'setWithdrawalRate'?", "category": 1, "code": 2552, "canonicalHead": {"code": 18004, "messageText": "No value exists in scope for the shorthand property 'safeWithdrawalRate'. Either declare one or provide an initializer."}, "relatedInformation": [{"start": 7943, "length": 17, "messageText": "'setWithdrawalRate' is declared here.", "category": 3, "code": 2728}]}, {"start": 68672, "length": 10, "messageText": "Cannot find name 'fire<PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"start": 71028, "length": 1, "messageText": "Parameter 'e' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 71902, "length": 1, "messageText": "Parameter 'e' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 72952, "length": 1, "messageText": "Parameter 'e' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 78275, "length": 18, "messageText": "Cannot find name 'safeWithdrawalRate'. Did you mean 'setWithdrawalRate'?", "category": 1, "code": 2552, "canonicalHead": {"code": 18004, "messageText": "No value exists in scope for the shorthand property 'safeWithdrawalRate'. Either declare one or provide an initializer."}, "relatedInformation": [{"start": 7943, "length": 17, "messageText": "'setWithdrawalRate' is declared here.", "category": 3, "code": 2728}]}, {"start": 82673, "length": 18, "messageText": "Cannot find name 'safeWithdrawalRate'. Did you mean 'setWithdrawalRate'?", "category": 1, "code": 2552, "canonicalHead": {"code": 18004, "messageText": "No value exists in scope for the shorthand property 'safeWithdrawalRate'. Either declare one or provide an initializer."}, "relatedInformation": [{"start": 7943, "length": 17, "messageText": "'setWithdrawalRate' is declared here.", "category": 3, "code": 2728}]}, {"start": 83851, "length": 18, "messageText": "Cannot find name 'safeWithdrawalRate'. Did you mean 'setWithdrawalRate'?", "category": 1, "code": 2552, "canonicalHead": {"code": 18004, "messageText": "No value exists in scope for the shorthand property 'safeWithdrawalRate'. Either declare one or provide an initializer."}, "relatedInformation": [{"start": 7943, "length": 17, "messageText": "'setWithdrawalRate' is declared here.", "category": 3, "code": 2728}]}, {"start": 88033, "length": 18, "messageText": "Cannot find name 'safeWithdrawalRate'. Did you mean 'setWithdrawalRate'?", "category": 1, "code": 2552, "canonicalHead": {"code": 18004, "messageText": "No value exists in scope for the shorthand property 'safeWithdrawalRate'. Either declare one or provide an initializer."}, "relatedInformation": [{"start": 7943, "length": 17, "messageText": "'setWithdrawalRate' is declared here.", "category": 3, "code": 2728}]}, {"start": 98865, "length": 18, "messageText": "Cannot find name 'safeWithdrawalRate'. Did you mean 'setWithdrawalRate'?", "category": 1, "code": 2552, "canonicalHead": {"code": 18004, "messageText": "No value exists in scope for the shorthand property 'safeWithdrawalRate'. Either declare one or provide an initializer."}, "relatedInformation": [{"start": 7943, "length": 17, "messageText": "'setWithdrawalRate' is declared here.", "category": 3, "code": 2728}]}, {"start": 99935, "length": 21, "messageText": "Cannot find name 'setSafeWithdrawalRate'. Did you mean 'setWithdrawalRate'?", "category": 1, "code": 2552, "canonicalHead": {"code": 2304, "messageText": "Cannot find name 'setSafeWithdrawalRate'."}, "relatedInformation": [{"start": 7943, "length": 17, "messageText": "'setWithdrawalRate' is declared here.", "category": 3, "code": 2728}]}]], [98, [{"start": 1368, "length": 8, "messageText": "'expenses' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 1440, "length": 5, "messageText": "'t' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 2670, "length": 14, "messageText": "'expectedReturn' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [100, [{"start": 2765, "length": 5, "messageText": "'t' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [101, [{"start": 1107, "length": 18, "messageText": "'currentInvestments' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 1148, "length": 5, "messageText": "'t' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [102, [{"start": 887, "length": 5, "messageText": "'t' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [103, [{"start": 778, "length": 5, "messageText": "'t' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [104, [{"start": 1128, "length": 5, "messageText": "'t' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 5106, "length": 17, "code": 2561, "category": 1, "messageText": "Object literal may only specify known properties, but 'employagementRate' does not exist in type 'CantonData'. Did you mean to write 'employmentRate'?"}]], [105, [{"start": 912, "length": 5, "messageText": "'t' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [106, [{"start": 674, "length": 5, "messageText": "'t' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 709, "length": 11, "messageText": "'isExporting' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [108, [{"start": 870, "length": 7, "messageText": "Not all code paths return a value.", "category": 1, "code": 7030}, {"start": 1285, "length": 18, "messageText": "Cannot find name 'performanceMonitor'. Did you mean 'PerformanceEntry'?", "category": 1, "code": 2552, "canonicalHead": {"code": 2304, "messageText": "Cannot find name 'performanceMonitor'."}, "relatedInformation": [{"file": "./node_modules/typescript/lib/lib.dom.d.ts", "start": 797313, "length": 16, "messageText": "'PerformanceEntry' is declared here.", "category": 3, "code": 2728}]}, {"start": 1346, "length": 18, "messageText": "Cannot find name 'performanceMonitor'. Did you mean 'PerformanceEntry'?", "category": 1, "code": 2552, "canonicalHead": {"code": 2304, "messageText": "Cannot find name 'performanceMonitor'."}, "relatedInformation": [{"file": "./node_modules/typescript/lib/lib.dom.d.ts", "start": 797313, "length": 16, "messageText": "'PerformanceEntry' is declared here.", "category": 3, "code": 2728}]}, {"start": 1414, "length": 18, "messageText": "Cannot find name 'performanceMonitor'.", "category": 1, "code": 2304}, {"start": 1537, "length": 18, "messageText": "Cannot find name 'performanceMonitor'.", "category": 1, "code": 2304}, {"start": 1754, "length": 18, "messageText": "Cannot find name 'performanceMonitor'.", "category": 1, "code": 2304}, {"start": 1988, "length": 13, "messageText": "Cannot find name 'error<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"start": 2086, "length": 13, "messageText": "Cannot find name 'error<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"start": 5969, "length": 18, "messageText": "Cannot find name 'performanceMonitor'.", "category": 1, "code": 2304}, {"start": 6020, "length": 13, "messageText": "Cannot find name 'error<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"start": 17677, "length": 13, "messageText": "Cannot find name 'error<PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}]], [109, [{"start": 714, "length": 17, "messageText": "This member must have an 'override' modifier because it overrides a member in the base class 'Component<Props, State, any>'.", "category": 1, "code": 4114}, {"start": 3986, "length": 6, "messageText": "This member must have an 'override' modifier because it overrides a member in the base class 'Component<Props, State, any>'.", "category": 1, "code": 4114}]], [112, [{"start": 2571, "length": 20, "code": 2740, "category": 1, "messageText": "Type '{ ZH: { code: string; name: string; baseTax: { min: number; max: number; rate: number; base: number; }[]; multiplier: number; wealthTaxRate: number; wealthTaxExemption: { single: number; married: number; }; }; ... 4 more ...; BS: { ...; }; }' is missing the following properties from type 'Record<CantonCode, CantonTaxConfig>': BL, AG, SG, LU, and 16 more.", "canonicalHead": {"code": 2322, "messageText": "Type '{ ZH: { code: string; name: string; baseTax: { min: number; max: number; rate: number; base: number; }[]; multiplier: number; wealthTaxRate: number; wealthTaxExemption: { single: number; married: number; }; }; ... 4 more ...; BS: { ...; }; }' is not assignable to type 'Record<CantonCode, CantonTaxConfig>'."}}, {"start": 9552, "length": 3, "code": 2339, "category": 1, "messageText": "Property 'mul' does not exist on type 'Decimal'."}]], [113, [{"start": 10821, "length": 59, "messageText": "Object is possibly 'undefined'.", "category": 1, "code": 2532}, {"start": 11624, "length": 18, "messageText": "'supplementaryPlans' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 18562, "length": 7, "messageText": "'profile' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 20448, "length": 9, "messageText": "'totalCost' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [114, [{"start": 15273, "length": 12, "messageText": "'currentCosts' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 19164, "length": 13, "messageText": "'healthProfile' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 19202, "length": 9, "messageText": "'fireGoals' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 21345, "length": 9, "messageText": "'fireGoals' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 21371, "length": 6, "messageText": "'canton' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [116, [{"start": 347, "length": 21, "messageText": "'SWISS_HEALTH_INSURERS' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 610, "length": 23, "messageText": "'SWISS_PREMIUM_DATA_2024' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 1317, "length": 5, "messageText": "'t' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [117, [{"start": 26, "length": 9, "messageText": "'useEffect' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 1089, "length": 8, "messageText": "'expenses' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 1106, "length": 11, "messageText": "'investments' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 1126, "length": 12, "messageText": "'savingsGoals' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 1161, "length": 5, "messageText": "'t' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [118, [{"start": 26, "length": 9, "messageText": "'useEffect' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 37, "length": 7, "messageText": "'useMemo' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 1109, "length": 5, "messageText": "'t' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 1404, "length": 10, "messageText": "'volatility' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 1416, "length": 13, "messageText": "'setVolatility' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [119, [{"start": 26, "length": 9, "messageText": "'useEffect' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 2041, "length": 5, "messageText": "'t' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [120, [{"start": 687, "length": 5, "messageText": "'t' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 1526, "length": 15, "code": 2339, "category": 1, "messageText": "Property 'navigationStart' does not exist on type 'PerformanceNavigationTiming'."}]], [121, [{"start": 994, "length": 5, "messageText": "'t' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 5276, "length": 14, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type 'any[]' is not assignable to parameter of type 'never[] | ((prevValue: never[]) => never[])'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'any[]' is not assignable to type 'never[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'any' is not assignable to type 'never'.", "category": 1, "code": 2322}]}]}}, {"start": 5557, "length": 14, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type '{ date: string; savings: number; investments: any; netWorth: any; savingsRate: number; }[]' is not assignable to parameter of type 'never[] | ((prevValue: never[]) => never[])'.", "category": 1, "code": 2345, "next": [{"messageText": "Type '{ date: string; savings: number; investments: any; netWorth: any; savingsRate: number; }[]' is not assignable to type 'never[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ date: string; savings: number; investments: any; netWorth: any; savingsRate: number; }' is not assignable to type 'never'.", "category": 1, "code": 2322}]}]}}]], [122, [{"start": 1184, "length": 5, "messageText": "'t' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 5983, "length": 14, "messageText": "'updateProperty' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [123, [{"start": 37, "length": 7, "messageText": "'useMemo' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 1116, "length": 5, "messageText": "'t' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 2364, "length": 10, "messageText": "'fireTarget' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 2440, "length": 16, "messageText": "'totalInvestments' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 2539, "length": 13, "messageText": "'totalExpenses' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [124, [{"start": 1763, "length": 7, "messageText": "Not all code paths return a value.", "category": 1, "code": 7030}]], [125, [{"start": 5866, "length": 5, "messageText": "'error' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [126, [{"start": 37, "length": 7, "messageText": "'useMemo' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 1072, "length": 5, "messageText": "'t' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 1635, "length": 13, "messageText": "'retirementAge' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [127, [{"start": 1003, "length": 5, "messageText": "'t' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 1650, "length": 17, "messageText": "'yearsToRetirement' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 5717, "length": 11, "messageText": "'savingsRate' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 6428, "length": 3, "messageText": "'sum' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 6434, "length": 6, "messageText": "'amount' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 6553, "length": 6, "messageText": "'amount' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 6562, "length": 13, "messageText": "'totalExpenses' is of type 'unknown'.", "category": 1, "code": 18046}]], [130, [{"start": 676, "length": 5, "messageText": "'t' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 1211, "length": 18, "messageText": "'setSelectedCantons' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 15952, "length": 1, "messageText": "'a' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 15965, "length": 1, "messageText": "'b' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 16372, "length": 6, "messageText": "'result' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 16467, "length": 6, "messageText": "'result' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 16568, "length": 6, "messageText": "'result' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 16688, "length": 6, "messageText": "'result' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 16761, "length": 1, "messageText": "'r' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 16839, "length": 6, "messageText": "'result' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 16912, "length": 1, "messageText": "'r' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 16983, "length": 6, "messageText": "'result' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 17054, "length": 1, "messageText": "'r' is of type 'unknown'.", "category": 1, "code": 18046}]], [131, [{"start": 26, "length": 9, "messageText": "'useEffect' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 942, "length": 5, "messageText": "'t' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 8732, "length": 17, "messageText": "'optimizeDeduction' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [132, [{"start": 1132, "length": 5, "messageText": "'t' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 1223, "length": 16, "messageText": "'selectedScenario' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 1241, "length": 19, "messageText": "'setSelectedScenario' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 22254, "length": 5, "messageText": "'index' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [134, [{"start": 1239, "length": 5, "messageText": "'t' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 18114, "length": 13, "code": 2322, "category": 1, "messageText": {"messageText": "Type 'CivilStatus' is not assignable to type '\"single\" | \"married\"'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '\"divorced\"' is not assignable to type '\"single\" | \"married\"'.", "category": 1, "code": 2322}]}, "relatedInformation": [{"file": "./src/components/SwissTaxDeductionOptimizer.tsx", "start": 239, "length": 13, "messageText": "The expected type comes from property 'maritalStatus' which is declared here on type '{ annualIncome: number; canton: string; maritalStatus: \"single\" | \"married\"; children: number; currentPillar3a: number; professionalExpenses: number; insurancePremiums: number; medicalExpenses: number; donations: number; interestExpenses: number; }'", "category": 3, "code": 6500}]}]], [137, [{"start": 486, "length": 5, "messageText": "'t' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 10832, "length": 3, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type 'string | number' is not assignable to parameter of type 'string'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'number' is not assignable to type 'string'.", "category": 1, "code": 2322}]}}, {"start": 15140, "length": 6, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type 'string | number' is not assignable to parameter of type 'string'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'number' is not assignable to type 'string'.", "category": 1, "code": 2322}]}}, {"start": 15166, "length": 16, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'additionalIncome' does not exist in type 'Partial<UserData>'."}, {"start": 15563, "length": 15, "code": 2339, "category": 1, "messageText": "Property 'experienceLevel' does not exist on type 'Partial<UserData>'."}, {"start": 17323, "length": 15, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'experienceLevel' does not exist in type 'Partial<UserData>'."}]], [138, [{"start": 964, "length": 5, "messageText": "'t' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 5591, "length": 3, "code": 2339, "category": 1, "messageText": "Property 'age' does not exist on type '{ fireAge?: number | undefined; fireProgress?: number | undefined; monthsToFire?: number | undefined; }'."}]], [139, [{"start": 13567, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'chrome' does not exist on type 'Window & typeof globalThis'."}, {"start": 13584, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'chrome' does not exist on type 'Window & typeof globalThis'."}, {"start": 15902, "length": 10, "messageText": "'loadEvents' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [140, [{"start": 4394, "length": 23, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ enabled: boolean; retentionDays: number; encryptionRequired: boolean; exportAllowed: boolean; } | { enabled: boolean; retentionDays: number; encryptionRequired: boolean; exportAllowed: boolean; } | ... 33 more ... | { ...; }' is not assignable to type '{ enabled: boolean; retentionDays: number; encryptionRequired: boolean; exportAllowed: boolean; } & { enabled: boolean; retentionDays: number; encryptionRequired: boolean; exportAllowed: boolean; } & { ...; } & { ...; } & { ...; } & { ...; }'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ enabled: boolean; retentionDays: number; encryptionRequired: boolean; exportAllowed: boolean; }' is not assignable to type '{ enabled: boolean; retentionDays: number; encryptionRequired: boolean; exportAllowed: boolean; } & { enabled: boolean; retentionDays: number; encryptionRequired: boolean; exportAllowed: boolean; } & { ...; } & { ...; } & { ...; } & { ...; }'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'anonymized' is missing in type '{ enabled: boolean; retentionDays: number; encryptionRequired: boolean; exportAllowed: boolean; }' but required in type '{ enabled: boolean; retentionDays: number; anonymized: boolean; exportAllowed: boolean; }'.", "category": 1, "code": 2741, "canonicalHead": {"code": 2322, "messageText": "Type '{ enabled: boolean; retentionDays: number; encryptionRequired: boolean; exportAllowed: boolean; }' is not assignable to type '{ enabled: boolean; retentionDays: number; anonymized: boolean; exportAllowed: boolean; }'."}}]}]}, "relatedInformation": [{"start": 918, "length": 10, "messageText": "'anonymized' is declared here.", "category": 3, "code": 2728}]}, {"start": 10252, "length": 7, "messageText": "'request' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 12698, "length": 11, "code": 2339, "category": 1, "messageText": {"messageText": "Property 'autoCleanup' does not exist on type '{ enabled: boolean; retentionDays: number; encryptionRequired: boolean; exportAllowed: boolean; } | { enabled: boolean; retentionDays: number; encryptionRequired: boolean; exportAllowed: boolean; } | { ...; } | { ...; } | { ...; } | { ...; }'.", "category": 1, "code": 2339, "next": [{"messageText": "Property 'autoCleanup' does not exist on type '{ enabled: boolean; retentionDays: number; encryptionRequired: boolean; exportAllowed: boolean; }'.", "category": 1, "code": 2339}]}}]], [141, [{"start": 188, "length": 32, "messageText": "'Buffer' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 3158, "length": 17, "messageText": "'keyDerivationTime' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [142, [{"start": 316, "length": 8, "messageText": "'Settings' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 340, "length": 6, "messageText": "'Trash2' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 389, "length": 14, "messageText": "Cannot find module 'lucide-react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 619, "length": 13, "messageText": "'SecurityUtils' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [144, [{"start": 12044, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'memory' does not exist on type 'Performance'."}, {"start": 13525, "length": 8, "messageText": "'filename' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 14774, "length": 5, "messageText": "'React' refers to a UMD global, but the current file is a module. Consider adding an import instead.", "category": 1, "code": 2686}]], [145, [{"start": 4883, "length": 15, "messageText": "'performanceData' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 11229, "length": 5, "messageText": "'error' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [147, [{"start": 1353, "length": 16, "messageText": "'trackCalculation' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 1808, "length": 14, "messageText": "'trackTabChange' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 9143, "length": 11, "messageText": "'handleError' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [148, [{"start": 92, "length": 17, "messageText": "'DateFormatOptions' is declared but never used.", "category": 1, "code": 6196, "reportsUnnecessary": true}]], [162, [{"start": 3074, "length": 13, "messageText": "'fallbackValue' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [164, [{"start": 294, "length": 7, "messageText": "Module '\"decimal.js\"' has no exported member 'Decimal'.", "category": 1, "code": 2305}]], [165, [{"start": 7649, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'suggestion' does not exist on type 'DiagnosticResult'."}, {"start": 8782, "length": 5, "messageText": "'React' refers to a UMD global, but the current file is a module. Consider adding an import instead.", "category": 1, "code": 2686}, {"start": 8831, "length": 8, "messageText": "'ReactDOM' refers to a UMD global, but the current file is a module. Consider adding an import instead.", "category": 1, "code": 2686}]], [173, [{"start": 15, "length": 6, "messageText": "'expect' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [177, [{"start": 209, "length": 4, "messageText": "'Page' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 9380, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'greaterThan' does not exist on type '(count: number, options?: { timeout?: number | undefined; } | undefined) => Promise<void>'."}, {"start": 13712, "length": 15, "messageText": "'originalSetItem' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [179, [{"start": 2687, "length": 6, "code": 2551, "category": 1, "messageText": "Property 'errors' does not exist on type 'Window & typeof globalThis'. Did you mean 'Error'?", "relatedInformation": [{"file": "./node_modules/typescript/lib/lib.es5.d.ts", "start": 44357, "length": 5, "messageText": "'Error' is declared here.", "category": 3, "code": 2728}]}, {"start": 7434, "length": 14, "messageText": "'contrastIssues' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 15256, "length": 10, "messageText": "'focusedTag' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 18647, "length": 8, "messageText": "'page' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [181, [{"start": 164, "length": 6, "messageText": "'config' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [184, [{"start": 704, "length": 8, "messageText": "'page' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 968, "length": 11, "messageText": "'testProfile' is possibly 'undefined'.", "category": 1, "code": 18048}, {"start": 1018, "length": 11, "messageText": "'testProfile' is possibly 'undefined'.", "category": 1, "code": 18048}, {"start": 1069, "length": 11, "messageText": "'testProfile' is possibly 'undefined'.", "category": 1, "code": 18048}, {"start": 1106, "length": 11, "messageText": "'testProfile' is possibly 'undefined'.", "category": 1, "code": 18048}, {"start": 1139, "length": 11, "messageText": "'testProfile' is possibly 'undefined'.", "category": 1, "code": 18048}, {"start": 1764, "length": 11, "messageText": "'testProfile' is possibly 'undefined'.", "category": 1, "code": 18048}, {"start": 2250, "length": 11, "messageText": "'testProfile' is possibly 'undefined'.", "category": 1, "code": 18048}, {"start": 2323, "length": 11, "messageText": "'testProfile' is possibly 'undefined'.", "category": 1, "code": 18048}, {"start": 2374, "length": 11, "messageText": "'testProfile' is possibly 'undefined'.", "category": 1, "code": 18048}, {"start": 2411, "length": 11, "messageText": "'testProfile' is possibly 'undefined'.", "category": 1, "code": 18048}, {"start": 2444, "length": 11, "messageText": "'testProfile' is possibly 'undefined'.", "category": 1, "code": 18048}, {"start": 2738, "length": 7, "messageText": "Cannot find name 'results'.", "category": 1, "code": 2304}, {"start": 3205, "length": 8, "messageText": "'page' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 3451, "length": 11, "messageText": "'testProfile' is possibly 'undefined'.", "category": 1, "code": 18048}, {"start": 3501, "length": 11, "messageText": "'testProfile' is possibly 'undefined'.", "category": 1, "code": 18048}, {"start": 3552, "length": 11, "messageText": "'testProfile' is possibly 'undefined'.", "category": 1, "code": 18048}, {"start": 3589, "length": 11, "messageText": "'testProfile' is possibly 'undefined'.", "category": 1, "code": 18048}, {"start": 3646, "length": 11, "messageText": "'testProfile' is possibly 'undefined'.", "category": 1, "code": 18048}, {"start": 4956, "length": 8, "messageText": "'page' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 6089, "length": 8, "messageText": "'page' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 7262, "length": 8, "messageText": "'page' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 9610, "length": 24, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type 'number | null' is not assignable to parameter of type 'number | bigint'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'null' is not assignable to type 'number | bigint'.", "category": 1, "code": 2322}]}}, {"start": 9830, "length": 8, "messageText": "'page' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 11215, "length": 8, "messageText": "'page' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [185, [{"start": 1416, "length": 14, "messageText": "'lowRiskProfile' is possibly 'undefined'.", "category": 1, "code": 18048}, {"start": 1559, "length": 14, "messageText": "'lowRiskProfile' is possibly 'undefined'.", "category": 1, "code": 18048}, {"start": 1688, "length": 14, "messageText": "'lowRiskProfile' is possibly 'undefined'.", "category": 1, "code": 18048}, {"start": 3875, "length": 15, "messageText": "'highRiskProfile' is possibly 'undefined'.", "category": 1, "code": 18048}, {"start": 4019, "length": 15, "messageText": "'highRiskProfile' is possibly 'undefined'.", "category": 1, "code": 18048}, {"start": 4149, "length": 15, "messageText": "'highRiskProfile' is possibly 'undefined'.", "category": 1, "code": 18048}, {"start": 5893, "length": 17, "messageText": "'mediumRiskProfile' is possibly 'undefined'.", "category": 1, "code": 18048}, {"start": 6027, "length": 17, "messageText": "'mediumRiskProfile' is possibly 'undefined'.", "category": 1, "code": 18048}, {"start": 11743, "length": 11, "messageText": "Cannot find name 'baseResults'.", "category": 1, "code": 2304}, {"start": 15590, "length": 8, "messageText": "'page' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [187, [{"start": 314, "length": 74, "messageText": "'swissFinancialData' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 4054, "length": 15, "messageText": "'pillar3aContent' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 6859, "length": 8, "messageText": "'page' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 8756, "length": 8, "messageText": "'page' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 11917, "length": 8, "messageText": "'page' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 12772, "length": 8, "messageText": "'page' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [188, [{"start": 2902, "length": 5, "messageText": "'error' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 3794, "length": 5, "messageText": "'error' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 6064, "length": 5, "messageText": "'error' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 7309, "length": 23, "messageText": "Operator '<' cannot be applied to types 'number' and 'string'.", "category": 1, "code": 2365}, {"start": 7376, "length": 3, "code": 2349, "category": 1, "messageText": {"messageText": "This expression is not callable.", "category": 1, "code": 2349, "next": [{"messageText": "Type 'String' has no call signatures.", "category": 1, "code": 2757}]}}, {"start": 7445, "length": 7, "code": 2349, "category": 1, "messageText": {"messageText": "This expression is not callable.", "category": 1, "code": 2349, "next": [{"messageText": "Type 'String' has no call signatures.", "category": 1, "code": 2757}]}}, {"start": 7824, "length": 5, "messageText": "'error' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 9510, "length": 5, "messageText": "'error' is of type 'unknown'.", "category": 1, "code": 18046}]], [189, [{"start": 1560, "length": 5, "messageText": "'error' is of type 'unknown'.", "category": 1, "code": 18046}]], [190, [{"start": 1026, "length": 10, "messageText": "'profileTab' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 1732, "length": 19, "messageText": "'subsidyOptimization' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 10784, "length": 19, "messageText": "This member must have an 'override' modifier because it overrides a member in the base class 'BasePage'.", "category": 1, "code": 4114}]], [195, [{"start": 1729, "length": 15, "code": 2339, "category": 1, "messageText": "Property 'processingStart' does not exist on type 'PerformanceEntry'."}, {"start": 11653, "length": 19, "messageText": "'firstVisitResources' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [196, [{"start": 290, "length": 10, "messageText": "'testSuites' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 302, "length": 14, "messageText": "'testCategories' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 5558, "length": 5, "messageText": "'suite' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 6323, "length": 6, "messageText": "'result' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 13313, "length": 14, "messageText": "Re-exporting a type when 'isolatedModules' is enabled requires using 'export type'.", "category": 1, "code": 1205}]], [197, [{"start": 1657, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'overviewTab' does not exist on type 'DashboardPage'."}, {"start": 1758, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'budgetTab' does not exist on type 'DashboardPage'."}, {"start": 1865, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'targetTab' does not exist on type 'DashboardPage'."}, {"start": 2002, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'targetTab' does not exist on type 'DashboardPage'."}, {"start": 4115, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'overviewTab' does not exist on type 'DashboardPage'."}, {"start": 4150, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'budgetTab' does not exist on type 'DashboardPage'."}, {"start": 4183, "length": 18, "code": 2551, "category": 1, "messageText": "Property 'taxOptimizationTab' does not exist on type 'DashboardPage'. Did you mean 'taxOptimizationSubTab'?", "relatedInformation": [{"file": "./tests/e2e/pages/dashboard-page.ts", "start": 1453, "length": 21, "messageText": "'taxOptimizationSubTab' is declared here.", "category": 3, "code": 2728}]}, {"start": 4225, "length": 19, "code": 2339, "category": 1, "messageText": "Property 'fireAccelerationTab' does not exist on type 'DashboardPage'."}, {"start": 6541, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'navigateToVizTab' does not exist on type 'DashboardPage'."}, {"start": 9179, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'devices' does not exist on type 'Browser'."}, {"start": 10590, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'overviewTab' does not exist on type 'DashboardPage'."}, {"start": 10625, "length": 18, "code": 2551, "category": 1, "messageText": "Property 'taxOptimizationTab' does not exist on type 'DashboardPage'. Did you mean 'taxOptimizationSubTab'?", "relatedInformation": [{"file": "./tests/e2e/pages/dashboard-page.ts", "start": 1453, "length": 21, "messageText": "'taxOptimizationSubTab' is declared here.", "category": 3, "code": 2728}]}, {"start": 10667, "length": 18, "code": 2339, "category": 1, "messageText": "Property 'swissRelocationTab' does not exist on type 'DashboardPage'."}]], [198, [{"start": 72, "length": 22, "messageText": "Cannot find module '@axe-core/playwright' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 850, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'clickTab' does not exist on type 'DashboardPage'."}, {"start": 2113, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'clickTab' does not exist on type 'DashboardPage'."}, {"start": 3122, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'toLowerCase' does not exist on type 'Promise<string | null>'."}, {"start": 4232, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'clickTab' does not exist on type 'DashboardPage'."}, {"start": 5981, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'clickTab' does not exist on type 'DashboardPage'."}, {"start": 7823, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'clickTab' does not exist on type 'DashboardPage'."}, {"start": 9031, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'clickTab' does not exist on type 'DashboardPage'."}, {"start": 10769, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'clickTab' does not exist on type 'DashboardPage'."}, {"start": 12334, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'clickTab' does not exist on type 'DashboardPage'."}, {"start": 13581, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'clickTab' does not exist on type 'DashboardPage'."}]], [203, [{"start": 655, "length": 15, "messageText": "'page' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 2872, "length": 8, "messageText": "'page' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 4053, "length": 8, "messageText": "'page' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 4969, "length": 8, "messageText": "'page' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 6019, "length": 8, "messageText": "'page' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 6471, "length": 25, "code": 2339, "category": 1, "messageText": "Property 'enterPillar3aContribution' does not exist on type 'DashboardPage'."}, {"start": 7086, "length": 8, "messageText": "'page' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 8167, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'bonusIncomeInput' does not exist on type 'DashboardPage'."}, {"start": 8274, "length": 13, "code": 2339, "category": 1, "messageText": "Property 'pillar3aInput' does not exist on type 'DashboardPage'."}]], [205, [{"start": 4209, "length": 8, "messageText": "'page' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 6245, "length": 4, "messageText": "'page' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 6956, "length": 28, "code": 2339, "category": 1, "messageText": "Property 'navigateToSwissRelocationTab' does not exist on type 'DashboardPage'."}, {"start": 7058, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}, {"start": 9125, "length": 8, "messageText": "'page' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 9619, "length": 28, "code": 2339, "category": 1, "messageText": "Property 'navigateToSwissRelocationTab' does not exist on type 'DashboardPage'."}]], [207, [{"start": 12910, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}, {"start": 12957, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}, {"start": 13072, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}]], [208, [{"start": 6222, "length": 8, "messageText": "'page' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [209, [{"start": 344, "length": 11, "messageText": "'testHelpers' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 775, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}, {"start": 973, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}, {"start": 1109, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}, {"start": 1224, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}, {"start": 1457, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'overviewTab' does not exist on type 'DashboardPage'."}, {"start": 1818, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}, {"start": 1901, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}, {"start": 1985, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}, {"start": 2384, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}, {"start": 2389, "length": 11, "messageText": "Expected 1-2 arguments, but got 0.", "category": 1, "code": 2554, "relatedInformation": [{"file": "./node_modules/playwright-core/types/types.d.ts", "start": 199385, "length": 16, "messageText": "An argument for 'selector' was not provided.", "category": 3, "code": 6210}]}, {"start": 2617, "length": 11, "messageText": "'pageContent' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 3098, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}, {"start": 3181, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}, {"start": 3265, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}, {"start": 3344, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}, {"start": 3489, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}, {"start": 4256, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}, {"start": 4374, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}, {"start": 4457, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}, {"start": 4541, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}, {"start": 4693, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}, {"start": 5341, "length": 29, "code": 2339, "category": 1, "messageText": "Property 'navigateToFireAccelerationTab' does not exist on type 'DashboardPage'."}, {"start": 5427, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}, {"start": 5510, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}, {"start": 5594, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}, {"start": 5714, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}, {"start": 5719, "length": 11, "messageText": "Expected 1-2 arguments, but got 0.", "category": 1, "code": 2554, "relatedInformation": [{"file": "./node_modules/playwright-core/types/types.d.ts", "start": 199385, "length": 16, "messageText": "An argument for 'selector' was not provided.", "category": 3, "code": 6210}]}, {"start": 6103, "length": 11, "messageText": "'pageContent' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 6841, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}, {"start": 6924, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}, {"start": 7008, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}, {"start": 7735, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}, {"start": 7818, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}, {"start": 7902, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}, {"start": 8175, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}, {"start": 8595, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}]], [211, [{"start": 673, "length": 9, "messageText": "'startTime' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 3454, "length": 8, "messageText": "'page' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 4655, "length": 8, "messageText": "'page' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [212, [{"start": 930, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'clickTab' does not exist on type 'DashboardPage'."}, {"start": 1757, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'clickTab' does not exist on type 'DashboardPage'."}, {"start": 2553, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'clickTab' does not exist on type 'DashboardPage'."}, {"start": 3612, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'clickTab' does not exist on type 'DashboardPage'."}, {"start": 5209, "length": 7, "messageText": "'context' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 5328, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'clickTab' does not exist on type 'DashboardPage'."}, {"start": 6291, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'clickTab' does not exist on type 'DashboardPage'."}, {"start": 7627, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'clickTab' does not exist on type 'DashboardPage'."}, {"start": 8064, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'clickTab' does not exist on type 'DashboardPage'."}, {"start": 10249, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'clickTab' does not exist on type 'DashboardPage'."}, {"start": 11165, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'clickTab' does not exist on type 'DashboardPage'."}]], [213, [{"start": 395, "length": 8, "messageText": "'page' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 851, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'overviewTab' does not exist on type 'DashboardPage'."}, {"start": 993, "length": 8, "messageText": "'page' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 1779, "length": 8, "messageText": "'page' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 2549, "length": 28, "code": 2339, "category": 1, "messageText": "Property 'navigateToSwissRelocationTab' does not exist on type 'DashboardPage'."}, {"start": 2646, "length": 29, "code": 2339, "category": 1, "messageText": "Property 'navigateToFireAccelerationTab' does not exist on type 'DashboardPage'."}, {"start": 3073, "length": 8, "messageText": "'page' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 3694, "length": 28, "code": 2339, "category": 1, "messageText": "Property 'navigateToSwissRelocationTab' does not exist on type 'DashboardPage'."}, {"start": 3750, "length": 29, "code": 2339, "category": 1, "messageText": "Property 'navigateToFireAccelerationTab' does not exist on type 'DashboardPage'."}, {"start": 4075, "length": 8, "messageText": "'page' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 4662, "length": 28, "code": 2339, "category": 1, "messageText": "Property 'navigateToSwissRelocationTab' does not exist on type 'DashboardPage'."}, {"start": 5233, "length": 4, "messageText": "'page' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 7454, "length": 28, "code": 2339, "category": 1, "messageText": "Property 'navigateToSwissRelocationTab' does not exist on type 'DashboardPage'."}, {"start": 7525, "length": 29, "code": 2339, "category": 1, "messageText": "Property 'navigateToFireAccelerationTab' does not exist on type 'DashboardPage'."}, {"start": 8576, "length": 24, "code": 2551, "category": 1, "messageText": "Property 'navigateToProjectionsTab' does not exist on type 'DashboardPage'. Did you mean 'navigateToProjectionsSubTab'?", "relatedInformation": [{"file": "./tests/e2e/pages/dashboard-page.ts", "start": 5636, "length": 27, "messageText": "'navigateToProjectionsSubTab' is declared here.", "category": 3, "code": 2728}]}, {"start": 8655, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'navigateToVizTab' does not exist on type 'DashboardPage'."}, {"start": 8745, "length": 34, "code": 2339, "category": 1, "messageText": "Property 'navigateToInteractiveProjectionTab' does not exist on type 'DashboardPage'."}, {"start": 9451, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'devices' does not exist on type 'Browser'."}]], [217, [{"start": 190, "length": 68, "messageText": "'swissTestScenarios' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 873, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'clickTab' does not exist on type 'DashboardPage'."}, {"start": 2067, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'clickTab' does not exist on type 'DashboardPage'."}, {"start": 4588, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'clickTab' does not exist on type 'DashboardPage'."}, {"start": 6502, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'clickTab' does not exist on type 'DashboardPage'."}, {"start": 7497, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'clickTab' does not exist on type 'DashboardPage'."}, {"start": 9164, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'clickTab' does not exist on type 'DashboardPage'."}, {"start": 10935, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'clickTab' does not exist on type 'DashboardPage'."}, {"start": 12420, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'clickTab' does not exist on type 'DashboardPage'."}, {"start": 13629, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'clickTab' does not exist on type 'DashboardPage'."}, {"start": 14614, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'updateCanton' does not exist on type 'HealthcareOptimizerPage'."}, {"start": 15010, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'clickTab' does not exist on type 'DashboardPage'."}, {"start": 16046, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'clickTab' does not exist on type 'DashboardPage'."}, {"start": 16094, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'clickTab' does not exist on type 'DashboardPage'."}]], [218, [{"start": 942, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}, {"start": 1126, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}, {"start": 1596, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}, {"start": 2023, "length": 28, "code": 2339, "category": 1, "messageText": "Property 'navigateToSwissRelocationTab' does not exist on type 'DashboardPage'."}, {"start": 2134, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}, {"start": 3611, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}, {"start": 4030, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}, {"start": 6512, "length": 29, "code": 2339, "category": 1, "messageText": "Property 'navigateToFireAccelerationTab' does not exist on type 'DashboardPage'."}, {"start": 6634, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}, {"start": 6824, "length": 4, "messageText": "Property 'page' is protected and only accessible within class 'BasePage' and its subclasses.", "category": 1, "code": 2445}, {"start": 7062, "length": 19, "messageText": "'recommendationsText' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 7241, "length": 19, "messageText": "'recommendationsText' is possibly 'null'.", "category": 1, "code": 18047}]], [219, [{"start": 13373, "length": 8, "messageText": "'page' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 14567, "length": 8, "messageText": "'page' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [220, [{"start": 1267, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'clickTab' does not exist on type 'DashboardPage'."}, {"start": 6502, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'clickTab' does not exist on type 'DashboardPage'."}, {"start": 8495, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'clickTab' does not exist on type 'DashboardPage'."}, {"start": 10412, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'clickTab' does not exist on type 'DashboardPage'."}, {"start": 12238, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'clickTab' does not exist on type 'DashboardPage'."}, {"start": 14842, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'clickTab' does not exist on type 'DashboardPage'."}, {"start": 15796, "length": 14, "messageText": "Cannot find name 'desktopSavings'.", "category": 1, "code": 2304}]], [223, [{"start": 246, "length": 6, "messageText": "'config' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 4356, "length": 9, "messageText": "'tempFiles' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [503, [{"start": 408, "length": 9, "messageText": "'fireEvent' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 1037, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ isOpen: boolean; onComplete: Mock<Procedure>; onClose: Mock<Procedure>; darkMode: false; }' is not assignable to type 'IntrinsicAttributes & OnboardingWizardProps'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'isOpen' does not exist on type 'IntrinsicAttributes & OnboardingWizardProps'.", "category": 1, "code": 2339}]}}, {"start": 1434, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ isOpen: boolean; onComplete: Mock<Procedure>; onClose: Mock<Procedure>; darkMode: false; }' is not assignable to type 'IntrinsicAttributes & OnboardingWizardProps'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'isOpen' does not exist on type 'IntrinsicAttributes & OnboardingWizardProps'.", "category": 1, "code": 2339}]}}, {"start": 1904, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ isOpen: boolean; onComplete: Mock<Procedure>; onClose: Mock<Procedure>; darkMode: false; }' is not assignable to type 'IntrinsicAttributes & OnboardingWizardProps'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'isOpen' does not exist on type 'IntrinsicAttributes & OnboardingWizardProps'.", "category": 1, "code": 2339}]}}, {"start": 2485, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ isOpen: boolean; onComplete: Mock<Procedure>; onClose: Mock<Procedure>; darkMode: false; }' is not assignable to type 'IntrinsicAttributes & OnboardingWizardProps'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'isOpen' does not exist on type 'IntrinsicAttributes & OnboardingWizardProps'.", "category": 1, "code": 2339}]}}, {"start": 3175, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ isOpen: boolean; onComplete: Mock<Procedure>; onClose: Mock<Procedure>; darkMode: false; }' is not assignable to type 'IntrinsicAttributes & OnboardingWizardProps'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'isOpen' does not exist on type 'IntrinsicAttributes & OnboardingWizardProps'.", "category": 1, "code": 2339}]}}, {"start": 3665, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ isOpen: boolean; onComplete: Mock<Procedure>; onClose: Mock<Procedure>; darkMode: false; }' is not assignable to type 'IntrinsicAttributes & OnboardingWizardProps'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'isOpen' does not exist on type 'IntrinsicAttributes & OnboardingWizardProps'.", "category": 1, "code": 2339}]}}, {"start": 6132, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ isOpen: boolean; onComplete: Mock<Procedure>; onClose: Mock<Procedure>; darkMode: false; }' is not assignable to type 'IntrinsicAttributes & OnboardingWizardProps'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'isOpen' does not exist on type 'IntrinsicAttributes & OnboardingWizardProps'.", "category": 1, "code": 2339}]}}, {"start": 9094, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ isOpen: boolean; onComplete: Mock<Procedure>; onClose: Mock<Procedure>; darkMode: true; }' is not assignable to type 'IntrinsicAttributes & OnboardingWizardProps'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'isOpen' does not exist on type 'IntrinsicAttributes & OnboardingWizardProps'.", "category": 1, "code": 2339}]}}, {"start": 9559, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ isOpen: boolean; onComplete: Mock<Procedure>; onClose: Mock<Procedure>; darkMode: false; }' is not assignable to type 'IntrinsicAttributes & OnboardingWizardProps'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'isOpen' does not exist on type 'IntrinsicAttributes & OnboardingWizardProps'.", "category": 1, "code": 2339}]}}, {"start": 10071, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ isOpen: boolean; onComplete: Mock<Procedure>; onClose: Mock<Procedure>; darkMode: false; }' is not assignable to type 'IntrinsicAttributes & OnboardingWizardProps'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'isOpen' does not exist on type 'IntrinsicAttributes & OnboardingWizardProps'.", "category": 1, "code": 2339}]}}, {"start": 10614, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ isOpen: boolean; onComplete: Mock<Procedure>; onClose: Mock<Procedure>; darkMode: true; }' is not assignable to type 'IntrinsicAttributes & OnboardingWizardProps'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'isOpen' does not exist on type 'IntrinsicAttributes & OnboardingWizardProps'.", "category": 1, "code": 2339}]}}, {"start": 11055, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ isOpen: boolean; onComplete: Mock<Procedure>; onClose: Mock<Procedure>; darkMode: false; }' is not assignable to type 'IntrinsicAttributes & OnboardingWizardProps'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'isOpen' does not exist on type 'IntrinsicAttributes & OnboardingWizardProps'.", "category": 1, "code": 2339}]}}, {"start": 11926, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ isOpen: boolean; onComplete: Mock<Procedure>; onClose: Mock<Procedure>; darkMode: false; }' is not assignable to type 'IntrinsicAttributes & OnboardingWizardProps'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'isOpen' does not exist on type 'IntrinsicAttributes & OnboardingWizardProps'.", "category": 1, "code": 2339}]}}, {"start": 12817, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ isOpen: boolean; onComplete: Mock<Procedure>; onClose: Mock<Procedure>; darkMode: false; }' is not assignable to type 'IntrinsicAttributes & OnboardingWizardProps'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'isOpen' does not exist on type 'IntrinsicAttributes & OnboardingWizardProps'.", "category": 1, "code": 2339}]}}, {"start": 14395, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ isOpen: boolean; onComplete: Mock<Procedure>; onClose: Mock<Procedure>; darkMode: false; }' is not assignable to type 'IntrinsicAttributes & OnboardingWizardProps'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'isOpen' does not exist on type 'IntrinsicAttributes & OnboardingWizardProps'.", "category": 1, "code": 2339}]}}, {"start": 15889, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ isOpen: boolean; onComplete: Mock<Procedure>; onClose: Mock<Procedure>; darkMode: false; }' is not assignable to type 'IntrinsicAttributes & OnboardingWizardProps'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'isOpen' does not exist on type 'IntrinsicAttributes & OnboardingWizardProps'.", "category": 1, "code": 2339}]}}, {"start": 16298, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ isOpen: boolean; onComplete: Mock<Procedure>; onClose: Mock<Procedure>; darkMode: false; }' is not assignable to type 'IntrinsicAttributes & OnboardingWizardProps'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'isOpen' does not exist on type 'IntrinsicAttributes & OnboardingWizardProps'.", "category": 1, "code": 2339}]}}, {"start": 16895, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ isOpen: boolean; onComplete: Mock<Procedure>; onClose: Mock<Procedure>; darkMode: false; }' is not assignable to type 'IntrinsicAttributes & OnboardingWizardProps'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'isOpen' does not exist on type 'IntrinsicAttributes & OnboardingWizardProps'.", "category": 1, "code": 2339}]}}]], [504, [{"start": 185, "length": 26, "messageText": "'React' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 4618, "length": 11, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type '{ totalEvents: number; eventsByType: { data_access: number; }; eventsBySeverity: { low: number; medium: number; high: number; critical: number; }; threatsDetected: number; threatsResolved: number; averageResponseTime: number; lastThreatDetected: Date; }' is not assignable to parameter of type 'SecurityMetrics'.", "category": 1, "code": 2345, "next": [{"messageText": "Types of property 'eventsByType' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type '{ data_access: number; }' is missing the following properties from type 'Record<SecurityEventType, number>': encryption_key_derived, encryption_success, encryption_failure, decryption_success, and 17 more.", "category": 1, "code": 2740, "canonicalHead": {"code": 2322, "messageText": "Type '{ data_access: number; }' is not assignable to type 'Record<SecurityEventType, number>'."}}]}]}}]], [507, [{"start": 559, "length": 11, "messageText": "'CivilStatus' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [511, [{"start": 0, "length": 26, "messageText": "'React' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 250, "length": 4, "messageText": "Cannot use namespace 'jest' as a value.", "category": 1, "code": 2708}, {"start": 426, "length": 4, "messageText": "Cannot use namespace 'jest' as a value.", "category": 1, "code": 2708}, {"start": 502, "length": 4, "messageText": "Cannot use namespace 'jest' as a value.", "category": 1, "code": 2708}, {"start": 511, "length": 3, "messageText": "'key' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 561, "length": 4, "messageText": "Cannot use namespace 'jest' as a value.", "category": 1, "code": 2708}, {"start": 1295, "length": 8, "messageText": "Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 1336, "length": 10, "messageText": "Cannot find name 'before<PERSON><PERSON>'.", "category": 1, "code": 2304}, {"start": 1359, "length": 4, "messageText": "Cannot use namespace 'jest' as a value.", "category": 1, "code": 2708}, {"start": 1390, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 1673, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 1750, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 1840, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 2119, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 2218, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 2540, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 2650, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 2717, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 2785, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 2849, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 2920, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 3226, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 3377, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 3432, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 3503, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 3819, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 4018, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 4288, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 4589, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 4847, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 4890, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 5108, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 5215, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 5647, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 5913, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 5991, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 6483, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 7009, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 7127, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 7440, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 7651, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 7722, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 8038, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 8470, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 8744, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 8855, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 9113, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 9195, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 9697, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 9856, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}]], [512, [{"start": 0, "length": 26, "messageText": "'React' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 262, "length": 4, "messageText": "Cannot use namespace 'jest' as a value.", "category": 1, "code": 2708}, {"start": 438, "length": 4, "messageText": "Cannot use namespace 'jest' as a value.", "category": 1, "code": 2708}, {"start": 514, "length": 4, "messageText": "Cannot use namespace 'jest' as a value.", "category": 1, "code": 2708}, {"start": 523, "length": 3, "messageText": "'key' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 573, "length": 4, "messageText": "Cannot use namespace 'jest' as a value.", "category": 1, "code": 2708}, {"start": 686, "length": 8, "messageText": "Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 733, "length": 10, "messageText": "Cannot find name 'before<PERSON><PERSON>'.", "category": 1, "code": 2304}, {"start": 756, "length": 4, "messageText": "Cannot use namespace 'jest' as a value.", "category": 1, "code": 2708}, {"start": 787, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 972, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 1061, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 1171, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 1347, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 1426, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 1494, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 1555, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 1635, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 1807, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 1907, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 2242, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 2328, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 2596, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 2835, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 3105, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 3487, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 3747, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 3932, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 4000, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 4045, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 4290, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 4357, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 4428, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 4498, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 4597, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 4810, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 4878, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 4981, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 5541, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 5617, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 5829, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 5918, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 6166, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 6233, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 6305, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 6372, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 6468, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 6677, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 6882, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 6953, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 7688, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 7777, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 8191, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 8277, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 8431, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 8525, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 8879, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 9000, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 9237, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 9299, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 9390, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 9702, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 9789, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 10288, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}]], [513, [{"start": 25, "length": 9, "messageText": "'fireEvent' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 36, "length": 7, "messageText": "'waitFor' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 356, "length": 59, "messageText": "'Expense' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 531, "length": 3, "messageText": "'key' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 598, "length": 5, "messageText": "'React' refers to a UMD global, but the current file is a module. Consider adding an import instead.", "category": 1, "code": 2686}]], [514, [{"start": 0, "length": 26, "messageText": "'React' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 262, "length": 4, "messageText": "Cannot use namespace 'jest' as a value.", "category": 1, "code": 2708}, {"start": 438, "length": 4, "messageText": "Cannot use namespace 'jest' as a value.", "category": 1, "code": 2708}, {"start": 514, "length": 4, "messageText": "Cannot use namespace 'jest' as a value.", "category": 1, "code": 2708}, {"start": 523, "length": 3, "messageText": "'key' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 573, "length": 4, "messageText": "Cannot use namespace 'jest' as a value.", "category": 1, "code": 2708}, {"start": 1307, "length": 8, "messageText": "Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 1354, "length": 10, "messageText": "Cannot find name 'before<PERSON><PERSON>'.", "category": 1, "code": 2304}, {"start": 1377, "length": 4, "messageText": "Cannot use namespace 'jest' as a value.", "category": 1, "code": 2708}, {"start": 1408, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 1703, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 1791, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 1889, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 2173, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 2380, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 2452, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 2732, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 2970, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 3039, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 3329, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 3411, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 3514, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 3596, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 4036, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 4176, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 4288, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 4728, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 4873, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 4943, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 5014, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 5084, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 5156, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 5597, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 5798, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 5854, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 6297, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 6662, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 6792, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 7242, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 7431, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 7576, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 7997, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 8134, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 8192, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 8255, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 8323, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 8393, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 8459, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 8531, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 8951, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 9121, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 9275, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 9362, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 9809, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 10064, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 10136, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 10400, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 10493, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 11165, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 11277, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 11701, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 11814, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 11913, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 12051, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}]], [515, [{"start": 0, "length": 26, "messageText": "'React' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 256, "length": 4, "messageText": "Cannot use namespace 'jest' as a value.", "category": 1, "code": 2708}, {"start": 1069, "length": 8, "messageText": "Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 1113, "length": 10, "messageText": "Cannot find name 'before<PERSON><PERSON>'.", "category": 1, "code": 2304}, {"start": 1136, "length": 4, "messageText": "Cannot use namespace 'jest' as a value.", "category": 1, "code": 2708}, {"start": 1167, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 1416, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 1496, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 1596, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 1838, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 1919, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 2202, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 2307, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 2371, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 2435, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 2506, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 2771, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 3249, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 3318, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 3587, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 3750, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 3906, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 4055, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 4207, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 4352, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 4431, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 4702, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 4769, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 4879, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 5154, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 5314, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 5369, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 5429, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 5879, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 6121, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 6205, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 6668, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 6912, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 6996, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 7424, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 7609, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 7706, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 8101, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 8269, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 8362, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 8638, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 8853, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 8924, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 9201, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 9601, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 9644, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 9865, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 9950, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 10429, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 10583, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 10652, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 10922, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 11093, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}]], [516, [{"start": 0, "length": 26, "messageText": "'React' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 266, "length": 4, "messageText": "Cannot use namespace 'jest' as a value.", "category": 1, "code": 2708}, {"start": 618, "length": 8, "messageText": "Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 667, "length": 10, "messageText": "Cannot find name 'before<PERSON><PERSON>'.", "category": 1, "code": 2304}, {"start": 690, "length": 4, "messageText": "Cannot use namespace 'jest' as a value.", "category": 1, "code": 2708}, {"start": 721, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 911, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 997, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 1093, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 1271, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 1347, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 1426, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 1500, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 1578, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 1766, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 1856, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 2077, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 2186, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 2257, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 2327, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 2406, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 2609, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 2743, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 2814, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 2885, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 2955, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 3027, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 3250, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 3415, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 3489, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 3563, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 3671, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 3742, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 3962, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 4139, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 4390, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 4599, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 4753, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 4901, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 5041, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 5121, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 5328, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 5486, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 5556, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 5620, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 5755, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 5844, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 6057, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 6496, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 6545, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 6747, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 6880, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 6941, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 7007, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 7075, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 7274, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 7441, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 7532, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 7743, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 7948, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 8019, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 8325, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 8480, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 8538, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 8630, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 8838, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 8985, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 9087, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 9224, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 9326, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 9426, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 9582, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 9673, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 9984, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 10147, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 10228, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"start": 10434, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 10620, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"start": 10692, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}]], [517, [{"start": 25, "length": 9, "messageText": "'fireEvent' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 36, "length": 7, "messageText": "'waitFor' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 366, "length": 62, "messageText": "'SavingsGoal' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 544, "length": 3, "messageText": "'key' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 611, "length": 5, "messageText": "'React' refers to a UMD global, but the current file is a module. Consider adding an import instead.", "category": 1, "code": 2686}]], [523, [{"start": 571, "length": 4, "code": 2820, "category": 1, "messageText": "Type '\"emergency-fund\"' is not assignable to type 'SavingsGoalType'. Did you mean '\"emergency_fund\"'?", "relatedInformation": [{"file": "./src/types/savings.ts", "start": 556, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'SavingsGoal'", "category": 3, "code": 6500}]}, {"start": 977, "length": 4, "code": 2739, "category": 1, "messageText": "Type '{ id: string; name: string; type: \"vacation\"; targetAmount: number; currentAmount: number; priority: \"medium\"; status: \"active\"; targetDate: string; createdAt: string; updatedAt: string; }' is missing the following properties from type 'SavingsGoal': monthlyContribution, autoContribute, swissSpecific", "canonicalHead": {"code": 2322, "messageText": "Type '{ id: string; name: string; type: \"vacation\"; targetAmount: number; currentAmount: number; priority: \"medium\"; status: \"active\"; targetDate: string; createdAt: string; updatedAt: string; }' is not assignable to type 'SavingsGoal'."}}, {"start": 1400, "length": 4, "code": 2739, "category": 1, "messageText": "Type '{ id: string; name: string; type: \"other\"; targetAmount: number; currentAmount: number; priority: \"low\"; status: \"completed\"; targetDate: string; createdAt: string; updatedAt: string; }' is missing the following properties from type 'SavingsGoal': monthlyContribution, autoContribute, swissSpecific", "canonicalHead": {"code": 2322, "messageText": "Type '{ id: string; name: string; type: \"other\"; targetAmount: number; currentAmount: number; priority: \"low\"; status: \"completed\"; targetDate: string; createdAt: string; updatedAt: string; }' is not assignable to type 'SavingsGoal'."}}, {"start": 1837, "length": 4, "code": 2739, "category": 1, "messageText": "Type '{ id: string; name: string; type: \"other\"; targetAmount: number; currentAmount: number; priority: \"low\"; status: \"active\"; targetDate: string; createdAt: string; updatedAt: string; }' is missing the following properties from type 'SavingsGoal': monthlyContribution, autoContribute, swissSpecific", "canonicalHead": {"code": 2322, "messageText": "Type '{ id: string; name: string; type: \"other\"; targetAmount: number; currentAmount: number; priority: \"low\"; status: \"active\"; targetDate: string; createdAt: string; updatedAt: string; }' is not assignable to type 'SavingsGoal'."}}, {"start": 10885, "length": 4, "code": 2820, "category": 1, "messageText": "Type '\"emergency-fund\"' is not assignable to type 'SavingsGoalType'. Did you mean '\"emergency_fund\"'?", "relatedInformation": [{"file": "./src/types/savings.ts", "start": 556, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'SavingsGoal'", "category": 3, "code": 6500}]}, {"start": 11538, "length": 4, "code": 2820, "category": 1, "messageText": "Type '\"emergency-fund\"' is not assignable to type 'SavingsGoalType'. Did you mean '\"emergency_fund\"'?", "relatedInformation": [{"file": "./src/types/savings.ts", "start": 556, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'SavingsGoal'", "category": 3, "code": 6500}]}, {"start": 12285, "length": 4, "code": 2739, "category": 1, "messageText": "Type '{ id: string; name: string; type: SavingsGoalType; targetAmount: number; currentAmount: number; priority: \"medium\"; status: \"active\"; targetDate: string; createdAt: string; updatedAt: string; }' is missing the following properties from type 'SavingsGoal': monthlyContribution, autoContribute, swissSpecific", "canonicalHead": {"code": 2322, "messageText": "Type '{ id: string; name: string; type: SavingsGoalType; targetAmount: number; currentAmount: number; priority: \"medium\"; status: \"active\"; targetDate: string; createdAt: string; updatedAt: string; }' is not assignable to type 'SavingsGoal'."}}]]], "affectedFilesPendingEmit": [92, 98, 100, 101, 102, 103, 104, 105, 106, 108, 109, 110, 86, 91, 116, 117, 88, 135, 136, 118, 119, 120, 121, 122, 123, 125, 126, 90, 127, 130, 131, 132, 134, 82, 83, 81, 80, 78, 75, 76, 79, 77, 137, 138, 142, 74, 115, 147, 148, 124, 85, 149, 146, 96, 162, 97, 141, 140, 139, 163, 84, 99, 87, 89, 128, 107, 144, 145, 164, 165, 113, 114, 143, 133, 112, 129, 179, 180, 181, 182, 184, 185, 187, 173, 174, 175, 176, 191, 192, 193, 189, 194, 190, 195, 196, 177, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 188, 178, 222, 223, 503, 504, 505, 506, 507, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 521, 522, 523, 524], "version": "5.8.3"}