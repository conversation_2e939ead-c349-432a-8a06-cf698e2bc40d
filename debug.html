<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fire or Retire - Debug Page</title>
    <style>
        body { 
            font-family: system-ui, -apple-system, sans-serif; 
            margin: 0; 
            padding: 20px;
            background: #f5f5f5;
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white; 
            padding: 20px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status { 
            padding: 10px; 
            margin: 10px 0; 
            border-radius: 5px; 
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        pre { 
            background: #f8f9fa; 
            padding: 15px; 
            border-radius: 5px; 
            overflow-x: auto; 
            font-size: 12px;
            border: 1px solid #e9ecef;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #0056b3; }
        .grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        @media (max-width: 768px) { .grid { grid-template-columns: 1fr; } }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 Fire or Retire Calculator - Debug Console</h1>
        
        <div class="status info">
            <strong>Debug Mode Active</strong> - Checking application health...
        </div>

        <div class="grid">
            <div>
                <h3>🌐 Network Tests</h3>
                <div id="network-status">Running network tests...</div>
                <button class="btn" onclick="runNetworkTests()">🔄 Retest Network</button>
            </div>
            
            <div>
                <h3>📱 Application Tests</h3>
                <div id="app-status">Checking application...</div>
                <button class="btn" onclick="testApplication()">🧪 Test App</button>
            </div>
        </div>

        <div style="margin-top: 20px;">
            <h3>🚀 Quick Actions</h3>
            <button class="btn" onclick="openApp()">📱 Open Application</button>
            <button class="btn" onclick="checkConsole()">🔍 Check Console</button>
            <button class="btn" onclick="downloadLogs()">📋 Download Logs</button>
        </div>

        <div style="margin-top: 20px;">
            <h3>📊 Diagnostic Information</h3>
            <pre id="diagnostic-info">Loading diagnostic information...</pre>
        </div>

        <div style="margin-top: 20px;">
            <h3>🔧 Console Output</h3>
            <pre id="console-output">Console messages will appear here...</pre>
        </div>
    </div>

    <script>
        let consoleMessages = [];
        
        // Capture console messages
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        console.log = function(...args) {
            consoleMessages.push({type: 'log', message: args.join(' '), time: new Date().toISOString()});
            updateConsoleOutput();
            originalLog.apply(console, args);
        };
        
        console.error = function(...args) {
            consoleMessages.push({type: 'error', message: args.join(' '), time: new Date().toISOString()});
            updateConsoleOutput();
            originalError.apply(console, args);
        };
        
        console.warn = function(...args) {
            consoleMessages.push({type: 'warn', message: args.join(' '), time: new Date().toISOString()});
            updateConsoleOutput();
            originalWarn.apply(console, args);
        };

        function updateConsoleOutput() {
            const output = document.getElementById('console-output');
            output.innerHTML = consoleMessages.map(msg => 
                `[${msg.time}] ${msg.type.toUpperCase()}: ${msg.message}`
            ).join('\n');
            output.scrollTop = output.scrollHeight;
        }

        async function runNetworkTests() {
            const statusEl = document.getElementById('network-status');
            statusEl.innerHTML = '<div class="status info">🔄 Running network tests...</div>';
            
            const tests = [
                { name: 'Main Page', url: 'http://localhost:4173/' },
                { name: 'JavaScript Bundle', url: 'http://localhost:4173/assets/index-CxSe0Lcq.js' },
                { name: 'CSS Styles', url: 'http://localhost:4173/assets/index-CjExssID.css' }
            ];
            
            let results = '';
            let allPassed = true;
            
            for (const test of tests) {
                try {
                    const response = await fetch(test.url, { method: 'HEAD' });
                    const status = response.ok ? '✅' : '❌';
                    const statusClass = response.ok ? 'success' : 'error';
                    results += `<div class="status ${statusClass}">${status} ${test.name}: ${response.status}</div>`;
                    if (!response.ok) allPassed = false;
                } catch (error) {
                    results += `<div class="status error">❌ ${test.name}: ${error.message}</div>`;
                    allPassed = false;
                }
            }
            
            statusEl.innerHTML = results;
            return allPassed;
        }

        async function testApplication() {
            const statusEl = document.getElementById('app-status');
            statusEl.innerHTML = '<div class="status info">🧪 Testing application...</div>';
            
            try {
                // Test if we can load the main page
                const response = await fetch('http://localhost:4173/');
                const html = await response.text();
                
                const hasRoot = html.includes('id="root"');
                const hasScript = html.includes('index-CxSe0Lcq.js');
                const hasCSS = html.includes('index-CjExssID.css');
                
                let results = '';
                results += `<div class="status ${hasRoot ? 'success' : 'error'}">${hasRoot ? '✅' : '❌'} Root element found</div>`;
                results += `<div class="status ${hasScript ? 'success' : 'error'}">${hasScript ? '✅' : '❌'} JavaScript bundle linked</div>`;
                results += `<div class="status ${hasCSS ? 'success' : 'error'}">${hasCSS ? '✅' : '❌'} CSS styles linked</div>`;
                
                // Try to detect React
                if (hasScript) {
                    try {
                        const jsResponse = await fetch('http://localhost:4173/assets/index-CxSe0Lcq.js');
                        const jsContent = await jsResponse.text();
                        const hasReact = jsContent.includes('React') || jsContent.includes('react');
                        results += `<div class="status ${hasReact ? 'success' : 'warning'}">${hasReact ? '✅' : '⚠️'} React detected in bundle</div>`;
                    } catch (e) {
                        results += `<div class="status error">❌ Could not analyze JavaScript bundle</div>`;
                    }
                }
                
                statusEl.innerHTML = results;
                
            } catch (error) {
                statusEl.innerHTML = `<div class="status error">❌ Application test failed: ${error.message}</div>`;
            }
        }

        function openApp() {
            window.open('http://localhost:4173', '_blank');
        }

        function checkConsole() {
            const iframe = document.createElement('iframe');
            iframe.src = 'http://localhost:4173';
            iframe.style.display = 'none';
            document.body.appendChild(iframe);
            
            setTimeout(() => {
                console.log('Iframe loaded, checking for errors...');
                document.body.removeChild(iframe);
            }, 3000);
        }

        function downloadLogs() {
            const logs = {
                timestamp: new Date().toISOString(),
                consoleMessages: consoleMessages,
                userAgent: navigator.userAgent,
                url: window.location.href
            };
            
            const blob = new Blob([JSON.stringify(logs, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'fire-or-retire-debug-logs.json';
            a.click();
            URL.revokeObjectURL(url);
        }

        function updateDiagnosticInfo() {
            const info = {
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent,
                location: window.location.href,
                screen: `${screen.width}x${screen.height}`,
                viewport: `${window.innerWidth}x${window.innerHeight}`,
                cookiesEnabled: navigator.cookieEnabled,
                language: navigator.language,
                platform: navigator.platform,
                onLine: navigator.onLine
            };
            
            document.getElementById('diagnostic-info').textContent = JSON.stringify(info, null, 2);
        }

        // Initialize
        window.onload = function() {
            updateDiagnosticInfo();
            runNetworkTests();
            testApplication();
            
            console.log('🔥 Fire or Retire Debug Console initialized');
            console.log('🧪 Running diagnostic tests...');
        };

        // Capture any unhandled errors
        window.onerror = function(message, source, lineno, colno, error) {
            console.error(`Unhandled error: ${message} at ${source}:${lineno}:${colno}`, error);
        };

        window.addEventListener('unhandledrejection', function(event) {
            console.error('Unhandled promise rejection:', event.reason);
        });
    </script>
</body>
</html>
