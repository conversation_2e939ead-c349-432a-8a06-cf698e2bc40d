<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Fire or Retire App</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { 
            font-family: system-ui, -apple-system, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 600px;
            width: 90%;
        }
        .status { 
            padding: 1rem; 
            border-radius: 10px; 
            margin: 1rem 0; 
            font-weight: 500;
        }
        .success { background: #d1fae5; color: #065f46; border: 1px solid #a7f3d0; }
        .error { background: #fee2e2; color: #991b1b; border: 1px solid #fca5a5; }
        .loading { background: #dbeafe; color: #1e40af; border: 1px solid #93c5fd; }
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            transition: transform 0.2s;
        }
        .btn:hover { transform: translateY(-2px); }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; color: #374151; margin-bottom: 2rem; font-size: 2rem; font-weight: bold;">
            🔥 Fire or Retire Calculator Test
        </h1>
        
        <div id="status" class="status loading">
            🔄 Testing application...
        </div>
        
        <div style="text-align: center; margin: 2rem 0;">
            <button class="btn" onclick="testApp()">🧪 Test Application</button>
            <button class="btn" onclick="openApp()" style="margin-left: 1rem;">🚀 Open App</button>
        </div>
        
        <div id="details" style="margin-top: 2rem; padding: 1rem; background: #f9fafb; border-radius: 10px; font-family: monospace; font-size: 0.875rem;">
            <strong>Test Results:</strong><br>
            <div id="test-results">Running tests...</div>
        </div>
    </div>

    <script>
        async function testApp() {
            const statusEl = document.getElementById('status');
            const resultsEl = document.getElementById('test-results');
            
            statusEl.className = 'status loading';
            statusEl.innerHTML = '🔄 Testing application components...';
            resultsEl.innerHTML = 'Running tests...<br>';
            
            const tests = [
                { name: 'HTML Page', url: 'http://localhost:4173/' },
                { name: 'JavaScript Bundle', url: 'http://localhost:4173/assets/index-CxSe0Lcq.js' },
                { name: 'CSS Styles', url: 'http://localhost:4173/assets/index-CjExssID.css' },
                { name: 'Tailwind CDN', url: 'https://cdn.tailwindcss.com' }
            ];
            
            let allPassed = true;
            
            for (const test of tests) {
                try {
                    const response = await fetch(test.url, { method: 'HEAD' });
                    const status = response.ok ? '✅ PASS' : '❌ FAIL';
                    resultsEl.innerHTML += `${status} ${test.name}: ${response.status}<br>`;
                    if (!response.ok) allPassed = false;
                } catch (error) {
                    resultsEl.innerHTML += `❌ FAIL ${test.name}: ${error.message}<br>`;
                    allPassed = false;
                }
            }
            
            // Test if React app is mounting
            try {
                const response = await fetch('http://localhost:4173/');
                const html = await response.text();
                const hasRoot = html.includes('id="root"');
                const hasReact = html.includes('react');
                
                resultsEl.innerHTML += `${hasRoot ? '✅' : '❌'} Root element found<br>`;
                resultsEl.innerHTML += `${hasReact ? '✅' : '❌'} React references found<br>`;
                
            } catch (error) {
                resultsEl.innerHTML += `❌ FAIL HTML Analysis: ${error.message}<br>`;
                allPassed = false;
            }
            
            if (allPassed) {
                statusEl.className = 'status success';
                statusEl.innerHTML = '✅ All tests passed! Application should be working.';
                resultsEl.innerHTML += '<br><strong>🎉 Application appears to be running correctly!</strong><br>';
                resultsEl.innerHTML += 'If you see a blank page, check browser console for JavaScript errors.';
            } else {
                statusEl.className = 'status error';
                statusEl.innerHTML = '❌ Some tests failed. Check the details below.';
            }
        }
        
        function openApp() {
            window.open('http://localhost:4173', '_blank');
        }
        
        // Auto-run test on page load
        window.onload = () => {
            setTimeout(testApp, 1000);
        };
    </script>
</body>
</html>
