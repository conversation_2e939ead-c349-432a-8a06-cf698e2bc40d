#!/bin/bash

# Fire or Retire Calculator - Main Management Script
# This script provides a unified interface for development and production environments

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}"
    echo "🔥 ███████╗██╗██████╗ ███████╗    ██████╗ ██████╗     ██████╗ ███████╗████████╗██╗██████╗ ███████╗"
    echo "   ██╔════╝██║██╔══██╗██╔════╝   ██╔═══██╗██╔══██╗    ██╔══██╗██╔════╝╚══██╔══╝██║██╔══██╗██╔════╝"
    echo "   █████╗  ██║██████╔╝█████╗     ██║   ██║██████╔╝    ██████╔╝█████╗     ██║   ██║██████╔╝█████╗  "
    echo "   ██╔══╝  ██║██╔══██╗██╔══╝     ██║   ██║██╔══██╗    ██╔══██╗██╔══╝     ██║   ██║██╔══██╗██╔══╝  "
    echo "   ██║     ██║██║  ██║███████╗   ╚██████╔╝██║  ██║    ██║  ██║███████╗   ██║   ██║██║  ██║███████╗"
    echo "   ╚═╝     ╚═╝╚═╝  ╚═╝╚══════╝    ╚═════╝ ╚═╝  ╚═╝    ╚═╝  ╚═╝╚══════╝   ╚═╝   ╚═╝╚═╝  ╚═╝╚══════╝"
    echo -e "${NC}"
    echo -e "${CYAN}Swiss Budget Pro - Financial Planning Application${NC}"
    echo -e "${CYAN}=================================================${NC}"
}

# Function to show help
show_help() {
    print_header
    echo -e "${CYAN}Usage:${NC} $0 <mode> [command]"
    echo ""
    echo -e "${CYAN}Modes:${NC}"
    echo -e "  ${YELLOW}dev${NC}      Development mode with volume mapping and hot reload"
    echo -e "  ${YELLOW}prod${NC}     Production mode with optimized build"
    echo ""
    echo -e "${CYAN}Commands (for both modes):${NC}"
    echo -e "  ${YELLOW}start${NC}    Start the environment (default)"
    echo -e "  ${YELLOW}stop${NC}     Stop the environment"
    echo -e "  ${YELLOW}restart${NC}  Restart the environment"
    echo -e "  ${YELLOW}logs${NC}     Show logs"
    echo -e "  ${YELLOW}shell${NC}    Open shell in container"
    echo -e "  ${YELLOW}status${NC}   Show environment status"
    echo ""
    echo -e "${CYAN}Development-specific commands:${NC}"
    echo -e "  ${YELLOW}build${NC}    Build production version in dev container"
    echo ""
    echo -e "${CYAN}Production-specific commands:${NC}"
    echo -e "  ${YELLOW}deploy${NC}   Deploy latest changes (rebuild and restart)"
    echo ""
    echo -e "${CYAN}Examples:${NC}"
    echo -e "  ${GREEN}$0 dev${NC}           # Start development environment"
    echo -e "  ${GREEN}$0 dev logs${NC}      # Show development logs"
    echo -e "  ${GREEN}$0 prod deploy${NC}   # Deploy to production"
    echo -e "  ${GREEN}$0 prod status${NC}   # Check production status"
    echo ""
    echo -e "${CYAN}Development URLs:${NC}"
    echo -e "  ${YELLOW}Dev Server:${NC}     http://localhost:5173"
    echo -e "  ${YELLOW}Traefik:${NC}        http://fire-dev.docker.localhost"
    echo ""
    echo -e "${CYAN}Production URLs:${NC}"
    echo -e "  ${YELLOW}Prod Server:${NC}    http://localhost:4173"
    echo -e "  ${YELLOW}Traefik:${NC}        http://fire.docker.localhost"
}

# Function to check if scripts exist
check_scripts() {
    if [[ ! -f "scripts/dev.sh" ]]; then
        print_error "Development script not found: scripts/dev.sh"
        exit 1
    fi
    
    if [[ ! -f "scripts/prod.sh" ]]; then
        print_error "Production script not found: scripts/prod.sh"
        exit 1
    fi
}

# Main script logic
if [[ $# -eq 0 ]]; then
    show_help
    exit 0
fi

MODE="$1"
COMMAND="${2:-start}"

case "$MODE" in
    "dev"|"development")
        check_scripts
        print_status "Running in development mode..."
        exec ./scripts/dev.sh "$COMMAND"
        ;;
    "prod"|"production")
        check_scripts
        print_status "Running in production mode..."
        exec ./scripts/prod.sh "$COMMAND"
        ;;
    "help"|"-h"|"--help")
        show_help
        ;;
    *)
        print_error "Unknown mode: $MODE"
        echo ""
        show_help
        exit 1
        ;;
esac
