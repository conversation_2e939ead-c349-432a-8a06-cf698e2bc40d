{"cantons": [{"code": "ZH", "name": "Zurich", "taxRate": 0.12, "wealthTaxThreshold": 100000, "averageHealthcarePremium": 450}, {"code": "GE", "name": "Geneva", "taxRate": 0.15, "wealthTaxThreshold": 75000, "averageHealthcarePremium": 520}, {"code": "VD", "name": "<PERSON><PERSON>", "taxRate": 0.14, "wealthTaxThreshold": 80000, "averageHealthcarePremium": 480}, {"code": "BE", "name": "Bern", "taxRate": 0.13, "wealthTaxThreshold": 90000, "averageHealthcarePremium": 420}, {"code": "ZG", "name": "<PERSON>ug", "taxRate": 0.08, "wealthTaxThreshold": 150000, "averageHealthcarePremium": 380}, {"code": "BS", "name": "Basel-Stadt", "taxRate": 0.16, "wealthTaxThreshold": 70000, "averageHealthcarePremium": 500}, {"code": "BL", "name": "Basel-Landschaft", "taxRate": 0.11, "wealthTaxThreshold": 95000, "averageHealthcarePremium": 440}, {"code": "AG", "name": "Aargau", "taxRate": 0.1, "wealthTaxThreshold": 100000, "averageHealthcarePremium": 400}], "userProfiles": [{"id": "young-professional", "name": "Young Professional", "age": 28, "income": 85000, "expenses": 60000, "savings": 25000, "canton": "ZH", "goals": "FIRE by 50", "riskTolerance": "high", "investmentExperience": "intermediate", "description": "Tech professional starting FIRE journey"}, {"id": "mid-career-family", "name": "Mid-Career Family", "age": 42, "income": 180000, "expenses": 120000, "savings": 350000, "canton": "GE", "goals": "Traditional retirement", "married": true, "children": 2, "riskTolerance": "medium", "investmentExperience": "advanced", "description": "Dual-income family with children"}, {"id": "high-earner", "name": "High Earner", "age": 35, "income": 250000, "expenses": 100000, "savings": 800000, "canton": "ZG", "goals": "Early retirement optimization", "riskTolerance": "high", "investmentExperience": "expert", "description": "High-income professional in low-tax canton"}, {"id": "conservative-saver", "name": "Conservative Saver", "age": 50, "income": 120000, "expenses": 80000, "savings": 600000, "canton": "BE", "goals": "Secure retirement", "riskTolerance": "low", "investmentExperience": "beginner", "description": "Risk-averse saver nearing retirement"}, {"id": "startup-founder", "name": "Startup Founder", "age": 32, "income": 60000, "expenses": 45000, "savings": 15000, "canton": "VD", "goals": "Build wealth through equity", "riskTolerance": "very-high", "investmentExperience": "intermediate", "description": "Entrepreneur with variable income"}], "healthcareProfiles": [{"id": "low-risk", "name": "Healthy Young Adult", "age": 30, "healthStatus": "excellent", "annualCosts": 1200, "recommendedDeductible": 2500, "chronicConditions": false, "regularMedications": false, "description": "Rarely visits doctor, good for high deductible"}, {"id": "medium-risk", "name": "Average Health Profile", "age": 45, "healthStatus": "good", "annualCosts": 2800, "recommendedDeductible": 1500, "chronicConditions": false, "regularMedications": true, "description": "Occasional medical visits, moderate deductible optimal"}, {"id": "high-risk", "name": "Chronic Condition", "age": 60, "healthStatus": "fair", "annualCosts": 4500, "recommendedDeductible": 300, "chronicConditions": true, "regularMedications": true, "description": "Regular medical care needed, low deductible beneficial"}, {"id": "family-profile", "name": "Family with Children", "age": 38, "healthStatus": "good", "annualCosts": 3200, "recommendedDeductible": 1000, "familySize": 4, "childrenAges": [8, 12], "description": "Family coverage with moderate healthcare usage"}], "investmentScenarios": [{"id": "conservative", "name": "Conservative Portfolio", "stocks": 30, "bonds": 60, "cash": 10, "expectedReturn": 0.04, "volatility": 0.08, "description": "Low-risk portfolio for risk-averse investors"}, {"id": "balanced", "name": "Balanced Portfolio", "stocks": 60, "bonds": 35, "cash": 5, "expectedReturn": 0.06, "volatility": 0.12, "description": "Moderate risk-return balance"}, {"id": "aggressive", "name": "Aggressive Growth", "stocks": 90, "bonds": 10, "cash": 0, "expectedReturn": 0.08, "volatility": 0.18, "description": "High-growth portfolio for long-term investors"}, {"id": "swiss-focused", "name": "Swiss Market Focus", "swissStocks": 40, "internationalStocks": 30, "swissBonds": 20, "cash": 10, "expectedReturn": 0.055, "volatility": 0.14, "description": "Home-biased portfolio with Swiss market focus"}], "testScenarios": [{"id": "fire-young-professional", "name": "Young Professional FIRE Journey", "profile": "young-professional", "targetAge": 50, "expectedYears": 22, "expectedFIRENumber": 1500000, "savingsRate": 0.29, "description": "Typical FIRE scenario for young tech professional"}, {"id": "family-traditional-retirement", "name": "Family Traditional Retirement", "profile": "mid-career-family", "targetAge": 65, "expectedYears": 23, "expectedFIRENumber": 3000000, "savingsRate": 0.33, "description": "Traditional retirement planning for family"}, {"id": "high-earner-optimization", "name": "High Earner Tax Optimization", "profile": "high-earner", "targetAge": 45, "expectedYears": 10, "expectedFIRENumber": 2500000, "savingsRate": 0.6, "description": "Aggressive FIRE with tax optimization in Zug"}, {"id": "edge-case-minimal-income", "name": "Minimal Income Scenario", "income": 36000, "expenses": 33600, "savings": 5000, "canton": "AG", "expectedChallenges": ["low-savings-rate", "long-timeline"], "description": "Testing edge case with minimal savings capacity"}, {"id": "edge-case-negative-savings", "name": "Negative Savings Rate", "income": 48000, "expenses": 54000, "savings": 10000, "canton": "GE", "expectedResult": "impossible", "description": "Testing scenario where expenses exceed income"}], "calculationBenchmarks": [{"scenario": "simple-fire", "income": 100000, "expenses": 60000, "savings": 100000, "expectedFireYears": 18.9, "expectedFireNumber": 1500000, "tolerance": 0.1, "description": "Simple FIRE calculation benchmark"}, {"scenario": "complex-tax-optimization", "income": 200000, "expenses": 120000, "savings": 500000, "canton": "ZH", "pillar3a": 7056, "expectedTaxSavings": 2500, "tolerance": 100, "description": "Complex tax optimization benchmark"}, {"scenario": "healthcare-optimization", "age": 35, "healthStatus": "good", "annualCosts": 2000, "expectedOptimalDeductible": 1500, "expectedSavings": 800, "tolerance": 50, "description": "Healthcare deductible optimization benchmark"}], "localizationData": {"de-CH": {"currency": "CHF", "numberFormat": "1'234.56", "dateFormat": "dd.mm.yyyy", "decimalSeparator": ".", "thousandsSeparator": "'", "sampleText": "Monatliches Einkommen"}, "fr-CH": {"currency": "CHF", "numberFormat": "1'234.56", "dateFormat": "dd.mm.yyyy", "decimalSeparator": ".", "thousandsSeparator": "'", "sampleText": "<PERSON><PERSON><PERSON> mensuel"}, "it-CH": {"currency": "CHF", "numberFormat": "1'234.56", "dateFormat": "dd.mm.yyyy", "decimalSeparator": ".", "thousandsSeparator": "'", "sampleText": "<PERSON><PERSON><PERSON> mensile"}, "en-CH": {"currency": "CHF", "numberFormat": "1,234.56", "dateFormat": "dd/mm/yyyy", "decimalSeparator": ".", "thousandsSeparator": ",", "sampleText": "Monthly Income"}}, "performanceBenchmarks": {"pageLoad": {"target": 3000, "good": 2000, "excellent": 1000, "unit": "ms"}, "fireCalculation": {"target": 500, "good": 300, "excellent": 100, "unit": "ms"}, "taxCalculation": {"target": 300, "good": 200, "excellent": 50, "unit": "ms"}, "mobileLoad": {"target": 5000, "good": 3000, "excellent": 2000, "unit": "ms"}, "bundleSize": {"target": 5242880, "good": 3145728, "excellent": 2097152, "unit": "bytes"}}, "metadata": {"version": "1.0.0", "lastUpdated": "2024-01-15", "description": "Comprehensive test data for Swiss Budget Pro E2E testing", "author": "Swiss Budget Pro Test Team", "dataSource": "Swiss financial regulations and market data"}}