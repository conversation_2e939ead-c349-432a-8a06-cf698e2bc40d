"""
Browser Manager for Swiss Budget Pro BDD Tests
Handles browser initialization, configuration, and management
"""

import logging
from playwright.sync_api import sync_playwright, <PERSON><PERSON><PERSON>, <PERSON>rowserContex<PERSON>, Page
from typing import Dict, Optional

logger = logging.getLogger(__name__)

class BrowserManager:
    """
    Manages browser instances for BDD testing with Swiss-specific configurations
    """
    
    def __init__(self, config: Dict):
        self.config = config
        self.playwright = None
        self.browser = None
        self.contexts = []
        self.pages = []
        
        # Swiss-specific browser settings
        self.swiss_settings = {
            'locale': config.get('swiss_locale', 'de-CH'),
            'timezone_id': 'Europe/Zurich',
            'extra_http_headers': {
                'Accept-Language': 'de-CH,de;q=0.9,fr-CH,fr;q=0.8,it-CH,it;q=0.7,en;q=0.6'
            }
        }
        
        self._initialize_browser()
    
    def _initialize_browser(self):
        """Initialize Playwright and browser"""
        try:
            self.playwright = sync_playwright().start()
            
            browser_type = getattr(self.playwright, self.config['browser'])
            
            # Browser launch options
            launch_options = {
                'headless': self.config['headless'],
                'slow_mo': 100 if not self.config['headless'] else 0,  # Slow down for visual debugging
            }
            
            # Add Swiss-specific launch arguments
            if self.config['browser'] == 'chromium':
                launch_options['args'] = [
                    '--lang=de-CH',
                    '--disable-web-security',  # For local testing
                    '--disable-features=VizDisplayCompositor',  # Better performance
                ]
            
            self.browser = browser_type.launch(**launch_options)
            logger.info(f"✅ Browser initialized: {self.config['browser']}")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize browser: {e}")
            raise
    
    def get_browser(self) -> Browser:
        """Get the browser instance"""
        if not self.browser:
            self._initialize_browser()
        return self.browser
    
    def create_context(self, **kwargs) -> BrowserContext:
        """
        Create a new browser context with Swiss-specific settings
        """
        context_options = {
            **self.swiss_settings,
            'viewport': {'width': 1280, 'height': 720},
            'ignore_https_errors': True,
            'record_video_dir': 'reports/videos' if not self.config['headless'] else None,
            **kwargs
        }
        
        context = self.browser.new_context(**context_options)
        self.contexts.append(context)
        
        logger.info("🌐 New browser context created with Swiss settings")
        return context
    
    def create_mobile_context(self, device_name: str = 'iPhone 12') -> BrowserContext:
        """
        Create a mobile browser context for responsive testing
        """
        mobile_devices = {
            'iPhone 12': {
                'viewport': {'width': 390, 'height': 844},
                'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
                'device_scale_factor': 3,
                'is_mobile': True,
                'has_touch': True,
            },
            'Samsung Galaxy S21': {
                'viewport': {'width': 384, 'height': 854},
                'user_agent': 'Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36',
                'device_scale_factor': 2.75,
                'is_mobile': True,
                'has_touch': True,
            },
            'iPad Pro': {
                'viewport': {'width': 1024, 'height': 1366},
                'user_agent': 'Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
                'device_scale_factor': 2,
                'is_mobile': False,
                'has_touch': True,
            }
        }
        
        device_config = mobile_devices.get(device_name, mobile_devices['iPhone 12'])
        
        context_options = {
            **self.swiss_settings,
            **device_config,
            'ignore_https_errors': True,
        }
        
        context = self.browser.new_context(**context_options)
        self.contexts.append(context)
        
        logger.info(f"📱 Mobile context created for {device_name}")
        return context
    
    def create_accessibility_context(self) -> BrowserContext:
        """
        Create a browser context optimized for accessibility testing
        """
        context_options = {
            **self.swiss_settings,
            'viewport': {'width': 1280, 'height': 720},
            'reduced_motion': 'reduce',
            'forced_colors': 'none',
            'color_scheme': 'light',
            'ignore_https_errors': True,
        }
        
        context = self.browser.new_context(**context_options)
        self.contexts.append(context)
        
        logger.info("♿ Accessibility context created")
        return context
    
    def create_performance_context(self) -> BrowserContext:
        """
        Create a browser context optimized for performance testing
        """
        context_options = {
            **self.swiss_settings,
            'viewport': {'width': 1280, 'height': 720},
            'ignore_https_errors': True,
            'record_har_path': 'reports/performance.har',  # Record network activity
        }
        
        context = self.browser.new_context(**context_options)
        self.contexts.append(context)
        
        logger.info("⚡ Performance context created")
        return context
    
    def navigate_to_app(self, page: Page, path: str = '') -> None:
        """
        Navigate to Swiss Budget Pro application
        """
        url = f"{self.config['base_url']}{path}"
        
        try:
            logger.info(f"🌐 Navigating to: {url}")
            page.goto(url, wait_until='networkidle', timeout=30000)
            
            # Wait for Swiss Budget Pro to load
            page.wait_for_selector('[data-testid="app-title"], h1:has-text("Swiss Budget Pro")', timeout=10000)
            
            logger.info("✅ Swiss Budget Pro loaded successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to navigate to Swiss Budget Pro: {e}")
            raise
    
    def take_screenshot(self, page: Page, name: str, full_page: bool = True) -> str:
        """
        Take a screenshot for debugging or reporting
        """
        screenshot_path = f"reports/screenshots/{name}_{self.config['browser']}.png"
        
        try:
            page.screenshot(path=screenshot_path, full_page=full_page)
            logger.info(f"📸 Screenshot saved: {screenshot_path}")
            return screenshot_path
            
        except Exception as e:
            logger.error(f"❌ Failed to take screenshot: {e}")
            return ""
    
    def wait_for_calculation(self, page: Page, timeout: int = 10000) -> None:
        """
        Wait for Swiss Budget Pro calculations to complete
        """
        try:
            # Wait for loading indicator to disappear
            page.wait_for_selector('[data-testid="loading"], .loading, .spinner', 
                                  state='hidden', timeout=timeout)
            
            # Wait for results to be visible
            page.wait_for_selector('[data-testid="fire-years"], [data-testid="results"]', 
                                  state='visible', timeout=timeout)
            
            logger.info("✅ Calculation completed")
            
        except Exception as e:
            logger.warning(f"⚠️ Calculation wait timeout: {e}")
    
    def verify_swiss_locale(self, page: Page) -> bool:
        """
        Verify that Swiss locale settings are applied
        """
        try:
            # Check for Swiss-specific elements
            swiss_indicators = [
                'CHF',  # Swiss Franc currency
                'Kanton',  # German for Canton
                'Säule 3a',  # Pillar 3a in German
                'Franchise',  # Deductible in French/German
            ]
            
            page_content = page.content()
            
            for indicator in swiss_indicators:
                if indicator in page_content:
                    logger.info(f"🇨🇭 Swiss locale verified: Found '{indicator}'")
                    return True
            
            logger.warning("⚠️ Swiss locale indicators not found")
            return False
            
        except Exception as e:
            logger.error(f"❌ Failed to verify Swiss locale: {e}")
            return False
    
    def close_context(self, context: BrowserContext) -> None:
        """Close a specific browser context"""
        try:
            context.close()
            if context in self.contexts:
                self.contexts.remove(context)
            logger.info("🔒 Browser context closed")
            
        except Exception as e:
            logger.error(f"❌ Failed to close context: {e}")
    
    def close_all(self) -> None:
        """Close all browser contexts and the browser"""
        try:
            # Close all contexts
            for context in self.contexts:
                try:
                    context.close()
                except Exception as e:
                    logger.warning(f"Error closing context: {e}")
            
            self.contexts.clear()
            
            # Close browser
            if self.browser:
                self.browser.close()
                self.browser = None
            
            # Stop Playwright
            if self.playwright:
                self.playwright.stop()
                self.playwright = None
            
            logger.info("🔒 All browser resources closed")
            
        except Exception as e:
            logger.error(f"❌ Error during browser cleanup: {e}")
    
    def get_performance_metrics(self, page: Page) -> Dict:
        """
        Get performance metrics from the page
        """
        try:
            metrics = page.evaluate("""
                () => {
                    const navigation = performance.getEntriesByType('navigation')[0];
                    const paint = performance.getEntriesByType('paint');
                    
                    return {
                        loadTime: navigation ? navigation.loadEventEnd - navigation.loadEventStart : 0,
                        domContentLoaded: navigation ? navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart : 0,
                        firstPaint: paint.find(p => p.name === 'first-paint')?.startTime || 0,
                        firstContentfulPaint: paint.find(p => p.name === 'first-contentful-paint')?.startTime || 0,
                        memory: performance.memory ? {
                            used: performance.memory.usedJSHeapSize,
                            total: performance.memory.totalJSHeapSize,
                            limit: performance.memory.jsHeapSizeLimit
                        } : null
                    };
                }
            """)
            
            logger.info(f"📊 Performance metrics collected: {metrics}")
            return metrics
            
        except Exception as e:
            logger.error(f"❌ Failed to get performance metrics: {e}")
            return {}
