"""
Data helpers for Swiss Budget Pro BDD tests
Provides utilities for test data management and validation
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
from decimal import Decimal
import random

logger = logging.getLogger(__name__)

class DataHelpers:
    """
    Utility class for managing test data and scenarios
    """
    
    def __init__(self):
        self.fixtures_path = Path(__file__).parent.parent / 'fixtures'
        self.personas = {}
        self.test_data = {}
        self.financial_scenarios = {}
        
        self.load_all_data()
    
    def load_all_data(self):
        """Load all test data files"""
        try:
            # Load Swiss personas
            personas_file = self.fixtures_path / 'swiss_personas.json'
            if personas_file.exists():
                with open(personas_file, 'r', encoding='utf-8') as f:
                    self.personas = json.load(f)
                logger.info(f"✅ Loaded {len(self.personas)} Swiss personas")
            
            # Load test data
            test_data_file = self.fixtures_path / 'test_data.json'
            if test_data_file.exists():
                with open(test_data_file, 'r', encoding='utf-8') as f:
                    self.test_data = json.load(f)
                logger.info("✅ Loaded Swiss test data")
            
            # Load financial scenarios
            scenarios_file = self.fixtures_path / 'financial_scenarios.json'
            if scenarios_file.exists():
                with open(scenarios_file, 'r', encoding='utf-8') as f:
                    self.financial_scenarios = json.load(f)
                logger.info("✅ Loaded financial scenarios")
            
        except Exception as e:
            logger.error(f"❌ Failed to load test data: {e}")
    
    def get_persona(self, persona_id: str) -> Optional[Dict]:
        """Get a specific Swiss persona by ID"""
        persona = self.personas.get(persona_id)
        if persona:
            logger.info(f"📋 Retrieved persona: {persona_id}")
            return persona
        else:
            logger.warning(f"⚠️ Persona not found: {persona_id}")
            return None
    
    def get_persona_by_profile(self, age: int, profession: str, canton: str) -> Optional[Dict]:
        """Find a persona matching the given profile"""
        for persona_id, persona in self.personas.items():
            if (persona.get('age') == age and 
                persona.get('profession', '').lower() == profession.lower() and
                persona.get('canton', '').lower() == canton.lower()):
                logger.info(f"📋 Found matching persona: {persona_id}")
                return persona
        
        logger.warning(f"⚠️ No persona found for {age}-year-old {profession} in {canton}")
        return None
    
    def create_test_persona(self, 
                           age: int,
                           profession: str,
                           canton: str,
                           income: int,
                           expenses: int,
                           savings: int) -> Dict:
        """Create a custom test persona"""
        persona = {
            'name': f'Test {profession}',
            'age': age,
            'profession': profession,
            'canton': canton,
            'financial_profile': {
                'monthly_income': income,
                'monthly_expenses': expenses,
                'current_savings': savings,
                'target_retirement_age': 65,
                'risk_tolerance': 'medium',
                'investment_experience': 'intermediate'
            },
            'health_profile': {
                'status': 'good',
                'annual_health_costs': 2000,
                'chronic_conditions': False,
                'recommended_deductible': 1500
            },
            'description': f'Custom test persona for {profession} in {canton}'
        }
        
        logger.info(f"🆕 Created custom persona: {profession} in {canton}")
        return persona
    
    def get_canton_data(self, canton: str) -> Optional[Dict]:
        """Get tax and healthcare data for a specific canton"""
        canton_code = self.get_canton_code(canton)
        if not canton_code:
            return None
        
        canton_data = {
            'code': canton_code,
            'name': canton,
            'tax_rate': self.test_data.get('tax_rates', {}).get('cantonal', {}).get(canton_code, {}).get('rate', 0.12),
            'wealth_tax': self.test_data.get('tax_rates', {}).get('cantonal', {}).get(canton_code, {}).get('wealth_tax', 0.002),
            'healthcare_premium': self.test_data.get('healthcare_premiums', {}).get(canton_code, {}).get('average', 450)
        }
        
        logger.info(f"🇨🇭 Retrieved canton data for {canton} ({canton_code})")
        return canton_data
    
    def get_canton_code(self, canton_name: str) -> Optional[str]:
        """Convert canton name to canton code"""
        canton_mapping = {
            'zurich': 'ZH',
            'geneva': 'GE',
            'vaud': 'VD',
            'bern': 'BE',
            'zug': 'ZG',
            'basel': 'BS',
            'basel-stadt': 'BS',
            'basel-landschaft': 'BL',
            'aargau': 'AG'
        }
        
        canton_code = canton_mapping.get(canton_name.lower())
        if not canton_code:
            # Try direct lookup if already a code
            if canton_name.upper() in ['ZH', 'GE', 'VD', 'BE', 'ZG', 'BS', 'BL', 'AG']:
                canton_code = canton_name.upper()
        
        return canton_code
    
    def get_validation_rules(self, field: str) -> Dict:
        """Get validation rules for a specific field"""
        return self.test_data.get('validation_rules', {}).get(field, {})
    
    def generate_invalid_data(self, field: str) -> List[Any]:
        """Generate invalid data for testing validation"""
        rules = self.get_validation_rules(field)
        invalid_data = []
        
        if field == 'income':
            invalid_data.extend([
                -1000,  # Negative
                'abc',  # Non-numeric
                '',     # Empty
                None,   # Null
                999999999  # Too high
            ])
        elif field == 'expenses':
            invalid_data.extend([
                -500,   # Negative
                'xyz',  # Non-numeric
                '',     # Empty
                None    # Null
            ])
        elif field == 'savings':
            invalid_data.extend([
                -100,   # Negative
                'invalid',  # Non-numeric
                999999999999  # Unrealistic
            ])
        elif field == 'canton':
            invalid_data.extend([
                'Invalid',  # Invalid canton
                'XX',       # Invalid code
                '',         # Empty
                None        # Null
            ])
        
        return invalid_data
    
    def get_test_scenario(self, scenario_name: str) -> Optional[Dict]:
        """Get a predefined test scenario"""
        return self.test_data.get('test_scenarios', {}).get(scenario_name)
    
    def calculate_expected_results(self, 
                                 income: int,
                                 expenses: int,
                                 savings: int,
                                 canton: str = 'ZH') -> Dict:
        """Calculate expected results for validation"""
        try:
            # Simple FIRE calculation
            annual_income = income * 12
            annual_expenses = expenses * 12
            annual_savings = annual_income - annual_expenses
            
            if annual_savings <= 0:
                return {
                    'fire_possible': False,
                    'error': 'FIRE not possible with current income and expenses'
                }
            
            # FIRE number (25x annual expenses)
            fire_number = annual_expenses * 25
            
            # Years to FIRE (simplified)
            years_to_fire = (fire_number - savings) / annual_savings
            
            # Savings rate
            savings_rate = (annual_savings / annual_income) * 100
            
            return {
                'fire_possible': True,
                'fire_years': round(years_to_fire, 1),
                'fire_number': fire_number,
                'savings_rate': round(savings_rate, 1),
                'monthly_savings': annual_savings // 12
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to calculate expected results: {e}")
            return {'error': str(e)}
    
    def get_performance_benchmark(self, metric: str) -> Dict:
        """Get performance benchmark for a specific metric"""
        return self.test_data.get('performance_benchmarks', {}).get(metric, {})
    
    def validate_calculation_accuracy(self, 
                                    expected: Dict,
                                    actual: Dict,
                                    tolerance: float = 0.1) -> Dict:
        """Validate calculation accuracy within tolerance"""
        validation_result = {
            'passed': True,
            'errors': [],
            'warnings': []
        }
        
        # Check FIRE years
        if 'fire_years' in expected and 'fire_years' in actual:
            expected_years = expected['fire_years']
            actual_years = actual['fire_years']
            
            if abs(expected_years - actual_years) > tolerance:
                validation_result['passed'] = False
                validation_result['errors'].append(
                    f"FIRE years mismatch: expected {expected_years}, got {actual_years}"
                )
        
        # Check FIRE number
        if 'fire_number' in expected and 'fire_number' in actual:
            expected_number = expected['fire_number']
            actual_number = actual['fire_number']
            
            diff_percent = abs(expected_number - actual_number) / expected_number
            if diff_percent > tolerance:
                validation_result['passed'] = False
                validation_result['errors'].append(
                    f"FIRE number mismatch: expected {expected_number}, got {actual_number}"
                )
        
        # Check savings rate
        if 'savings_rate' in expected and 'savings_rate' in actual:
            expected_rate = expected['savings_rate']
            actual_rate = actual['savings_rate']
            
            if abs(expected_rate - actual_rate) > tolerance * 100:
                validation_result['warnings'].append(
                    f"Savings rate difference: expected {expected_rate}%, got {actual_rate}%"
                )
        
        return validation_result
    
    def get_random_persona(self, criteria: Optional[Dict] = None) -> Dict:
        """Get a random persona, optionally matching criteria"""
        available_personas = list(self.personas.values())
        
        if criteria:
            # Filter personas based on criteria
            filtered_personas = []
            for persona in available_personas:
                match = True
                
                if 'canton' in criteria:
                    if persona.get('canton', '').lower() != criteria['canton'].lower():
                        match = False
                
                if 'age_range' in criteria:
                    age = persona.get('age', 0)
                    min_age, max_age = criteria['age_range']
                    if not (min_age <= age <= max_age):
                        match = False
                
                if 'risk_tolerance' in criteria:
                    risk = persona.get('financial_profile', {}).get('risk_tolerance', '')
                    if risk.lower() != criteria['risk_tolerance'].lower():
                        match = False
                
                if match:
                    filtered_personas.append(persona)
            
            available_personas = filtered_personas
        
        if available_personas:
            selected_persona = random.choice(available_personas)
            logger.info(f"🎲 Selected random persona: {selected_persona.get('name')}")
            return selected_persona
        else:
            logger.warning("⚠️ No personas match the specified criteria")
            return {}
    
    def format_currency(self, amount: float, currency: str = 'CHF') -> str:
        """Format currency amount in Swiss format"""
        # Swiss number formatting: 1'234.56
        formatted = f"{amount:,.2f}".replace(',', "'")
        return f"{formatted} {currency}"
    
    def parse_currency(self, currency_string: str) -> float:
        """Parse currency string to float"""
        try:
            # Remove currency symbol and Swiss formatting
            clean_string = currency_string.replace('CHF', '').replace("'", '').strip()
            return float(clean_string)
        except ValueError:
            logger.error(f"❌ Failed to parse currency: {currency_string}")
            return 0.0
    
    def get_localization_data(self, locale: str = 'de-CH') -> Dict:
        """Get localization data for the specified locale"""
        return self.test_data.get('localization', {})
    
    def create_test_report_data(self, test_results: Dict) -> Dict:
        """Create structured data for test reporting"""
        report_data = {
            'timestamp': test_results.get('timestamp'),
            'test_suite': test_results.get('suite'),
            'browser': test_results.get('browser'),
            'environment': test_results.get('environment'),
            'scenarios_run': test_results.get('total_scenarios', 0),
            'scenarios_passed': test_results.get('passed_scenarios', 0),
            'scenarios_failed': test_results.get('failed_scenarios', 0),
            'pass_rate': test_results.get('pass_rate', 0),
            'duration': test_results.get('duration', 0),
            'swiss_features_tested': {
                'fire_calculations': len(test_results.get('fire_results', [])),
                'tax_optimizations': len(test_results.get('tax_results', [])),
                'healthcare_optimizations': len(test_results.get('healthcare_results', []))
            }
        }
        
        return report_data
