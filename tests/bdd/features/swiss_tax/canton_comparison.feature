Feature: Swiss Canton Tax Comparison
  As a Swiss resident considering relocation or tax optimization
  I want to compare tax implications across different cantons
  So that I can make informed decisions about where to live and optimize my tax burden

  Background:
    Given I am on the Swiss Budget Pro tax optimization page
    And the canton comparison tool is available
    And I can see all 26 Swiss cantons listed

  @critical @swiss @tax @comparison
  Scenario: Compare major cantons for high-income professional
    Given I am a high-income professional considering relocation
    When I enter an annual income of 200000 CHF
    And I compare taxes between the following cantons:
      | Canton    | Expected Tax Rate | Expected Annual Tax |
      | Zurich    | 28%              | 56000              |
      | Zug       | 22%              | 44000              |
      | Geneva    | 32%              | 64000              |
      | Vaud      | 30%              | 60000              |
      | Basel     | 34%              | 68000              |
    Then I should see accurate tax calculations for each canton
    And I should see Zug has the lowest total tax burden
    And I should see Basel has the highest total tax burden
    And I should see potential annual savings of 24000 CHF by moving from Basel to Zug
    And I should see a clear comparison table with all tax components

  @swiss @tax @pillar3a
  Scenario: Pillar 3a optimization across cantons
    Given I want to optimize my Pillar 3a contributions
    When I enter an annual income of 150000 CHF
    And I compare Pillar 3a tax benefits across cantons:
      | Canton    | Marginal Tax Rate | Max Contribution | Tax Savings |
      | Zurich    | 35%              | 7056            | 2470       |
      | Zug       | 25%              | 7056            | 1764       |
      | Geneva    | 40%              | 7056            | 2822       |
      | Bern      | 32%              | 7056            | 2258       |
    Then I should see accurate Pillar 3a tax savings for each canton
    And I should see Geneva offers the highest Pillar 3a tax benefits
    And I should see Zug offers the lowest Pillar 3a tax benefits
    And I should see recommendations for maximizing Pillar 3a contributions
    And I should see the net cost after tax savings for each canton

  @swiss @tax @wealth-tax
  Scenario: Wealth tax comparison for high net worth individuals
    Given I am a high net worth individual with significant assets
    When I enter the following wealth information:
      | Asset Type           | Value    |
      | Real Estate          | 2000000  |
      | Investment Portfolio | 1500000  |
      | Cash and Savings     | 500000   |
      | Total Net Worth      | 4000000  |
    And I compare wealth tax across cantons
    Then I should see wealth tax calculations for applicable cantons
    And I should see cantons with no wealth tax clearly marked
    And I should see the wealth tax threshold for each canton
    And I should see annual wealth tax amounts
    And I should see recommendations for wealth tax optimization

  @swiss @tax @progressive
  Scenario: Progressive tax bracket analysis
    Given I want to understand progressive taxation in Switzerland
    When I analyze tax brackets for different income levels:
      | Income Level | Federal Tax | Cantonal Tax (ZH) | Total Tax Rate |
      | 50000       | 2500       | 6000             | 17%           |
      | 100000      | 8000       | 12000            | 20%           |
      | 200000      | 25000      | 24000            | 24.5%         |
      | 500000      | 80000      | 60000            | 28%           |
    Then I should see accurate progressive tax calculations
    And I should see how tax rates increase with income
    And I should see the marginal tax rate for each income level
    And I should see effective tax rate calculations
    And I should see tax bracket breakdowns

  @swiss @tax @married
  Scenario: Tax comparison for married couples
    Given I am married and want to compare joint taxation
    When I enter combined household income of 180000 CHF
    And I compare married vs single taxation in different cantons
    Then I should see joint taxation benefits where applicable
    And I should see splitting advantages in certain cantons
    And I should see recommendations for income distribution between spouses
    And I should see child deduction benefits where applicable
    And I should see family-specific tax optimization strategies

  @swiss @tax @self-employed
  Scenario: Self-employed tax optimization
    Given I am self-employed with variable income
    When I enter the following self-employment information:
      | Business Income      | 180000  |
      | Business Expenses    | 30000   |
      | Net Business Income  | 150000  |
      | Pillar 3a Max        | 35280   |
    And I compare self-employed tax benefits across cantons
    Then I should see higher Pillar 3a contribution limits
    And I should see business expense deduction benefits
    And I should see self-employed specific tax rates
    And I should see recommendations for business structure optimization
    And I should see quarterly tax payment estimates

  @swiss @tax @relocation
  Scenario: Relocation tax impact analysis
    Given I am considering relocating within Switzerland
    When I enter my current situation in Zurich:
      | Current Canton       | Zurich  |
      | Annual Income        | 160000  |
      | Current Annual Tax   | 45000   |
      | Moving Costs         | 15000   |
    And I analyze relocation to Zug
    Then I should see the tax savings from relocation
    And I should see the break-even period for moving costs
    And I should see quality of life factors to consider
    And I should see cost of living adjustments
    And I should see recommendations about timing the move

  @swiss @tax @inheritance
  Scenario: Inheritance and gift tax comparison
    Given I want to understand inheritance tax implications
    When I enter inheritance planning information:
      | Inheritance Amount   | 1000000 |
      | Relationship         | Child   |
      | Gift vs Inheritance  | Gift    |
    And I compare inheritance tax across cantons
    Then I should see cantons with no inheritance tax
    And I should see gift tax rates where applicable
    And I should see family relationship exemptions
    And I should see recommendations for inheritance planning
    And I should see timing considerations for gifts

  @swiss @tax @real-time
  Scenario: Real-time tax comparison updates
    Given I am on the canton comparison tool
    When I enter an income of 120000 CHF
    Then I should see tax calculations update in real-time
    When I adjust the income to 140000 CHF
    Then I should see all canton comparisons update automatically
    And the updates should complete within 2 seconds
    And I should see the ranking of cantons adjust based on new income
    And I should see percentage changes highlighted

  @swiss @tax @export
  Scenario: Export canton comparison report
    Given I have completed a canton tax comparison
    When I click the export comparison button
    Then I should be able to download a detailed PDF report
    And the report should include all canton comparisons
    And the report should show tax breakdown by category
    And the report should include relocation recommendations
    And the report should show potential savings calculations
    And the export should complete within 15 seconds

  @swiss @tax @validation
  Scenario: Tax calculation accuracy validation
    Given I want to verify tax calculation accuracy
    When I enter known test scenarios with expected results
    And I compare against official Swiss tax calculators
    Then the calculations should match within 1% accuracy
    And federal tax calculations should be precise
    And cantonal tax estimates should be reasonable
    And social insurance calculations should be accurate
    And I should see disclaimers about estimate accuracy

  @swiss @tax @accessibility
  Scenario: Accessible tax comparison interface
    Given I am using assistive technology
    When I navigate the canton comparison tool
    Then all tax information should be screen reader accessible
    And comparison tables should have proper headers
    And tax amounts should be clearly announced
    And I should be able to navigate using keyboard only
    And color coding should have text alternatives

  @swiss @tax @mobile
  Scenario: Mobile canton tax comparison
    Given I am using a mobile device
    When I access the canton comparison tool
    Then the interface should be optimized for mobile
    And I should be able to scroll through all cantons
    And tax comparisons should be clearly displayed
    And I should be able to select cantons easily with touch
    And the calculations should work smoothly on mobile
