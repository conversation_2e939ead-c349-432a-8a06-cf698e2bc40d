Feature: Swiss Healthcare Deductible Optimization
  As a Swiss resident with mandatory health insurance
  I want to optimize my deductible choice based on my health profile and financial situation
  So that I can minimize my total healthcare costs while managing risk appropriately

  Background:
    Given I am on the Swiss Budget Pro healthcare optimization page
    And the deductible calculator is available
    And I can see all deductible options from 300 to 2500 CHF

  @critical @swiss @healthcare @optimization
  Scenario: Low-risk healthy young adult deductible optimization
    Given I am a healthy 25-year-old with minimal medical needs
    When I enter my health profile information:
      | Field                    | Value     |
      | Age                      | 25        |
      | Health Status            | Excellent |
      | Annual Medical Visits    | 1         |
      | Chronic Conditions       | None      |
      | Regular Medications      | None      |
      | Annual Health Costs      | 800       |
      | Risk Tolerance           | High      |
      | Canton                   | Zurich    |
    And I analyze all deductible options
    Then I should see 2500 CHF deductible recommended
    And I should see potential annual savings of 1200 CHF
    And I should see total annual cost breakdown for each deductible
    And I should see risk warnings about high deductible choice
    And I should see emergency fund recommendations

  @critical @swiss @healthcare @high-risk
  Scenario: High-risk profile with chronic conditions
    Given I am a 55-year-old with chronic health conditions
    When I enter my health profile information:
      | Field                    | Value     |
      | Age                      | 55        |
      | Health Status            | Fair      |
      | Annual Medical Visits    | 12        |
      | Chronic Conditions       | Diabetes  |
      | Regular Medications      | 3         |
      | Annual Health Costs      | 4500      |
      | Risk Tolerance           | Low       |
      | Canton                   | Geneva    |
    And I analyze all deductible options
    Then I should see 300 CHF deductible recommended
    And I should see total annual cost comparison
    And I should see that low deductible minimizes out-of-pocket costs
    And I should see medication cost considerations
    And I should see specialist visit cost analysis

  @swiss @healthcare @family
  Scenario: Family healthcare deductible optimization
    Given I am optimizing healthcare costs for my family
    When I enter family health information:
      | Family Member | Age | Health Status | Annual Costs |
      | Adult 1       | 38  | Good         | 2000        |
      | Adult 2       | 35  | Good         | 1800        |
      | Child 1       | 8   | Excellent    | 600         |
      | Child 2       | 5   | Good         | 800         |
    And I analyze family deductible strategies
    Then I should see individual vs family deductible recommendations
    And I should see cost optimization for each family member
    And I should see children's healthcare cost considerations
    And I should see family maximum out-of-pocket calculations
    And I should see recommendations for different deductible combinations

  @swiss @healthcare @premium-comparison
  Scenario: Premium comparison across Swiss cantons
    Given I want to compare healthcare premiums across cantons
    When I enter my profile for premium comparison:
      | Field                    | Value     |
      | Age                      | 35        |
      | Deductible Choice        | 1500      |
      | Insurance Model          | Standard  |
    And I compare premiums across major cantons:
      | Canton    | Average Premium | Range        |
      | Zurich    | 450            | 380-520     |
      | Geneva    | 520            | 450-590     |
      | Vaud      | 480            | 410-550     |
      | Bern      | 420            | 360-480     |
      | Zug       | 380            | 320-440     |
    Then I should see accurate premium comparisons
    And I should see potential savings from canton differences
    And I should see insurance company variations within cantons
    And I should see recommendations for premium optimization

  @swiss @healthcare @subsidy
  Scenario: Healthcare premium subsidy eligibility
    Given I want to check my eligibility for premium subsidies
    When I enter my financial information for subsidy calculation:
      | Field                    | Value     |
      | Annual Income            | 45000     |
      | Canton                   | Vaud      |
      | Household Size           | 2         |
      | Assets                   | 80000     |
      | Monthly Premium          | 480       |
    And I check subsidy eligibility
    Then I should see if I qualify for premium subsidies
    And I should see the estimated subsidy amount
    And I should see how subsidies affect deductible choice
    And I should see application process information
    And I should see income thresholds for different household sizes

  @swiss @healthcare @fire-impact
  Scenario: Healthcare costs impact on FIRE planning
    Given I am planning for FIRE and want to include healthcare costs
    When I enter my FIRE planning information:
      | Field                    | Value     |
      | Current Age              | 35        |
      | FIRE Target Age          | 55        |
      | Annual Expenses          | 60000     |
      | Healthcare Portion       | 8000      |
      | Expected Health Changes  | Aging     |
    And I analyze healthcare cost projections for FIRE
    Then I should see healthcare cost inflation projections
    And I should see how healthcare costs affect FIRE number
    And I should see recommendations for healthcare reserves
    And I should see early retirement healthcare considerations
    And I should see insurance continuation strategies

  @swiss @healthcare @alternative-models
  Scenario: Alternative insurance model comparison
    Given I want to compare different insurance models
    When I analyze alternative insurance models:
      | Model Type           | Premium Discount | Restrictions        |
      | HMO                 | 15%             | Network doctors     |
      | Family Doctor       | 10%             | GP referral required|
      | Telemedicine        | 12%             | Phone consultation  |
      | Standard            | 0%              | Free choice         |
    And I enter my healthcare usage patterns
    Then I should see cost-benefit analysis for each model
    And I should see suitability recommendations based on my profile
    And I should see potential annual savings from each model
    And I should see flexibility trade-offs
    And I should see quality of care considerations

  @swiss @healthcare @age-progression
  Scenario: Healthcare cost progression with age
    Given I want to understand how healthcare costs change with age
    When I project healthcare costs over time:
      | Age Range | Expected Annual Costs | Premium Changes |
      | 25-35     | 1200                 | Stable         |
      | 35-45     | 1800                 | Gradual increase|
      | 45-55     | 2800                 | Moderate increase|
      | 55-65     | 4200                 | Significant increase|
      | 65+       | 6000                 | High costs     |
    Then I should see age-based cost projections
    And I should see optimal deductible strategies for each age group
    And I should see long-term healthcare planning recommendations
    And I should see retirement healthcare cost planning
    And I should see preventive care investment benefits

  @swiss @healthcare @emergency-scenarios
  Scenario: Emergency healthcare cost scenarios
    Given I want to understand emergency healthcare cost impacts
    When I model emergency scenarios:
      | Scenario Type        | Estimated Cost | Deductible Impact |
      | Minor Emergency      | 2000          | Full deductible   |
      | Major Surgery        | 15000         | Deductible + 10%  |
      | Chronic Treatment    | 8000/year     | Annual deductible |
      | Accident Recovery    | 25000         | Insurance coverage|
    And I analyze impact on different deductible choices
    Then I should see how emergencies affect total costs
    And I should see risk mitigation strategies
    And I should see emergency fund recommendations
    And I should see insurance coverage details
    And I should see worst-case scenario planning

  @swiss @healthcare @optimization-calculator
  Scenario: Interactive deductible optimization calculator
    Given I am using the interactive deductible calculator
    When I adjust my health profile parameters in real-time:
      | Parameter            | Initial | Adjusted |
      | Annual Health Costs  | 2000   | 3000     |
      | Risk Tolerance       | Medium | Low      |
      | Age                  | 30     | 40       |
    Then I should see recommendations update automatically
    And I should see cost calculations adjust in real-time
    And I should see visual charts showing cost comparisons
    And I should see sensitivity analysis for different scenarios
    And the updates should complete within 1 second

  @swiss @healthcare @export
  Scenario: Export healthcare optimization report
    Given I have completed healthcare deductible optimization
    When I generate a comprehensive healthcare report
    Then I should be able to download a detailed PDF
    And the report should include my optimal deductible recommendation
    And the report should show cost comparison tables
    And the report should include risk assessment
    And the report should provide implementation recommendations
    And the report should include annual review reminders

  @swiss @healthcare @validation
  Scenario: Healthcare calculation accuracy validation
    Given I want to verify healthcare calculation accuracy
    When I test known scenarios with expected outcomes
    Then premium calculations should match insurance company data
    And deductible cost calculations should be accurate
    And out-of-pocket maximums should be correctly applied
    And subsidy calculations should align with official rates
    And I should see data source disclaimers

  @swiss @healthcare @accessibility
  Scenario: Accessible healthcare optimization interface
    Given I am using assistive technology for healthcare planning
    When I navigate the healthcare optimization tools
    Then all health information should be screen reader accessible
    And medical terminology should have explanations
    And Cost comparisons should be clearly announced
    And I should be able to complete optimization using keyboard only
    And Health risk information should be appropriately conveyed
