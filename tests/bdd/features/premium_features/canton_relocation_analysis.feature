Feature: Canton Relocation Tax Optimizer
  As a Swiss resident planning for FIRE
  I want to analyze potential tax savings from relocating to different cantons
  So that I can optimize my tax burden while considering quality of life factors

  Background:
    Given I am on the Swiss FIRE Calculator homepage
    And I have entered my financial information for relocation analysis:
      | Field           | Value  |
      | Monthly Income  | 10000  |
      | Current Savings | 250000 |
      | Family Size     | 2      |
    And I navigate to the Premium Features tab

  @smoke @premium @relocation
  Scenario: Canton Relocation Analysis displays comprehensive interface
    When I access the Canton Relocation Tax Optimizer section
    Then I should see the Canton Relocation Tax Optimizer component
    And I should see "Analyze potential tax savings and lifestyle impacts"
    And I should see "Analysis Configuration" section
    And the system should automatically analyze relocation opportunities
    And I should see a loading message "Analyzing canton relocation opportunities"
    When the relocation analysis completes
    Then I should see canton analysis results
    And I should see Swiss canton options displayed

  @premium @relocation @configuration
  Scenario: Analysis configuration options are available
    Given I access the Canton Relocation Analysis
    When I view the configuration section
    Then I should see "Current Canton" selector
    And I should see "Sort By" dropdown
    And I should see "Show only recommended" checkbox
    And the current canton should default to "Zurich (ZH)"
    When I change the current canton to "Geneva (GE)"
    Then the analysis should update with Geneva as baseline
    When I change the sort option to "Tax Savings"
    Then results should be sorted by tax savings amount

  @premium @relocation @swiss-cantons
  Scenario: All 26 Swiss cantons are available for analysis
    Given the Canton Relocation Analysis has completed
    When I view the canton analysis results
    Then I should see analysis for multiple Swiss cantons
    And I should see low-tax cantons like:
      | Canton           |
      | Zug (ZG)         |
      | Schwyz (SZ)      |
      | Nidwalden (NW)   |
    And I should see major urban cantons like:
      | Canton           |
      | Zurich (ZH)      |
      | Geneva (GE)      |
      | Basel-Stadt (BS) |
    And each canton should show comprehensive analysis

  @premium @relocation @key-metrics
  Scenario: Key financial metrics are displayed for each canton
    Given the Canton Relocation Analysis has completed
    When I examine each canton analysis
    Then I should see key metrics for each canton:
      | Metric           |
      | Tax Savings      |
      | Cost Difference  |
      | Payback Period   |
      | 20-Year Value    |
    And all monetary values should be formatted in CHF
    And metrics should help compare relocation benefits

  @premium @relocation @recommendations
  Scenario: Recommendation levels are clearly displayed
    Given the Canton Relocation Analysis has completed
    When I view the canton recommendations
    Then I should see recommendation levels:
      | Level                    | Icon |
      | Highly Recommended       | 🌟   |
      | Recommended             | 👍   |
      | Neutral                 | 🤔   |
      | Not Recommended         | ❌   |
    And each canton should have an appropriate recommendation level
    And recommendations should be based on comprehensive scoring

  @premium @relocation @advantages-considerations
  Scenario: Advantages and considerations are provided for each canton
    Given the Canton Relocation Analysis has completed
    When I examine canton details
    Then each canton should show "Advantages" section
    And each canton should show "Considerations" section
    And advantages should highlight positive aspects
    And considerations should note potential drawbacks
    And information should help make informed decisions

  @premium @relocation @canton-statistics
  Scenario: Detailed canton statistics are displayed
    Given the Canton Relocation Analysis has completed
    When I view canton details
    Then I should see detailed statistics:
      | Statistic        |
      | Income Tax       |
      | Quality of Life  |
      | Employment       |
      | Languages        |
    And statistics should provide comprehensive canton information
    And data should be accurate and up-to-date

  @premium @relocation @tax-calculations
  Scenario: Tax savings calculations are accurate and detailed
    Given I have high income suitable for tax optimization
    When the Canton Relocation Analysis completes
    Then I should see significant tax savings for low-tax cantons
    And Zug should show the highest tax savings
    And Geneva should show minimal or negative savings
    And calculations should consider income tax and wealth tax
    And savings should be displayed annually and over 20 years

  @premium @relocation @quality-of-life
  Scenario: Quality of life factors are considered in analysis
    Given the Canton Relocation Analysis has completed
    When I examine canton recommendations
    Then quality of life should be factored into scoring
    And I should see employment rate information
    And I should see cultural and recreational considerations
    And recommendations should balance tax savings with lifestyle
    And scoring should reflect comprehensive evaluation

  @premium @relocation @sorting-filtering
  Scenario: Results can be sorted and filtered effectively
    Given the Canton Relocation Analysis has completed
    When I change the sort option to "Net Benefit"
    Then cantons should be sorted by total net benefit
    When I change the sort option to "Overall Score"
    Then cantons should be sorted by comprehensive score
    When I enable "Show only recommended"
    Then only recommended cantons should be displayed
    And I should see fewer results with higher scores

  @premium @relocation @payback-analysis
  Scenario: Payback period analysis is provided
    Given the Canton Relocation Analysis has completed
    When I examine payback period calculations
    Then each canton should show payback period in months or years
    And payback should consider relocation costs
    And shorter payback periods should indicate better opportunities
    And payback analysis should help prioritize relocations

  @premium @relocation @language-information
  Scenario: Language information is provided for each canton
    Given the Canton Relocation Analysis has completed
    When I view canton language information
    Then I should see primary languages for each canton
    And German-speaking cantons should be identified
    And French-speaking cantons should be identified
    And Italian-speaking cantons should be identified
    And language barriers should be considered in recommendations

  @premium @relocation @family-considerations
  Scenario: Family size affects relocation analysis
    Given I have a family size of 4
    When the Canton Relocation Analysis completes
    Then analysis should consider family-specific factors
    And recommendations should account for family needs
    And cost calculations should reflect family size
    Given I have a family size of 1
    When the analysis recalculates
    Then recommendations should adapt to single person needs
    And cost-benefit analysis should change accordingly

  @premium @relocation @refresh
  Scenario: Analysis can be refreshed with updated parameters
    Given the Canton Relocation Analysis has completed
    When I change my monthly income to 15000
    And I click "Refresh Analysis"
    Then I should see "Analyzing..." loading state
    And the analysis should recalculate with higher income
    And tax savings should increase for low-tax cantons
    And recommendations should update based on new income

  @premium @relocation @empty-opportunities
  Scenario: No opportunities scenario is handled gracefully
    Given I have minimal income and savings:
      | Monthly Income  | 3000 |
      | Current Savings | 5000 |
    And I enable "Show only recommended"
    When the Canton Relocation Analysis completes
    Then I might see "No relocation opportunities found" message
    Or I should see appropriate guidance for my situation
    And the system should handle this gracefully
    And I should receive helpful advice

  @premium @relocation @mobile
  Scenario: Canton Relocation Analysis works on mobile devices
    Given I am using a mobile device
    And I access the Canton Relocation Analysis
    When the analysis completes
    Then all components should be visible and functional on mobile
    And configuration controls should work on mobile
    And I should be able to scroll through canton results
    And the interface should be touch-friendly
    And canton details should be readable on small screens

  @premium @relocation @performance
  Scenario: Relocation analysis completes within reasonable time
    Given I access the Canton Relocation Analysis
    When the system starts analyzing relocation opportunities
    Then the analysis should complete within 8 seconds
    And the interface should remain responsive during analysis
    And loading indicators should show progress
    And there should be no performance issues

  @premium @relocation @edge-cases
  Scenario: Analysis handles edge cases gracefully
    Given I have entered minimal data:
      | Monthly Income  | 0 |
      | Current Savings | 0 |
      | Family Size     | 0 |
    When I access the Canton Relocation Analysis
    Then the system should handle the edge case gracefully
    And I should not see any error messages
    And the component should not crash
    And I should see appropriate guidance

  @premium @relocation @dark-mode
  Scenario: Canton Relocation Analysis works in dark mode
    Given I have enabled dark mode
    When I access the Canton Relocation Analysis
    Then all components should display correctly in dark mode
    And text should be readable with proper contrast
    And recommendation indicators should be visible
    And all visual elements should be properly styled

  @premium @relocation @comprehensive-scoring
  Scenario: Comprehensive scoring considers multiple factors
    Given the Canton Relocation Analysis has completed
    When I examine canton scores
    Then scores should be out of 100 points
    And scoring should consider tax benefits (30 points)
    And scoring should consider cost factors (25 points)
    And scoring should consider quality of life (25 points)
    And scoring should consider practical factors (20 points)
    And total scores should help prioritize relocations
