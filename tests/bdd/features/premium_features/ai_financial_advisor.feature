Feature: AI Financial Advisor
  As a Swiss resident planning for FIRE
  I want to receive AI-powered financial insights and recommendations
  So that I can optimize my financial strategy with personalized guidance

  Background:
    Given I am on the Swiss FIRE Calculator homepage
    And I have entered my basic financial information:
      | Field                | Value  |
      | Current Age          | 35     |
      | Retirement Age       | 65     |
      | Current Savings      | 100000 |
      | Monthly Income       | 8000   |
      | Monthly Expenses     | 5000   |
      | Expected Return      | 7      |
      | Inflation Rate       | 2.5    |
      | Safe Withdrawal Rate | 4      |
    And I navigate to the Premium Features tab

  @smoke @premium @ai
  Scenario: AI Financial Advisor displays comprehensive analysis
    When I access the AI Financial Advisor section
    Then I should see the AI Financial Advisor component
    And I should see "Personalized financial insights and recommendations"
    And the AI should automatically analyze my financial situation
    And I should see a loading message "AI is analyzing your financial situation"
    When the AI analysis completes
    Then I should see the "AI Financial Health Score" section
    And I should see my overall score displayed
    And I should see my FIRE readiness percentage
    And I should see my risk level classification
    And I should see my optimization potential

  @premium @ai @insights
  Scenario: AI generates personalized FIRE plan
    Given the AI Financial Advisor has completed analysis
    When I view the personalized FIRE plan section
    Then I should see "Your Personalized FIRE Plan"
    And I should see 6 numbered plan steps
    And step 1 should contain "Primary Goal"
    And step 2 should contain "Savings Strategy"
    And step 3 should contain "Investment Allocation"
    And step 4 should contain "Swiss Optimization"
    And step 5 should contain "Risk Management"
    And step 6 should contain "Monitoring Framework"

  @premium @ai @insights
  Scenario: AI provides categorized insights and recommendations
    Given the AI Financial Advisor has completed analysis
    When I view the AI insights section
    Then I should see "AI Insights & Recommendations"
    And I should see a category filter dropdown
    When I select "All Insights" from the category filter
    Then I should see insights from multiple categories
    When I select "Optimization" from the category filter
    Then I should see only optimization-related insights
    When I select "Risk Management" from the category filter
    Then I should see only risk-related insights
    When I select "Swiss Specific" from the category filter
    Then I should see Swiss-specific recommendations
    And I should see mentions of "Pillar 3a"
    And I should see mentions of "CHF"

  @premium @ai @chat
  Scenario: Interactive AI chat provides financial guidance
    Given the AI Financial Advisor has completed analysis
    When I access the AI chat section
    Then I should see "Ask Your AI Financial Advisor"
    And I should see a chat input field
    And I should see a send button
    When I type "What should I do about my Pillar 3a?" in the chat
    And I click the send button
    Then I should see my message in the chat history
    And I should see "What should I do about my Pillar 3a?" displayed
    And I should receive an AI response within 5 seconds
    And the AI response should mention Pillar 3a strategies

  @premium @ai @chat
  Scenario: AI chat handles different question types
    Given the AI Financial Advisor has completed analysis
    And I am in the AI chat section
    When I ask "How should I invest my money?"
    Then the AI should provide investment guidance
    And the response should mention "portfolio" or "diversification"
    When I ask "How can I optimize my taxes?"
    Then the AI should provide tax optimization advice
    And the response should mention Swiss tax strategies
    When I ask "When can I retire?"
    Then the AI should provide retirement timeline guidance
    And the response should mention my FIRE timeline

  @premium @ai @scenarios
  Scenario: AI adapts recommendations to different savings rates
    Given I have a high savings rate scenario:
      | Monthly Income   | 10000 |
      | Monthly Expenses | 4000  |
    When the AI analyzes my financial situation
    Then I should see "Excellent Savings Rate" in the insights
    And I should see aggressive growth recommendations
    Given I have a low savings rate scenario:
      | Monthly Income   | 6000 |
      | Monthly Expenses | 5500 |
    When the AI analyzes my financial situation
    Then I should see "Low Savings Rate" warnings
    And I should see expense reduction recommendations

  @premium @ai @swiss
  Scenario: AI provides Swiss-specific financial recommendations
    Given the AI Financial Advisor has completed analysis
    When I filter insights by "Swiss Specific"
    Then I should see recommendations about Pillar 3a optimization
    And I should see canton-specific tax strategies
    And I should see Swiss investment recommendations
    And I should see CHF-denominated calculations
    And I should see references to Swiss financial regulations

  @premium @ai @confidence
  Scenario: AI insights display confidence scores and priorities
    Given the AI Financial Advisor has completed analysis
    When I view the AI insights
    Then each insight should display a confidence score
    And insights should be marked with priority levels
    And I should see "HIGH", "MEDIUM", or "LOW" priority indicators
    And I should see "Actionable" badges on relevant insights
    And confidence scores should be between 0% and 100%

  @premium @ai @refresh
  Scenario: AI analysis can be refreshed with updated data
    Given the AI Financial Advisor has completed analysis
    When I update my monthly income to 9000
    And I click "Refresh AI Analysis"
    Then I should see "Analyzing..." loading state
    And the AI should recalculate recommendations
    And the updated income should be reflected in the analysis
    And I should see updated insights based on the new data

  @premium @ai @mobile
  Scenario: AI Financial Advisor works on mobile devices
    Given I am using a mobile device
    And I access the AI Financial Advisor
    When the AI completes its analysis
    Then all AI components should be visible and functional
    And the chat interface should work on mobile
    And I should be able to scroll through insights
    And buttons should be touch-friendly

  @premium @ai @error
  Scenario: AI handles invalid data gracefully
    Given I have entered invalid financial data:
      | Current Age     | 0 |
      | Monthly Income  | 0 |
      | Monthly Expenses| 0 |
    When I access the AI Financial Advisor
    Then the AI should handle the invalid data gracefully
    And I should not see any error messages
    And the AI should provide general guidance
    And the component should not crash

  @premium @ai @persistence
  Scenario: AI chat history persists across navigation
    Given the AI Financial Advisor has completed analysis
    And I have sent a message "Test message for persistence"
    And I have received an AI response
    When I navigate to the Dashboard tab
    And I navigate back to the Premium Features tab
    Then I should still see my previous chat message
    And I should still see the AI response
    And the chat history should be preserved

  @premium @ai @dark-mode
  Scenario: AI Financial Advisor works in dark mode
    Given I have enabled dark mode
    When I access the AI Financial Advisor
    Then the AI components should display correctly in dark mode
    And text should be readable with proper contrast
    And the chat interface should work in dark mode
    And all visual elements should be properly styled

  @premium @ai @performance
  Scenario: AI analysis completes within reasonable time
    Given I access the AI Financial Advisor
    When the AI starts analyzing my financial situation
    Then the analysis should complete within 10 seconds
    And the loading indicators should be responsive
    And the interface should remain interactive during analysis
    And there should be no performance issues or lag
