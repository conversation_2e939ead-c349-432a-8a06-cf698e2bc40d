@premium @swiss @social-insurance
Feature: Swiss Social Insurance Calculator
  As a Swiss employee or self-employed person
  I want to calculate my social insurance contributions accurately
  So that I can understand my total employment costs and net income

  Background:
    Given I am on the Swiss FIRE Calculator
    And I have access to the Swiss Social Insurance Calculator

  @smoke
  Scenario: Calculate AHV/IV/EO contributions for standard employee
    Given I have an annual salary of CHF 100,000
    And I am 35 years old
    When I calculate my social insurance contributions
    Then my AHV/IV/EO employee contribution should be CHF 4,350
    And my AHV/IV/EO employer contribution should be CHF 4,350
    And the total AHV/IV/EO contribution should be CHF 8,700
    And the AHV/IV/EO rate should be displayed as "4.35%" for employee and employer

  @smoke
  Scenario: Calculate ALV contributions for income below maximum
    Given I have an annual salary of CHF 100,000
    When I calculate my ALV contributions
    Then my ALV employee contribution should be CHF 1,100
    And my ALV employer contribution should be CHF 1,100
    And there should be no additional ALV contribution
    And the total ALV contribution should be CHF 2,200
    And the ALV rate should be displayed as "1.1%"

  Scenario: Calculate ALV contributions for high income above maximum
    Given I have an annual salary of CHF 200,000
    When I calculate my ALV contributions
    Then my standard ALV employee contribution should be CHF 1,630.20 (1.1% of CHF 148,200)
    And my ALV employer contribution should be CHF 1,630.20
    And I should have additional ALV contribution of CHF 259 (0.5% of CHF 51,800)
    And my total ALV employee contribution should be CHF 1,889.20
    And the system should explain the additional ALV for high earners

  Scenario: Calculate NBU accident insurance contributions
    Given I have an annual salary of CHF 100,000
    When I calculate my NBU contributions
    Then my NBU employee contribution should be approximately CHF 1,000
    And my NBU employer contribution should be approximately CHF 1,000
    And the total NBU contribution should be approximately CHF 2,000
    And the NBU rate should be displayed as "~1.0%" (employer-specific)

  Scenario: Calculate BVG pension fund contributions for young employee
    Given I have an annual salary of CHF 100,000
    And I am 30 years old
    When I calculate my BVG contributions
    Then my insured salary should be CHF 62,475 (limited by maximum insured salary)
    And my BVG employee contribution should include 3.5% savings contribution
    And my BVG employer contribution should include 3.5% savings contribution
    And risk premiums and administrative costs should be split equally
    And the age group should be displayed as "25-34 years"

  Scenario: Calculate BVG pension fund contributions for older employee
    Given I have an annual salary of CHF 100,000
    And I am 50 years old
    When I calculate my BVG contributions
    Then my insured salary should be CHF 62,475
    And my BVG employee contribution should include 7.5% savings contribution
    And my BVG employer contribution should include 7.5% savings contribution
    And the age group should be displayed as "45-54 years"
    And contributions should be higher than for younger employees

  Scenario: Calculate BVG contributions for employee below minimum threshold
    Given I have an annual salary of CHF 20,000
    And I am 30 years old
    When I calculate my BVG contributions
    Then my insured salary should be CHF 0
    And my BVG employee contribution should be CHF 0
    And my BVG employer contribution should be CHF 0
    And I should see a message explaining the minimum salary requirement

  Scenario: Calculate family allowances (employer-only)
    Given I have an annual salary of CHF 100,000
    When I calculate family allowances
    Then the employer family allowance contribution should be CHF 1,200
    And the employee family allowance contribution should be CHF 0
    And the family allowance rate should be displayed as "1.2% (employer only)"

  Scenario: Calculate complete social insurance package
    Given I have an annual salary of CHF 100,000
    And I am 35 years old
    When I calculate my complete social insurance
    Then I should see a breakdown of all contributions:
      | Component | Employee | Employer | Total |
      | AHV/IV/EO | 4,350    | 4,350    | 8,700 |
      | ALV       | 1,100    | 1,100    | 2,200 |
      | NBU       | ~1,000   | ~1,000   | ~2,000|
      | BVG       | varies   | varies   | varies|
      | Family    | 0        | 1,200    | 1,200 |
    And my total employee contributions should be calculated
    And my total employer contributions should be calculated
    And my net salary should be gross salary minus employee contributions

  Scenario: Calculate social insurance with 13th salary
    Given I have an annual salary of CHF 100,000
    And I receive a 13th month salary
    And I am 35 years old
    When I calculate my annual social insurance
    Then the calculation should be based on CHF 108,333 (100,000 + 100,000/12)
    And all contribution amounts should be proportionally higher
    And the system should clearly indicate the 13th salary inclusion

  Scenario: Calculate social insurance with monthly bonuses
    Given I have an annual salary of CHF 100,000
    And I receive CHF 500 monthly bonus
    And I have a 13th month salary
    And I am 35 years old
    When I calculate my annual social insurance
    Then the calculation should be based on CHF 114,333 (100,000 + 8,333 + 6,000)
    And all contributions should reflect the total compensation
    And the bonus impact should be clearly shown

  Scenario: Calculate self-employed social insurance without BVG
    Given I am self-employed with net business income of CHF 100,000
    And I am 35 years old
    And I do not have voluntary BVG
    When I calculate my self-employed social insurance
    Then my AHV/IV/EO contribution should be CHF 8,700 (double rate)
    And I should have no ALV contributions
    And I should have no NBU contributions (unless voluntary)
    And I should have no BVG contributions
    And my total contributions should be CHF 8,700

  Scenario: Calculate self-employed social insurance with voluntary BVG
    Given I am self-employed with net business income of CHF 100,000
    And I am 35 years old
    And I have voluntary BVG insurance
    When I calculate my self-employed social insurance
    Then my AHV/IV/EO contribution should be CHF 8,700
    And I should have BVG contributions calculated on my insured income
    And I should pay both employee and employer portions of BVG
    And my total contributions should be higher than without BVG

  Scenario: View social insurance summary for different income levels
    Given I want to compare social insurance across income levels
    When I request a social insurance summary
    Then I should see calculations for income levels: CHF 50,000, 75,000, 100,000, 125,000, 150,000, 200,000
    And each level should show total contributions and effective rate
    And I should see how contributions scale with income
    And effective rates should be between 8-15% for social insurance only

  Scenario: Validate 2024 Swiss social insurance rates
    When I check the current social insurance rates
    Then the AHV/IV/EO rate should be 4.35% for both employee and employer
    And the ALV rate should be 1.1% up to CHF 148,200
    And the additional ALV rate should be 0.5% above CHF 148,200
    And the family allowance rate should be 1.2% (employer only)
    And the coordination deduction should be CHF 25,725
    And the maximum insured BVG salary should be CHF 88,200

  Scenario: Handle edge cases and validation
    Given I enter invalid or extreme values
    When I calculate social insurance contributions
    Then the system should handle zero income gracefully
    And negative values should be rejected with appropriate messages
    And extremely high incomes should be calculated correctly
    And age validation should prevent calculations for invalid ages
    And all calculations should maintain precision and accuracy

  Scenario: Display contribution breakdown with explanations
    Given I have calculated my social insurance contributions
    When I view the detailed breakdown
    Then each contribution type should have a clear explanation
    And I should see what each insurance covers (retirement, unemployment, accident, etc.)
    And rates and thresholds should be clearly displayed
    And I should understand the difference between employee and employer portions

  Scenario: Mobile responsiveness for social insurance calculator
    Given I am using a mobile device
    When I access the social insurance calculator
    Then all input fields should be touch-friendly
    And calculation results should be clearly displayed on small screens
    And tables should scroll horizontally if needed
    And the interface should remain functional and readable

  Scenario: Integration with tax planning dashboard
    Given I have calculated my social insurance contributions
    When I access the tax planning dashboard
    Then my social insurance data should be automatically integrated
    And the tax calculations should include social insurance contributions
    And changes in salary should update both social insurance and tax calculations
    And the data should remain synchronized across all components

  Scenario: Performance validation for complex calculations
    Given I have multiple employees or complex scenarios
    When I perform bulk social insurance calculations
    Then all calculations should complete within reasonable time
    And the interface should remain responsive
    And results should be accurate for all scenarios processed
    And memory usage should remain efficient
