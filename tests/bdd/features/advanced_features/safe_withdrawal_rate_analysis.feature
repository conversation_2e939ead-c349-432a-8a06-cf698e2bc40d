Feature: Safe Withdrawal Rate Analysis
  As a Swiss resident planning for FIRE
  I want to analyze safe withdrawal rates using Monte Carlo simulation
  So that I can determine the optimal withdrawal rate for sustainable retirement income

  Background:
    Given I am on the Swiss FIRE Calculator homepage
    And I have entered retirement planning information:
      | Field                | Value  |
      | Current Age          | 45     |
      | Retirement Age       | 65     |
      | Current Savings      | 300000 |
      | Monthly Income       | 12000  |
      | Monthly Expenses     | 7000   |
      | Expected Return      | 7      |
      | Inflation Rate       | 2.5    |
      | Safe Withdrawal Rate | 4      |
    And I navigate to the Advanced Features tab

  @smoke @advanced @withdrawal
  Scenario: Safe Withdrawal Rate Analysis displays comprehensive simulation
    When I access the Safe Withdrawal Rate Analysis section
    Then I should see the Safe Withdrawal Rate Analysis component
    And I should see "Advanced analysis of withdrawal rates"
    And I should see "Analysis Parameters" configuration section
    And the system should automatically run Monte Carlo simulations
    And I should see a loading message "Running Monte Carlo simulations"
    When the withdrawal analysis completes
    Then I should see the "Current Rate Assessment" section
    And I should see my current withdrawal rate displayed
    And I should see the recommended withdrawal rate

  @advanced @withdrawal @configuration
  Scenario: Analysis parameters can be configured
    Given I access the Safe Withdrawal Rate Analysis
    When I view the analysis parameters section
    Then I should see "Retirement Length (Years)" selector
    And I should see "Stock Allocation (%)" slider
    And I should see "Refresh Analysis" button
    When I change the retirement length to "25 years"
    And I change the stock allocation to "80%"
    Then I should see "80% Stocks, 20% Bonds" displayed
    When I click "Refresh Analysis"
    Then the analysis should recalculate with new parameters

  @advanced @withdrawal @risk-factors
  Scenario: Risk factors are comprehensively analyzed
    Given the Safe Withdrawal Rate Analysis has completed
    When I view the risk factors section
    Then I should see "Risk Factors" analysis
    And I should see all 4 risk factors:
      | Risk Factor        |
      | Retirement Length  |
      | Market Volatility  |
      | Inflation Impact   |
      | Sequence Risk      |
    And each risk factor should show a percentage impact
    And risk factors should be explained clearly

  @advanced @withdrawal @scenarios
  Scenario: Multiple withdrawal rate scenarios are analyzed
    Given the Safe Withdrawal Rate Analysis has completed
    When I view the withdrawal rate scenarios
    Then I should see "Withdrawal Rate Scenarios" section
    And I should see 8 different withdrawal rate scenarios:
      | Withdrawal Rate | Approach     |
      | 2.5%           | CONSERVATIVE |
      | 3.0%           | CONSERVATIVE |
      | 3.5%           | MODERATE     |
      | 4.0%           | MODERATE     |
      | 4.5%           | AGGRESSIVE   |
      | 5.0%           | AGGRESSIVE   |
      | 5.5%           | RISKY        |
      | 6.0%           | RISKY        |
    And each scenario should show success rate
    And each scenario should show median portfolio value
    And each scenario should show worst case scenario

  @advanced @withdrawal @success-rates
  Scenario: Success rates are calculated and displayed
    Given the Safe Withdrawal Rate Analysis has completed
    When I view the withdrawal scenarios
    Then each scenario should display a "Success Rate" percentage
    And success rates should be based on Monte Carlo simulation
    And higher withdrawal rates should generally show lower success rates
    And the 4.0% scenario should show the traditional safe withdrawal rate
    And success rates should help guide withdrawal rate selection

  @advanced @withdrawal @portfolio-projections
  Scenario: Portfolio value projections are shown for each scenario
    Given the Safe Withdrawal Rate Analysis has completed
    When I examine each withdrawal rate scenario
    Then I should see "Median Portfolio" values in CHF
    And I should see "Worst Case" portfolio values in CHF
    And I should see "Years to Depletion" for failed scenarios
    And portfolio values should be formatted in Swiss CHF
    And projections should help assess long-term sustainability

  @advanced @withdrawal @current-rate
  Scenario: Current withdrawal rate is assessed accurately
    Given I have set my safe withdrawal rate to 3.5%
    When the Safe Withdrawal Rate Analysis completes
    Then I should see "Your current 3.5% withdrawal rate" in the assessment
    And I should see the success rate for my current rate
    And I should see how it compares to recommended rates
    And I should see specific guidance for my current rate

  @advanced @withdrawal @4-percent-rule
  Scenario: The 4% rule is validated and explained
    Given the Safe Withdrawal Rate Analysis has completed
    When I view the 4.0% withdrawal rate scenario
    Then I should see it marked as "Traditional safe withdrawal rate"
    And I should see its historical success rate
    And I should see explanation of the 4% rule
    And I should see how it applies to Swiss market conditions

  @advanced @withdrawal @key-insights
  Scenario: Key insights are provided for withdrawal planning
    Given the Safe Withdrawal Rate Analysis has completed
    When I view the key insights section
    Then I should see "Key Insights" section
    And I should see insights about the 4% rule
    And I should see information about success rates
    And I should see bond tent strategy recommendations
    And insights should be relevant to Swiss investors

  @advanced @withdrawal @retirement-length
  Scenario: Different retirement lengths affect analysis
    Given I set retirement length to "25 years"
    When the analysis completes
    Then I should see analysis for 25-year retirement
    And success rates should be higher for shorter retirement
    Given I set retirement length to "40 years"
    When the analysis completes
    Then I should see analysis for 40-year retirement
    And success rates should be lower for longer retirement
    And recommendations should adapt to retirement length

  @advanced @withdrawal @portfolio-allocation
  Scenario: Portfolio allocation affects withdrawal rate sustainability
    Given I set stock allocation to "100%"
    When the analysis completes
    Then I should see "100% Stocks, 0% Bonds" allocation
    And volatility risk should be higher
    Given I set stock allocation to "30%"
    When the analysis completes
    Then I should see "30% Stocks, 70% Bonds" allocation
    And volatility risk should be lower
    And success rates should reflect allocation impact

  @advanced @withdrawal @extreme-scenarios
  Scenario: Extreme withdrawal rates are handled appropriately
    Given the Safe Withdrawal Rate Analysis has completed
    When I view the 2.5% withdrawal rate scenario
    Then it should be marked as "CONSERVATIVE APPROACH"
    And it should show very high success rates
    And it should indicate potential for large inheritance
    When I view the 6.0% withdrawal rate scenario
    Then it should be marked as "RISKY APPROACH"
    And it should show lower success rates
    And it should warn about potential spending adjustments

  @advanced @withdrawal @swiss-context
  Scenario: Analysis considers Swiss market context
    Given the Safe Withdrawal Rate Analysis has completed
    When I view the analysis results
    Then all monetary values should be displayed in CHF
    And the analysis should consider Swiss market conditions
    And recommendations should be relevant to Swiss investors
    And tax implications should be considered in Swiss context

  @advanced @withdrawal @refresh
  Scenario: Analysis can be refreshed with updated parameters
    Given the Safe Withdrawal Rate Analysis has completed
    When I change the retirement length to "35 years"
    And I change the stock allocation to "70%"
    And I click "Refresh Analysis"
    Then I should see "Analyzing..." loading state
    And the analysis should recalculate with new parameters
    And results should reflect the updated configuration
    And I should see updated success rates and projections

  @advanced @withdrawal @mobile
  Scenario: Safe Withdrawal Rate Analysis works on mobile devices
    Given I am using a mobile device
    And I access the Safe Withdrawal Rate Analysis
    When the analysis completes
    Then all components should be visible and functional on mobile
    And configuration controls should work on mobile
    And I should be able to scroll through all scenarios
    And the interface should be touch-friendly

  @advanced @withdrawal @performance
  Scenario: Monte Carlo simulation completes within reasonable time
    Given I access the Safe Withdrawal Rate Analysis
    When the system starts running Monte Carlo simulations
    Then the analysis should complete within 15 seconds
    And progress indicators should show simulation progress
    And the interface should remain responsive during calculation
    And there should be no performance issues or freezing

  @advanced @withdrawal @edge-cases
  Scenario: Analysis handles edge cases gracefully
    Given I have entered minimal expense data:
      | Monthly Expenses | 100 |
    When I access the Safe Withdrawal Rate Analysis
    Then the system should handle the edge case gracefully
    And I should not see any error messages
    And the component should not crash
    And I should see appropriate guidance for unusual scenarios

  @advanced @withdrawal @loading-states
  Scenario: Loading states are properly displayed during analysis
    Given I access the Safe Withdrawal Rate Analysis
    When I click "Refresh Analysis"
    Then I should see "Running Monte Carlo simulations..." message
    And I should see loading indicators
    And the refresh button should be disabled during analysis
    When the analysis completes
    Then loading indicators should disappear
    And the refresh button should be enabled again
    And results should be displayed

  @advanced @withdrawal @dark-mode
  Scenario: Safe Withdrawal Rate Analysis works in dark mode
    Given I have enabled dark mode
    When I access the Safe Withdrawal Rate Analysis
    Then all components should display correctly in dark mode
    And text should be readable with proper contrast
    And charts and graphs should be visible in dark mode
    And all visual elements should be properly styled for dark theme
