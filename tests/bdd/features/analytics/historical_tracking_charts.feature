Feature: Historical Tracking Charts
  As a Swiss resident planning for FIRE
  I want to track my financial progress over time with interactive charts
  So that I can visualize trends and monitor my journey toward financial independence

  Background:
    Given I am on the Swiss FIRE Calculator homepage
    And I have entered my financial information:
      | Field                | Value  |
      | Current Age          | 35     |
      | Retirement Age       | 65     |
      | Current Savings      | 150000 |
      | Monthly Income       | 9000   |
      | Monthly Expenses     | 5500   |
      | Expected Return      | 7      |
      | Inflation Rate       | 2.5    |
      | Safe Withdrawal Rate | 4      |
    And I have added expenses and investments
    And I navigate to the Analysis tab

  @smoke @analytics @charts
  Scenario: Historical Tracking Charts displays comprehensive interface
    When I access the Historical Tracking Charts section
    Then I should see the Historical Tracking Charts component
    And I should see "Track your financial progress over time"
    And I should see timeframe selection controls
    And I should see metric selection checkboxes
    And I should see an empty state message "No historical data available"
    And I should see "Generate sample data to see your financial trends"
    And I should see "Generate Sample Data" button

  @analytics @charts @timeframes
  Scenario: Timeframe selection controls are available
    Given I access the Historical Tracking Charts
    When I view the timeframe controls
    Then I should see "Time Period" label
    And I should see timeframe buttons:
      | Timeframe |
      | 1M        |
      | 3M        |
      | 6M        |
      | 1Y        |
      | 2Y        |
      | ALL       |
    And the "1Y" timeframe should be selected by default

  @analytics @charts @metrics
  Scenario: Metric selection checkboxes are available
    Given I access the Historical Tracking Charts
    When I view the metric selection controls
    Then I should see "Metrics to Display" label
    And I should see metric checkboxes:
      | Metric           |
      | Net Worth        |
      | Monthly Income   |
      | Monthly Expenses |
      | Monthly Savings  |
    And all metrics should be checked by default

  @analytics @charts @sample-data
  Scenario: Sample data can be generated for demonstration
    Given I access the Historical Tracking Charts
    When I click "Generate Sample Data"
    Then I should see "Generating Sample Data..." loading message
    And the generate button should be disabled
    When the data generation completes
    Then I should see "Summary Statistics (1Y)" section
    And I should see "Financial Trends" charts section
    And I should see "Historical Data Table" section
    And the generate button should be enabled again

  @analytics @charts @summary-stats
  Scenario: Summary statistics are displayed after data generation
    Given I have generated sample historical data
    When I view the summary statistics section
    Then I should see "Summary Statistics (1Y)"
    And I should see all summary metrics:
      | Metric              |
      | Net Worth Growth    |
      | Current Net Worth   |
      | Avg Savings Rate    |
      | FIRE Progress       |
    And values should be formatted in CHF
    And percentages should be properly formatted
    And growth should show positive or negative indicators

  @analytics @charts @financial-trends
  Scenario: Financial trend charts are displayed with interactive features
    Given I have generated sample historical data
    When I view the financial trends section
    Then I should see "Financial Trends" heading
    And I should see SVG charts rendered
    And charts should display line graphs for selected metrics
    And I should see data points on the charts
    When I hover over a data point
    Then I should see tooltip information
    And tooltip should show date and value

  @analytics @charts @timeframe-changes
  Scenario: Timeframe changes update the display
    Given I have generated sample historical data
    When I click the "3M" timeframe button
    Then I should see "Summary Statistics (3M)"
    And charts should update to show 3-month data
    And the "3M" button should be highlighted
    When I click the "6M" timeframe button
    Then I should see "Summary Statistics (6M)"
    And charts should update to show 6-month data
    When I click the "ALL" timeframe button
    Then I should see "Summary Statistics (ALL)"
    And charts should show all available data

  @analytics @charts @metric-selection
  Scenario: Metric selection changes update the charts
    Given I have generated sample historical data
    When I uncheck the "Monthly Expenses" checkbox
    Then the Monthly Expenses line should disappear from charts
    And the legend should update accordingly
    When I check the "Monthly Expenses" checkbox again
    Then the Monthly Expenses line should reappear
    When I uncheck "Net Worth" checkbox
    Then the Net Worth area chart should disappear
    And I can still see other selected metrics

  @analytics @charts @data-table
  Scenario: Historical data table displays comprehensive information
    Given I have generated sample historical data
    When I view the historical data table
    Then I should see "Historical Data Table" heading
    And I should see table headers:
      | Header           |
      | Date             |
      | Net Worth        |
      | Monthly Income   |
      | Monthly Expenses |
      | Savings Rate     |
      | FIRE Progress    |
    And table should contain historical data rows
    And dates should be formatted in Swiss format (DD.MM.YYYY)
    And monetary values should be formatted in CHF
    And percentages should be properly formatted

  @analytics @charts @swiss-formatting
  Scenario: All data is formatted according to Swiss conventions
    Given I have generated sample historical data
    When I view all displayed data
    Then monetary values should be formatted as "CHF X,XXX.XX"
    And dates should be in DD.MM.YYYY format
    And percentages should include the % symbol
    And large numbers should use Swiss thousand separators
    And decimal places should be appropriate for each data type

  @analytics @charts @color-coding
  Scenario: Charts use appropriate color coding and legends
    Given I have generated sample historical data
    When I view the financial trends charts
    Then each metric should have a distinct color
    And I should see color indicators in the metric selection
    And colors should be consistent between charts and legend
    And colors should be accessible and distinguishable

  @analytics @charts @refresh-data
  Scenario: Sample data can be regenerated
    Given I have generated sample historical data
    When I click "Generate Sample Data" again
    Then I should see "Generating Sample Data..." loading message
    And new sample data should be generated
    And charts should update with the new data
    And summary statistics should reflect the new data

  @analytics @charts @persistence
  Scenario: Generated data persists across navigation
    Given I have generated sample historical data
    When I navigate to the Dashboard tab
    And I navigate back to the Analysis tab
    Then I should still see the historical data
    And charts should still be displayed
    And I should not need to regenerate data
    And all previous selections should be preserved

  @analytics @charts @mobile
  Scenario: Historical Tracking Charts work on mobile devices
    Given I am using a mobile device
    And I have generated sample historical data
    When I view the Historical Tracking Charts
    Then all components should be visible and functional on mobile
    And timeframe buttons should be touch-friendly
    And I should be able to scroll through charts
    And checkboxes should work with touch input
    And the data table should be scrollable horizontally

  @analytics @charts @performance
  Scenario: Data generation and chart rendering perform well
    Given I access the Historical Tracking Charts
    When I click "Generate Sample Data"
    Then data generation should complete within 5 seconds
    And charts should render smoothly
    And timeframe changes should be responsive
    And there should be no lag when interacting with controls
    And the interface should remain responsive throughout

  @analytics @charts @edge-cases
  Scenario: Charts handle edge cases gracefully
    Given I have minimal financial data entered:
      | Current Savings  | 0 |
      | Monthly Income   | 0 |
      | Monthly Expenses | 0 |
    When I generate sample data
    Then the system should handle the edge case gracefully
    And charts should still be generated
    And I should not see any error messages
    And the component should not crash

  @analytics @charts @dark-mode
  Scenario: Historical Tracking Charts work in dark mode
    Given I have enabled dark mode
    And I have generated sample historical data
    When I view the Historical Tracking Charts
    Then all components should display correctly in dark mode
    And charts should be visible with appropriate contrast
    And text should be readable in dark mode
    And colors should be adapted for dark theme
    And all visual elements should be properly styled

  @analytics @charts @loading-states
  Scenario: Loading states are properly displayed
    Given I access the Historical Tracking Charts
    When I click "Generate Sample Data"
    Then I should see "Generating Sample Data..." message
    And the generate button should show "Generating..." text
    And the button should be disabled during generation
    When generation completes
    Then loading message should disappear
    And button should return to normal state
    And data should be displayed

  @analytics @charts @empty-state
  Scenario: Empty state is handled appropriately
    Given I access the Historical Tracking Charts
    When no historical data is available
    Then I should see "No historical data available" message
    And I should see helpful guidance text
    And I should see the "Generate Sample Data" button prominently
    And no charts should be displayed
    And the interface should guide me to generate data

  @analytics @charts @chart-interactions
  Scenario: Chart interactions work correctly
    Given I have generated sample historical data
    When I interact with the charts
    Then I should be able to hover over data points
    And tooltips should appear with relevant information
    And chart interactions should be smooth and responsive
    And I should be able to view different parts of the timeline
    And all interactive elements should work as expected
