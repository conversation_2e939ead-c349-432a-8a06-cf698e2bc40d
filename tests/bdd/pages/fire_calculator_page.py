"""
Page Object Model for Swiss Budget Pro FIRE Calculator
Provides methods to interact with the FIRE calculation interface
"""

import time
import logging
from typing import Dict, List, Optional
from playwright.sync_api import Page, expect

logger = logging.getLogger(__name__)

class FireCalculatorPage:
    """
    Page object for Swiss Budget Pro FIRE calculator interface
    """
    
    def __init__(self, page: Page):
        self.page = page
        
        # Locators for FIRE calculator elements
        self.locators = {
            # Main interface
            'app_title': '[data-testid="app-title"], h1:has-text("Swiss Budget Pro")',
            'calculator_container': '[data-testid="fire-calculator"], .fire-calculator',
            
            # Input fields
            'monthly_income': '[data-testid="monthly-income"], input[name="monthlyIncome"]',
            'monthly_expenses': '[data-testid="monthly-expenses"], input[name="monthlyExpenses"]',
            'current_savings': '[data-testid="current-savings"], input[name="currentSavings"]',
            'canton_select': '[data-testid="canton-select"], select[name="canton"]',
            'age_input': '[data-testid="age"], input[name="age"]',
            'retirement_age': '[data-testid="retirement-age"], input[name="retirementAge"]',
            'withdrawal_rate': '[data-testid="withdrawal-rate"], select[name="withdrawalRate"]',
            
            # Buttons
            'calculate_button': '[data-testid="calculate-fire"], button:has-text("Calculate")',
            'reset_button': '[data-testid="reset"], button:has-text("Reset")',
            'export_button': '[data-testid="export"], button:has-text("Export")',
            
            # Results
            'fire_years': '[data-testid="fire-years"], .fire-years',
            'fire_number': '[data-testid="fire-number"], .fire-number',
            'savings_rate': '[data-testid="savings-rate"], .savings-rate',
            'monthly_savings': '[data-testid="monthly-savings"], .monthly-savings',
            'net_income': '[data-testid="net-income"], .net-income',
            
            # Recommendations
            'tax_recommendations': '[data-testid="tax-recommendations"], .tax-recommendations',
            'pillar3a_suggestions': '[data-testid="pillar3a-suggestions"], .pillar3a-suggestions',
            'tax_opportunities': '[data-testid="tax-opportunities"], .tax-opportunities',
            
            # Error and validation
            'error_message': '[data-testid="error-message"], .error-message',
            'validation_errors': '[data-testid="validation-error"], .validation-error',
            'loading_indicator': '[data-testid="loading"], .loading, .spinner',
            
            # Mobile specific
            'mobile_menu': '[data-testid="mobile-menu"], .mobile-menu',
            'mobile_results': '[data-testid="mobile-results"], .mobile-results',
        }
    
    def wait_for_page_load(self, timeout: int = 10000) -> None:
        """Wait for the page to load completely"""
        try:
            self.page.wait_for_selector(self.locators['app_title'], timeout=timeout)
            self.page.wait_for_load_state('networkidle')
            logger.info("✅ FIRE calculator page loaded")
        except Exception as e:
            logger.error(f"❌ Page load failed: {e}")
            raise
    
    def is_page_loaded(self) -> bool:
        """Check if the page has loaded successfully"""
        try:
            return self.page.locator(self.locators['app_title']).is_visible()
        except Exception:
            return False
    
    def is_calculator_visible(self) -> bool:
        """Check if the main calculator interface is visible"""
        try:
            return (self.page.locator(self.locators['monthly_income']).is_visible() and
                   self.page.locator(self.locators['calculate_button']).is_visible())
        except Exception:
            return False
    
    # Input methods
    
    def enter_monthly_income(self, amount: int) -> None:
        """Enter monthly income amount"""
        try:
            income_field = self.page.locator(self.locators['monthly_income'])
            income_field.clear()
            income_field.fill(str(amount))
            logger.info(f"💰 Monthly income entered: {amount} CHF")
        except Exception as e:
            logger.error(f"❌ Failed to enter monthly income: {e}")
            raise
    
    def enter_monthly_income_text(self, text: str) -> None:
        """Enter text in monthly income field (for validation testing)"""
        try:
            income_field = self.page.locator(self.locators['monthly_income'])
            income_field.clear()
            income_field.fill(text)
            logger.info(f"💰 Monthly income text entered: {text}")
        except Exception as e:
            logger.error(f"❌ Failed to enter monthly income text: {e}")
            raise
    
    def enter_monthly_expenses(self, amount: int) -> None:
        """Enter monthly expenses amount"""
        try:
            expenses_field = self.page.locator(self.locators['monthly_expenses'])
            expenses_field.clear()
            expenses_field.fill(str(amount))
            logger.info(f"💸 Monthly expenses entered: {amount} CHF")
        except Exception as e:
            logger.error(f"❌ Failed to enter monthly expenses: {e}")
            raise
    
    def enter_monthly_expenses_text(self, text: str) -> None:
        """Enter text in monthly expenses field (for validation testing)"""
        try:
            expenses_field = self.page.locator(self.locators['monthly_expenses'])
            expenses_field.clear()
            expenses_field.fill(text)
            logger.info(f"💸 Monthly expenses text entered: {text}")
        except Exception as e:
            logger.error(f"❌ Failed to enter monthly expenses text: {e}")
            raise
    
    def enter_current_savings(self, amount: int) -> None:
        """Enter current savings amount"""
        try:
            savings_field = self.page.locator(self.locators['current_savings'])
            savings_field.clear()
            savings_field.fill(str(amount))
            logger.info(f"💰 Current savings entered: {amount} CHF")
        except Exception as e:
            logger.error(f"❌ Failed to enter current savings: {e}")
            raise
    
    def enter_current_savings_text(self, text: str) -> None:
        """Enter text in current savings field (for validation testing)"""
        try:
            savings_field = self.page.locator(self.locators['current_savings'])
            savings_field.clear()
            savings_field.fill(text)
            logger.info(f"💰 Current savings text entered: {text}")
        except Exception as e:
            logger.error(f"❌ Failed to enter current savings text: {e}")
            raise
    
    def select_canton(self, canton: str) -> None:
        """Select Swiss canton"""
        try:
            canton_select = self.page.locator(self.locators['canton_select'])
            canton_select.select_option(label=canton)
            logger.info(f"🇨🇭 Canton selected: {canton}")
        except Exception as e:
            logger.error(f"❌ Failed to select canton: {e}")
            raise
    
    def select_canton_invalid(self, canton: str) -> None:
        """Attempt to select invalid canton (for validation testing)"""
        try:
            canton_select = self.page.locator(self.locators['canton_select'])
            # Try to select invalid option
            canton_select.fill(canton)
            logger.info(f"🇨🇭 Invalid canton attempted: {canton}")
        except Exception as e:
            logger.info(f"✅ Invalid canton selection failed as expected: {e}")
    
    def enter_age(self, age: int) -> None:
        """Enter age"""
        try:
            age_field = self.page.locator(self.locators['age_input'])
            age_field.clear()
            age_field.fill(str(age))
            logger.info(f"👤 Age entered: {age}")
        except Exception as e:
            logger.error(f"❌ Failed to enter age: {e}")
            raise
    
    def enter_retirement_age(self, age: int) -> None:
        """Enter target retirement age"""
        try:
            retirement_field = self.page.locator(self.locators['retirement_age'])
            retirement_field.clear()
            retirement_field.fill(str(age))
            logger.info(f"🎯 Retirement age entered: {age}")
        except Exception as e:
            logger.error(f"❌ Failed to enter retirement age: {e}")
            raise
    
    def select_withdrawal_rate(self, rate: float) -> None:
        """Select withdrawal rate"""
        try:
            rate_select = self.page.locator(self.locators['withdrawal_rate'])
            rate_percent = f"{rate * 100:.1f}%"
            rate_select.select_option(label=rate_percent)
            logger.info(f"📊 Withdrawal rate selected: {rate_percent}")
        except Exception as e:
            logger.error(f"❌ Failed to select withdrawal rate: {e}")
            raise
    
    # Action methods
    
    def click_calculate_button(self) -> None:
        """Click the calculate FIRE button"""
        try:
            calculate_btn = self.page.locator(self.locators['calculate_button'])
            calculate_btn.click()
            logger.info("🔄 Calculate FIRE button clicked")
        except Exception as e:
            logger.error(f"❌ Failed to click calculate button: {e}")
            raise
    
    def is_calculate_button_disabled(self) -> bool:
        """Check if calculate button is disabled"""
        try:
            calculate_btn = self.page.locator(self.locators['calculate_button'])
            return calculate_btn.is_disabled()
        except Exception:
            return False
    
    # Result extraction methods
    
    def get_fire_years(self) -> float:
        """Get FIRE timeline in years"""
        try:
            years_element = self.page.locator(self.locators['fire_years'])
            years_text = years_element.text_content()
            # Extract number from text like "18.5 years" or "18.5"
            years_value = float(''.join(filter(lambda x: x.isdigit() or x == '.', years_text)))
            logger.info(f"📊 FIRE years extracted: {years_value}")
            return years_value
        except Exception as e:
            logger.error(f"❌ Failed to get FIRE years: {e}")
            return 0.0
    
    def get_fire_number(self) -> float:
        """Get FIRE number amount"""
        try:
            number_element = self.page.locator(self.locators['fire_number'])
            number_text = number_element.text_content()
            # Extract number from text like "1,650,000 CHF" or "1650000"
            number_clean = ''.join(filter(lambda x: x.isdigit(), number_text))
            number_value = float(number_clean) if number_clean else 0.0
            logger.info(f"💰 FIRE number extracted: {number_value}")
            return number_value
        except Exception as e:
            logger.error(f"❌ Failed to get FIRE number: {e}")
            return 0.0
    
    def get_savings_rate(self) -> float:
        """Get savings rate percentage"""
        try:
            rate_element = self.page.locator(self.locators['savings_rate'])
            rate_text = rate_element.text_content()
            # Extract number from text like "35.2%" or "35.2"
            rate_value = float(''.join(filter(lambda x: x.isdigit() or x == '.', rate_text)))
            logger.info(f"📈 Savings rate extracted: {rate_value}%")
            return rate_value
        except Exception as e:
            logger.error(f"❌ Failed to get savings rate: {e}")
            return 0.0
    
    def get_monthly_savings(self) -> float:
        """Get monthly savings amount"""
        try:
            savings_element = self.page.locator(self.locators['monthly_savings'])
            savings_text = savings_element.text_content()
            # Extract number from text
            savings_clean = ''.join(filter(lambda x: x.isdigit(), savings_text))
            savings_value = float(savings_clean) if savings_clean else 0.0
            logger.info(f"💰 Monthly savings extracted: {savings_value}")
            return savings_value
        except Exception as e:
            logger.error(f"❌ Failed to get monthly savings: {e}")
            return 0.0
    
    # Recommendation and suggestion methods
    
    def has_tax_recommendations(self) -> bool:
        """Check if tax recommendations are displayed"""
        try:
            return self.page.locator(self.locators['tax_recommendations']).is_visible()
        except Exception:
            return False
    
    def get_tax_recommendations(self) -> List[str]:
        """Get tax optimization recommendations"""
        try:
            recommendations = []
            elements = self.page.locator(self.locators['tax_recommendations']).all()
            for element in elements:
                text = element.text_content()
                if text:
                    recommendations.append(text.strip())
            return recommendations
        except Exception as e:
            logger.error(f"❌ Failed to get tax recommendations: {e}")
            return []
    
    def has_pillar3a_suggestions(self) -> bool:
        """Check if Pillar 3a suggestions are displayed"""
        try:
            return self.page.locator(self.locators['pillar3a_suggestions']).is_visible()
        except Exception:
            return False
    
    def get_pillar3a_suggestions(self) -> List[str]:
        """Get Pillar 3a contribution suggestions"""
        try:
            suggestions = []
            elements = self.page.locator(self.locators['pillar3a_suggestions']).all()
            for element in elements:
                text = element.text_content()
                if text:
                    suggestions.append(text.strip())
            return suggestions
        except Exception as e:
            logger.error(f"❌ Failed to get Pillar 3a suggestions: {e}")
            return []
    
    def has_significant_tax_opportunities(self) -> bool:
        """Check if significant tax opportunities are highlighted"""
        try:
            return self.page.locator(self.locators['tax_opportunities']).is_visible()
        except Exception:
            return False
    
    def get_tax_opportunities(self) -> List[str]:
        """Get tax optimization opportunities"""
        try:
            opportunities = []
            elements = self.page.locator(self.locators['tax_opportunities']).all()
            for element in elements:
                text = element.text_content()
                if text:
                    opportunities.append(text.strip())
            return opportunities
        except Exception as e:
            logger.error(f"❌ Failed to get tax opportunities: {e}")
            return []
    
    # Error and validation methods
    
    def has_error_message(self, message: str) -> bool:
        """Check if specific error message is displayed"""
        try:
            error_elements = self.page.locator(self.locators['error_message']).all()
            for element in error_elements:
                if message.lower() in element.text_content().lower():
                    return True
            return False
        except Exception:
            return False
    
    def has_validation_errors(self) -> bool:
        """Check if validation errors are displayed"""
        try:
            return self.page.locator(self.locators['validation_errors']).count() > 0
        except Exception:
            return False
    
    def get_validation_errors(self) -> List[str]:
        """Get all validation error messages"""
        try:
            errors = []
            elements = self.page.locator(self.locators['validation_errors']).all()
            for element in elements:
                text = element.text_content()
                if text:
                    errors.append(text.strip())
            return errors
        except Exception as e:
            logger.error(f"❌ Failed to get validation errors: {e}")
            return []
    
    def has_specific_error(self, error_text: str) -> bool:
        """Check if specific error text is displayed"""
        try:
            page_content = self.page.content()
            return error_text.lower() in page_content.lower()
        except Exception:
            return False
    
    # Real-time update methods
    
    def has_automatic_updates(self) -> bool:
        """Check if automatic updates are working"""
        try:
            # This would need to be implemented based on the actual behavior
            # For now, assume it's working if results are visible
            return self.page.locator(self.locators['fire_years']).is_visible()
        except Exception:
            return False
    
    def measure_update_time(self) -> float:
        """Measure time for updates to complete"""
        try:
            start_time = time.time()
            # Wait for loading indicator to disappear
            self.page.wait_for_selector(self.locators['loading_indicator'], 
                                      state='hidden', timeout=5000)
            return time.time() - start_time
        except Exception:
            return 0.0
    
    # Mobile and accessibility methods
    
    def are_results_mobile_friendly(self) -> bool:
        """Check if results are displayed properly on mobile"""
        try:
            # Check if mobile-specific elements are visible
            return (self.page.locator(self.locators['fire_years']).is_visible() and
                   self.page.viewport_size['width'] < 768)
        except Exception:
            return False
    
    def is_interface_touch_friendly(self) -> bool:
        """Check if interface is touch-friendly"""
        try:
            # Check if buttons are large enough for touch
            calculate_btn = self.page.locator(self.locators['calculate_button'])
            box = calculate_btn.bounding_box()
            return box and box['height'] >= 44  # Minimum touch target size
        except Exception:
            return False
    
    def are_results_screen_reader_accessible(self) -> bool:
        """Check if results are accessible to screen readers"""
        try:
            # Check for proper ARIA labels and roles
            results_element = self.page.locator(self.locators['fire_years'])
            aria_label = results_element.get_attribute('aria-label')
            return aria_label is not None or results_element.get_attribute('role') is not None
        except Exception:
            return False
    
    def have_all_fields_proper_labels(self) -> bool:
        """Check if all form fields have proper labels"""
        try:
            input_fields = [
                self.locators['monthly_income'],
                self.locators['monthly_expenses'],
                self.locators['current_savings']
            ]
            
            for field_locator in input_fields:
                field = self.page.locator(field_locator)
                if not (field.get_attribute('aria-label') or 
                       field.get_attribute('aria-labelledby') or
                       field.get_attribute('id')):
                    return False
            
            return True
        except Exception:
            return False
    
    # Keyboard navigation methods
    
    def navigate_with_keyboard(self) -> None:
        """Navigate through form using keyboard"""
        try:
            # Tab through form fields
            self.page.keyboard.press('Tab')
            self.page.keyboard.press('Tab')
            self.page.keyboard.press('Tab')
            logger.info("⌨️ Keyboard navigation completed")
        except Exception as e:
            logger.error(f"❌ Keyboard navigation failed: {e}")
            raise
    
    def enter_data_with_keyboard(self, data: Dict) -> None:
        """Enter data using keyboard input"""
        try:
            # Focus and enter income
            self.page.locator(self.locators['monthly_income']).focus()
            self.page.keyboard.type(str(data['income']))
            
            # Tab to expenses and enter
            self.page.keyboard.press('Tab')
            self.page.keyboard.type(str(data['expenses']))
            
            # Tab to savings and enter
            self.page.keyboard.press('Tab')
            self.page.keyboard.type(str(data['savings']))
            
            logger.info("⌨️ Data entered with keyboard")
        except Exception as e:
            logger.error(f"❌ Keyboard data entry failed: {e}")
            raise
    
    def activate_calculate_with_enter(self) -> None:
        """Activate calculate button using Enter key"""
        try:
            self.page.locator(self.locators['calculate_button']).focus()
            self.page.keyboard.press('Enter')
            logger.info("⌨️ Calculate button activated with Enter")
        except Exception as e:
            logger.error(f"❌ Enter key activation failed: {e}")
            raise
