{"tax_rates": {"federal": {"brackets": [{"min": 0, "max": 14500, "rate": 0, "description": "Tax-free threshold"}, {"min": 14500, "max": 31600, "rate": 0.077, "description": "First tax bracket"}, {"min": 31600, "max": 41400, "rate": 0.088, "description": "Second tax bracket"}, {"min": 41400, "max": 55200, "rate": 0.11, "description": "Third tax bracket"}, {"min": 55200, "max": 72500, "rate": 0.132, "description": "Fourth tax bracket"}, {"min": 72500, "max": 78100, "rate": 0.154, "description": "Fifth tax bracket"}, {"min": 78100, "max": *********, "rate": 0.165, "description": "Top tax bracket"}]}, "cantonal": {"ZH": {"rate": 0.12, "wealth_tax": 0.002, "wealth_tax_threshold": 100000, "description": "Zurich cantonal tax rates"}, "GE": {"rate": 0.15, "wealth_tax": 0.003, "wealth_tax_threshold": 75000, "description": "Geneva cantonal tax rates"}, "VD": {"rate": 0.14, "wealth_tax": 0.0025, "wealth_tax_threshold": 80000, "description": "Vaud cantonal tax rates"}, "BE": {"rate": 0.13, "wealth_tax": 0.002, "wealth_tax_threshold": 90000, "description": "Bern cantonal tax rates"}, "ZG": {"rate": 0.08, "wealth_tax": 0.001, "wealth_tax_threshold": 150000, "description": "Zug cantonal tax rates (low tax)"}, "BS": {"rate": 0.16, "wealth_tax": 0.003, "wealth_tax_threshold": 70000, "description": "Basel-Stadt cantonal tax rates"}, "BL": {"rate": 0.11, "wealth_tax": 0.002, "wealth_tax_threshold": 95000, "description": "Basel-Landschaft cantonal tax rates"}, "AG": {"rate": 0.1, "wealth_tax": 0.0015, "wealth_tax_threshold": 100000, "description": "Aargau cantonal tax rates"}}}, "social_insurance": {"ahv": {"rate": 0.0525, "max_income": 88200, "description": "AHV/IV/EO contributions"}, "alv": {"rate": 0.011, "max_income": 148200, "additional_rate": 0.005, "additional_threshold": 148200, "description": "Unemployment insurance"}, "nbv": {"rate": 0.0125, "description": "Accident insurance (estimated)"}}, "pillar3a": {"max_contribution_2024": 7056, "max_contribution_self_employed_2024": 35280, "tax_deductible": true, "withdrawal_age": 60, "early_withdrawal_penalty": 0.05, "description": "Pillar 3a retirement savings limits and rules"}, "healthcare_deductibles": [300, 500, 1000, 1500, 2000, 2500], "healthcare_premiums": {"ZH": {"average": 450, "range": [380, 520], "description": "Zurich average healthcare premiums"}, "GE": {"average": 520, "range": [450, 590], "description": "Geneva average healthcare premiums"}, "VD": {"average": 480, "range": [410, 550], "description": "Vaud average healthcare premiums"}, "BE": {"average": 420, "range": [360, 480], "description": "Bern average healthcare premiums"}, "ZG": {"average": 380, "range": [320, 440], "description": "Zug average healthcare premiums"}, "BS": {"average": 500, "range": [430, 570], "description": "Basel-Stadt average healthcare premiums"}, "BL": {"average": 440, "range": [380, 500], "description": "Basel-Landschaft average healthcare premiums"}, "AG": {"average": 400, "range": [340, 460], "description": "Aargau average healthcare premiums"}}, "fire_calculations": {"withdrawal_rate": 0.04, "safe_withdrawal_rate": 0.035, "conservative_withdrawal_rate": 0.03, "multiplier": 25, "safe_multiplier": 28.57, "conservative_multiplier": 33.33, "description": "FIRE calculation parameters and withdrawal rates"}, "market_assumptions": {"stock_return": {"nominal": 0.07, "real": 0.05, "volatility": 0.15, "description": "Long-term stock market return assumptions"}, "bond_return": {"nominal": 0.03, "real": 0.01, "volatility": 0.05, "description": "Long-term bond return assumptions"}, "inflation": {"target": 0.02, "historical": 0.018, "range": [0.01, 0.03], "description": "Swiss inflation assumptions"}}, "test_scenarios": {"basic_fire": {"income": 100000, "expenses": 60000, "savings": 100000, "expected_fire_years": 18.9, "expected_fire_number": 1500000, "tolerance": 0.1}, "high_earner": {"income": 300000, "expenses": 120000, "savings": 800000, "canton": "ZG", "expected_fire_years": 6.5, "expected_fire_number": 3000000, "tolerance": 0.1}, "impossible_fire": {"income": 60000, "expenses": 65000, "savings": 20000, "expected_result": "impossible", "expected_error": "FIRE not possible with current income and expenses"}}, "validation_rules": {"income": {"min": 0, "max": 10000000, "required": true, "error_messages": {"negative": "Income must be positive", "too_high": "Income amount seems unrealistic", "required": "Monthly income is required"}}, "expenses": {"min": 0, "max": 5000000, "required": true, "error_messages": {"negative": "Expenses must be positive", "invalid": "Expenses must be a valid number", "required": "Monthly expenses are required"}}, "savings": {"min": 0, "max": 100000000, "required": false, "error_messages": {"negative": "Savings cannot be negative", "unrealistic": "Savings amount seems unrealistic"}}, "canton": {"valid_options": ["ZH", "GE", "VD", "BE", "ZG", "BS", "BL", "AG"], "required": true, "error_messages": {"invalid": "Please select a valid Swiss canton", "required": "Canton selection is required"}}}, "performance_benchmarks": {"page_load": {"target": 3000, "good": 2000, "excellent": 1000, "unit": "ms"}, "fire_calculation": {"target": 500, "good": 300, "excellent": 100, "unit": "ms"}, "real_time_updates": {"target": 1000, "good": 500, "excellent": 200, "unit": "ms"}}, "accessibility_requirements": {"wcag_level": "AA", "color_contrast_ratio": 4.5, "keyboard_navigation": true, "screen_reader_support": true, "aria_labels_required": true, "focus_indicators": true}, "mobile_requirements": {"responsive_breakpoints": [320, 768, 1024, 1280], "touch_target_size": 44, "mobile_performance_target": 5000, "supported_devices": ["iPhone 12", "Samsung Galaxy S21", "iPad Pro"]}, "localization": {"supported_locales": ["de-CH", "fr-CH", "it-CH", "en-CH"], "currency": "CHF", "number_format": {"decimal_separator": ".", "thousands_separator": "'", "currency_symbol": "CHF"}, "date_format": "dd.mm.yyyy"}, "error_scenarios": {"network_timeout": {"description": "Network request timeout", "expected_behavior": "Show user-friendly error message"}, "invalid_input": {"description": "Invalid user input", "expected_behavior": "Show validation errors"}, "calculation_error": {"description": "Calculation engine error", "expected_behavior": "Show error message and recovery options"}}}