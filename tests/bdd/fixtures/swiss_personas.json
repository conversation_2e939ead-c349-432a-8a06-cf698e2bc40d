{"young_professional": {"name": "Young Professional", "age": 28, "profession": "Software Engineer", "canton": "Zurich", "financial_profile": {"monthly_income": 8500, "monthly_expenses": 5500, "current_savings": 50000, "target_retirement_age": 50, "risk_tolerance": "high", "investment_experience": "intermediate"}, "expected_results": {"fire_years": 18, "fire_number": 1650000, "savings_rate": 35, "monthly_savings": 2400}, "health_profile": {"status": "excellent", "annual_health_costs": 800, "chronic_conditions": false, "recommended_deductible": 2500}, "description": "Tech professional starting FIRE journey in expensive Zurich"}, "family_zurich": {"name": "Mid-Career Family", "age": 42, "profession": "Manager", "canton": "Geneva", "family_status": {"married": true, "children": 2, "spouse_income": 5000}, "financial_profile": {"monthly_income": 15000, "monthly_expenses": 10000, "current_savings": 350000, "target_retirement_age": 65, "risk_tolerance": "medium", "investment_experience": "advanced"}, "expected_results": {"fire_years": 15, "fire_number": 3000000, "savings_rate": 40, "monthly_savings": 4000}, "health_profile": {"status": "good", "annual_health_costs": 2800, "chronic_conditions": false, "recommended_deductible": 1500, "family_coverage": true}, "description": "Dual-income family with children in Geneva"}, "high_earner_zug": {"name": "High Earner Executive", "age": 35, "profession": "Executive", "canton": "<PERSON>ug", "financial_profile": {"monthly_income": 25000, "monthly_expenses": 10000, "current_savings": 800000, "target_retirement_age": 50, "risk_tolerance": "high", "investment_experience": "expert"}, "expected_results": {"fire_years": 7, "fire_number": 3000000, "savings_rate": 65, "monthly_savings": 12000}, "health_profile": {"status": "good", "annual_health_costs": 1500, "chronic_conditions": false, "recommended_deductible": 2500}, "tax_optimization": {"pillar3a_eligible": true, "wealth_tax_applicable": true, "optimization_potential": "high"}, "description": "High-income executive in low-tax Zug canton"}, "conservative_saver": {"name": "Conservative Saver", "age": 45, "profession": "Teacher", "canton": "Bern", "financial_profile": {"monthly_income": 12000, "monthly_expenses": 8000, "current_savings": 600000, "target_retirement_age": 60, "risk_tolerance": "low", "investment_experience": "beginner"}, "expected_results": {"fire_years": 12, "fire_number": 3200000, "savings_rate": 33, "monthly_savings": 3200}, "health_profile": {"status": "good", "annual_health_costs": 2200, "chronic_conditions": false, "recommended_deductible": 1000}, "investment_preferences": {"withdrawal_rate": 0.03, "bond_allocation": 0.6, "stock_allocation": 0.4}, "description": "Risk-averse saver approaching retirement"}, "startup_founder": {"name": "Startup Founder", "age": 32, "profession": "Entrepreneur", "canton": "<PERSON><PERSON>", "financial_profile": {"monthly_income": 6000, "monthly_expenses": 4500, "current_savings": 15000, "target_retirement_age": 55, "risk_tolerance": "very_high", "investment_experience": "intermediate", "variable_income": true}, "expected_results": {"fire_years": 25, "fire_number": 1350000, "savings_rate": 25, "monthly_savings": 1200}, "health_profile": {"status": "excellent", "annual_health_costs": 600, "chronic_conditions": false, "recommended_deductible": 2500}, "business_profile": {"self_employed": true, "business_expenses": 1000, "pillar3a_max": 35280}, "description": "Entrepreneur with variable income and equity upside"}, "senior_professional": {"name": "Senior Professional", "age": 55, "profession": "Doctor", "canton": "Basel", "financial_profile": {"monthly_income": 20000, "monthly_expenses": 12000, "current_savings": 1200000, "target_retirement_age": 65, "risk_tolerance": "medium", "investment_experience": "advanced"}, "expected_results": {"fire_years": 8, "fire_number": 3600000, "savings_rate": 40, "monthly_savings": 6400}, "health_profile": {"status": "fair", "annual_health_costs": 3500, "chronic_conditions": true, "recommended_deductible": 500, "specialist_visits": 8}, "description": "High-earning medical professional nearing retirement"}, "young_couple": {"name": "<PERSON>", "age": 30, "profession": "Engineers", "canton": "Aargau", "family_status": {"married": true, "children": 0, "planning_children": true}, "financial_profile": {"monthly_income": 14000, "monthly_expenses": 8000, "current_savings": 120000, "target_retirement_age": 55, "risk_tolerance": "high", "investment_experience": "intermediate"}, "expected_results": {"fire_years": 20, "fire_number": 2400000, "savings_rate": 43, "monthly_savings": 4800}, "health_profile": {"status": "excellent", "annual_health_costs": 1200, "chronic_conditions": false, "recommended_deductible": 2000}, "description": "Young dual-income couple planning for family"}, "minimal_income": {"name": "Minimal Income Worker", "age": 30, "profession": "Service Worker", "canton": "Aargau", "financial_profile": {"monthly_income": 4500, "monthly_expenses": 4200, "current_savings": 10000, "target_retirement_age": 65, "risk_tolerance": "low", "investment_experience": "none"}, "expected_results": {"fire_years": 45, "fire_number": 1260000, "savings_rate": 7, "monthly_savings": 240}, "health_profile": {"status": "good", "annual_health_costs": 1800, "chronic_conditions": false, "recommended_deductible": 1000, "subsidy_eligible": true}, "challenges": ["low_savings_rate", "long_timeline", "limited_optimization_options"], "description": "Low-income worker with minimal savings capacity"}, "impossible_scenario": {"name": "Overspender", "age": 35, "profession": "Marketing Manager", "canton": "Geneva", "financial_profile": {"monthly_income": 6000, "monthly_expenses": 6500, "current_savings": 20000, "target_retirement_age": 65, "risk_tolerance": "medium", "investment_experience": "beginner"}, "expected_results": {"fire_possible": false, "error_message": "FIRE not possible with current income and expenses", "recommendations": ["reduce_expenses", "increase_income", "lifestyle_changes"]}, "health_profile": {"status": "good", "annual_health_costs": 2500, "chronic_conditions": false, "recommended_deductible": 1500}, "description": "Scenario where expenses exceed income"}, "international_expat": {"name": "International Expat", "age": 38, "profession": "Consultant", "canton": "Zurich", "expat_status": {"home_country": "USA", "years_in_switzerland": 5, "tax_treaty_applicable": true}, "financial_profile": {"monthly_income": 18000, "monthly_expenses": 11000, "current_savings": 400000, "target_retirement_age": 60, "risk_tolerance": "high", "investment_experience": "expert"}, "expected_results": {"fire_years": 14, "fire_number": 3300000, "savings_rate": 39, "monthly_savings": 5600}, "health_profile": {"status": "good", "annual_health_costs": 2000, "chronic_conditions": false, "recommended_deductible": 1500}, "special_considerations": ["international_tax_planning", "currency_hedging", "repatriation_planning"], "description": "International expat with complex tax situation"}}