"""
Swiss Budget Pro BDD Test Environment Configuration
Manages test setup, teardown, and browser automation for Behave tests
"""

import json
import os
import logging
from pathlib import Path
from playwright.sync_api import sync_playwright
from utils.browser_manager import BrowserManager
from utils.data_helpers import DataHelpers
from utils.swiss_calculations import SwissCalculations

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def before_all(context):
    """
    Global setup before all tests
    """
    logger.info("🇨🇭 Initializing Swiss Budget Pro BDD Test Environment")
    
    # Load configuration from userdata
    context.config = {
        'browser': context.config.userdata.get('browser', 'chromium'),
        'headless': context.config.userdata.get('headless', 'false').lower() == 'true',
        'base_url': context.config.userdata.get('base_url', 'http://localhost:5173'),
        'timeout': int(context.config.userdata.get('timeout', '30')),
        'swiss_locale': context.config.userdata.get('swiss_locale', 'de-CH'),
        'test_env': context.config.userdata.get('test_env', 'local'),
    }
    
    logger.info(f"Configuration: {context.config}")
    
    # Initialize browser manager
    context.browser_manager = BrowserManager(context.config)
    
    # Load test data and fixtures
    context.data_helpers = DataHelpers()
    context.swiss_calculations = SwissCalculations()
    
    # Load Swiss personas and test data
    fixtures_path = Path(__file__).parent / 'fixtures'
    
    try:
        with open(fixtures_path / 'swiss_personas.json', 'r', encoding='utf-8') as f:
            context.personas = json.load(f)
        logger.info(f"Loaded {len(context.personas)} Swiss personas")
        
        with open(fixtures_path / 'financial_scenarios.json', 'r', encoding='utf-8') as f:
            context.financial_scenarios = json.load(f)
        logger.info(f"Loaded {len(context.financial_scenarios)} financial scenarios")
        
        with open(fixtures_path / 'test_data.json', 'r', encoding='utf-8') as f:
            context.test_data = json.load(f)
        logger.info("Loaded Swiss test data")
        
    except FileNotFoundError as e:
        logger.warning(f"Test data file not found: {e}")
        context.personas = {}
        context.financial_scenarios = {}
        context.test_data = {}
    
    # Initialize test results tracking
    context.test_results = {
        'passed': 0,
        'failed': 0,
        'skipped': 0,
        'scenarios': [],
        'swiss_specific_results': {
            'fire_calculations': [],
            'tax_optimizations': [],
            'healthcare_optimizations': [],
            'canton_comparisons': []
        }
    }
    
    # Create reports directory
    reports_dir = Path(__file__).parent / 'reports'
    reports_dir.mkdir(exist_ok=True)
    
    logger.info("✅ Swiss Budget Pro BDD environment initialized successfully")

def before_feature(context, feature):
    """
    Setup before each feature
    """
    logger.info(f"🎭 Starting feature: {feature.name}")
    
    # Tag-based setup
    if 'swiss' in feature.tags:
        logger.info("🇨🇭 Swiss-specific feature detected")
        context.swiss_mode = True
    
    if 'mobile' in feature.tags:
        logger.info("📱 Mobile feature detected")
        context.mobile_mode = True
    
    if 'performance' in feature.tags:
        logger.info("⚡ Performance feature detected")
        context.performance_mode = True

def before_scenario(context, scenario):
    """
    Setup before each scenario
    """
    logger.info(f"🎬 Starting scenario: {scenario.name}")
    
    # Initialize browser for scenario
    context.browser = context.browser_manager.get_browser()
    context.page = context.browser.new_page()
    
    # Configure page for Swiss Budget Pro
    context.page.set_default_timeout(context.config['timeout'] * 1000)
    context.page.set_viewport_size({"width": 1280, "height": 720})
    
    # Set Swiss locale if specified
    if context.config['swiss_locale']:
        context.page.set_extra_http_headers({
            'Accept-Language': f"{context.config['swiss_locale']},en;q=0.9"
        })
    
    # Initialize scenario-specific data
    context.scenario_data = {
        'name': scenario.name,
        'tags': scenario.tags,
        'start_time': None,
        'end_time': None,
        'screenshots': [],
        'calculations': {},
        'errors': []
    }
    
    # Tag-based scenario setup
    if 'critical' in scenario.tags:
        logger.info("🔥 Critical scenario - enabling detailed logging")
        context.detailed_logging = True

    if 'fire' in scenario.tags:
        logger.info("💰 FIRE calculation scenario")
        context.fire_mode = True

    if 'tax' in scenario.tags:
        logger.info("💸 Tax optimization scenario")
        context.tax_mode = True

    if 'healthcare' in scenario.tags:
        logger.info("🏥 Healthcare optimization scenario")
        context.healthcare_mode = True

    if 'premium' in scenario.tags:
        logger.info("💎 Premium features scenario")
        context.premium_mode = True

    if 'ai' in scenario.tags:
        logger.info("🤖 AI Financial Advisor scenario")
        context.ai_mode = True

    if 'risk' in scenario.tags:
        logger.info("⚠️ Risk Assessment scenario")
        context.risk_mode = True

    if 'withdrawal' in scenario.tags:
        logger.info("📊 Safe Withdrawal Rate scenario")
        context.withdrawal_mode = True

    if 'analytics' in scenario.tags:
        logger.info("📈 Analytics scenario")
        context.analytics_mode = True

    if 'relocation' in scenario.tags:
        logger.info("🏛️ Canton Relocation scenario")
        context.relocation_mode = True

def after_scenario(context, scenario):
    """
    Cleanup after each scenario
    """
    logger.info(f"🎬 Completed scenario: {scenario.name} - Status: {scenario.status}")
    
    # Update test results
    if scenario.status == 'passed':
        context.test_results['passed'] += 1
    elif scenario.status == 'failed':
        context.test_results['failed'] += 1
    else:
        context.test_results['skipped'] += 1
    
    # Take screenshot on failure
    if scenario.status == 'failed' and hasattr(context, 'page'):
        screenshot_path = f"reports/screenshots/failed_{scenario.name.replace(' ', '_')}.png"
        try:
            context.page.screenshot(path=screenshot_path, full_page=True)
            logger.info(f"📸 Screenshot saved: {screenshot_path}")
            context.scenario_data['screenshots'].append(screenshot_path)
        except Exception as e:
            logger.error(f"Failed to take screenshot: {e}")
    
    # Store scenario results
    context.test_results['scenarios'].append({
        'name': scenario.name,
        'status': scenario.status,
        'tags': scenario.tags,
        'data': context.scenario_data
    })
    
    # Close browser page
    if hasattr(context, 'page'):
        try:
            context.page.close()
        except Exception as e:
            logger.warning(f"Error closing page: {e}")
    
    # Reset scenario-specific flags
    context.detailed_logging = False
    context.fire_mode = False
    context.tax_mode = False
    context.healthcare_mode = False
    context.premium_mode = False
    context.ai_mode = False
    context.risk_mode = False
    context.withdrawal_mode = False
    context.analytics_mode = False
    context.relocation_mode = False

def after_feature(context, feature):
    """
    Cleanup after each feature
    """
    logger.info(f"🎭 Completed feature: {feature.name}")
    
    # Reset feature-specific flags
    context.swiss_mode = False
    context.mobile_mode = False
    context.performance_mode = False

def after_all(context):
    """
    Global cleanup after all tests
    """
    logger.info("🧹 Cleaning up Swiss Budget Pro BDD Test Environment")
    
    # Generate final test report
    try:
        report_data = {
            'summary': {
                'total': context.test_results['passed'] + context.test_results['failed'] + context.test_results['skipped'],
                'passed': context.test_results['passed'],
                'failed': context.test_results['failed'],
                'skipped': context.test_results['skipped'],
                'pass_rate': context.test_results['passed'] / max(1, context.test_results['passed'] + context.test_results['failed']) * 100
            },
            'scenarios': context.test_results['scenarios'],
            'swiss_specific': context.test_results['swiss_specific_results'],
            'configuration': context.config
        }
        
        with open('reports/bdd-summary.json', 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        logger.info("📊 Test summary report generated: reports/bdd-summary.json")
        
    except Exception as e:
        logger.error(f"Failed to generate test report: {e}")
    
    # Close browser
    if hasattr(context, 'browser_manager'):
        context.browser_manager.close_all()
    
    # Log final summary
    total_tests = context.test_results['passed'] + context.test_results['failed'] + context.test_results['skipped']
    logger.info(f"✅ BDD Test Summary: {context.test_results['passed']}/{total_tests} passed")
    
    if context.test_results['failed'] > 0:
        logger.warning(f"❌ {context.test_results['failed']} tests failed")
    
    logger.info("🇨🇭 Swiss Budget Pro BDD Test Environment cleanup completed")

def before_step(context, step):
    """
    Setup before each step (optional, for detailed logging)
    """
    if hasattr(context, 'detailed_logging') and context.detailed_logging:
        logger.debug(f"🔧 Executing step: {step.step_type} {step.name}")

def after_step(context, step):
    """
    Cleanup after each step (optional, for error handling)
    """
    if step.status == 'failed':
        logger.error(f"❌ Step failed: {step.step_type} {step.name}")
        if hasattr(context, 'scenario_data'):
            context.scenario_data['errors'].append({
                'step': f"{step.step_type} {step.name}",
                'error': str(step.exception) if step.exception else 'Unknown error'
            })
