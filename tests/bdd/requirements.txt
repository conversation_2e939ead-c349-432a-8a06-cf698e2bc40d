# Swiss Budget Pro BDD Testing Dependencies

# Core BDD framework
behave==1.2.6
behave-html-formatter==0.9.10

# Browser automation
playwright==1.40.0
selenium==4.15.2

# Data handling and validation
jsonschema==4.20.0
pydantic==2.5.0
faker==20.1.0

# Swiss-specific libraries
babel==2.13.1  # For Swiss localization
python-dateutil==2.8.2  # For Swiss date formats

# HTTP requests and API testing
requests==2.32.4
httpx==0.25.2

# Data analysis and calculations
numpy==1.25.2
pandas==2.1.4

# Configuration and environment
python-dotenv==1.0.0
pyyaml==6.0.1

# Reporting and logging
allure-behave==2.13.2
pytest-html==4.1.1
colorlog==6.8.0

# Image comparison for visual testing
pillow==10.3.0
opencv-python==********

# Swiss financial calculations
decimal==1.70  # For precise financial calculations

# Testing utilities
parameterized==0.9.0
freezegun==1.2.2  # For time-based testing

# Performance monitoring
psutil==5.9.6
memory-profiler==0.61.0

# Swiss data validation
schwifty==2023.9.1  # Swiss IBAN validation
phonenumbers==8.13.26  # Swiss phone number validation

# Development and debugging
ipdb==0.13.13
rich==13.7.0  # For beautiful console output

# CI/CD integration
pytest-xdist==3.5.0
coverage==7.3.2
