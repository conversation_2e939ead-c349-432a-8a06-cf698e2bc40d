#!/usr/bin/env python3
"""
Swiss Budget Pro BDD Test Runner
Comprehensive test execution script for Behave BDD tests
"""

import os
import sys
import argparse
import subprocess
import json
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SwissBDDTestRunner:
    """
    Swiss Budget Pro BDD Test Runner with comprehensive reporting and analysis
    """
    
    def __init__(self):
        self.test_suites = {
            'critical': [
                '--tags=@critical',
                'features/fire_calculation/',
                'features/swiss_tax/canton_comparison.feature',
                'features/healthcare/deductible_optimization.feature'
            ],
            'fire': [
                '--tags=@fire',
                'features/fire_calculation/'
            ],
            'tax': [
                '--tags=@tax',
                'features/swiss_tax/'
            ],
            'healthcare': [
                '--tags=@healthcare',
                'features/healthcare/'
            ],
            'mobile': [
                '--tags=@mobile',
                'features/'
            ],
            'accessibility': [
                '--tags=@accessibility',
                'features/'
            ],
            'swiss': [
                '--tags=@swiss',
                'features/'
            ],
            'smoke': [
                '--tags=@smoke',
                'features/'
            ],
            'regression': [
                '--tags=@regression',
                'features/'
            ],
            'all': [
                'features/'
            ]
        }
        
        self.browsers = ['chromium', 'firefox', 'webkit']
        self.environments = ['local', 'staging', 'production']
        
        # Create reports directory
        self.reports_dir = Path('reports')
        self.reports_dir.mkdir(exist_ok=True)
        
        # Create subdirectories
        (self.reports_dir / 'html').mkdir(exist_ok=True)
        (self.reports_dir / 'json').mkdir(exist_ok=True)
        (self.reports_dir / 'junit').mkdir(exist_ok=True)
        (self.reports_dir / 'screenshots').mkdir(exist_ok=True)
        (self.reports_dir / 'videos').mkdir(exist_ok=True)
    
    def run(self):
        """Main entry point for test runner"""
        parser = self.create_argument_parser()
        args = parser.parse_args()
        
        try:
            logger.info("🇨🇭 Swiss Budget Pro BDD Test Runner Starting")
            
            # Validate environment
            self.validate_environment()
            
            # Run tests
            results = self.run_test_suite(
                suite=args.suite,
                browser=args.browser,
                environment=args.environment,
                headless=args.headless,
                parallel=args.parallel,
                tags=args.tags,
                features=args.features
            )
            
            # Generate reports
            self.generate_comprehensive_report(results)
            
            # Analyze results
            self.analyze_results(results)
            
            # Exit with appropriate code
            sys.exit(0 if results['success'] else 1)
            
        except Exception as e:
            logger.error(f"❌ Test execution failed: {e}")
            sys.exit(1)
    
    def create_argument_parser(self) -> argparse.ArgumentParser:
        """Create command line argument parser"""
        parser = argparse.ArgumentParser(
            description='Swiss Budget Pro BDD Test Runner',
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
Examples:
  python run_bdd_tests.py critical                    # Run critical tests
  python run_bdd_tests.py fire --browser chromium     # Run FIRE tests on Chrome
  python run_bdd_tests.py all --headless              # Run all tests headless
  python run_bdd_tests.py --tags @swiss,@mobile       # Run Swiss mobile tests
  python run_bdd_tests.py --features fire_calculation # Run specific feature
            """
        )
        
        parser.add_argument(
            'suite',
            nargs='?',
            default='critical',
            choices=list(self.test_suites.keys()),
            help='Test suite to run (default: critical)'
        )
        
        parser.add_argument(
            '--browser',
            default='chromium',
            choices=self.browsers,
            help='Browser to use (default: chromium)'
        )
        
        parser.add_argument(
            '--environment',
            default='local',
            choices=self.environments,
            help='Environment to test (default: local)'
        )
        
        parser.add_argument(
            '--headless',
            action='store_true',
            help='Run tests in headless mode'
        )
        
        parser.add_argument(
            '--parallel',
            action='store_true',
            help='Run tests in parallel (experimental)'
        )
        
        parser.add_argument(
            '--tags',
            help='Comma-separated list of tags to run (e.g., @critical,@swiss)'
        )
        
        parser.add_argument(
            '--features',
            help='Specific feature directory or file to run'
        )
        
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Perform a dry run without executing tests'
        )
        
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Enable verbose output'
        )
        
        return parser
    
    def validate_environment(self):
        """Validate test environment setup"""
        logger.info("🔧 Validating test environment...")
        
        # Check Python dependencies
        try:
            import behave
            import playwright
            logger.info("✅ Python dependencies available")
        except ImportError as e:
            logger.error(f"❌ Missing Python dependency: {e}")
            raise
        
        # Check Playwright browsers
        try:
            result = subprocess.run(['playwright', 'install', '--dry-run'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                logger.info("✅ Playwright browsers available")
            else:
                logger.warning("⚠️ Some Playwright browsers may be missing")
        except FileNotFoundError:
            logger.error("❌ Playwright not found in PATH")
            raise
        
        # Check test data files
        required_files = [
            'fixtures/swiss_personas.json',
            'fixtures/test_data.json',
            'behave.ini'
        ]
        
        for file_path in required_files:
            if not Path(file_path).exists():
                logger.error(f"❌ Required file missing: {file_path}")
                raise FileNotFoundError(f"Required file missing: {file_path}")
        
        logger.info("✅ Environment validation completed")
    
    def run_test_suite(self, 
                      suite: str,
                      browser: str = 'chromium',
                      environment: str = 'local',
                      headless: bool = False,
                      parallel: bool = False,
                      tags: Optional[str] = None,
                      features: Optional[str] = None) -> Dict:
        """Run the specified test suite"""
        
        logger.info(f"🧪 Running {suite} test suite on {browser}")
        
        # Build behave command
        cmd = ['behave']
        
        # Add suite-specific arguments
        if features:
            cmd.extend([f'features/{features}'])
        elif tags:
            cmd.extend(['--tags', tags, 'features/'])
        else:
            cmd.extend(self.test_suites[suite])
        
        # Add format options
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        cmd.extend([
            '--format', 'pretty',
            '--format', f'json:reports/json/bdd-results-{timestamp}.json',
            '--format', f'html:reports/html/bdd-report-{timestamp}.html',
            '--format', f'junit:reports/junit/bdd-junit-{timestamp}.xml'
        ])
        
        # Add user data for configuration
        base_url = self.get_base_url(environment)
        cmd.extend([
            '-D', f'browser={browser}',
            '-D', f'headless={str(headless).lower()}',
            '-D', f'base_url={base_url}',
            '-D', f'test_env={environment}'
        ])
        
        # Add parallel execution if requested
        if parallel:
            cmd.extend(['--processes', '2'])
        
        # Execute tests
        start_time = datetime.now()
        
        try:
            logger.info(f"🚀 Executing: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=3600)
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            # Parse results
            results = {
                'success': result.returncode == 0,
                'return_code': result.returncode,
                'duration': duration,
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'stdout': result.stdout,
                'stderr': result.stderr,
                'suite': suite,
                'browser': browser,
                'environment': environment,
                'command': ' '.join(cmd)
            }
            
            logger.info(f"✅ Test execution completed in {duration:.2f}s")
            return results
            
        except subprocess.TimeoutExpired:
            logger.error("❌ Test execution timed out after 1 hour")
            return {
                'success': False,
                'error': 'Test execution timed out',
                'duration': 3600,
                'suite': suite,
                'browser': browser,
                'environment': environment
            }
        
        except Exception as e:
            logger.error(f"❌ Test execution failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'suite': suite,
                'browser': browser,
                'environment': environment
            }
    
    def get_base_url(self, environment: str) -> str:
        """Get base URL for the specified environment"""
        urls = {
            'local': 'http://localhost:5173',
            'staging': 'https://staging.swissbudgetpro.com',
            'production': 'https://swissbudgetpro.com'
        }
        return urls.get(environment, urls['local'])
    
    def generate_comprehensive_report(self, results: Dict):
        """Generate comprehensive test report"""
        logger.info("📊 Generating comprehensive test report...")
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # Load JSON results if available
        json_results = self.load_json_results()
        
        # Create comprehensive report
        report = {
            'metadata': {
                'timestamp': timestamp,
                'suite': results.get('suite'),
                'browser': results.get('browser'),
                'environment': results.get('environment'),
                'duration': results.get('duration'),
                'success': results.get('success')
            },
            'execution_summary': {
                'total_scenarios': 0,
                'passed_scenarios': 0,
                'failed_scenarios': 0,
                'skipped_scenarios': 0,
                'pass_rate': 0.0
            },
            'swiss_specific_results': {
                'fire_calculations': [],
                'tax_optimizations': [],
                'healthcare_optimizations': [],
                'canton_comparisons': []
            },
            'performance_metrics': {
                'average_scenario_duration': 0,
                'slowest_scenarios': [],
                'fastest_scenarios': []
            },
            'error_analysis': {
                'common_failures': [],
                'error_patterns': [],
                'recommendations': []
            }
        }
        
        # Analyze JSON results if available
        if json_results:
            report = self.analyze_json_results(json_results, report)
        
        # Save comprehensive report
        report_path = self.reports_dir / f'comprehensive-report-{timestamp}.json'
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # Generate markdown summary
        self.generate_markdown_summary(report, timestamp)
        
        logger.info(f"📊 Comprehensive report saved: {report_path}")
    
    def load_json_results(self) -> Optional[Dict]:
        """Load the most recent JSON results file"""
        try:
            json_files = list((self.reports_dir / 'json').glob('bdd-results-*.json'))
            if json_files:
                latest_file = max(json_files, key=lambda x: x.stat().st_mtime)
                with open(latest_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"⚠️ Could not load JSON results: {e}")
        return None
    
    def analyze_json_results(self, json_results: Dict, report: Dict) -> Dict:
        """Analyze JSON results and update report"""
        try:
            # Extract scenario statistics
            scenarios = json_results.get('elements', [])
            
            total = len(scenarios)
            passed = sum(1 for s in scenarios if s.get('status') == 'passed')
            failed = sum(1 for s in scenarios if s.get('status') == 'failed')
            skipped = sum(1 for s in scenarios if s.get('status') == 'skipped')
            
            report['execution_summary'].update({
                'total_scenarios': total,
                'passed_scenarios': passed,
                'failed_scenarios': failed,
                'skipped_scenarios': skipped,
                'pass_rate': (passed / total * 100) if total > 0 else 0
            })
            
            # Analyze Swiss-specific scenarios
            for scenario in scenarios:
                tags = scenario.get('tags', [])
                name = scenario.get('name', '')
                
                if any('fire' in tag.get('name', '') for tag in tags):
                    report['swiss_specific_results']['fire_calculations'].append({
                        'name': name,
                        'status': scenario.get('status'),
                        'duration': scenario.get('duration', 0)
                    })
                
                if any('tax' in tag.get('name', '') for tag in tags):
                    report['swiss_specific_results']['tax_optimizations'].append({
                        'name': name,
                        'status': scenario.get('status'),
                        'duration': scenario.get('duration', 0)
                    })
                
                if any('healthcare' in tag.get('name', '') for tag in tags):
                    report['swiss_specific_results']['healthcare_optimizations'].append({
                        'name': name,
                        'status': scenario.get('status'),
                        'duration': scenario.get('duration', 0)
                    })
            
        except Exception as e:
            logger.warning(f"⚠️ Error analyzing JSON results: {e}")
        
        return report
    
    def generate_markdown_summary(self, report: Dict, timestamp: str):
        """Generate markdown summary report"""
        summary = f"""# Swiss Budget Pro BDD Test Report

## Test Execution Summary
- **Timestamp**: {report['metadata']['timestamp']}
- **Suite**: {report['metadata']['suite']}
- **Browser**: {report['metadata']['browser']}
- **Environment**: {report['metadata']['environment']}
- **Duration**: {report['metadata']['duration']:.2f}s
- **Success**: {'✅ PASSED' if report['metadata']['success'] else '❌ FAILED'}

## Scenario Results
- **Total Scenarios**: {report['execution_summary']['total_scenarios']}
- **Passed**: {report['execution_summary']['passed_scenarios']}
- **Failed**: {report['execution_summary']['failed_scenarios']}
- **Skipped**: {report['execution_summary']['skipped_scenarios']}
- **Pass Rate**: {report['execution_summary']['pass_rate']:.1f}%

## Swiss-Specific Features
### FIRE Calculations
- Tests: {len(report['swiss_specific_results']['fire_calculations'])}

### Tax Optimizations
- Tests: {len(report['swiss_specific_results']['tax_optimizations'])}

### Healthcare Optimizations
- Tests: {len(report['swiss_specific_results']['healthcare_optimizations'])}

## Reports Generated
- **HTML Report**: reports/html/bdd-report-{timestamp}.html
- **JSON Results**: reports/json/bdd-results-{timestamp}.json
- **JUnit XML**: reports/junit/bdd-junit-{timestamp}.xml
- **Comprehensive Report**: reports/comprehensive-report-{timestamp}.json

---
Generated by Swiss Budget Pro BDD Test Runner
"""
        
        summary_path = self.reports_dir / f'test-summary-{timestamp}.md'
        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write(summary)
        
        logger.info(f"📝 Markdown summary saved: {summary_path}")
    
    def analyze_results(self, results: Dict):
        """Analyze test results and provide recommendations"""
        logger.info("🔍 Analyzing test results...")
        
        if results['success']:
            logger.info("✅ All tests passed successfully!")
        else:
            logger.warning("❌ Some tests failed")
            
            if 'stderr' in results and results['stderr']:
                logger.error(f"Error output: {results['stderr']}")
        
        # Performance analysis
        duration = results.get('duration', 0)
        if duration > 300:  # 5 minutes
            logger.warning(f"⚠️ Test execution took {duration:.2f}s - consider optimization")
        elif duration < 60:  # 1 minute
            logger.info(f"⚡ Fast test execution: {duration:.2f}s")
        
        # Recommendations
        recommendations = []
        
        if not results['success']:
            recommendations.append("Review failed scenarios and fix issues")
            recommendations.append("Check application logs for errors")
            recommendations.append("Verify test data and environment setup")
        
        if duration > 300:
            recommendations.append("Consider running tests in parallel")
            recommendations.append("Optimize slow test scenarios")
            recommendations.append("Use headless mode for faster execution")
        
        if recommendations:
            logger.info("💡 Recommendations:")
            for rec in recommendations:
                logger.info(f"  - {rec}")

if __name__ == '__main__':
    runner = SwissBDDTestRunner()
    runner.run()
