"""
Step definitions for Swiss Budget Pro FIRE calculation BDD tests
"""

import time
import logging
from behave import given, when, then, step
from playwright.sync_api import expect
from pages.fire_calculator_page import FireCalculatorPage
from utils.swiss_calculations import SwissCalculations

logger = logging.getLogger(__name__)

# Given steps - Setup and preconditions

@given('I am on the Swiss Budget Pro homepage')
def step_navigate_to_homepage(context):
    """Navigate to the Swiss Budget Pro homepage"""
    context.browser_manager.navigate_to_app(context.page)
    context.fire_page = FireCalculatorPage(context.page)

@given('the application has loaded successfully')
def step_verify_app_loaded(context):
    """Verify that the application has loaded successfully"""
    context.fire_page.wait_for_page_load()
    assert context.fire_page.is_page_loaded(), "Swiss Budget Pro application did not load properly"

@given('I can see the main calculator interface')
def step_verify_calculator_interface(context):
    """Verify the main calculator interface is visible"""
    assert context.fire_page.is_calculator_visible(), "Main calculator interface is not visible"

@given('I am a {age:d}-year-old {profession} in {canton}')
def step_set_user_profile(context, age, profession, canton):
    """Set user profile for the test scenario"""
    context.user_profile = {
        'age': age,
        'profession': profession,
        'canton': canton
    }
    logger.info(f"User profile set: {age}-year-old {profession} in {canton}")

@given('I am using a mobile device')
def step_set_mobile_context(context):
    """Switch to mobile browser context"""
    mobile_context = context.browser_manager.create_mobile_context()
    context.page = mobile_context.new_page()
    context.fire_page = FireCalculatorPage(context.page)
    context.browser_manager.navigate_to_app(context.page)

@given('I am using a screen reader')
def step_set_accessibility_context(context):
    """Switch to accessibility-optimized browser context"""
    a11y_context = context.browser_manager.create_accessibility_context()
    context.page = a11y_context.new_page()
    context.fire_page = FireCalculatorPage(context.page)
    context.browser_manager.navigate_to_app(context.page)

# When steps - Actions and interactions

@when('I enter the following financial information')
def step_enter_financial_information(context):
    """Enter financial information from a data table"""
    for row in context.table:
        field = row['Field']
        value = row['Value']
        
        if field == 'Monthly Income':
            context.fire_page.enter_monthly_income(int(value))
        elif field == 'Monthly Expenses':
            context.fire_page.enter_monthly_expenses(int(value))
        elif field == 'Current Savings':
            context.fire_page.enter_current_savings(int(value))
        elif field == 'Canton':
            context.fire_page.select_canton(value)
        elif field == 'Age':
            context.fire_page.enter_age(int(value))
        elif field == 'Target Retirement Age':
            context.fire_page.enter_retirement_age(int(value))
        
        # Small delay to allow for real-time updates
        time.sleep(0.1)

@when('I enter my monthly income of {amount:d} CHF')
def step_enter_monthly_income(context, amount):
    """Enter monthly income amount"""
    context.fire_page.enter_monthly_income(amount)
    context.scenario_data['monthly_income'] = amount

@when('I enter my monthly expenses of {amount:d} CHF')
def step_enter_monthly_expenses(context, amount):
    """Enter monthly expenses amount"""
    context.fire_page.enter_monthly_expenses(amount)
    context.scenario_data['monthly_expenses'] = amount

@when('I enter my current savings of {amount:d} CHF')
def step_enter_current_savings(context, amount):
    """Enter current savings amount"""
    context.fire_page.enter_current_savings(amount)
    context.scenario_data['current_savings'] = amount

@when('I select "{canton}" as my canton')
def step_select_canton(context, canton):
    """Select Swiss canton"""
    context.fire_page.select_canton(canton)
    context.scenario_data['canton'] = canton

@when('I select a conservative withdrawal rate of {rate:d}%')
def step_select_withdrawal_rate(context, rate):
    """Select a specific withdrawal rate"""
    withdrawal_rate = rate / 100.0
    context.fire_page.select_withdrawal_rate(withdrawal_rate)
    context.scenario_data['withdrawal_rate'] = withdrawal_rate

@when('I click the calculate FIRE button')
def step_click_calculate_fire(context):
    """Click the calculate FIRE button"""
    start_time = time.time()
    context.fire_page.click_calculate_button()
    context.browser_manager.wait_for_calculation(context.page)
    context.scenario_data['calculation_time'] = time.time() - start_time

@when('I change my monthly expenses to {amount:d} CHF')
def step_change_monthly_expenses(context, amount):
    """Change monthly expenses to test real-time updates"""
    context.fire_page.enter_monthly_expenses(amount)
    context.scenario_data['updated_expenses'] = amount

@when('I enter invalid financial information')
def step_enter_invalid_information(context):
    """Enter invalid financial information for validation testing"""
    for row in context.table:
        field = row['Field']
        value = row['Value']
        
        if field == 'Monthly Income':
            context.fire_page.enter_monthly_income_text(value)  # Enter text instead of number
        elif field == 'Monthly Expenses':
            context.fire_page.enter_monthly_expenses_text(value)
        elif field == 'Current Savings':
            context.fire_page.enter_current_savings_text(value)
        elif field == 'Canton':
            context.fire_page.select_canton_invalid(value)

@when('I attempt to calculate FIRE')
def step_attempt_calculate_fire(context):
    """Attempt to calculate FIRE (may fail due to validation)"""
    try:
        context.fire_page.click_calculate_button()
    except Exception as e:
        context.scenario_data['calculation_error'] = str(e)

@when('I navigate through the form using keyboard only')
def step_navigate_keyboard_only(context):
    """Navigate through the form using only keyboard"""
    context.fire_page.navigate_with_keyboard()

@when('I enter financial information using keyboard input')
def step_enter_info_keyboard(context):
    """Enter financial information using keyboard input"""
    context.fire_page.enter_data_with_keyboard({
        'income': 8000,
        'expenses': 5000,
        'savings': 100000,
        'canton': 'Zurich'
    })

@when('I activate the calculate button with Enter key')
def step_activate_calculate_enter(context):
    """Activate calculate button using Enter key"""
    context.fire_page.activate_calculate_with_enter()

# Then steps - Assertions and verifications

@then('I should see my FIRE timeline is approximately {years:d} years')
def step_verify_fire_timeline(context, years):
    """Verify FIRE timeline is approximately the expected years"""
    actual_years = context.fire_page.get_fire_years()
    tolerance = 1  # Allow 1 year tolerance
    
    assert abs(actual_years - years) <= tolerance, \
        f"Expected FIRE timeline ~{years} years, got {actual_years} years"
    
    context.scenario_data['fire_years'] = actual_years
    logger.info(f"✅ FIRE timeline verified: {actual_years} years (expected ~{years})")

@then('I should see my FIRE timeline is less than {years:d} years')
def step_verify_fire_timeline_less_than(context, years):
    """Verify FIRE timeline is less than specified years"""
    actual_years = context.fire_page.get_fire_years()
    
    assert actual_years < years, \
        f"Expected FIRE timeline < {years} years, got {actual_years} years"
    
    context.scenario_data['fire_years'] = actual_years
    logger.info(f"✅ FIRE timeline verified: {actual_years} years (< {years})")

@then('I should see my FIRE number is approximately {amount:d} CHF')
def step_verify_fire_number(context, amount):
    """Verify FIRE number is approximately the expected amount"""
    actual_amount = context.fire_page.get_fire_number()
    tolerance = 0.1  # 10% tolerance
    
    assert abs(actual_amount - amount) / amount <= tolerance, \
        f"Expected FIRE number ~{amount} CHF, got {actual_amount} CHF"
    
    context.scenario_data['fire_number'] = actual_amount
    logger.info(f"✅ FIRE number verified: {actual_amount} CHF (expected ~{amount})")

@then('I should see my savings rate is approximately {rate:d}%')
def step_verify_savings_rate(context, rate):
    """Verify savings rate is approximately the expected percentage"""
    actual_rate = context.fire_page.get_savings_rate()
    tolerance = 2  # 2 percentage points tolerance
    
    assert abs(actual_rate - rate) <= tolerance, \
        f"Expected savings rate ~{rate}%, got {actual_rate}%"
    
    context.scenario_data['savings_rate'] = actual_rate
    logger.info(f"✅ Savings rate verified: {actual_rate}% (expected ~{rate}%)")

@then('I should see my savings rate is greater than {rate:d}%')
def step_verify_savings_rate_greater(context, rate):
    """Verify savings rate is greater than specified percentage"""
    actual_rate = context.fire_page.get_savings_rate()
    
    assert actual_rate > rate, \
        f"Expected savings rate > {rate}%, got {actual_rate}%"
    
    context.scenario_data['savings_rate'] = actual_rate
    logger.info(f"✅ Savings rate verified: {actual_rate}% (> {rate}%)")

@then('I should see my monthly savings is approximately {amount:d} CHF')
def step_verify_monthly_savings(context, amount):
    """Verify monthly savings amount"""
    actual_amount = context.fire_page.get_monthly_savings()
    tolerance = 100  # 100 CHF tolerance
    
    assert abs(actual_amount - amount) <= tolerance, \
        f"Expected monthly savings ~{amount} CHF, got {actual_amount} CHF"
    
    context.scenario_data['monthly_savings'] = actual_amount
    logger.info(f"✅ Monthly savings verified: {actual_amount} CHF (expected ~{amount})")

@then('the calculation should complete within {seconds:d} seconds')
def step_verify_calculation_time(context, seconds):
    """Verify calculation completes within specified time"""
    calculation_time = context.scenario_data.get('calculation_time', 0)
    
    assert calculation_time <= seconds, \
        f"Calculation took {calculation_time:.2f}s, expected ≤ {seconds}s"
    
    logger.info(f"✅ Calculation time verified: {calculation_time:.2f}s (≤ {seconds}s)")

@then('I should see tax optimization recommendations')
def step_verify_tax_recommendations(context):
    """Verify tax optimization recommendations are shown"""
    assert context.fire_page.has_tax_recommendations(), \
        "Tax optimization recommendations not found"
    
    recommendations = context.fire_page.get_tax_recommendations()
    context.scenario_data['tax_recommendations'] = recommendations
    logger.info("✅ Tax optimization recommendations verified")

@then('I should see Pillar 3a contribution suggestions')
def step_verify_pillar3a_suggestions(context):
    """Verify Pillar 3a contribution suggestions are shown"""
    assert context.fire_page.has_pillar3a_suggestions(), \
        "Pillar 3a contribution suggestions not found"
    
    suggestions = context.fire_page.get_pillar3a_suggestions()
    context.scenario_data['pillar3a_suggestions'] = suggestions
    logger.info("✅ Pillar 3a suggestions verified")

@then('I should see significant tax optimization opportunities')
def step_verify_significant_tax_opportunities(context):
    """Verify significant tax optimization opportunities are highlighted"""
    assert context.fire_page.has_significant_tax_opportunities(), \
        "Significant tax optimization opportunities not found"
    
    opportunities = context.fire_page.get_tax_opportunities()
    context.scenario_data['tax_opportunities'] = opportunities
    logger.info("✅ Significant tax optimization opportunities verified")

@then('I should see an error message "{message}"')
def step_verify_error_message(context, message):
    """Verify specific error message is displayed"""
    assert context.fire_page.has_error_message(message), \
        f"Expected error message '{message}' not found"
    
    logger.info(f"✅ Error message verified: {message}")

@then('I should see validation errors for each invalid field')
def step_verify_validation_errors(context):
    """Verify validation errors are shown for invalid fields"""
    assert context.fire_page.has_validation_errors(), \
        "Validation errors not found for invalid inputs"
    
    errors = context.fire_page.get_validation_errors()
    context.scenario_data['validation_errors'] = errors
    logger.info(f"✅ Validation errors verified: {len(errors)} errors found")

@then('I should see "{error_text}" error')
def step_verify_specific_error(context, error_text):
    """Verify specific error text is displayed"""
    assert context.fire_page.has_specific_error(error_text), \
        f"Expected error text '{error_text}' not found"
    
    logger.info(f"✅ Specific error verified: {error_text}")

@then('the calculate button should remain disabled until errors are fixed')
def step_verify_button_disabled(context):
    """Verify calculate button is disabled when there are validation errors"""
    assert context.fire_page.is_calculate_button_disabled(), \
        "Calculate button should be disabled with validation errors"
    
    logger.info("✅ Calculate button disabled state verified")

@then('I should see the FIRE calculation update automatically')
def step_verify_automatic_update(context):
    """Verify FIRE calculation updates automatically"""
    assert context.fire_page.has_automatic_updates(), \
        "Automatic FIRE calculation updates not working"
    
    logger.info("✅ Automatic calculation updates verified")

@then('the updates should happen within {seconds:d} second of input change')
def step_verify_update_speed(context, seconds):
    """Verify updates happen within specified time"""
    update_time = context.fire_page.measure_update_time()
    
    assert update_time <= seconds, \
        f"Updates took {update_time:.2f}s, expected ≤ {seconds}s"
    
    logger.info(f"✅ Update speed verified: {update_time:.2f}s (≤ {seconds}s)")

@then('I should see my FIRE results displayed clearly on mobile')
def step_verify_mobile_results(context):
    """Verify FIRE results are displayed clearly on mobile"""
    assert context.fire_page.are_results_mobile_friendly(), \
        "FIRE results not properly displayed on mobile"
    
    logger.info("✅ Mobile FIRE results display verified")

@then('the interface should be responsive and touch-friendly')
def step_verify_mobile_interface(context):
    """Verify interface is responsive and touch-friendly"""
    assert context.fire_page.is_interface_touch_friendly(), \
        "Interface is not touch-friendly"
    
    logger.info("✅ Touch-friendly interface verified")

@then('I should hear the FIRE results announced clearly')
def step_verify_screen_reader_results(context):
    """Verify FIRE results are announced clearly by screen reader"""
    assert context.fire_page.are_results_screen_reader_accessible(), \
        "FIRE results not properly accessible to screen readers"
    
    logger.info("✅ Screen reader accessibility verified")

@then('all form fields should have proper labels')
def step_verify_form_labels(context):
    """Verify all form fields have proper labels for accessibility"""
    assert context.fire_page.have_all_fields_proper_labels(), \
        "Some form fields missing proper labels"
    
    logger.info("✅ Form field labels verified")

# Additional utility steps

@step('I wait for {seconds:d} seconds')
def step_wait_seconds(context, seconds):
    """Wait for specified number of seconds"""
    time.sleep(seconds)

@step('I take a screenshot named "{name}"')
def step_take_screenshot(context, name):
    """Take a screenshot for debugging"""
    screenshot_path = context.browser_manager.take_screenshot(context.page, name)
    context.scenario_data['screenshots'].append(screenshot_path)

@step('I verify Swiss locale is applied')
def step_verify_swiss_locale(context):
    """Verify Swiss locale settings are applied"""
    assert context.browser_manager.verify_swiss_locale(context.page), \
        "Swiss locale not properly applied"
    
    logger.info("✅ Swiss locale verified")
