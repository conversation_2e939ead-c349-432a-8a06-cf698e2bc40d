# Swiss-Specific Testing Guide

This guide focuses on testing Swiss financial regulations, calculations, and compliance requirements that are unique to the Swiss Budget Pro application.

## Overview

Swiss Budget Pro requires specialized testing approaches due to the complexity of Swiss financial regulations, tax systems, and healthcare requirements. This guide covers testing strategies for Swiss-specific features.

```{admonition} 🇨🇭 Swiss Testing Focus Areas
:class: tip

**26 Cantonal Tax Systems**: Comprehensive testing across all Swiss cantons
**Healthcare Premium Data**: Real 2024 premium validation for all insurers
**Pillar 3a Regulations**: Retirement savings optimization compliance
**BVG Integration**: Occupational pension calculations
**Swiss Economic Data**: SNB and SIX market data integration
**Regulatory Compliance**: FINMA and data protection requirements
```

## Swiss Tax Engine Testing

### Cantonal Tax System Validation

```typescript
// test/swiss/tax-engine-comprehensive.test.ts
import { describe, test, expect } from 'vitest';
import { SwissTaxEngine } from '../../src/engines/SwissTaxEngine';

describe('Swiss Tax Engine - All Cantons', () => {
  const swissCantons = [
    { code: 'AG', name: 'Aargau', multiplier: 0.96 },
    { code: 'AI', name: 'Appenzell Innerrhoden', multiplier: 0.78 },
    { code: 'AR', name: 'Appenzell Ausserrhoden', multiplier: 0.87 },
    { code: 'BE', name: 'Bern', multiplier: 1.24 },
    { code: 'BL', name: 'Basel-Landschaft', multiplier: 1.12 },
    { code: 'BS', name: 'Basel-Stadt', multiplier: 1.19 },
    { code: 'FR', name: 'Fribourg', multiplier: 1.15 },
    { code: 'GE', name: 'Geneva', multiplier: 1.45 },
    { code: 'GL', name: 'Glarus', multiplier: 0.89 },
    { code: 'GR', name: 'Graubünden', multiplier: 0.92 },
    { code: 'JU', name: 'Jura', multiplier: 1.38 },
    { code: 'LU', name: 'Lucerne', multiplier: 0.95 },
    { code: 'NE', name: 'Neuchâtel', multiplier: 1.42 },
    { code: 'NW', name: 'Nidwalden', multiplier: 0.82 },
    { code: 'OW', name: 'Obwalden', multiplier: 0.85 },
    { code: 'SG', name: 'St. Gallen', multiplier: 1.02 },
    { code: 'SH', name: 'Schaffhausen', multiplier: 0.98 },
    { code: 'SO', name: 'Solothurn', multiplier: 1.08 },
    { code: 'SZ', name: 'Schwyz', multiplier: 0.79 },
    { code: 'TG', name: 'Thurgau', multiplier: 0.91 },
    { code: 'TI', name: 'Ticino', multiplier: 1.25 },
    { code: 'UR', name: 'Uri', multiplier: 0.88 },
    { code: 'VD', name: 'Vaud', multiplier: 1.38 },
    { code: 'VS', name: 'Valais', multiplier: 1.18 },
    { code: 'ZG', name: 'Zug', multiplier: 0.65 },
    { code: 'ZH', name: 'Zurich', multiplier: 1.00 }
  ];

  describe('Federal Tax Calculation (Uniform Across Cantons)', () => {
    const federalTaxTestCases = [
      { income: 31600, expectedTax: 0 },
      { income: 50000, expectedTax: 77 },
      { income: 100000, expectedTax: 2103 },
      { income: 150000, expectedTax: 4851 },
      { income: 200000, expectedTax: 8513 },
      { income: 300000, expectedTax: 18513 }
    ];

    federalTaxTestCases.forEach(({ income, expectedTax }) => {
      test(`calculates federal tax for CHF ${income}`, () => {
        // Test with multiple cantons to ensure federal tax is consistent
        const testCantons = ['ZH', 'GE', 'ZG', 'BS'];
        
        testCantons.forEach(canton => {
          const result = SwissTaxEngine.calculateTax({
            income,
            canton,
            civilStatus: 'single',
            year: 2024
          });
          
          expect(result.federalTax).toBeCloseTo(expectedTax, 2);
        });
      });
    });
  });

  describe('Cantonal Tax Variations', () => {
    swissCantons.forEach(({ code, name, multiplier }) => {
      test(`calculates ${name} (${code}) cantonal tax correctly`, () => {
        const testIncome = 100000;
        const result = SwissTaxEngine.calculateTax({
          income: testIncome,
          canton: code,
          civilStatus: 'single',
          year: 2024
        });

        // Verify basic tax structure
        expect(result.federalTax).toBeGreaterThan(0);
        expect(result.cantonalTax).toBeGreaterThan(0);
        expect(result.totalTax).toBe(result.federalTax + result.cantonalTax + result.municipalTax);
        
        // Verify cantonal tax reflects the multiplier (approximate)
        const expectedCantonalRange = {
          min: 5000 * multiplier * 0.8, // Allow 20% variance
          max: 5000 * multiplier * 1.2
        };
        
        expect(result.cantonalTax).toBeGreaterThan(expectedCantonalRange.min);
        expect(result.cantonalTax).toBeLessThan(expectedCantonalRange.max);
        
        // Sanity check: total effective rate should be reasonable
        expect(result.effectiveRate).toBeGreaterThan(0.05); // At least 5%
        expect(result.effectiveRate).toBeLessThan(0.35); // At most 35%
      });
    });
  });

  describe('Civil Status Impact', () => {
    const civilStatusTests = [
      { status: 'single', multiplier: 1.0 },
      { status: 'married', multiplier: 0.85 }, // Joint taxation benefit
      { status: 'married_separate', multiplier: 1.0 }
    ];

    civilStatusTests.forEach(({ status, multiplier }) => {
      test(`calculates tax correctly for ${status} status`, () => {
        const result = SwissTaxEngine.calculateTax({
          income: 120000,
          canton: 'ZH',
          civilStatus: status as any,
          year: 2024
        });

        expect(result.totalTax).toBeGreaterThan(0);
        
        // Married couples should generally pay less tax
        if (status === 'married') {
          const singleResult = SwissTaxEngine.calculateTax({
            income: 120000,
            canton: 'ZH',
            civilStatus: 'single',
            year: 2024
          });
          
          expect(result.totalTax).toBeLessThan(singleResult.totalTax);
        }
      });
    });
  });
});
```

### Wealth Tax Testing

```typescript
// test/swiss/wealth-tax.test.ts
describe('Swiss Wealth Tax', () => {
  const wealthTaxCantons = [
    { canton: 'ZH', threshold: 0, rate: 0.002 },
    { canton: 'GE', threshold: 0, rate: 0.005 },
    { canton: 'VD', threshold: 25000, rate: 0.003 },
    { canton: 'ZG', threshold: 0, rate: 0.001 }
  ];

  wealthTaxCantons.forEach(({ canton, threshold, rate }) => {
    test(`calculates wealth tax for ${canton}`, () => {
      const wealth = 1000000; // CHF 1M
      
      const result = SwissTaxEngine.calculateWealthTax({
        wealth,
        canton,
        civilStatus: 'single',
        year: 2024
      });

      if (wealth > threshold) {
        const expectedTax = (wealth - threshold) * rate;
        expect(result.wealthTax).toBeCloseTo(expectedTax, 100);
      } else {
        expect(result.wealthTax).toBe(0);
      }
    });
  });
});
```

## Healthcare Cost Optimizer Testing

### Premium Data Validation

```typescript
// test/swiss/healthcare-premiums.test.ts
import { describe, test, expect } from 'vitest';
import { HealthcareCostOptimizer } from '../../src/engines/HealthcareCostOptimizer';

describe('Swiss Healthcare Premium Data', () => {
  const swissCantons = ['ZH', 'BE', 'VD', 'AG', 'SG', 'LU', 'TI', 'VS', 'BS', 'GE'];
  const ageGroups = [25, 35, 45, 55, 65];
  const deductibles = [300, 500, 1000, 1500, 2000, 2500];

  describe('Premium Data Completeness', () => {
    swissCantons.forEach(canton => {
      test(`has complete premium data for ${canton}`, () => {
        ageGroups.forEach(age => {
          const premiums = HealthcareCostOptimizer.getPremiumData(canton, age);
          
          expect(premiums).toBeDefined();
          expect(premiums.length).toBeGreaterThan(5); // At least 5 insurers
          
          premiums.forEach(premium => {
            expect(premium).toHaveProperty('insurer');
            expect(premium).toHaveProperty('premium');
            expect(premium).toHaveProperty('deductible');
            expect(premium.premium).toBeGreaterThan(200); // Minimum realistic premium
            expect(premium.premium).toBeLessThan(800); // Maximum realistic premium
          });
        });
      });
    });
  });

  describe('Deductible Impact on Premiums', () => {
    test('higher deductibles result in lower premiums', () => {
      const canton = 'ZH';
      const age = 30;
      
      const premiums300 = HealthcareCostOptimizer.getPremiumData(canton, age, 300);
      const premiums2500 = HealthcareCostOptimizer.getPremiumData(canton, age, 2500);
      
      // Compare same insurer across deductibles
      const insurer = premiums300[0].insurer;
      const premium300 = premiums300.find(p => p.insurer === insurer)?.premium;
      const premium2500 = premiums2500.find(p => p.insurer === insurer)?.premium;
      
      expect(premium2500).toBeLessThan(premium300);
      
      // Savings should be reasonable (CHF 100-200 per month)
      const monthlySavings = premium300 - premium2500;
      expect(monthlySavings).toBeGreaterThan(100);
      expect(monthlySavings).toBeLessThan(250);
    });
  });

  describe('Cantonal Premium Variations', () => {
    test('premium variations across cantons are realistic', () => {
      const age = 30;
      const deductible = 300;
      
      const cantonPremiums = swissCantons.map(canton => {
        const premiums = HealthcareCostOptimizer.getPremiumData(canton, age, deductible);
        return {
          canton,
          averagePremium: premiums.reduce((sum, p) => sum + p.premium, 0) / premiums.length
        };
      });
      
      // Sort by premium
      cantonPremiums.sort((a, b) => a.averagePremium - b.averagePremium);
      
      // Verify expected patterns
      const cheapest = cantonPremiums[0];
      const mostExpensive = cantonPremiums[cantonPremiums.length - 1];
      
      // AI and NW should be among cheapest
      expect(['AI', 'NW', 'UR'].includes(cheapest.canton)).toBe(true);
      
      // BS, GE, VD should be among most expensive
      expect(['BS', 'GE', 'VD'].includes(mostExpensive.canton)).toBe(true);
      
      // Price difference should be significant but realistic
      const priceDifference = mostExpensive.averagePremium - cheapest.averagePremium;
      expect(priceDifference).toBeGreaterThan(100); // At least CHF 100 difference
      expect(priceDifference).toBeLessThan(300); // At most CHF 300 difference
    });
  });
});
```

### Deductible Optimization Testing

```typescript
// test/swiss/deductible-optimization.test.ts
describe('Healthcare Deductible Optimization', () => {
  const riskProfiles = [
    { profile: 'low', expectedCosts: 500, recommendedDeductible: 2500 },
    { profile: 'medium', expectedCosts: 1500, recommendedDeductible: 1000 },
    { profile: 'high', expectedCosts: 3000, recommendedDeductible: 300 }
  ];

  riskProfiles.forEach(({ profile, expectedCosts, recommendedDeductible }) => {
    test(`recommends correct deductible for ${profile} risk profile`, () => {
      const result = HealthcareCostOptimizer.optimizeDeductible({
        age: 30,
        canton: 'ZH',
        riskProfile: profile as any,
        expectedAnnualCosts: expectedCosts
      });

      expect(result.recommendedDeductible).toBe(recommendedDeductible);
      expect(result.annualSavings).toBeGreaterThan(0);
      expect(result.confidence).toBeGreaterThan(0.7); // At least 70% confidence
    });
  });

  test('calculates break-even analysis correctly', () => {
    const result = HealthcareCostOptimizer.calculateBreakEven({
      canton: 'ZH',
      age: 30,
      currentDeductible: 300,
      proposedDeductible: 2500
    });

    expect(result.breakEvenCosts).toBeGreaterThan(2000);
    expect(result.annualPremiumSavings).toBeGreaterThan(1000);
    expect(result.additionalRisk).toBe(2200); // 2500 - 300
  });
});
```

## Pillar 3a Testing

### Contribution Limits and Tax Benefits

```typescript
// test/swiss/pillar-3a.test.ts
import { describe, test, expect } from 'vitest';
import { Pillar3aOptimizer } from '../../src/engines/Pillar3aOptimizer';

describe('Pillar 3a Optimization', () => {
  describe('Contribution Limits (2024)', () => {
    test('enforces correct limits for employed persons', () => {
      const result = Pillar3aOptimizer.calculateOptimalContribution({
        employmentStatus: 'employed',
        annualIncome: 100000,
        currentContribution: 10000,
        canton: 'ZH',
        marginalTaxRate: 0.25
      });

      expect(result.maxContribution).toBe(7056); // 2024 limit for employed
      expect(result.recommendedContribution).toBeLessThanOrEqual(7056);
    });

    test('enforces correct limits for self-employed persons', () => {
      const result = Pillar3aOptimizer.calculateOptimalContribution({
        employmentStatus: 'self_employed',
        annualIncome: 100000,
        currentContribution: 20000,
        canton: 'ZH',
        marginalTaxRate: 0.25,
        bvgContributions: 0 // No BVG for self-employed
      });

      // 20% of income or CHF 35,280, whichever is lower
      const expectedLimit = Math.min(100000 * 0.2, 35280);
      expect(result.maxContribution).toBe(expectedLimit);
    });
  });

  describe('Tax Savings Calculation', () => {
    const testCases = [
      { canton: 'ZH', income: 80000, contribution: 7056, expectedSavings: 1764 },
      { canton: 'GE', income: 120000, contribution: 7056, expectedSavings: 2469 },
      { canton: 'ZG', income: 150000, contribution: 7056, expectedSavings: 1694 }
    ];

    testCases.forEach(({ canton, income, contribution, expectedSavings }) => {
      test(`calculates tax savings for ${canton}`, () => {
        const result = Pillar3aOptimizer.calculateTaxSavings({
          canton,
          annualIncome: income,
          contribution,
          civilStatus: 'single'
        });

        expect(result.totalSavings).toBeCloseTo(expectedSavings, 100);
        expect(result.federalSavings).toBeGreaterThan(0);
        expect(result.cantonalSavings).toBeGreaterThan(0);
        expect(result.effectiveRate).toBeGreaterThan(0.15); // At least 15% savings
      });
    });
  });

  describe('Withdrawal Strategy Optimization', () => {
    test('optimizes withdrawal timing for tax efficiency', () => {
      const accounts = [
        { balance: 100000, provider: 'UBS', years: 5 },
        { balance: 80000, provider: 'Credit Suisse', years: 3 },
        { balance: 120000, provider: 'Viac', years: 7 }
      ];

      const result = Pillar3aOptimizer.optimizeWithdrawalStrategy({
        accounts,
        retirementAge: 65,
        currentAge: 58,
        canton: 'ZH',
        expectedRetirementIncome: 60000
      });

      expect(result.withdrawalPlan).toHaveLength(3);
      expect(result.totalTaxSavings).toBeGreaterThan(0);
      
      // Verify staggered withdrawal strategy
      const withdrawalYears = result.withdrawalPlan.map(w => w.year);
      expect(new Set(withdrawalYears).size).toBe(3); // Different years
    });
  });
});
```

## Swiss Economic Data Integration Testing

### SNB Data Integration

```typescript
// test/swiss/economic-data.test.ts
import { describe, test, expect, vi } from 'vitest';
import { SwissEconomicDataEngine } from '../../src/engines/SwissEconomicDataEngine';

describe('Swiss Economic Data Integration', () => {
  describe('SNB Policy Rate Integration', () => {
    test('fetches current SNB policy rate', async () => {
      // Mock SNB API response
      const mockSNBData = {
        policyRate: 1.75,
        lastUpdate: '2024-12-19',
        trend: 'stable'
      };

      vi.spyOn(global, 'fetch').mockResolvedValueOnce({
        ok: true,
        json: async () => mockSNBData
      } as Response);

      const result = await SwissEconomicDataEngine.fetchSNBData();

      expect(result.policyRate).toBe(1.75);
      expect(result.lastUpdate).toBe('2024-12-19');
      expect(result.trend).toBe('stable');
    });

    test('handles SNB API failures gracefully', async () => {
      vi.spyOn(global, 'fetch').mockRejectedValueOnce(new Error('Network error'));

      const result = await SwissEconomicDataEngine.fetchSNBData();

      // Should return fallback data
      expect(result.policyRate).toBeGreaterThan(0);
      expect(result.source).toBe('fallback');
    });
  });

  describe('Inflation Rate Calculations', () => {
    test('calculates Swiss inflation rate correctly', () => {
      const historicalData = [
        { year: 2020, cpi: 100.0 },
        { year: 2021, cpi: 100.6 },
        { year: 2022, cpi: 103.5 },
        { year: 2023, cpi: 105.8 },
        { year: 2024, cpi: 107.2 }
      ];

      const result = SwissEconomicDataEngine.calculateInflationRate(historicalData);

      expect(result.currentRate).toBeCloseTo(0.013, 3); // ~1.3% for 2024
      expect(result.averageRate).toBeCloseTo(0.018, 3); // ~1.8% average
      expect(result.trend).toBe('moderate');
    });
  });

  describe('Market Data Integration', () => {
    test('integrates SIX Swiss Exchange data', async () => {
      const mockMarketData = {
        smi: 11234.56,
        spi: 14567.89,
        change: 0.012,
        volume: 1234567
      };

      vi.spyOn(global, 'fetch').mockResolvedValueOnce({
        ok: true,
        json: async () => mockMarketData
      } as Response);

      const result = await SwissEconomicDataEngine.fetchMarketData();

      expect(result.smi).toBe(11234.56);
      expect(result.spi).toBe(14567.89);
      expect(result.change).toBe(0.012);
    });
  });
});
```

## Regulatory Compliance Testing

### Data Protection (GDPR/DPA) Testing

```typescript
// test/swiss/data-protection.test.ts
describe('Swiss Data Protection Compliance', () => {
  test('implements data minimization principle', () => {
    const userProfile = {
      age: 30,
      income: 80000,
      canton: 'ZH'
      // Should NOT collect: name, address, phone, etc.
    };

    const result = DataService.validateUserProfile(userProfile);
    
    expect(result.isValid).toBe(true);
    expect(result.collectedFields).not.toContain('name');
    expect(result.collectedFields).not.toContain('address');
    expect(result.collectedFields).not.toContain('phone');
  });

  test('provides complete data export', () => {
    const userData = DataService.exportUserData('test-user-id');
    
    expect(userData).toHaveProperty('profile');
    expect(userData).toHaveProperty('scenarios');
    expect(userData).toHaveProperty('calculations');
    expect(userData).toHaveProperty('preferences');
    expect(userData.exportDate).toBeDefined();
    expect(userData.format).toBe('JSON');
  });

  test('implements secure data deletion', () => {
    const userId = 'test-user-id';
    
    // Create test data
    DataService.saveUserProfile(userId, { age: 30, income: 80000 });
    
    // Delete data
    const result = DataService.deleteUserData(userId);
    
    expect(result.success).toBe(true);
    expect(result.deletedItems).toContain('profile');
    expect(result.deletedItems).toContain('scenarios');
    
    // Verify deletion
    const retrievedData = DataService.getUserProfile(userId);
    expect(retrievedData).toBeNull();
  });
});
```

### Financial Disclosure Testing

```typescript
// test/swiss/financial-disclosure.test.ts
describe('Financial Disclosure Compliance', () => {
  test('displays required risk warnings', () => {
    const riskWarnings = DisclosureService.getRiskWarnings();
    
    expect(riskWarnings).toContain('Past performance does not guarantee future results');
    expect(riskWarnings).toContain('Investment values may fluctuate');
    expect(riskWarnings).toContain('Tax laws may change');
    expect(riskWarnings).toContain('Consult professional financial advisor');
  });

  test('documents calculation assumptions', () => {
    const assumptions = CalculationEngine.getAssumptions();
    
    expect(assumptions).toHaveProperty('expectedReturn');
    expect(assumptions).toHaveProperty('inflationRate');
    expect(assumptions).toHaveProperty('taxRates');
    expect(assumptions).toHaveProperty('lastUpdated');
    
    // Verify assumptions are reasonable
    expect(assumptions.expectedReturn).toBeGreaterThan(0.03);
    expect(assumptions.expectedReturn).toBeLessThan(0.12);
    expect(assumptions.inflationRate).toBeGreaterThan(0.005);
    expect(assumptions.inflationRate).toBeLessThan(0.05);
  });
});
```

## Performance Testing for Swiss Features

### Large Dataset Performance

```typescript
// test/swiss/performance.test.ts
describe('Swiss Feature Performance', () => {
  test('handles all 26 cantons tax calculation efficiently', async () => {
    const startTime = performance.now();
    
    const cantons = ['AG', 'AI', 'AR', 'BE', 'BL', 'BS', 'FR', 'GE', 'GL', 'GR',
                     'JU', 'LU', 'NE', 'NW', 'OW', 'SG', 'SH', 'SO', 'SZ', 'TG',
                     'TI', 'UR', 'VD', 'VS', 'ZG', 'ZH'];
    
    const results = cantons.map(canton => 
      SwissTaxEngine.calculateTax({
        income: 100000,
        canton,
        civilStatus: 'single',
        year: 2024
      })
    );
    
    const endTime = performance.now();
    const executionTime = endTime - startTime;
    
    expect(executionTime).toBeLessThan(100); // Should complete in < 100ms
    expect(results).toHaveLength(26);
    results.forEach(result => {
      expect(result.totalTax).toBeGreaterThan(0);
    });
  });

  test('healthcare premium comparison performance', async () => {
    const startTime = performance.now();
    
    const comparison = await HealthcareCostOptimizer.compareAllCantons({
      age: 30,
      deductible: 300,
      includeSubsidies: true
    });
    
    const endTime = performance.now();
    const executionTime = endTime - startTime;
    
    expect(executionTime).toBeLessThan(500); // Should complete in < 500ms
    expect(comparison.cantons).toHaveLength(26);
  });
});
```

## Test Data Management for Swiss Features

### Swiss Test Fixtures

```typescript
// fixtures/swiss-test-data.ts
export const swissTestData = {
  cantons: {
    zurich: {
      code: 'ZH',
      name: 'Zurich',
      taxMultiplier: 1.00,
      averageHealthcarePremium: 350,
      costOfLivingIndex: 120
    },
    geneva: {
      code: 'GE',
      name: 'Geneva',
      taxMultiplier: 1.45,
      averageHealthcarePremium: 420,
      costOfLivingIndex: 125
    },
    zug: {
      code: 'ZG',
      name: 'Zug',
      taxMultiplier: 0.65,
      averageHealthcarePremium: 320,
      costOfLivingIndex: 115
    }
  },
  
  userProfiles: {
    youngProfessional: {
      age: 28,
      income: 85000,
      expenses: 55000,
      savings: 30000,
      riskProfile: 'medium',
      canton: 'ZH'
    },
    seniorExecutive: {
      age: 52,
      income: 180000,
      expenses: 120000,
      savings: 650000,
      riskProfile: 'conservative',
      canton: 'ZG'
    }
  },
  
  taxScenarios: {
    singleHighIncome: {
      income: 200000,
      civilStatus: 'single',
      expectedFederalTax: 8513,
      expectedEffectiveRate: 0.15
    },
    marriedMediumIncome: {
      income: 120000,
      civilStatus: 'married',
      expectedFederalTax: 3103,
      expectedEffectiveRate: 0.12
    }
  }
};
```

---

*This Swiss-specific testing guide ensures comprehensive coverage of all Swiss financial regulations and compliance requirements unique to Swiss Budget Pro.*
