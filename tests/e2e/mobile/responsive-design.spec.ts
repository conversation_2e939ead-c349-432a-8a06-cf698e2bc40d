/**
 * E2E Tests for Mobile Responsive Design
 * Tests Swiss Budget Pro functionality across different mobile devices and screen sizes
 */

import { test, expect, devices } from '@playwright/test';
import { SwissBudgetProPage } from '../../page-objects/pages/SwissBudgetProPage';

test.describe('Mobile Responsive Design', () => {
  const mobileDevices = [
    { name: 'iPhone 12', device: devices['iPhone 12'] },
    { name: 'iPhone 12 Pro', device: devices['iPhone 12 Pro'] },
    { name: 'Pixel 5', device: devices['Pixel 5'] },
    { name: 'Galaxy S21', device: devices['Galaxy S21'] },
  ];

  const tabletDevices = [
    { name: 'iPad Pro', device: devices['iPad Pro'] },
    { name: 'Galaxy Tab S4', device: devices['Galaxy Tab S4'] },
  ];

  test.describe('Mobile Phone Testing', () => {
    mobileDevices.forEach(({ name, device }) => {
      test(`E2E-MOBILE-001: Basic functionality on ${name}`, async ({ browser }) => {
        const context = await browser.newContext({
          ...device,
        });
        const page = await context.newPage();
        const swissBudgetPage = new SwissBudgetProPage(page);

        test.step('Navigate to application on mobile', async () => {
          await swissBudgetPage.goto();
          await swissBudgetPage.verifyPageLoaded();
        });

        test.step('Verify mobile layout and navigation', async () => {
          // Check if mobile navigation is visible
          const mobileNav = page.locator('[data-testid="mobile-nav"]').or(
            page.locator('.mobile-nav, .hamburger-menu'),
          );
          
          // On mobile, either mobile nav should be visible or regular nav should be responsive
          const hasNavigation = await swissBudgetPage.inputTab.isVisible() || await mobileNav.isVisible();
          expect(hasNavigation).toBeTruthy();

          // Verify app title is visible and properly sized
          await expect(swissBudgetPage.appTitle).toBeVisible();
          
          // Check viewport meta tag for proper mobile scaling
          const viewportMeta = await page.locator('meta[name="viewport"]').getAttribute('content');
          expect(viewportMeta).toContain('width=device-width');
        });

        test.step('Test form input on mobile', async () => {
          // Fill basic financial information using mobile-friendly interactions
          await swissBudgetPage.monthlyIncomeInput.tap();
          await swissBudgetPage.monthlyIncomeInput.fill('6000');
          
          await swissBudgetPage.monthlyExpensesInput.tap();
          await swissBudgetPage.monthlyExpensesInput.fill('4000');
          
          await swissBudgetPage.currentSavingsInput.tap();
          await swissBudgetPage.currentSavingsInput.fill('80000');

          // Test dropdown selection on mobile
          await swissBudgetPage.cantonSelect.tap();
          await swissBudgetPage.cantonSelect.selectOption('ZH');

          // Verify inputs are properly filled
          expect(await swissBudgetPage.monthlyIncomeInput.inputValue()).toBe('6000');
          expect(await swissBudgetPage.monthlyExpensesInput.inputValue()).toBe('4000');
        });

        test.step('Test calculation on mobile', async () => {
          // Trigger calculation
          await swissBudgetPage.calculateButton.tap();
          await swissBudgetPage.waitForCalculationComplete();

          // Verify results are displayed properly on mobile
          await expect(swissBudgetPage.fireYearsResult).toBeVisible();
          
          const results = await swissBudgetPage.getFIREResults();
          expect(results.fireYears).toBeGreaterThan(0);
        });

        test.step('Test mobile navigation between tabs', async () => {
          // Test tab navigation on mobile
          if (await swissBudgetPage.analysisTab.isVisible()) {
            await swissBudgetPage.analysisTab.tap();
            await page.waitForTimeout(500);
            
            // Verify analysis content is visible
            await expect(swissBudgetPage.taxOptimizationSection).toBeVisible();
          }
        });

        test.step('Test mobile scrolling and content accessibility', async () => {
          // Scroll to bottom to ensure all content is accessible
          await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
          await page.waitForTimeout(500);
          
          // Scroll back to top
          await page.evaluate(() => window.scrollTo(0, 0));
          await page.waitForTimeout(500);
          
          // Verify key elements are still accessible after scrolling
          await expect(swissBudgetPage.appTitle).toBeVisible();
        });

        await context.close();
      });
    });
  });

  test.describe('Tablet Testing', () => {
    tabletDevices.forEach(({ name, device }) => {
      test(`E2E-MOBILE-002: Tablet functionality on ${name}`, async ({ browser }) => {
        const context = await browser.newContext({
          ...device,
        });
        const page = await context.newPage();
        const swissBudgetPage = new SwissBudgetProPage(page);

        test.step('Navigate to application on tablet', async () => {
          await swissBudgetPage.goto();
          await swissBudgetPage.verifyPageLoaded();
        });

        test.step('Verify tablet layout optimization', async () => {
          // Tablets should show more content than phones but less than desktop
          const viewport = page.viewportSize();
          expect(viewport?.width).toBeGreaterThan(600); // Tablet width
          
          // Navigation should be visible on tablets
          await expect(swissBudgetPage.inputTab).toBeVisible();
          await expect(swissBudgetPage.analysisTab).toBeVisible();
        });

        test.step('Test multi-column layout on tablet', async () => {
          // Fill comprehensive financial data
          await swissBudgetPage.fillBasicFinancialInfo({
            monthlyIncome: 8000,
            monthlyExpenses: 5500,
            currentSavings: 150000,
            canton: 'GE',
            age: 35,
            retirementAge: 60,
          });

          await swissBudgetPage.calculateFIRE();
          
          // Navigate to analysis tab
          await swissBudgetPage.navigateToTab('analysis');
          
          // Verify multiple sections are visible simultaneously on tablet
          await expect(swissBudgetPage.taxOptimizationSection).toBeVisible();
          await expect(swissBudgetPage.healthcareSection).toBeVisible();
        });

        test.step('Test tablet touch interactions', async () => {
          // Test touch-specific interactions
          await swissBudgetPage.navigateToTab('visualization');
          
          // Look for charts or interactive elements
          const chartElements = page.locator('[data-testid*="chart"], .chart, canvas');
          const chartCount = await chartElements.count();
          
          if (chartCount > 0) {
            // Test touch interactions with charts
            const firstChart = chartElements.first();
            await firstChart.tap();
            
            // Verify chart interaction works
            console.log(`Found ${chartCount} chart elements on tablet`);
          }
        });

        await context.close();
      });
    });
  });

  test.describe('Responsive Breakpoint Testing', () => {
    const breakpoints = [
      { name: 'Small Mobile', width: 320, height: 568 },
      { name: 'Large Mobile', width: 414, height: 896 },
      { name: 'Small Tablet', width: 768, height: 1024 },
      { name: 'Large Tablet', width: 1024, height: 1366 },
      { name: 'Small Desktop', width: 1280, height: 720 },
    ];

    breakpoints.forEach(({ name, width, height }) => {
      test(`E2E-MOBILE-003: Responsive design at ${name} (${width}x${height})`, async ({ page }) => {
        const swissBudgetPage = new SwissBudgetProPage(page);

        test.step(`Set viewport to ${name}`, async () => {
          await page.setViewportSize({ width, height });
          await swissBudgetPage.goto();
          await swissBudgetPage.verifyPageLoaded();
        });

        test.step('Verify layout adaptation', async () => {
          // Take screenshot for visual verification
          await page.screenshot({
            path: `test-results/screenshots/responsive-${name.toLowerCase().replace(' ', '-')}-${width}x${height}.png`,
            fullPage: true,
          });

          // Verify key elements are visible and properly positioned
          await expect(swissBudgetPage.appTitle).toBeVisible();
          await expect(swissBudgetPage.monthlyIncomeInput).toBeVisible();
          await expect(swissBudgetPage.calculateButton).toBeVisible();
        });

        test.step('Test form usability at breakpoint', async () => {
          // Fill form to test input field sizing and spacing
          await swissBudgetPage.fillBasicFinancialInfo({
            monthlyIncome: 7000,
            monthlyExpenses: 4500,
            currentSavings: 100000,
            canton: 'VD',
            age: 30,
          });

          // Verify form is usable at this breakpoint
          const incomeValue = await swissBudgetPage.monthlyIncomeInput.inputValue();
          expect(incomeValue).toBe('7000');
        });

        test.step('Test navigation at breakpoint', async () => {
          // Test tab navigation at different screen sizes
          if (width >= 768) {
            // Tablets and larger should show full navigation
            await expect(swissBudgetPage.inputTab).toBeVisible();
            await expect(swissBudgetPage.analysisTab).toBeVisible();
          } else {
            // Mobile might use hamburger menu or different navigation
            const mobileNav = page.locator('[data-testid="mobile-nav"]');
            if (await mobileNav.isVisible()) {
              await mobileNav.click();
            }
          }
        });
      });
    });
  });

  test.describe('Touch Interaction Testing', () => {
    test('E2E-MOBILE-004: Touch gestures and interactions', async ({ browser }) => {
      const context = await browser.newContext({
        ...devices['iPhone 12'],
        hasTouch: true,
      });
      const page = await context.newPage();
      const swissBudgetPage = new SwissBudgetProPage(page);

      test.step('Test touch input methods', async () => {
        await swissBudgetPage.goto();
        await swissBudgetPage.verifyPageLoaded();

        // Test tap interactions
        await swissBudgetPage.monthlyIncomeInput.tap();
        await page.keyboard.type('5500');

        // Test touch and hold (if applicable)
        await swissBudgetPage.monthlyExpensesInput.tap();
        await page.keyboard.type('3500');

        // Test swipe gestures (if carousel or similar exists)
        const carousel = page.locator('[data-testid="carousel"], .carousel, .swiper');
        if (await carousel.isVisible()) {
          const box = await carousel.boundingBox();
          if (box) {
            // Swipe left
            await page.touchscreen.tap(box.x + box.width * 0.8, box.y + box.height / 2);
            await page.touchscreen.tap(box.x + box.width * 0.2, box.y + box.height / 2);
          }
        }
      });

      test.step('Test pinch-to-zoom on charts', async () => {
        await swissBudgetPage.fillBasicFinancialInfo({
          monthlyIncome: 6000,
          monthlyExpenses: 4000,
          currentSavings: 90000,
          canton: 'BE',
        });

        await swissBudgetPage.calculateFIRE();
        await swissBudgetPage.navigateToTab('visualization');

        // Look for zoomable charts
        const charts = page.locator('[data-testid*="chart"], canvas, svg');
        const chartCount = await charts.count();

        if (chartCount > 0) {
          const firstChart = charts.first();
          const box = await firstChart.boundingBox();
          
          if (box) {
            // Simulate pinch gesture (zoom in)
            await page.touchscreen.tap(box.x + box.width * 0.3, box.y + box.height * 0.3);
            await page.touchscreen.tap(box.x + box.width * 0.7, box.y + box.height * 0.7);
          }
        }
      });

      await context.close();
    });
  });

  test.describe('Mobile Performance Testing', () => {
    test('E2E-MOBILE-005: Mobile performance benchmarks', async ({ browser }) => {
      const context = await browser.newContext({
        ...devices['Pixel 5'],
      });
      const page = await context.newPage();
      const swissBudgetPage = new SwissBudgetProPage(page);

      test.step('Measure mobile page load performance', async () => {
        const startTime = Date.now();
        
        await swissBudgetPage.goto();
        await swissBudgetPage.verifyPageLoaded();
        
        const loadTime = Date.now() - startTime;
        console.log(`Mobile page load time: ${loadTime}ms`);
        
        // Mobile load time should be reasonable (under 5 seconds)
        expect(loadTime).toBeLessThan(5000);
      });

      test.step('Measure mobile calculation performance', async () => {
        await swissBudgetPage.fillBasicFinancialInfo({
          monthlyIncome: 7500,
          monthlyExpenses: 5000,
          currentSavings: 120000,
          canton: 'ZH',
          age: 32,
        });

        const calcStartTime = Date.now();
        await swissBudgetPage.calculateFIRE();
        const calcTime = Date.now() - calcStartTime;
        
        console.log(`Mobile calculation time: ${calcTime}ms`);
        
        // Mobile calculations should complete quickly (under 3 seconds)
        expect(calcTime).toBeLessThan(3000);
      });

      test.step('Test mobile memory usage', async () => {
        // Perform multiple calculations to test memory stability
        const testScenarios = [
          { income: 5000, expenses: 3500, savings: 50000 },
          { income: 8000, expenses: 5500, savings: 150000 },
          { income: 12000, expenses: 7000, savings: 300000 },
        ];

        for (const scenario of testScenarios) {
          await swissBudgetPage.fillBasicFinancialInfo({
            monthlyIncome: scenario.income,
            monthlyExpenses: scenario.expenses,
            currentSavings: scenario.savings,
            canton: 'GE',
          });

          await swissBudgetPage.calculateFIRE();
          
          // Brief pause between calculations
          await page.waitForTimeout(500);
        }

        // Verify application is still responsive after multiple calculations
        await expect(swissBudgetPage.appTitle).toBeVisible();
      });

      await context.close();
    });
  });

  test.describe('Mobile Accessibility', () => {
    test('E2E-MOBILE-006: Mobile accessibility features', async ({ browser }) => {
      const context = await browser.newContext({
        ...devices['iPhone 12'],
        reducedMotion: 'reduce',
      });
      const page = await context.newPage();
      const swissBudgetPage = new SwissBudgetProPage(page);

      test.step('Test mobile screen reader compatibility', async () => {
        await swissBudgetPage.goto();
        await swissBudgetPage.verifyPageLoaded();

        // Check for proper ARIA labels on mobile
        const incomeInput = swissBudgetPage.monthlyIncomeInput;
        const ariaLabel = await incomeInput.getAttribute('aria-label');
        const label = await incomeInput.getAttribute('aria-labelledby');
        
        expect(ariaLabel || label).toBeTruthy();
      });

      test.step('Test mobile keyboard navigation', async () => {
        // Test tab navigation on mobile (external keyboard)
        await page.keyboard.press('Tab');
        await page.keyboard.press('Tab');
        await page.keyboard.press('Tab');
        
        // Verify focus is visible and logical
        const focusedElement = await page.evaluate(() => document.activeElement?.tagName);
        expect(focusedElement).toBeTruthy();
      });

      test.step('Test mobile color contrast and readability', async () => {
        // Take screenshot for manual color contrast verification
        await page.screenshot({
          path: 'test-results/screenshots/mobile-accessibility-contrast.png',
          fullPage: true,
        });

        // Verify text is readable (not too small)
        const titleFontSize = await swissBudgetPage.appTitle.evaluate(el => 
          window.getComputedStyle(el).fontSize,
        );
        
        const fontSize = parseInt(titleFontSize);
        expect(fontSize).toBeGreaterThan(16); // Minimum readable size on mobile
      });

      await context.close();
    });
  });
});
