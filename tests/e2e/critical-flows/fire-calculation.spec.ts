/**
 * E2E Tests for FIRE (Financial Independence, Retire Early) Calculations
 * Tests the core financial planning functionality of Swiss Budget Pro
 */

import { test, expect } from '@playwright/test';
import { SwissBudgetProPage } from '../../page-objects/pages/SwissBudgetProPage';
import swissTestData from '../../fixtures/swiss-test-data.json';

test.describe('FIRE Calculation Flow', () => {
  let swissBudgetPage: SwissBudgetProPage;

  test.beforeEach(async ({ page }) => {
    swissBudgetPage = new SwissBudgetProPage(page);
    await swissBudgetPage.goto();
    await swissBudgetPage.verifyPageLoaded();
  });

  test('E2E-FIRE-001: Complete FIRE Planning Journey - Young Professional', async ({ page }) => {
    const testProfile = swissTestData.userProfiles.find(p => p.id === 'young-professional');
    
    test.step('Input financial data for young professional', async () => {
      await swissBudgetPage.fillBasicFinancialInfo({
        monthlyIncome: testProfile.income / 12,
        monthlyExpenses: testProfile.expenses / 12,
        currentSavings: testProfile.savings,
        canton: testProfile.canton,
        age: testProfile.age,
        retirementAge: 50, // FIRE target age
      });
    });

    test.step('Calculate FIRE plan', async () => {
      await swissBudgetPage.calculateFIRE();
    });

    test.step('Verify FIRE calculation results', async () => {
      const results = await swissBudgetPage.getFIREResults();
      
      // Verify results are reasonable for the profile
      expect(results.fireYears).toBeGreaterThan(0);
      expect(results.fireYears).toBeLessThan(30); // Should be achievable within 30 years
      
      expect(results.fireNumber).toBeGreaterThan(0);
      expect(results.fireNumber).toBeGreaterThan(testProfile.expenses * 20); // At least 20x annual expenses
      
      expect(results.savingsRate).toBeGreaterThan(0);
      expect(results.savingsRate).toBeLessThan(100); // Should be less than 100%
      
      // Log results for verification
      console.log('FIRE Calculation Results:', results);
    });

    test.step('Perform scenario analysis', async () => {
      // Test income increase scenario
      await swissBudgetPage.fillBasicFinancialInfo({
        monthlyIncome: (testProfile.income * 1.2) / 12, // 20% increase
        monthlyExpenses: testProfile.expenses / 12,
        currentSavings: testProfile.savings,
        canton: testProfile.canton,
        age: testProfile.age,
        retirementAge: 50,
      });
      
      await swissBudgetPage.calculateFIRE();
      
      const improvedResults = await swissBudgetPage.getFIREResults();
      
      // Higher income should reduce years to FIRE
      expect(improvedResults.fireYears).toBeLessThan(results.fireYears);
      
      console.log('Improved Scenario Results:', improvedResults);
    });

    test.step('Generate and validate report', async () => {
      await swissBudgetPage.navigateToTab('reports');
      await swissBudgetPage.exportData();
      
      // Verify no errors occurred during export
      expect(await swissBudgetPage.hasErrorMessage()).toBeFalsy();
    });
  });

  test('E2E-FIRE-002: FIRE Calculation for High Earner in Zug', async ({ page }) => {
    const testProfile = swissTestData.userProfiles.find(p => p.id === 'high-earner');
    
    test.step('Input high earner financial data', async () => {
      await swissBudgetPage.fillBasicFinancialInfo({
        monthlyIncome: testProfile.income / 12,
        monthlyExpenses: testProfile.expenses / 12,
        currentSavings: testProfile.savings,
        canton: testProfile.canton, // Zug - low tax canton
        age: testProfile.age,
        retirementAge: 45, // Early retirement target
      });
    });

    test.step('Calculate FIRE plan for high earner', async () => {
      await swissBudgetPage.calculateFIRE();
    });

    test.step('Verify high earner FIRE results', async () => {
      const results = await swissBudgetPage.getFIREResults();
      
      // High earner should achieve FIRE faster
      expect(results.fireYears).toBeGreaterThan(0);
      expect(results.fireYears).toBeLessThan(15); // Should achieve FIRE within 15 years
      
      // High savings rate expected
      expect(results.savingsRate).toBeGreaterThan(50); // Should save more than 50%
      
      // Large FIRE number due to high expenses
      expect(results.fireNumber).toBeGreaterThan(2000000); // Over 2M CHF
      
      console.log('High Earner FIRE Results:', results);
    });

    test.step('Test Zug tax optimization', async () => {
      await swissBudgetPage.navigateToTab('analysis');
      
      // Verify tax optimization section is visible
      await expect(swissBudgetPage.taxOptimizationSection).toBeVisible();
      
      // Verify Pillar 3a optimization is shown
      await expect(swissBudgetPage.pillar3aSection).toBeVisible();
    });
  });

  test('E2E-FIRE-003: Edge Case - Minimal Income Scenario', async ({ page }) => {
    test.step('Input minimal income scenario', async () => {
      await swissBudgetPage.fillBasicFinancialInfo({
        monthlyIncome: 3000, // CHF 36,000/year
        monthlyExpenses: 2800, // Very tight budget
        currentSavings: 5000,
        canton: 'AG', // Lower cost canton
        age: 25,
        retirementAge: 65,
      });
    });

    test.step('Calculate FIRE plan for minimal income', async () => {
      await swissBudgetPage.calculateFIRE();
    });

    test.step('Verify minimal income FIRE results', async () => {
      const results = await swissBudgetPage.getFIREResults();
      
      // Should still provide valid results
      expect(results.fireYears).toBeGreaterThan(0);
      
      // Low savings rate expected
      expect(results.savingsRate).toBeGreaterThan(0);
      expect(results.savingsRate).toBeLessThan(20); // Low savings rate
      
      // May take longer to achieve FIRE
      expect(results.fireYears).toBeGreaterThan(20);
      
      console.log('Minimal Income FIRE Results:', results);
    });
  });

  test('E2E-FIRE-004: Negative Savings Rate Handling', async ({ page }) => {
    test.step('Input scenario with expenses exceeding income', async () => {
      await swissBudgetPage.fillBasicFinancialInfo({
        monthlyIncome: 4000,
        monthlyExpenses: 4500, // Expenses exceed income
        currentSavings: 10000,
        canton: 'ZH',
        age: 30,
        retirementAge: 65,
      });
    });

    test.step('Attempt FIRE calculation with negative savings', async () => {
      await swissBudgetPage.calculateFIRE();
    });

    test.step('Verify appropriate error handling', async () => {
      // Should either show error message or indicate FIRE is not achievable
      const hasError = await swissBudgetPage.hasErrorMessage();
      
      if (hasError) {
        const errorMessage = await swissBudgetPage.getErrorMessage();
        expect(errorMessage).toContain('savings'); // Should mention savings issue
      } else {
        // If no error, should show that FIRE is not achievable
        const results = await swissBudgetPage.getFIREResults();
        expect(results.fireYears).toBeNull(); // Or some indication of impossibility
      }
    });
  });

  test('E2E-FIRE-005: Multi-Canton Comparison', async ({ page }) => {
    const baseProfile = {
      monthlyIncome: 8000,
      monthlyExpenses: 5000,
      currentSavings: 100000,
      age: 35,
      retirementAge: 55,
    };

    const cantons = ['ZH', 'ZG', 'GE', 'VD'];
    const cantonResults: Record<string, any> = {};

    for (const canton of cantons) {
      test.step(`Calculate FIRE for canton ${canton}`, async () => {
        await swissBudgetPage.fillBasicFinancialInfo({
          ...baseProfile,
          canton,
        });

        await swissBudgetPage.calculateFIRE();
        
        const results = await swissBudgetPage.getFIREResults();
        cantonResults[canton] = results;
        
        console.log(`${canton} FIRE Results:`, results);
      });
    }

    test.step('Verify canton tax impact on FIRE calculations', async () => {
      // Zug (low tax) should have better FIRE results than Geneva (high tax)
      expect(cantonResults['ZG'].fireYears).toBeLessThan(cantonResults['GE'].fireYears);
      
      // All cantons should provide valid results
      Object.values(cantonResults).forEach((result: any) => {
        expect(result.fireYears).toBeGreaterThan(0);
        expect(result.fireNumber).toBeGreaterThan(0);
      });
    });
  });

  test('E2E-FIRE-006: Real-time Calculation Updates', async ({ page }) => {
    test.step('Input initial financial data', async () => {
      await swissBudgetPage.fillBasicFinancialInfo({
        monthlyIncome: 6000,
        monthlyExpenses: 4000,
        currentSavings: 50000,
        canton: 'ZH',
        age: 30,
        retirementAge: 60,
      });
    });

    test.step('Verify real-time updates on input changes', async () => {
      // Initial calculation
      await swissBudgetPage.calculateFIRE();
      const initialResults = await swissBudgetPage.getFIREResults();
      
      // Change income and verify update
      await swissBudgetPage.monthlyIncomeInput.fill('7000');
      await page.waitForTimeout(1000); // Wait for potential auto-calculation
      
      // If auto-calculation is enabled, results should update
      // Otherwise, manually trigger calculation
      await swissBudgetPage.calculateFIRE();
      const updatedResults = await swissBudgetPage.getFIREResults();
      
      // Higher income should improve FIRE timeline
      expect(updatedResults.fireYears).toBeLessThanOrEqual(initialResults.fireYears);
      
      console.log('Initial Results:', initialResults);
      console.log('Updated Results:', updatedResults);
    });
  });

  test('E2E-FIRE-007: Data Persistence and Recovery', async ({ page }) => {
    const testData = {
      monthlyIncome: 7500,
      monthlyExpenses: 4500,
      currentSavings: 150000,
      canton: 'BE',
      age: 32,
      retirementAge: 58,
    };

    test.step('Input and save financial data', async () => {
      await swissBudgetPage.fillBasicFinancialInfo(testData);
      await swissBudgetPage.calculateFIRE();
      
      // Save the data
      await swissBudgetPage.saveData();
      await swissBudgetPage.waitForSuccessMessage();
    });

    test.step('Reset form and verify data is cleared', async () => {
      await swissBudgetPage.resetForm();
      
      // Verify form is cleared
      const incomeValue = await swissBudgetPage.monthlyIncomeInput.inputValue();
      expect(incomeValue).toBe('');
    });

    test.step('Load saved data and verify restoration', async () => {
      await swissBudgetPage.loadData();
      
      // Verify data is restored
      const incomeValue = await swissBudgetPage.monthlyIncomeInput.inputValue();
      expect(incomeValue).toBe(testData.monthlyIncome.toString());
      
      // Recalculate and verify results are consistent
      await swissBudgetPage.calculateFIRE();
      const results = await swissBudgetPage.getFIREResults();
      
      expect(results.fireYears).toBeGreaterThan(0);
      expect(results.fireNumber).toBeGreaterThan(0);
    });
  });

  test.afterEach(async ({ page }) => {
    // Take screenshot on failure for debugging
    if (test.info().status === 'failed') {
      await swissBudgetPage.takeScreenshot(`fire-calculation-failure-${test.info().title}`);
    }
  });
});
