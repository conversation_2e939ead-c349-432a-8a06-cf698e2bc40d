/**
 * E2E Tests for Swiss Tax Optimization
 * Tests Swiss-specific tax calculations and optimization strategies
 */

import { test, expect } from '@playwright/test';
import { SwissBudgetProPage } from '../../page-objects/pages/SwissBudgetProPage';
import swissTestData from '../../fixtures/swiss-test-data.json';
import swissFinancialData from '../../fixtures/swiss-financial-data.json';

test.describe('Swiss Tax Optimization', () => {
  let swissBudgetPage: SwissBudgetProPage;

  test.beforeEach(async ({ page }) => {
    swissBudgetPage = new SwissBudgetProPage(page);
    await swissBudgetPage.goto();
    await swissBudgetPage.verifyPageLoaded();
  });

  test('E2E-TAX-001: Multi-Canton Tax Comparison', async ({ page }) => {
    const testProfile = {
      monthlyIncome: 10000, // CHF 120,000/year
      monthlyExpenses: 6000,
      currentSavings: 200000,
      age: 35,
    };

    const cantonTaxResults: Record<string, any> = {};

    test.step('Calculate taxes for all major cantons', async () => {
      for (const canton of swissTestData.cantons) {
        await test.step(`Calculate tax for ${canton.name} (${canton.code})`, async () => {
          await swissBudgetPage.fillBasicFinancialInfo({
            ...testProfile,
            canton: canton.code,
          });

          await swissBudgetPage.calculateFIRE();
          await swissBudgetPage.navigateToTab('analysis');

          // Wait for tax optimization section to load
          await expect(swissBudgetPage.taxOptimizationSection).toBeVisible();

          // Extract tax information (this would need to be implemented based on actual UI)
          const taxInfo = await page.locator('[data-testid="tax-summary"]').textContent();
          cantonTaxResults[canton.code] = {
            canton: canton.name,
            taxRate: canton.taxRate,
            taxInfo,
          };

          console.log(`${canton.name} tax calculation completed`);
        });
      }
    });

    test.step('Verify tax rate differences between cantons', async () => {
      // Zug should have lower taxes than Geneva
      const zugTax = cantonTaxResults['ZG'];
      const genevaTax = cantonTaxResults['GE'];
      
      expect(zugTax).toBeDefined();
      expect(genevaTax).toBeDefined();
      
      // This would need actual tax amount comparison based on UI implementation
      console.log('Canton tax comparison:', cantonTaxResults);
    });

    test.step('Verify tax optimization recommendations', async () => {
      // Navigate to Zug (lowest tax canton)
      await swissBudgetPage.fillBasicFinancialInfo({
        ...testProfile,
        canton: 'ZG',
      });

      await swissBudgetPage.calculateFIRE();
      await swissBudgetPage.navigateToTab('analysis');

      // Verify Pillar 3a optimization is shown
      await expect(swissBudgetPage.pillar3aSection).toBeVisible();

      // Check for tax optimization recommendations
      const pillar3aText = await swissBudgetPage.pillar3aSection.textContent();
      expect(pillar3aText).toContain('7056'); // 2024 Pillar 3a limit
    });
  });

  test('E2E-TAX-002: Pillar 3a Optimization Validation', async ({ page }) => {
    test.step('Input high-income scenario for Pillar 3a optimization', async () => {
      await swissBudgetPage.fillBasicFinancialInfo({
        monthlyIncome: 15000, // CHF 180,000/year
        monthlyExpenses: 8000,
        currentSavings: 500000,
        canton: 'ZH',
        age: 40,
        retirementAge: 60,
      });
    });

    test.step('Calculate and verify Pillar 3a recommendations', async () => {
      await swissBudgetPage.calculateFIRE();
      await swissBudgetPage.navigateToTab('analysis');

      await expect(swissBudgetPage.pillar3aSection).toBeVisible();

      // Verify Pillar 3a section contains optimization information
      const pillar3aContent = await swissBudgetPage.pillar3aSection.textContent();
      
      // Should mention the maximum contribution
      expect(pillar3aContent).toContain('7056');
      
      // Should mention tax savings
      expect(pillar3aContent.toLowerCase()).toContain('tax');
      
      console.log('Pillar 3a optimization content:', pillar3aContent);
    });

    test.step('Verify tax savings calculation', async () => {
      // This would test the actual tax savings calculation
      // Implementation depends on how the UI displays this information
      
      const taxSavingsElement = page.locator('[data-testid="pillar3a-tax-savings"]');
      if (await taxSavingsElement.isVisible()) {
        const taxSavings = await taxSavingsElement.textContent();
        console.log('Pillar 3a tax savings:', taxSavings);
        
        // Verify tax savings is a reasonable amount
        const savingsAmount = parseFloat(taxSavings?.replace(/[^\d.]/g, '') || '0');
        expect(savingsAmount).toBeGreaterThan(1000); // Should save at least CHF 1000
      }
    });
  });

  test('E2E-TAX-003: Married vs Single Tax Filing Comparison', async ({ page }) => {
    const baseProfile = {
      monthlyIncome: 8000,
      monthlyExpenses: 5000,
      currentSavings: 150000,
      canton: 'VD',
      age: 35,
    };

    let singleResults: any;
    let marriedResults: any;

    test.step('Calculate taxes for single filing', async () => {
      await swissBudgetPage.fillBasicFinancialInfo(baseProfile);
      
      // Set marital status to single (if such option exists)
      const maritalStatusSelect = page.locator('[data-testid="marital-status"]');
      if (await maritalStatusSelect.isVisible()) {
        await maritalStatusSelect.selectOption('single');
      }

      await swissBudgetPage.calculateFIRE();
      singleResults = await swissBudgetPage.getFIREResults();
      
      console.log('Single filing results:', singleResults);
    });

    test.step('Calculate taxes for married filing', async () => {
      await swissBudgetPage.fillBasicFinancialInfo(baseProfile);
      
      // Set marital status to married
      const maritalStatusSelect = page.locator('[data-testid="marital-status"]');
      if (await maritalStatusSelect.isVisible()) {
        await maritalStatusSelect.selectOption('married');
      }

      await swissBudgetPage.calculateFIRE();
      marriedResults = await swissBudgetPage.getFIREResults();
      
      console.log('Married filing results:', marriedResults);
    });

    test.step('Compare single vs married tax implications', async () => {
      // Both should provide valid results
      expect(singleResults.fireYears).toBeGreaterThan(0);
      expect(marriedResults.fireYears).toBeGreaterThan(0);
      
      // Married filing often has tax advantages in Switzerland
      // This comparison would depend on the specific implementation
      console.log('Tax filing comparison completed');
    });
  });

  test('E2E-TAX-004: Progressive Tax Bracket Validation', async ({ page }) => {
    const incomeScenarios = [
      { income: 50000, description: 'Low income' },
      { income: 100000, description: 'Medium income' },
      { income: 200000, description: 'High income' },
      { income: 500000, description: 'Very high income' },
    ];

    const taxResults: Record<string, any> = {};

    test.step('Test progressive tax calculation across income levels', async () => {
      for (const scenario of incomeScenarios) {
        await test.step(`Calculate tax for ${scenario.description}: CHF ${scenario.income}`, async () => {
          await swissBudgetPage.fillBasicFinancialInfo({
            monthlyIncome: scenario.income / 12,
            monthlyExpenses: Math.min(scenario.income * 0.6, scenario.income - 10000) / 12,
            currentSavings: scenario.income * 2,
            canton: 'ZH',
            age: 35,
          });

          await swissBudgetPage.calculateFIRE();
          const results = await swissBudgetPage.getFIREResults();
          
          taxResults[scenario.income] = {
            ...results,
            description: scenario.description,
          };

          console.log(`${scenario.description} (CHF ${scenario.income}):`, results);
        });
      }
    });

    test.step('Verify progressive tax impact on FIRE calculations', async () => {
      // Higher income should generally lead to higher effective tax rates
      // but also potentially faster FIRE due to higher absolute savings
      
      const lowIncome = taxResults[50000];
      const highIncome = taxResults[500000];
      
      expect(lowIncome.fireYears).toBeGreaterThan(0);
      expect(highIncome.fireYears).toBeGreaterThan(0);
      
      // High income should achieve FIRE faster despite higher taxes
      expect(highIncome.fireYears).toBeLessThan(lowIncome.fireYears);
    });
  });

  test('E2E-TAX-005: Wealth Tax Considerations', async ({ page }) => {
    test.step('Input high net worth scenario', async () => {
      await swissBudgetPage.fillBasicFinancialInfo({
        monthlyIncome: 12000,
        monthlyExpenses: 7000,
        currentSavings: 2000000, // CHF 2M - subject to wealth tax
        canton: 'ZH',
        age: 45,
        retirementAge: 60,
      });
    });

    test.step('Calculate with wealth tax considerations', async () => {
      await swissBudgetPage.calculateFIRE();
      await swissBudgetPage.navigateToTab('analysis');

      // Check if wealth tax is mentioned in tax optimization
      const taxSection = await swissBudgetPage.taxOptimizationSection.textContent();
      
      // Wealth tax should be considered for high net worth individuals
      if (taxSection?.toLowerCase().includes('wealth')) {
        console.log('Wealth tax considerations found in analysis');
      }
    });

    test.step('Compare with lower net worth scenario', async () => {
      // Test same income but lower savings (below wealth tax threshold)
      await swissBudgetPage.fillBasicFinancialInfo({
        monthlyIncome: 12000,
        monthlyExpenses: 7000,
        currentSavings: 200000, // CHF 200K - below wealth tax threshold
        canton: 'ZH',
        age: 45,
        retirementAge: 60,
      });

      await swissBudgetPage.calculateFIRE();
      const lowerWealthResults = await swissBudgetPage.getFIREResults();
      
      expect(lowerWealthResults.fireYears).toBeGreaterThan(0);
      console.log('Lower wealth scenario results:', lowerWealthResults);
    });
  });

  test('E2E-TAX-006: Tax-Efficient Withdrawal Strategies', async ({ page }) => {
    test.step('Input retirement-ready scenario', async () => {
      await swissBudgetPage.fillBasicFinancialInfo({
        monthlyIncome: 10000,
        monthlyExpenses: 6000,
        currentSavings: 1500000, // Close to FIRE number
        canton: 'ZG',
        age: 55,
        retirementAge: 60,
      });
    });

    test.step('Calculate and review withdrawal strategies', async () => {
      await swissBudgetPage.calculateFIRE();
      await swissBudgetPage.navigateToTab('analysis');

      // Look for withdrawal strategy recommendations
      const analysisContent = await page.locator('[data-testid="withdrawal-strategy"]').textContent().catch(() => null);
      
      if (analysisContent) {
        // Should mention tax-efficient withdrawal strategies
        expect(analysisContent.toLowerCase()).toContain('withdrawal');
        console.log('Withdrawal strategy analysis:', analysisContent);
      }
    });

    test.step('Verify different withdrawal rate scenarios', async () => {
      // This would test different withdrawal rates (3%, 3.5%, 4%)
      // and their tax implications
      
      await swissBudgetPage.navigateToTab('visualization');
      
      // Check if withdrawal rate scenarios are visualized
      const chartSection = page.locator('[data-testid="withdrawal-scenarios"]');
      if (await chartSection.isVisible()) {
        console.log('Withdrawal rate scenarios visualization found');
      }
    });
  });

  test('E2E-TAX-007: Cross-Border Tax Implications', async ({ page }) => {
    test.step('Input cross-border worker scenario', async () => {
      await swissBudgetPage.fillBasicFinancialInfo({
        monthlyIncome: 9000,
        monthlyExpenses: 5500,
        currentSavings: 100000,
        canton: 'BS', // Basel - common for cross-border workers
        age: 32,
        retirementAge: 62,
      });
    });

    test.step('Check for cross-border tax considerations', async () => {
      await swissBudgetPage.calculateFIRE();
      await swissBudgetPage.navigateToTab('analysis');

      // Look for any cross-border tax information
      const taxAnalysis = await swissBudgetPage.taxOptimizationSection.textContent();
      
      // This would depend on whether the app handles cross-border scenarios
      console.log('Tax analysis for border canton:', taxAnalysis);
    });
  });

  test.afterEach(async ({ page }) => {
    // Take screenshot on failure for debugging
    if (test.info().status === 'failed') {
      await swissBudgetPage.takeScreenshot(`tax-optimization-failure-${test.info().title}`);
    }
  });
});
