/**
 * E2E Tests for Swiss Healthcare Cost Optimization
 * Tests the healthcare deductible optimization and cost analysis features
 */

import { test, expect } from '@playwright/test';
import { SwissBudgetProPage } from '../../page-objects/pages/SwissBudgetProPage';
import swissTestData from '../../fixtures/swiss-test-data.json';

test.describe('Swiss Healthcare Cost Optimization', () => {
  let swissBudgetPage: SwissBudgetProPage;

  test.beforeEach(async ({ page }) => {
    swissBudgetPage = new SwissBudgetProPage(page);
    await swissBudgetPage.goto();
    await swissBudgetPage.verifyPageLoaded();
  });

  test('E2E-HEALTH-001: Deductible Optimization for Low-Risk Profile', async ({ page }) => {
    const lowRiskProfile = swissTestData.healthcareProfiles.find(p => p.id === 'low-risk');
    
    test.step('Navigate to healthcare optimization section', async () => {
      await swissBudgetPage.navigateToTab('analysis');
      await expect(swissBudgetPage.healthcareSection).toBeVisible();
    });

    test.step('Input low-risk health profile', async () => {
      // Fill health-related information
      const ageInput = page.locator('[data-testid="health-age"]');
      const healthStatusSelect = page.locator('[data-testid="health-status"]');
      const annualCostsInput = page.locator('[data-testid="annual-health-costs"]');

      if (await ageInput.isVisible()) {
        await ageInput.fill(lowRiskProfile.age.toString());
      }
      
      if (await healthStatusSelect.isVisible()) {
        await healthStatusSelect.selectOption(lowRiskProfile.healthStatus);
      }
      
      if (await annualCostsInput.isVisible()) {
        await annualCostsInput.fill(lowRiskProfile.annualCosts.toString());
      }
    });

    test.step('Calculate optimal deductible', async () => {
      const calculateHealthButton = page.locator('[data-testid="calculate-healthcare"]');
      if (await calculateHealthButton.isVisible()) {
        await calculateHealthButton.click();
      }

      // Wait for calculation to complete
      await page.waitForTimeout(2000);
    });

    test.step('Verify deductible recommendation', async () => {
      const recommendedDeductible = page.locator('[data-testid="recommended-deductible"]');
      
      if (await recommendedDeductible.isVisible()) {
        const deductibleText = await recommendedDeductible.textContent();
        
        // For low-risk profile, should recommend high deductible (CHF 2500)
        expect(deductibleText).toContain('2500');
        
        console.log('Recommended deductible for low-risk profile:', deductibleText);
      }
    });

    test.step('Verify cost-benefit analysis', async () => {
      const costBenefitSection = page.locator('[data-testid="cost-benefit-analysis"]');
      
      if (await costBenefitSection.isVisible()) {
        const analysisText = await costBenefitSection.textContent();
        
        // Should show potential savings
        expect(analysisText?.toLowerCase()).toContain('savings');
        
        console.log('Cost-benefit analysis:', analysisText);
      }
    });
  });

  test('E2E-HEALTH-002: Deductible Optimization for High-Risk Profile', async ({ page }) => {
    const highRiskProfile = swissTestData.healthcareProfiles.find(p => p.id === 'high-risk');
    
    test.step('Navigate to healthcare optimization section', async () => {
      await swissBudgetPage.navigateToTab('analysis');
      await expect(swissBudgetPage.healthcareSection).toBeVisible();
    });

    test.step('Input high-risk health profile', async () => {
      const ageInput = page.locator('[data-testid="health-age"]');
      const healthStatusSelect = page.locator('[data-testid="health-status"]');
      const annualCostsInput = page.locator('[data-testid="annual-health-costs"]');

      if (await ageInput.isVisible()) {
        await ageInput.fill(highRiskProfile.age.toString());
      }
      
      if (await healthStatusSelect.isVisible()) {
        await healthStatusSelect.selectOption(highRiskProfile.healthStatus);
      }
      
      if (await annualCostsInput.isVisible()) {
        await annualCostsInput.fill(highRiskProfile.annualCosts.toString());
      }
    });

    test.step('Calculate optimal deductible for high-risk profile', async () => {
      const calculateHealthButton = page.locator('[data-testid="calculate-healthcare"]');
      if (await calculateHealthButton.isVisible()) {
        await calculateHealthButton.click();
      }

      await page.waitForTimeout(2000);
    });

    test.step('Verify low deductible recommendation', async () => {
      const recommendedDeductible = page.locator('[data-testid="recommended-deductible"]');
      
      if (await recommendedDeductible.isVisible()) {
        const deductibleText = await recommendedDeductible.textContent();
        
        // For high-risk profile, should recommend low deductible (CHF 300)
        expect(deductibleText).toContain('300');
        
        console.log('Recommended deductible for high-risk profile:', deductibleText);
      }
    });
  });

  test('E2E-HEALTH-003: Deductible Comparison Across All Options', async ({ page }) => {
    const deductibleOptions = [300, 500, 1000, 1500, 2000, 2500];
    const comparisonResults: Record<number, any> = {};

    test.step('Navigate to healthcare comparison tool', async () => {
      await swissBudgetPage.navigateToTab('analysis');
      await expect(swissBudgetPage.healthcareSection).toBeVisible();
    });

    test.step('Input medium-risk profile for comparison', async () => {
      const mediumRiskProfile = swissTestData.healthcareProfiles.find(p => p.id === 'medium-risk');
      
      const ageInput = page.locator('[data-testid="health-age"]');
      const annualCostsInput = page.locator('[data-testid="annual-health-costs"]');

      if (await ageInput.isVisible()) {
        await ageInput.fill(mediumRiskProfile.age.toString());
      }
      
      if (await annualCostsInput.isVisible()) {
        await annualCostsInput.fill(mediumRiskProfile.annualCosts.toString());
      }
    });

    test.step('Compare all deductible options', async () => {
      for (const deductible of deductibleOptions) {
        await test.step(`Test deductible CHF ${deductible}`, async () => {
          const deductibleSelect = page.locator('[data-testid="deductible-select"]');
          
          if (await deductibleSelect.isVisible()) {
            await deductibleSelect.selectOption(deductible.toString());
            
            // Trigger calculation
            const calculateButton = page.locator('[data-testid="calculate-healthcare"]');
            if (await calculateButton.isVisible()) {
              await calculateButton.click();
              await page.waitForTimeout(1000);
            }
            
            // Extract results
            const totalCostElement = page.locator('[data-testid="total-annual-cost"]');
            if (await totalCostElement.isVisible()) {
              const totalCost = await totalCostElement.textContent();
              comparisonResults[deductible] = {
                deductible,
                totalCost,
              };
            }
          }
        });
      }
    });

    test.step('Verify deductible comparison logic', async () => {
      // Should have results for all deductible options
      expect(Object.keys(comparisonResults)).toHaveLength(deductibleOptions.length);
      
      console.log('Deductible comparison results:', comparisonResults);
      
      // Verify that different deductibles produce different total costs
      const costs = Object.values(comparisonResults).map(r => r.totalCost);
      const uniqueCosts = new Set(costs);
      expect(uniqueCosts.size).toBeGreaterThan(1); // Should have different costs
    });
  });

  test('E2E-HEALTH-004: Premium Comparison Across Cantons', async ({ page }) => {
    const testCantons = ['ZH', 'GE', 'VD', 'BE'];
    const premiumResults: Record<string, any> = {};

    test.step('Compare healthcare premiums across cantons', async () => {
      for (const canton of testCantons) {
        await test.step(`Check premiums for canton ${canton}`, async () => {
          // Navigate to healthcare section
          await swissBudgetPage.navigateToTab('analysis');
          
          // Set canton
          const cantonSelect = page.locator('[data-testid="healthcare-canton"]');
          if (await cantonSelect.isVisible()) {
            await cantonSelect.selectOption(canton);
          }
          
          // Set standard profile
          const ageInput = page.locator('[data-testid="health-age"]');
          if (await ageInput.isVisible()) {
            await ageInput.fill('35');
          }
          
          // Calculate premiums
          const calculateButton = page.locator('[data-testid="calculate-healthcare"]');
          if (await calculateButton.isVisible()) {
            await calculateButton.click();
            await page.waitForTimeout(1500);
          }
          
          // Extract premium information
          const premiumElement = page.locator('[data-testid="monthly-premium"]');
          if (await premiumElement.isVisible()) {
            const premium = await premiumElement.textContent();
            premiumResults[canton] = {
              canton,
              premium,
            };
          }
        });
      }
    });

    test.step('Verify canton premium differences', async () => {
      console.log('Canton premium comparison:', premiumResults);
      
      // Should have premium data for all tested cantons
      expect(Object.keys(premiumResults)).toHaveLength(testCantons.length);
      
      // Premiums should vary between cantons
      const premiums = Object.values(premiumResults).map(r => r.premium);
      const uniquePremiums = new Set(premiums);
      expect(uniquePremiums.size).toBeGreaterThan(1);
    });
  });

  test('E2E-HEALTH-005: Healthcare Cost Impact on FIRE Calculation', async ({ page }) => {
    test.step('Input base financial scenario', async () => {
      await swissBudgetPage.fillBasicFinancialInfo({
        monthlyIncome: 8000,
        monthlyExpenses: 5000,
        currentSavings: 100000,
        canton: 'ZH',
        age: 35,
        retirementAge: 60,
      });
    });

    test.step('Calculate FIRE without healthcare optimization', async () => {
      await swissBudgetPage.calculateFIRE();
      const baseResults = await swissBudgetPage.getFIREResults();
      
      console.log('Base FIRE results (without healthcare optimization):', baseResults);
    });

    test.step('Apply healthcare optimization', async () => {
      await swissBudgetPage.navigateToTab('analysis');
      await expect(swissBudgetPage.healthcareSection).toBeVisible();
      
      // Optimize for high deductible (assuming healthy profile)
      const deductibleSelect = page.locator('[data-testid="deductible-select"]');
      if (await deductibleSelect.isVisible()) {
        await deductibleSelect.selectOption('2500'); // High deductible
      }
      
      const calculateHealthButton = page.locator('[data-testid="calculate-healthcare"]');
      if (await calculateHealthButton.isVisible()) {
        await calculateHealthButton.click();
      }
    });

    test.step('Recalculate FIRE with healthcare optimization', async () => {
      await swissBudgetPage.navigateToTab('input');
      await swissBudgetPage.calculateFIRE();
      const optimizedResults = await swissBudgetPage.getFIREResults();
      
      console.log('Optimized FIRE results (with healthcare optimization):', optimizedResults);
      
      // Healthcare optimization should improve FIRE timeline
      expect(optimizedResults.fireYears).toBeLessThanOrEqual(baseResults.fireYears);
    });
  });

  test('E2E-HEALTH-006: Family Healthcare Planning', async ({ page }) => {
    test.step('Input family scenario', async () => {
      await swissBudgetPage.navigateToTab('analysis');
      await expect(swissBudgetPage.healthcareSection).toBeVisible();
      
      // Set family size
      const familySizeSelect = page.locator('[data-testid="family-size"]');
      if (await familySizeSelect.isVisible()) {
        await familySizeSelect.selectOption('4'); // Family of 4
      }
      
      // Set ages for family members
      const adultAgeInputs = page.locator('[data-testid^="adult-age"]');
      const childAgeInputs = page.locator('[data-testid^="child-age"]');
      
      if (await adultAgeInputs.first().isVisible()) {
        await adultAgeInputs.first().fill('35');
        if (await adultAgeInputs.nth(1).isVisible()) {
          await adultAgeInputs.nth(1).fill('33');
        }
      }
      
      if (await childAgeInputs.first().isVisible()) {
        await childAgeInputs.first().fill('8');
        if (await childAgeInputs.nth(1).isVisible()) {
          await childAgeInputs.nth(1).fill('5');
        }
      }
    });

    test.step('Calculate family healthcare costs', async () => {
      const calculateButton = page.locator('[data-testid="calculate-healthcare"]');
      if (await calculateButton.isVisible()) {
        await calculateButton.click();
        await page.waitForTimeout(2000);
      }
    });

    test.step('Verify family healthcare optimization', async () => {
      const familyCostElement = page.locator('[data-testid="family-total-cost"]');
      
      if (await familyCostElement.isVisible()) {
        const familyCost = await familyCostElement.textContent();
        console.log('Family healthcare cost:', familyCost);
        
        // Family costs should be higher than individual
        expect(familyCost).toBeTruthy();
      }
      
      // Check for family-specific recommendations
      const recommendationsElement = page.locator('[data-testid="family-recommendations"]');
      if (await recommendationsElement.isVisible()) {
        const recommendations = await recommendationsElement.textContent();
        console.log('Family healthcare recommendations:', recommendations);
      }
    });
  });

  test('E2E-HEALTH-007: Healthcare Cost Trends and Projections', async ({ page }) => {
    test.step('Navigate to healthcare projections', async () => {
      await swissBudgetPage.navigateToTab('visualization');
      
      // Look for healthcare cost projections chart
      const healthcareChart = page.locator('[data-testid="healthcare-projections-chart"]');
      if (await healthcareChart.isVisible()) {
        console.log('Healthcare cost projections chart found');
      }
    });

    test.step('Verify healthcare cost inflation modeling', async () => {
      // Input current age and retirement age to see long-term projections
      const currentAge = 30;
      const retirementAge = 65;
      
      await swissBudgetPage.navigateToTab('input');
      await swissBudgetPage.fillBasicFinancialInfo({
        monthlyIncome: 7000,
        monthlyExpenses: 4500,
        currentSavings: 80000,
        canton: 'VD',
        age: currentAge,
        retirementAge: retirementAge,
      });
      
      await swissBudgetPage.calculateFIRE();
      
      // Check if healthcare cost inflation is factored into calculations
      await swissBudgetPage.navigateToTab('analysis');
      
      const healthcareInflationElement = page.locator('[data-testid="healthcare-inflation"]');
      if (await healthcareInflationElement.isVisible()) {
        const inflationInfo = await healthcareInflationElement.textContent();
        console.log('Healthcare inflation modeling:', inflationInfo);
      }
    });
  });

  test.afterEach(async ({ page }) => {
    // Take screenshot on failure for debugging
    if (test.info().status === 'failed') {
      await swissBudgetPage.takeScreenshot(`healthcare-optimization-failure-${test.info().title}`);
    }
  });
});
