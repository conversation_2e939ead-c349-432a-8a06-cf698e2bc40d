/**
 * E2E Tests for WCAG 2.1 AA Accessibility Compliance
 * Tests Swiss Budget Pro for accessibility standards compliance
 */

import { test, expect } from '@playwright/test';
import { SwissBudgetProPage } from '../../page-objects/pages/SwissBudgetProPage';

test.describe('WCAG 2.1 AA Accessibility Compliance', () => {
  let swissBudgetPage: SwissBudgetProPage;

  test.beforeEach(async ({ page }) => {
    swissBudgetPage = new SwissBudgetProPage(page);
    await swissBudgetPage.goto();
    await swissBudgetPage.verifyPageLoaded();
  });

  test('E2E-A11Y-001: Keyboard Navigation Compliance', async ({ page }) => {
    test.step('Test tab order and keyboard navigation', async () => {
      // Start from the beginning of the page
      await page.keyboard.press('Home');
      
      // Test tab navigation through all interactive elements
      const interactiveElements = [];
      let tabCount = 0;
      const maxTabs = 50; // Prevent infinite loop
      
      while (tabCount < maxTabs) {
        await page.keyboard.press('Tab');
        tabCount++;
        
        const focusedElement = await page.evaluate(() => {
          const el = document.activeElement;
          return el ? {
            tagName: el.tagName,
            type: el.getAttribute('type'),
            id: el.id,
            className: el.className,
            ariaLabel: el.getAttribute('aria-label'),
            textContent: el.textContent?.substring(0, 50),
          } : null;
        });
        
        if (focusedElement) {
          interactiveElements.push(focusedElement);
          
          // Stop if we've cycled back to the first element
          if (tabCount > 5 && 
              focusedElement.id === interactiveElements[0]?.id &&
              focusedElement.tagName === interactiveElements[0]?.tagName) {
            break;
          }
        }
      }
      
      console.log('Interactive elements found:', interactiveElements.length);
      console.log('Tab order:', interactiveElements.map(el => `${el.tagName}${el.id ? '#' + el.id : ''}`));
      
      // Verify logical tab order
      expect(interactiveElements.length).toBeGreaterThan(5); // Should have multiple interactive elements
    });

    test.step('Test keyboard shortcuts and access keys', async () => {
      // Test common keyboard shortcuts
      await page.keyboard.press('Alt+1'); // Skip to main content (if implemented)
      await page.keyboard.press('Alt+2'); // Skip to navigation (if implemented)
      
      // Test escape key functionality
      await page.keyboard.press('Escape');
      
      // Verify no JavaScript errors occurred
      const errors = await page.evaluate(() => window.errors || []);
      expect(errors.length).toBe(0);
    });

    test.step('Test focus visibility', async () => {
      // Navigate to input field and verify focus is visible
      await swissBudgetPage.monthlyIncomeInput.focus();
      
      // Check if focus indicator is visible
      const focusStyles = await swissBudgetPage.monthlyIncomeInput.evaluate(el => {
        const styles = window.getComputedStyle(el, ':focus');
        return {
          outline: styles.outline,
          outlineWidth: styles.outlineWidth,
          outlineColor: styles.outlineColor,
          boxShadow: styles.boxShadow,
        };
      });
      
      // Should have visible focus indicator
      const hasFocusIndicator = focusStyles.outline !== 'none' || 
                               focusStyles.outlineWidth !== '0px' ||
                               focusStyles.boxShadow !== 'none';
      
      expect(hasFocusIndicator).toBeTruthy();
      console.log('Focus styles:', focusStyles);
    });
  });

  test('E2E-A11Y-002: Screen Reader Compatibility', async ({ page }) => {
    test.step('Test ARIA labels and descriptions', async () => {
      // Check form inputs have proper labels
      const formInputs = [
        swissBudgetPage.monthlyIncomeInput,
        swissBudgetPage.monthlyExpensesInput,
        swissBudgetPage.currentSavingsInput,
        swissBudgetPage.cantonSelect,
      ];

      for (const input of formInputs) {
        const ariaLabel = await input.getAttribute('aria-label');
        const ariaLabelledBy = await input.getAttribute('aria-labelledby');
        const ariaDescribedBy = await input.getAttribute('aria-describedby');
        const associatedLabel = await input.evaluate(el => {
          const id = el.id;
          return id ? document.querySelector(`label[for="${id}"]`)?.textContent : null;
        });

        const hasAccessibleName = ariaLabel || ariaLabelledBy || associatedLabel;
        expect(hasAccessibleName).toBeTruthy();
        
        console.log('Input accessibility:', {
          ariaLabel,
          ariaLabelledBy,
          ariaDescribedBy,
          associatedLabel,
        });
      }
    });

    test.step('Test heading structure', async () => {
      // Check for proper heading hierarchy
      const headings = await page.locator('h1, h2, h3, h4, h5, h6').all();
      const headingStructure = [];

      for (const heading of headings) {
        const tagName = await heading.evaluate(el => el.tagName);
        const textContent = await heading.textContent();
        const level = parseInt(tagName.substring(1));
        
        headingStructure.push({ level, text: textContent?.trim() });
      }

      console.log('Heading structure:', headingStructure);

      // Should have at least one h1
      const h1Count = headingStructure.filter(h => h.level === 1).length;
      expect(h1Count).toBeGreaterThanOrEqual(1);

      // Check for logical heading progression (no skipping levels)
      for (let i = 1; i < headingStructure.length; i++) {
        const current = headingStructure[i];
        const previous = headingStructure[i - 1];
        
        if (current.level > previous.level) {
          // Should not skip heading levels (e.g., h2 to h4)
          expect(current.level - previous.level).toBeLessThanOrEqual(1);
        }
      }
    });

    test.step('Test landmark regions', async () => {
      // Check for proper landmark regions
      const landmarks = await page.locator('[role="main"], [role="navigation"], [role="banner"], [role="contentinfo"], main, nav, header, footer').all();
      
      const landmarkInfo = [];
      for (const landmark of landmarks) {
        const role = await landmark.getAttribute('role') || await landmark.evaluate(el => el.tagName.toLowerCase());
        const ariaLabel = await landmark.getAttribute('aria-label');
        const ariaLabelledBy = await landmark.getAttribute('aria-labelledby');
        
        landmarkInfo.push({ role, ariaLabel, ariaLabelledBy });
      }

      console.log('Landmark regions:', landmarkInfo);
      
      // Should have at least a main content area
      const hasMain = landmarkInfo.some(l => l.role === 'main' || l.role === 'MAIN');
      expect(hasMain).toBeTruthy();
    });
  });

  test('E2E-A11Y-003: Color Contrast and Visual Accessibility', async ({ page }) => {
    test.step('Test color contrast ratios', async () => {
      // Take screenshot for manual color contrast analysis
      await page.screenshot({
        path: 'test-results/screenshots/color-contrast-analysis.png',
        fullPage: true,
      });

      // Test text elements for sufficient contrast
      const textElements = await page.locator('p, span, div, label, button, a').all();
      const contrastIssues = [];

      // Sample a few key text elements
      const sampleElements = textElements.slice(0, 10);
      
      for (const element of sampleElements) {
        const styles = await element.evaluate(el => {
          const computed = window.getComputedStyle(el);
          return {
            color: computed.color,
            backgroundColor: computed.backgroundColor,
            fontSize: computed.fontSize,
            fontWeight: computed.fontWeight,
            textContent: el.textContent?.substring(0, 30),
          };
        });

        // Log for manual verification
        console.log('Text element styles:', styles);
      }
    });

    test.step('Test reduced motion preferences', async () => {
      // Test with reduced motion preference
      await page.emulateMedia({ reducedMotion: 'reduce' });
      
      // Navigate through the application
      await swissBudgetPage.fillBasicFinancialInfo({
        monthlyIncome: 6000,
        monthlyExpenses: 4000,
        currentSavings: 80000,
        canton: 'ZH',
      });

      await swissBudgetPage.calculateFIRE();
      
      // Verify animations are reduced or disabled
      const animatedElements = await page.locator('[style*="transition"], [style*="animation"], .animate').all();
      
      for (const element of animatedElements) {
        const animationStyles = await element.evaluate(el => {
          const computed = window.getComputedStyle(el);
          return {
            transition: computed.transition,
            animation: computed.animation,
            transform: computed.transform,
          };
        });
        
        console.log('Animation styles with reduced motion:', animationStyles);
      }
    });

    test.step('Test high contrast mode compatibility', async () => {
      // Simulate high contrast mode
      await page.addStyleTag({
        content: `
          @media (prefers-contrast: high) {
            * {
              background: black !important;
              color: white !important;
              border-color: white !important;
            }
          }
        `,
      });

      // Verify application is still usable
      await expect(swissBudgetPage.appTitle).toBeVisible();
      await expect(swissBudgetPage.monthlyIncomeInput).toBeVisible();
      
      // Take screenshot for verification
      await page.screenshot({
        path: 'test-results/screenshots/high-contrast-mode.png',
        fullPage: true,
      });
    });
  });

  test('E2E-A11Y-004: Form Accessibility', async ({ page }) => {
    test.step('Test form validation and error messages', async () => {
      // Submit form with invalid data to test error handling
      await swissBudgetPage.monthlyIncomeInput.fill('-1000'); // Invalid negative income
      await swissBudgetPage.calculateButton.click();

      // Check for accessible error messages
      const errorMessages = await page.locator('[role="alert"], .error, [aria-live="polite"], [aria-live="assertive"]').all();
      
      if (errorMessages.length > 0) {
        for (const error of errorMessages) {
          const errorText = await error.textContent();
          const ariaLive = await error.getAttribute('aria-live');
          const role = await error.getAttribute('role');
          
          console.log('Error message accessibility:', {
            text: errorText,
            ariaLive,
            role,
          });
          
          // Error messages should be announced to screen readers
          expect(ariaLive || role).toBeTruthy();
        }
      }
    });

    test.step('Test form field requirements and instructions', async () => {
      // Check for required field indicators
      const requiredFields = await page.locator('[required], [aria-required="true"]').all();
      
      for (const field of requiredFields) {
        const ariaRequired = await field.getAttribute('aria-required');
        const required = await field.getAttribute('required');
        const ariaDescribedBy = await field.getAttribute('aria-describedby');
        
        console.log('Required field accessibility:', {
          ariaRequired,
          required,
          ariaDescribedBy,
        });
        
        // Required fields should be properly marked
        expect(ariaRequired === 'true' || required !== null).toBeTruthy();
      }
    });

    test.step('Test form completion with keyboard only', async () => {
      // Complete entire form using only keyboard
      await page.keyboard.press('Home'); // Start from top
      
      // Navigate to and fill income field
      await page.keyboard.press('Tab');
      await page.keyboard.type('7500');
      
      // Navigate to and fill expenses field
      await page.keyboard.press('Tab');
      await page.keyboard.type('5000');
      
      // Navigate to and fill savings field
      await page.keyboard.press('Tab');
      await page.keyboard.type('100000');
      
      // Navigate to and select canton
      await page.keyboard.press('Tab');
      await page.keyboard.press('ArrowDown'); // Open dropdown
      await page.keyboard.press('ArrowDown'); // Select option
      await page.keyboard.press('Enter'); // Confirm selection
      
      // Navigate to and activate calculate button
      await page.keyboard.press('Tab');
      await page.keyboard.press('Enter');
      
      // Verify calculation completed
      await swissBudgetPage.waitForCalculationComplete();
      await expect(swissBudgetPage.fireYearsResult).toBeVisible();
    });
  });

  test('E2E-A11Y-005: Dynamic Content Accessibility', async ({ page }) => {
    test.step('Test live region updates', async () => {
      // Fill form and trigger calculation
      await swissBudgetPage.fillBasicFinancialInfo({
        monthlyIncome: 8000,
        monthlyExpenses: 5500,
        currentSavings: 120000,
        canton: 'VD',
      });

      // Monitor for live region updates during calculation
      const liveRegions = await page.locator('[aria-live], [role="status"], [role="alert"]').all();
      
      console.log(`Found ${liveRegions.length} live regions`);
      
      await swissBudgetPage.calculateFIRE();
      
      // Verify results are announced to screen readers
      const resultsContainer = swissBudgetPage.fireYearsResult.locator('..');
      const ariaLive = await resultsContainer.getAttribute('aria-live');
      const role = await resultsContainer.getAttribute('role');
      
      if (ariaLive || role) {
        console.log('Results accessibility:', { ariaLive, role });
      }
    });

    test.step('Test modal and dialog accessibility', async () => {
      // Look for modal triggers (export, help, etc.)
      const modalTriggers = await page.locator('[data-testid*="modal"], [aria-haspopup="dialog"], button:has-text("Export")').all();
      
      if (modalTriggers.length > 0) {
        const firstTrigger = modalTriggers[0];
        await firstTrigger.click();
        
        // Check for modal accessibility
        const modal = page.locator('[role="dialog"], [role="alertdialog"], .modal').first();
        
        if (await modal.isVisible()) {
          const ariaLabelledBy = await modal.getAttribute('aria-labelledby');
          const ariaDescribedBy = await modal.getAttribute('aria-describedby');
          const ariaModal = await modal.getAttribute('aria-modal');
          
          console.log('Modal accessibility:', {
            ariaLabelledBy,
            ariaDescribedBy,
            ariaModal,
          });
          
          // Modal should trap focus
          await page.keyboard.press('Tab');
          const focusedElement = await page.evaluate(() => document.activeElement?.tagName);
          
          // Focus should be within modal
          const focusWithinModal = await modal.evaluate((modal, focusedTag) => {
            return modal.contains(document.activeElement);
          }, focusedElement);
          
          expect(focusWithinModal).toBeTruthy();
          
          // Close modal with Escape
          await page.keyboard.press('Escape');
          await expect(modal).not.toBeVisible();
        }
      }
    });
  });

  test('E2E-A11Y-006: Swiss-Specific Accessibility Features', async ({ page }) => {
    test.step('Test multi-language accessibility', async () => {
      // Test language switching accessibility
      if (await swissBudgetPage.languageToggle.isVisible()) {
        const langToggleRole = await swissBudgetPage.languageToggle.getAttribute('role');
        const langToggleLabel = await swissBudgetPage.languageToggle.getAttribute('aria-label');
        
        console.log('Language toggle accessibility:', {
          role: langToggleRole,
          label: langToggleLabel,
        });
        
        // Switch to French
        await swissBudgetPage.switchLanguage('fr');
        
        // Verify page language attribute is updated
        const htmlLang = await page.getAttribute('html', 'lang');
        expect(htmlLang).toContain('fr');
        
        // Verify content is properly translated and accessible
        await expect(swissBudgetPage.appTitle).toBeVisible();
      }
    });

    test.step('Test Swiss financial terminology accessibility', async () => {
      // Navigate to analysis section with Swiss-specific content
      await swissBudgetPage.fillBasicFinancialInfo({
        monthlyIncome: 9000,
        monthlyExpenses: 6000,
        currentSavings: 200000,
        canton: 'ZH',
      });

      await swissBudgetPage.calculateFIRE();
      await swissBudgetPage.navigateToTab('analysis');

      // Check Pillar 3a section accessibility
      if (await swissBudgetPage.pillar3aSection.isVisible()) {
        const pillar3aHeading = swissBudgetPage.pillar3aSection.locator('h1, h2, h3, h4, h5, h6').first();
        
        if (await pillar3aHeading.isVisible()) {
          const headingText = await pillar3aHeading.textContent();
          console.log('Pillar 3a heading:', headingText);
          
          // Should have proper heading structure
          expect(headingText).toBeTruthy();
        }
        
        // Check for explanatory content
        const explanationText = await swissBudgetPage.pillar3aSection.textContent();
        expect(explanationText?.length).toBeGreaterThan(50); // Should have substantial explanatory content
      }
    });

    test.step('Test currency and number format accessibility', async () => {
      // Verify Swiss franc amounts are properly formatted and accessible
      await swissBudgetPage.fillBasicFinancialInfo({
        monthlyIncome: 8500,
        monthlyExpenses: 5800,
        currentSavings: 175000,
        canton: 'GE',
      });

      await swissBudgetPage.calculateFIRE();
      
      // Check FIRE number formatting
      const fireNumberText = await swissBudgetPage.fireNumberResult.textContent();
      
      if (fireNumberText) {
        // Should include currency symbol or abbreviation
        const hasCurrency = fireNumberText.includes('CHF') || fireNumberText.includes('Fr.');
        expect(hasCurrency).toBeTruthy();
        
        console.log('FIRE number formatting:', fireNumberText);
      }
    });
  });

  test.afterEach(async ({ page }) => {
    // Take screenshot on failure for accessibility analysis
    if (test.info().status === 'failed') {
      await swissBudgetPage.takeScreenshot(`accessibility-failure-${test.info().title}`);
    }
  });
});
