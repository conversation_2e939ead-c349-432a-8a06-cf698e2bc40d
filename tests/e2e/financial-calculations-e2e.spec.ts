/**
 * End-to-End Tests for Financial Calculations
 *
 * This test suite verifies that our financial calculation utilities
 * work correctly in the actual application environment.
 */

import { test, expect } from '@playwright/test';

test.describe('Financial Calculations E2E', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    // Wait for the app to load - look for any content that indicates the app is ready
    await page.waitForSelector('body', { timeout: 10000 });
    await page.waitForTimeout(2000); // Give the app time to fully initialize
  });

  test('Basic FIRE calculation workflow', async ({ page }) => {
    // Input basic financial data
    await test.step('Input financial data', async () => {
      // Enter monthly income
      const incomeInput = page
        .locator(
          'input[placeholder*="8000"], input[aria-label*="income"], input[name*="income"]',
        )
        .first();
      await incomeInput.fill('8000');

      // Enter monthly expenses
      const expensesInput = page
        .locator(
          'input[placeholder*="5000"], input[aria-label*="expense"], input[name*="expense"]',
        )
        .first();
      await expensesInput.fill('5000');

      // Select age
      const ageInput = page
        .locator(
          'input[placeholder*="30"], input[aria-label*="age"], input[name*="age"]',
        )
        .first();
      await ageInput.fill('30');

      // Wait for calculations to update
      await page.waitForTimeout(1000);
    });

    await test.step('Verify FIRE calculations appear', async () => {
      // Check that FIRE-related content is displayed
      const fireContent = page
        .locator('text=/FIRE|Financial Independence|Retirement/i')
        .first();
      await expect(fireContent).toBeVisible({ timeout: 5000 });

      // Check for savings rate calculation
      const savingsRate = page.locator('text=/%|percent/i').first();
      await expect(savingsRate).toBeVisible({ timeout: 5000 });
    });

    await test.step('Verify calculations are reasonable', async () => {
      // With 8000 income and 5000 expenses, savings rate should be 37.5%
      const savingsRateText = await page
        .locator('text=/37|38/')
        .first()
        .textContent();
      expect(savingsRateText).toBeTruthy();
    });
  });

  test('Swiss tax calculation integration', async ({ page }) => {
    await test.step('Select Swiss canton', async () => {
      // Look for canton selector
      const cantonSelect = page
        .locator('select[name*="canton"], select[aria-label*="canton"]')
        .first();
      if (await cantonSelect.isVisible()) {
        await cantonSelect.selectOption('ZH');
      }
    });

    await test.step('Input income for tax calculation', async () => {
      const incomeInput = page
        .locator('input[placeholder*="8000"], input[aria-label*="income"]')
        .first();
      await incomeInput.fill('100000');

      // Wait for tax calculations
      await page.waitForTimeout(2000);
    });

    await test.step('Verify tax information is displayed', async () => {
      // Look for tax-related content
      const taxContent = page.locator('text=/tax|CHF|franc/i').first();
      await expect(taxContent).toBeVisible({ timeout: 5000 });
    });
  });

  test('Input validation works correctly', async ({ page }) => {
    await test.step('Test negative income validation', async () => {
      const incomeInput = page
        .locator('input[placeholder*="8000"], input[aria-label*="income"]')
        .first();
      await incomeInput.fill('-1000');

      // Check for error message or input correction
      await page.waitForTimeout(500);
      const inputValue = await incomeInput.inputValue();

      // Either the input should be corrected or an error should be shown
      const hasError = await page
        .locator('text=/error|invalid|negative/i')
        .isVisible();
      const isValueCorrected = inputValue !== '-1000';

      expect(hasError || isValueCorrected).toBeTruthy();
    });

    await test.step('Test unrealistic age validation', async () => {
      const ageInput = page
        .locator('input[placeholder*="30"], input[aria-label*="age"]')
        .first();
      await ageInput.fill('150');

      await page.waitForTimeout(500);

      // Check for validation
      const hasError = await page
        .locator('text=/error|invalid|age/i')
        .isVisible();
      const inputValue = await ageInput.inputValue();
      const isValueCorrected = inputValue !== '150';

      expect(hasError || isValueCorrected).toBeTruthy();
    });
  });

  test('Dark mode functionality', async ({ page }) => {
    await test.step('Toggle dark mode', async () => {
      // Look for dark mode toggle
      const darkModeToggle = page
        .locator(
          'button[aria-label*="dark"], button[title*="dark"], [data-testid*="dark"]',
        )
        .first();

      if (await darkModeToggle.isVisible()) {
        await darkModeToggle.click();
        await page.waitForTimeout(500);

        // Verify dark mode is applied
        const body = page.locator('body');
        const bodyClass = await body.getAttribute('class');
        expect(bodyClass).toContain('dark');
      }
    });

    await test.step('Verify calculations still work in dark mode', async () => {
      const incomeInput = page
        .locator('input[placeholder*="8000"], input[aria-label*="income"]')
        .first();
      await incomeInput.fill('7500');

      await page.waitForTimeout(1000);

      // Verify content is still visible and calculations work
      const fireContent = page
        .locator('text=/FIRE|Financial Independence/i')
        .first();
      await expect(fireContent).toBeVisible();
    });
  });

  test('Data persistence across page reloads', async ({ page }) => {
    await test.step('Input data', async () => {
      const incomeInput = page
        .locator('input[placeholder*="8000"], input[aria-label*="income"]')
        .first();
      await incomeInput.fill('9000');

      const ageInput = page
        .locator('input[placeholder*="30"], input[aria-label*="age"]')
        .first();
      await ageInput.fill('35');

      await page.waitForTimeout(1000);
    });

    await test.step('Reload page and verify data persistence', async () => {
      await page.reload();
      await page.waitForSelector('[data-testid="app-container"]', {
        timeout: 10000,
      });

      // Check if data was persisted
      const incomeInput = page
        .locator('input[placeholder*="8000"], input[aria-label*="income"]')
        .first();
      const ageInput = page
        .locator('input[placeholder*="30"], input[aria-label*="age"]')
        .first();

      const incomeValue = await incomeInput.inputValue();
      const ageValue = await ageInput.inputValue();

      // Data should be persisted (or at least the app should load without errors)
      expect(incomeValue === '9000' || incomeValue === '').toBeTruthy();
      expect(ageValue === '35' || ageValue === '').toBeTruthy();
    });
  });

  test('Responsive design on mobile viewport', async ({ page }) => {
    await test.step('Set mobile viewport', async () => {
      await page.setViewportSize({ width: 375, height: 667 });
    });

    await test.step('Verify app is usable on mobile', async () => {
      // App should load and be interactive
      await page.waitForSelector('body', { timeout: 10000 });
      await page.waitForTimeout(2000);

      // Try to input data on mobile
      const incomeInput = page
        .locator('input[placeholder*="8000"], input[aria-label*="income"]')
        .first();
      if (await incomeInput.isVisible()) {
        await incomeInput.fill('6000');
      }

      await page.waitForTimeout(1000);

      // Verify calculations still work
      const content = page.locator('body');
      await expect(content).toBeVisible();
    });
  });

  test('Performance benchmarks', async ({ page }) => {
    await test.step('Measure page load time', async () => {
      const startTime = Date.now();
      await page.goto('/');
      await page.waitForSelector('body', { timeout: 10000 });
      await page.waitForTimeout(2000);
      const loadTime = Date.now() - startTime;

      // Page should load within 5 seconds
      expect(loadTime).toBeLessThan(5000);
    });

    await test.step('Measure calculation response time', async () => {
      const incomeInput = page
        .locator('input[placeholder*="8000"], input[aria-label*="income"]')
        .first();

      const startTime = Date.now();
      await incomeInput.fill('12000');

      // Wait for any visible change indicating calculation completion
      await page.waitForTimeout(100);
      const responseTime = Date.now() - startTime;

      // Calculations should be near-instantaneous
      expect(responseTime).toBeLessThan(2000);
    });
  });

  test('Accessibility compliance', async ({ page }) => {
    await test.step('Check for basic accessibility features', async () => {
      // Check for proper heading structure
      const h1 = page.locator('h1').first();
      await expect(h1).toBeVisible();

      // Check for form labels
      const inputs = page.locator('input');
      const inputCount = await inputs.count();

      if (inputCount > 0) {
        // At least some inputs should have labels or aria-labels
        const firstInput = inputs.first();
        const hasLabel =
          (await firstInput.getAttribute('aria-label')) !== null ||
          (await page.locator('label').first().isVisible());
        expect(hasLabel).toBeTruthy();
      }
    });

    await test.step('Test keyboard navigation', async () => {
      // Tab through the interface
      await page.keyboard.press('Tab');
      await page.keyboard.press('Tab');
      await page.keyboard.press('Tab');

      // Should be able to navigate without errors
      const focusedElement = page.locator(':focus');
      await expect(focusedElement).toBeVisible();
    });
  });
});
