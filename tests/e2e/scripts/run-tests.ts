#!/usr/bin/env node

/**
 * Comprehensive Test Runner for Swiss Budget Pro E2E Tests
 * Provides intelligent test execution with reporting and analysis
 */

import { execSync, spawn } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';
import { TestConfigManager, testSuites, testCategories } from '../config/test-config';

interface TestRunOptions {
  suite?: string;
  environment?: string;
  browsers?: string[];
  categories?: string[];
  tags?: string[];
  parallel?: boolean;
  headed?: boolean;
  debug?: boolean;
  retries?: number;
  timeout?: number;
  report?: boolean;
  cleanup?: boolean;
  dryRun?: boolean;
}

class TestRunner {
  private options: TestRunOptions;
  private startTime: number = 0;
  private results: any = {};

  constructor(options: TestRunOptions = {}) {
    this.options = {
      suite: 'smoke',
      environment: 'development',
      browsers: ['chromium'],
      parallel: true,
      headed: false,
      debug: false,
      retries: 1,
      timeout: 30000,
      report: true,
      cleanup: true,
      dryRun: false,
      ...options,
    };
  }

  /**
   * Main test execution method
   */
  async run(): Promise<void> {
    console.log('🚀 Swiss Budget Pro E2E Test Runner');
    console.log('=====================================');
    
    this.startTime = Date.now();
    
    try {
      await this.validateEnvironment();
      await this.setupTestEnvironment();
      
      if (this.options.dryRun) {
        await this.performDryRun();
        return;
      }
      
      await this.executeTests();
      await this.generateReports();
      
      if (this.options.cleanup) {
        await this.cleanup();
      }
      
      this.displaySummary();
      
    } catch (error) {
      console.error('❌ Test execution failed:', error);
      process.exit(1);
    }
  }

  /**
   * Validate test environment and dependencies
   */
  private async validateEnvironment(): Promise<void> {
    console.log('🔍 Validating test environment...');
    
    // Check if Playwright is installed
    try {
      execSync('npx playwright --version', { stdio: 'pipe' });
      console.log('✅ Playwright is installed');
    } catch (error) {
      throw new Error('Playwright is not installed. Run: npm run test:e2e:install');
    }
    
    // Check if browsers are installed
    try {
      execSync('npx playwright install --dry-run', { stdio: 'pipe' });
      console.log('✅ Playwright browsers are installed');
    } catch (error) {
      console.log('⚠️ Installing Playwright browsers...');
      execSync('npx playwright install', { stdio: 'inherit' });
    }
    
    // Check if development server is running (for local environment)
    if (this.options.environment === 'development') {
      try {
        const response = await fetch('http://localhost:5173');
        if (response.ok) {
          console.log('✅ Development server is running');
        } else {
          throw new Error('Development server returned non-200 status');
        }
      } catch (error) {
        console.log('⚠️ Development server not running. Starting...');
        await this.startDevServer();
      }
    }
  }

  /**
   * Setup test environment
   */
  private async setupTestEnvironment(): Promise<void> {
    console.log('⚙️ Setting up test environment...');
    
    // Create necessary directories
    const dirs = [
      'test-results',
      'test-results/screenshots',
      'test-results/videos',
      'test-results/traces',
      'test-results/reports',
      'playwright-report',
    ];
    
    dirs.forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        console.log(`📁 Created directory: ${dir}`);
      }
    });
    
    // Generate Playwright config for this run
    const config = TestConfigManager.generatePlaywrightConfig(
      this.options.suite!,
      this.options.environment!,
    );
    
    const configPath = 'playwright.config.generated.js';
    fs.writeFileSync(configPath, `module.exports = ${JSON.stringify(config, null, 2)};`);
    console.log(`📝 Generated Playwright config: ${configPath}`);
  }

  /**
   * Perform dry run to show what would be executed
   */
  private async performDryRun(): Promise<void> {
    console.log('🔍 Dry Run - Test Execution Plan');
    console.log('================================');
    
    const suite = TestConfigManager.getTestSuite(this.options.suite!);
    const plan = TestConfigManager.getExecutionPlan(this.options.suite!);
    
    console.log(`Suite: ${suite.name}`);
    console.log(`Categories: ${plan.totalCategories}`);
    console.log(`Critical Tests: ${plan.criticalTests}`);
    console.log(`Estimated Duration: ${Math.round(plan.estimatedDuration / 1000 / 60)} minutes`);
    console.log(`Browsers: ${plan.browsers.join(', ')}`);
    console.log(`Tags: ${plan.tags.join(', ')}`);
    console.log(`Parallel: ${plan.parallelizable} categories`);
    console.log(`Sequential: ${plan.sequential} categories`);
    
    console.log('\nTest Categories to Execute:');
    suite.categories.forEach(category => {
      console.log(`  📋 ${category.name} (${category.priority} priority)`);
      console.log(`     Timeout: ${category.timeout}ms, Retries: ${category.retries}`);
      console.log(`     Browsers: ${category.browsers.join(', ')}`);
      console.log(`     Tags: ${category.tags.join(', ')}`);
    });
  }

  /**
   * Execute the actual tests
   */
  private async executeTests(): Promise<void> {
    console.log('🧪 Executing tests...');
    
    const suite = TestConfigManager.getTestSuite(this.options.suite!);
    
    // Build Playwright command
    const playwrightArgs = [
      'test',
      '--config=playwright.config.generated.js',
    ];
    
    if (this.options.headed) {
      playwrightArgs.push('--headed');
    }
    
    if (this.options.debug) {
      playwrightArgs.push('--debug');
    }
    
    if (!this.options.parallel) {
      playwrightArgs.push('--workers=1');
    }
    
    if (this.options.browsers && this.options.browsers.length > 0) {
      this.options.browsers.forEach(browser => {
        playwrightArgs.push(`--project=${browser}`);
      });
    }
    
    // Execute tests
    try {
      console.log(`Running: npx playwright ${playwrightArgs.join(' ')}`);
      
      const result = execSync(`npx playwright ${playwrightArgs.join(' ')}`, {
        stdio: 'inherit',
        encoding: 'utf8',
      });
      
      console.log('✅ Tests completed successfully');
      this.results.success = true;
      
    } catch (error) {
      console.log('❌ Some tests failed');
      this.results.success = false;
      this.results.error = error;
    }
  }

  /**
   * Generate comprehensive test reports
   */
  private async generateReports(): Promise<void> {
    if (!this.options.report) {
      return;
    }
    
    console.log('📊 Generating test reports...');
    
    try {
      // Generate HTML report
      execSync('npx playwright show-report --host=0.0.0.0', { stdio: 'pipe' });
      console.log('✅ HTML report generated');
      
      // Generate custom summary report
      await this.generateCustomReport();
      
    } catch (error) {
      console.log('⚠️ Report generation failed:', error);
    }
  }

  /**
   * Generate custom test summary report
   */
  private async generateCustomReport(): Promise<void> {
    const reportData = {
      timestamp: new Date().toISOString(),
      duration: Date.now() - this.startTime,
      options: this.options,
      results: this.results,
      environment: {
        node: process.version,
        platform: process.platform,
        arch: process.arch,
      },
    };
    
    // Read Playwright results if available
    try {
      const playwrightResults = fs.readFileSync('playwright-report/results.json', 'utf8');
      reportData.results.playwright = JSON.parse(playwrightResults);
    } catch (error) {
      console.log('⚠️ Could not read Playwright results');
    }
    
    const reportPath = `test-results/reports/summary-${Date.now()}.json`;
    fs.writeFileSync(reportPath, JSON.stringify(reportData, null, 2));
    
    console.log(`📄 Custom report saved: ${reportPath}`);
  }

  /**
   * Start development server if needed
   */
  private async startDevServer(): Promise<void> {
    return new Promise((resolve, reject) => {
      const server = spawn('npm', ['run', 'dev'], {
        stdio: 'pipe',
        detached: true,
      });
      
      let serverReady = false;
      
      server.stdout?.on('data', (data) => {
        const output = data.toString();
        if (output.includes('Local:') && !serverReady) {
          serverReady = true;
          console.log('✅ Development server started');
          resolve();
        }
      });
      
      server.stderr?.on('data', (data) => {
        console.error('Server error:', data.toString());
      });
      
      // Timeout after 30 seconds
      setTimeout(() => {
        if (!serverReady) {
          server.kill();
          reject(new Error('Development server failed to start within 30 seconds'));
        }
      }, 30000);
    });
  }

  /**
   * Cleanup test artifacts
   */
  private async cleanup(): Promise<void> {
    console.log('🧹 Cleaning up...');
    
    // Remove generated config
    if (fs.existsSync('playwright.config.generated.js')) {
      fs.unlinkSync('playwright.config.generated.js');
    }
    
    // Clean up old screenshots (keep last 10)
    const screenshotDir = 'test-results/screenshots';
    if (fs.existsSync(screenshotDir)) {
      const files = fs.readdirSync(screenshotDir)
        .map(file => ({
          name: file,
          path: path.join(screenshotDir, file),
          time: fs.statSync(path.join(screenshotDir, file)).mtime.getTime(),
        }))
        .sort((a, b) => b.time - a.time);
      
      if (files.length > 10) {
        files.slice(10).forEach(file => {
          fs.unlinkSync(file.path);
        });
        console.log(`🗑️ Cleaned up ${files.length - 10} old screenshots`);
      }
    }
  }

  /**
   * Display test execution summary
   */
  private displaySummary(): void {
    const duration = Date.now() - this.startTime;
    const minutes = Math.floor(duration / 60000);
    const seconds = Math.floor((duration % 60000) / 1000);
    
    console.log('\n📋 Test Execution Summary');
    console.log('=========================');
    console.log(`Suite: ${this.options.suite}`);
    console.log(`Environment: ${this.options.environment}`);
    console.log(`Duration: ${minutes}m ${seconds}s`);
    console.log(`Status: ${this.results.success ? '✅ PASSED' : '❌ FAILED'}`);
    
    if (this.options.report) {
      console.log('\n📊 Reports:');
      console.log('  HTML Report: playwright-report/index.html');
      console.log('  JSON Results: playwright-report/results.json');
      console.log('  Custom Report: test-results/reports/');
    }
    
    console.log('\n🎉 Test execution completed!');
  }
}

/**
 * CLI Interface
 */
function parseArgs(): TestRunOptions {
  const args = process.argv.slice(2);
  const options: TestRunOptions = {};
  
  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    const nextArg = args[i + 1];
    
    switch (arg) {
      case '--suite':
        options.suite = nextArg;
        i++;
        break;
      case '--environment':
      case '--env':
        options.environment = nextArg;
        i++;
        break;
      case '--browsers':
        options.browsers = nextArg?.split(',');
        i++;
        break;
      case '--headed':
        options.headed = true;
        break;
      case '--debug':
        options.debug = true;
        break;
      case '--no-parallel':
        options.parallel = false;
        break;
      case '--no-report':
        options.report = false;
        break;
      case '--no-cleanup':
        options.cleanup = false;
        break;
      case '--dry-run':
        options.dryRun = true;
        break;
      case '--help':
      case '-h':
        showHelp();
        process.exit(0);
        break;
    }
  }
  
  return options;
}

function showHelp(): void {
  console.log(`
Swiss Budget Pro E2E Test Runner

Usage: npm run test:e2e:runner [options]

Options:
  --suite <name>        Test suite to run (smoke, critical, full, etc.)
  --environment <env>   Environment to test (development, staging, production)
  --browsers <list>     Comma-separated list of browsers (chromium,firefox,webkit)
  --headed             Run tests in headed mode
  --debug              Run tests in debug mode
  --no-parallel        Run tests sequentially
  --no-report          Skip report generation
  --no-cleanup         Skip cleanup after tests
  --dry-run            Show execution plan without running tests
  --help, -h           Show this help message

Examples:
  npm run test:e2e:runner --suite smoke --headed
  npm run test:e2e:runner --suite critical --environment staging
  npm run test:e2e:runner --suite full --browsers chromium,firefox
  npm run test:e2e:runner --dry-run --suite regression
`);
}

// Main execution
if (require.main === module) {
  const options = parseArgs();
  const runner = new TestRunner(options);
  runner.run().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

export { TestRunner, TestRunOptions };
