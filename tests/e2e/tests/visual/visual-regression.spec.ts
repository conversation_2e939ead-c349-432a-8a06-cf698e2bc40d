import { expect, test } from '@playwright/test';
import { DashboardPage } from '../../pages/dashboard-page';
import { swissTestScenarios } from '../../fixtures/swiss-scenarios';

test.describe('Visual Regression Tests', () => {
  let dashboardPage: DashboardPage;

  test.beforeEach(async ({ page }) => {
    dashboardPage = new DashboardPage(page);
    await dashboardPage.goto();
    await dashboardPage.clearLocalStorage();
  });

  test.describe('Layout Screenshots', () => {
    test('captures dashboard layout across viewports', async ({ page }) => {
      console.log('📸 Testing dashboard layout screenshots');
      
      // Set up consistent data for visual testing
      const scenario = swissTestScenarios.zurichProfessional;
      await dashboardPage.inputScenario(scenario);
      await dashboardPage.navigateToDashboardTab();
      await dashboardPage.waitForCalculations();
      
      // Desktop viewport
      await page.setViewportSize({ width: 1280, height: 720 });
      await page.waitForTimeout(1000);
      await expect(page).toHaveScreenshot('dashboard-desktop.png', {
        fullPage: true,
        threshold: 0.2,
      });
      
      // Tablet viewport
      await page.setViewportSize({ width: 768, height: 1024 });
      await page.waitForTimeout(1000);
      await expect(page).toHaveScreenshot('dashboard-tablet.png', {
        fullPage: true,
        threshold: 0.2,
      });
      
      // Mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      await page.waitForTimeout(1000);
      await expect(page).toHaveScreenshot('dashboard-mobile.png', {
        fullPage: true,
        threshold: 0.2,
      });
    });

    test('captures form layouts with data', async ({ page }) => {
      console.log('📸 Testing form layout screenshots');
      
      await page.setViewportSize({ width: 1280, height: 720 });
      
      // Income form
      await dashboardPage.navigateToIncomeSubTab();
      await dashboardPage.enterMonthlyIncome(8500);
      await page.waitForTimeout(500);
      await expect(page.locator('[data-testid="income-form"]')).toHaveScreenshot('income-form.png', {
        threshold: 0.2,
      });
      
      // Goals form
      await dashboardPage.navigateToGoalsSubTab();
      await dashboardPage.enterAge(30);
      await page.waitForTimeout(500);
      await expect(page.locator('[data-testid="goals-form"]')).toHaveScreenshot('goals-form.png', {
        threshold: 0.2,
      });
      
      // Tax optimization form
      await dashboardPage.navigateToTaxOptimizationSubTab();
      await dashboardPage.selectCanton('ZH');
      await dashboardPage.selectCivilStatus('single');
      await page.waitForTimeout(500);
      await expect(page.locator('[data-testid="tax-form"]')).toHaveScreenshot('tax-form.png', {
        threshold: 0.2,
      });
    });

    test('captures chart and visualization layouts', async ({ page }) => {
      console.log('📸 Testing chart layout screenshots');
      
      await page.setViewportSize({ width: 1280, height: 720 });
      
      // Set up data for charts
      const scenario = swissTestScenarios.vaudFamily;
      await dashboardPage.inputScenario(scenario);
      
      // Navigate to visualizations
      await dashboardPage.navigateToVisualizationsSubTab();
      await page.waitForTimeout(2000); // Wait for charts to render
      
      // Capture individual chart components
      const charts = page.locator('[data-testid*="chart"]');
      const chartCount = await charts.count();
      
      for (let i = 0; i < Math.min(chartCount, 3); i++) {
        const chart = charts.nth(i);
        if (await chart.isVisible()) {
          await expect(chart).toHaveScreenshot(`chart-${i}.png`, {
            threshold: 0.3, // Charts may have slight rendering differences
          });
        }
      }
      
      // Full visualizations page
      await expect(page).toHaveScreenshot('visualizations-page.png', {
        fullPage: true,
        threshold: 0.2,
      });
    });
  });

  test.describe('Component Visual States', () => {
    test('captures button states and interactions', async ({ page }) => {
      console.log('📸 Testing button state screenshots');
      
      await page.setViewportSize({ width: 1280, height: 720 });
      await dashboardPage.navigateToDataSubTab();
      
      // Normal button state
      const exportButton = page.locator('[data-testid="export-data"]');
      if (await exportButton.count() > 0) {
        await expect(exportButton).toHaveScreenshot('button-normal.png');
        
        // Hover state
        await exportButton.hover();
        await page.waitForTimeout(200);
        await expect(exportButton).toHaveScreenshot('button-hover.png');
        
        // Focus state
        await exportButton.focus();
        await page.waitForTimeout(200);
        await expect(exportButton).toHaveScreenshot('button-focus.png');
      }
    });

    test('captures input field states', async ({ page }) => {
      console.log('📸 Testing input field state screenshots');
      
      await page.setViewportSize({ width: 1280, height: 720 });
      await dashboardPage.navigateToIncomeSubTab();
      
      const incomeInput = page.locator('[data-testid="monthly-income"]');
      
      // Empty state
      await incomeInput.clear();
      await expect(incomeInput).toHaveScreenshot('input-empty.png');
      
      // Filled state
      await incomeInput.fill('8500');
      await expect(incomeInput).toHaveScreenshot('input-filled.png');
      
      // Focus state
      await incomeInput.focus();
      await expect(incomeInput).toHaveScreenshot('input-focused.png');
      
      // Error state (if validation exists)
      await incomeInput.clear();
      await incomeInput.fill('-1000');
      await page.waitForTimeout(500);
      
      const errorMessage = page.locator('[data-testid="monthly-income-error"]');
      if (await errorMessage.count() > 0) {
        await expect(incomeInput.locator('..').locator('..')).toHaveScreenshot('input-error.png');
      }
    });

    test('captures loading and calculation states', async ({ page }) => {
      console.log('📸 Testing loading state screenshots');
      
      await page.setViewportSize({ width: 1280, height: 720 });
      
      // Capture loading state if it exists
      const loadingIndicator = page.locator('[data-testid="loading"]');
      if (await loadingIndicator.count() > 0) {
        await expect(loadingIndicator).toHaveScreenshot('loading-state.png');
      }
      
      // Capture calculation in progress
      await dashboardPage.navigateToIncomeSubTab();
      await dashboardPage.enterMonthlyIncome(10000);
      
      // Try to capture calculation state (might be very brief)
      const calculatingIndicator = page.locator('[data-testid="calculating"]');
      if (await calculatingIndicator.count() > 0) {
        await expect(calculatingIndicator).toHaveScreenshot('calculating-state.png');
      }
    });
  });

  test.describe('Theme and Styling Consistency', () => {
    test('captures light theme consistency', async ({ page }) => {
      console.log('📸 Testing light theme screenshots');
      
      await page.setViewportSize({ width: 1280, height: 720 });
      
      // Ensure light theme is active
      const themeToggle = page.locator('[data-testid="theme-toggle"]');
      if (await themeToggle.count() > 0) {
        // Click to ensure light theme
        await themeToggle.click();
        await page.waitForTimeout(500);
      }
      
      // Capture different sections in light theme
      await dashboardPage.navigateToDashboardTab();
      await expect(page).toHaveScreenshot('light-theme-dashboard.png', {
        fullPage: true,
        threshold: 0.2,
      });
      
      await dashboardPage.navigateToIncomeSubTab();
      await expect(page).toHaveScreenshot('light-theme-forms.png', {
        fullPage: true,
        threshold: 0.2,
      });
    });

    test('captures dark theme consistency', async ({ page }) => {
      console.log('📸 Testing dark theme screenshots');
      
      await page.setViewportSize({ width: 1280, height: 720 });
      
      // Switch to dark theme
      const themeToggle = page.locator('[data-testid="theme-toggle"]');
      if (await themeToggle.count() > 0) {
        await themeToggle.click();
        await page.waitForTimeout(500);
        
        // Capture different sections in dark theme
        await dashboardPage.navigateToDashboardTab();
        await expect(page).toHaveScreenshot('dark-theme-dashboard.png', {
          fullPage: true,
          threshold: 0.2,
        });
        
        await dashboardPage.navigateToIncomeSubTab();
        await expect(page).toHaveScreenshot('dark-theme-forms.png', {
          fullPage: true,
          threshold: 0.2,
        });
      }
    });

    test('captures color scheme consistency', async ({ page }) => {
      console.log('📸 Testing color scheme consistency');
      
      await page.setViewportSize({ width: 1280, height: 720 });
      
      // Set up scenario with various data states
      const scenario = swissTestScenarios.genevaExecutive;
      await dashboardPage.inputScenario(scenario);
      
      // Capture color usage in different contexts
      await dashboardPage.navigateToDashboardTab();
      
      // Focus on specific color elements
      const metrics = page.locator('[data-testid="metrics-container"]');
      if (await metrics.count() > 0) {
        await expect(metrics).toHaveScreenshot('color-metrics.png', {
          threshold: 0.2,
        });
      }
      
      const charts = page.locator('[data-testid="charts-container"]');
      if (await charts.count() > 0) {
        await expect(charts).toHaveScreenshot('color-charts.png', {
          threshold: 0.2,
        });
      }
    });
  });

  test.describe('Responsive Design Visual Tests', () => {
    test('captures responsive breakpoint transitions', async ({ page }) => {
      console.log('📸 Testing responsive breakpoint screenshots');
      
      const scenario = swissTestScenarios.bernConservative;
      await dashboardPage.inputScenario(scenario);
      await dashboardPage.navigateToDashboardTab();
      
      // Test various breakpoints
      const breakpoints = [
        { width: 1920, height: 1080, name: 'xl' },
        { width: 1280, height: 720, name: 'lg' },
        { width: 1024, height: 768, name: 'md' },
        { width: 768, height: 1024, name: 'sm' },
        { width: 640, height: 960, name: 'xs' },
        { width: 375, height: 667, name: 'mobile' },
      ];
      
      for (const breakpoint of breakpoints) {
        await page.setViewportSize({ width: breakpoint.width, height: breakpoint.height });
        await page.waitForTimeout(1000);
        
        await expect(page).toHaveScreenshot(`responsive-${breakpoint.name}.png`, {
          fullPage: true,
          threshold: 0.2,
        });
      }
    });

    test('captures navigation layout changes', async ({ page }) => {
      console.log('📸 Testing navigation layout screenshots');
      
      // Desktop navigation
      await page.setViewportSize({ width: 1280, height: 720 });
      const navigation = page.locator('[data-testid="main-navigation"]');
      if (await navigation.count() > 0) {
        await expect(navigation).toHaveScreenshot('navigation-desktop.png');
      }
      
      // Mobile navigation
      await page.setViewportSize({ width: 375, height: 667 });
      await page.waitForTimeout(500);
      
      const mobileNav = page.locator('[data-testid="mobile-navigation"]');
      if (await mobileNav.count() > 0) {
        await expect(mobileNav).toHaveScreenshot('navigation-mobile.png');
      }
      
      // Mobile menu opened (if hamburger menu exists)
      const hamburger = page.locator('[data-testid="hamburger-menu"]');
      if (await hamburger.count() > 0) {
        await hamburger.click();
        await page.waitForTimeout(300);
        await expect(page).toHaveScreenshot('navigation-mobile-open.png');
      }
    });
  });

  test.describe('Error State Visual Tests', () => {
    test('captures error message displays', async ({ page }) => {
      console.log('📸 Testing error state screenshots');
      
      await page.setViewportSize({ width: 1280, height: 720 });
      
      // Input validation errors
      await dashboardPage.navigateToIncomeSubTab();
      await dashboardPage.enterMonthlyIncome(-1000);
      
      const errorMessage = page.locator('[data-testid="monthly-income-error"]');
      if (await errorMessage.count() > 0) {
        await expect(page.locator('[data-testid="income-form"]')).toHaveScreenshot('form-validation-error.png');
      }
      
      // Age validation error
      await dashboardPage.navigateToGoalsSubTab();
      await dashboardPage.enterAge(150);
      
      const ageError = page.locator('[data-testid="age-error"]');
      if (await ageError.count() > 0) {
        await expect(page.locator('[data-testid="goals-form"]')).toHaveScreenshot('age-validation-error.png');
      }
    });

    test('captures network error states', async ({ page }) => {
      console.log('📸 Testing network error screenshots');
      
      await page.setViewportSize({ width: 1280, height: 720 });
      
      // Simulate network error
      await page.route('**/*', route => route.abort());
      
      await dashboardPage.navigateToEconomicDataSubTab();
      await page.waitForTimeout(2000);
      
      const networkError = page.locator('[data-testid="network-error"]');
      if (await networkError.count() > 0) {
        await expect(page).toHaveScreenshot('network-error-state.png', {
          fullPage: true,
        });
      }
      
      // Restore network
      await page.unroute('**/*');
    });
  });

  test.describe('Animation and Transition Tests', () => {
    test('captures tab transition animations', async ({ page }) => {
      console.log('📸 Testing tab transition screenshots');
      
      await page.setViewportSize({ width: 1280, height: 720 });
      
      // Set up data
      await dashboardPage.navigateToIncomeSubTab();
      await dashboardPage.enterMonthlyIncome(8000);
      
      // Capture before transition
      await expect(page).toHaveScreenshot('tab-before-transition.png');
      
      // Trigger transition
      await dashboardPage.navigateToDashboardTab();
      
      // Capture after transition (animations should be complete)
      await page.waitForTimeout(1000);
      await expect(page).toHaveScreenshot('tab-after-transition.png');
    });

    test('captures loading animation states', async ({ page }) => {
      console.log('📸 Testing loading animation screenshots');
      
      await page.setViewportSize({ width: 1280, height: 720 });
      
      // If loading animations exist, capture them
      const loadingSpinner = page.locator('[data-testid="loading-spinner"]');
      if (await loadingSpinner.count() > 0) {
        await expect(loadingSpinner).toHaveScreenshot('loading-animation.png');
      }
      
      const progressBar = page.locator('[data-testid="progress-bar"]');
      if (await progressBar.count() > 0) {
        await expect(progressBar).toHaveScreenshot('progress-animation.png');
      }
    });
  });
});
