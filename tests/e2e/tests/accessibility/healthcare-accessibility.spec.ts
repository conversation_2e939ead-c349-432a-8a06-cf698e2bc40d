import { expect, test } from '@playwright/test';
import AxeBuilder from '@axe-core/playwright';
import { DashboardPage } from '../../pages/dashboard-page';
import { HealthcareOptimizerPage } from '../../pages/healthcare-optimizer-page';
import { healthcareTestScenarios } from '../../fixtures/healthcare-scenarios';

test.describe('Healthcare Cost Optimizer - Accessibility Tests', () => {
  let dashboardPage: DashboardPage;
  let healthcarePage: HealthcareOptimizerPage;

  test.describe.configure({ mode: 'serial', timeout: 60000 });

  test.beforeEach(async ({ page }) => {
    dashboardPage = new DashboardPage(page);
    healthcarePage = new HealthcareOptimizerPage(page);
    await dashboardPage.goto();
    await dashboardPage.clearLocalStorage();
  });

  test('Healthcare tab WCAG compliance', async ({ page }) => {
    await dashboardPage.clickTab('healthcare');

    await test.step('Check initial page accessibility', async () => {
      const accessibilityScanResults = await new AxeBuilder({ page })
        .withTags(['wcag2a', 'wcag2aa', 'wcag21aa'])
        .analyze();

      expect(accessibilityScanResults.violations).toEqual([]);
    });

    await test.step('Check form accessibility', async () => {
      // Fill form to test dynamic content
      await healthcarePage.fillHealthProfile(healthcareTestScenarios.youngProfessionalZurich.profile);

      const formAccessibilityResults = await new AxeBuilder({ page })
        .withTags(['wcag2a', 'wcag2aa'])
        .include('[data-testid="health-profile-form"]')
        .analyze();

      expect(formAccessibilityResults.violations).toEqual([]);
    });

    await test.step('Check optimization results accessibility', async () => {
      await healthcarePage.goToOptimization();

      const resultsAccessibilityResults = await new AxeBuilder({ page })
        .withTags(['wcag2a', 'wcag2aa'])
        .include('[data-testid="optimization-results"]')
        .analyze();

      expect(resultsAccessibilityResults.violations).toEqual([]);
    });
  });

  test('Keyboard navigation support', async ({ page }) => {
    await dashboardPage.clickTab('healthcare');

    await test.step('Test tab navigation through form', async () => {
      // Start from first input
      await page.keyboard.press('Tab');
      const focusedElement = await page.locator(':focus').getAttribute('type');
      expect(focusedElement).toBe('number'); // Age input

      // Navigate through all form elements
      const expectedElements = [
        'number', // Age
        'select-one', // Canton
        'number', // Income
        'select-one', // Health status
        'number', // Family size
        'checkbox', // Has children
        'number', // Current premium
        'select-one', // Current deductible
        'number', // Expected expenses
        'select-one', // Risk tolerance
      ];

      for (let i = 1; i < expectedElements.length; i++) {
        await page.keyboard.press('Tab');
        const elementType = await page.locator(':focus').getAttribute('type') || 
                           await page.locator(':focus').getAttribute('tagName')?.toLowerCase();
        
        // Handle select elements
        if (elementType === 'SELECT') {
          expect('select-one').toBe('select-one');
        } else {
          expect(elementType).toBe(expectedElements[i]);
        }
      }
    });

    await test.step('Test tab navigation between sections', async () => {
      // Navigate to optimization tab using keyboard
      await page.keyboard.press('Tab');
      await page.keyboard.press('Tab'); // Navigate to tab buttons
      await page.keyboard.press('ArrowRight'); // Move to optimization tab
      await page.keyboard.press('Enter');

      await expect(page.locator('[data-testid="optimization-results"]')).toBeVisible();
    });

    await test.step('Test escape key functionality', async () => {
      // Test if escape key closes any modals or dropdowns
      await page.keyboard.press('Escape');
      
      // Should still be on healthcare page
      await expect(page.locator('h1:has-text("Swiss Healthcare Cost Optimizer")')).toBeVisible();
    });
  });

  test('Screen reader support', async ({ page }) => {
    await dashboardPage.clickTab('healthcare');

    await test.step('Check ARIA labels and roles', async () => {
      // Check main heading has proper role
      const mainHeading = page.locator('h1:has-text("Swiss Healthcare Cost Optimizer")');
      await expect(mainHeading).toHaveAttribute('role', 'heading');

      // Check form labels are properly associated
      const ageInput = page.locator('input[type="number"]').first();
      const ageLabel = page.locator('label:has-text("Age")');
      
      const labelFor = await ageLabel.getAttribute('for');
      const inputId = await ageInput.getAttribute('id');
      expect(labelFor).toBe(inputId);
    });

    await test.step('Check ARIA live regions for dynamic content', async () => {
      await healthcarePage.fillHealthProfile(healthcareTestScenarios.youngProfessionalZurich.profile);
      await healthcarePage.goToOptimization();

      // Check if results have proper ARIA live regions
      const resultsSection = page.locator('[data-testid="optimization-results"]');
      const ariaLive = await resultsSection.getAttribute('aria-live');
      expect(ariaLive).toBeTruthy();
    });

    await test.step('Check descriptive text for complex elements', async () => {
      await healthcarePage.goToOptimization();

      // Check if charts/graphs have proper descriptions
      const savingsDisplay = page.locator('[data-testid="total-savings"]');
      const ariaDescribedBy = await savingsDisplay.getAttribute('aria-describedby');
      
      if (ariaDescribedBy) {
        const description = page.locator(`#${ariaDescribedBy}`);
        await expect(description).toBeVisible();
      }
    });
  });

  test('Color contrast and visual accessibility', async ({ page }) => {
    await dashboardPage.clickTab('healthcare');

    await test.step('Test high contrast mode', async () => {
      // Enable high contrast mode
      await page.emulateMedia({ colorScheme: 'dark' });
      
      // Check that content is still visible and accessible
      await expect(page.locator('h1:has-text("Swiss Healthcare Cost Optimizer")')).toBeVisible();
      
      // Fill form to test contrast in different states
      await healthcarePage.fillHealthProfile(healthcareTestScenarios.youngProfessionalZurich.profile);
      await healthcarePage.goToOptimization();
      
      // Check that results are visible in high contrast
      await expect(page.locator('[data-testid="total-savings"]')).toBeVisible();
    });

    await test.step('Test reduced motion preferences', async () => {
      // Enable reduced motion
      await page.emulateMedia({ reducedMotion: 'reduce' });
      
      // Test navigation animations
      await page.click('button:has-text("FIRE Integration")');
      await page.click('button:has-text("Health Profile")');
      
      // Should still function without animations
      await expect(page.locator('input[type="number"]').first()).toBeVisible();
    });

    await test.step('Check color-only information', async () => {
      await healthcarePage.fillHealthProfile(healthcareTestScenarios.youngProfessionalZurich.profile);
      await healthcarePage.goToOptimization();

      // Check that important information isn't conveyed by color alone
      const recommendations = page.locator('[data-testid="insurance-recommendation"]');
      const firstRec = recommendations.first();
      
      // Should have text indicators, not just color
      await expect(firstRec.locator('text=Save')).toBeVisible();
    });
  });

  test('Focus management and visual indicators', async ({ page }) => {
    await dashboardPage.clickTab('healthcare');

    await test.step('Test focus visibility', async () => {
      // Check that focused elements have visible indicators
      await page.keyboard.press('Tab');
      const focusedElement = page.locator(':focus');
      
      // Check if focus outline is visible
      const outlineStyle = await focusedElement.evaluate(el => 
        window.getComputedStyle(el).outline,
      );
      expect(outlineStyle).not.toBe('none');
    });

    await test.step('Test focus trap in modals', async () => {
      // If there are any modal dialogs, test focus trap
      // This would be implemented if modals exist in the healthcare optimizer
    });

    await test.step('Test skip links', async () => {
      // Check if skip links are available for keyboard users
      await page.keyboard.press('Tab');
      const skipLink = page.locator('a:has-text("Skip to main content")');
      
      if (await skipLink.isVisible()) {
        await skipLink.click();
        const mainContent = page.locator('main, [role="main"]');
        await expect(mainContent).toBeFocused();
      }
    });
  });

  test('Form accessibility and error handling', async ({ page }) => {
    await dashboardPage.clickTab('healthcare');

    await test.step('Test form validation accessibility', async () => {
      // Test invalid input
      await healthcarePage.updateAge(150);
      await page.keyboard.press('Tab'); // Trigger validation
      
      // Check if error message is properly announced
      const errorMessage = page.locator('[data-testid="age-error"]');
      if (await errorMessage.isVisible()) {
        const ariaLive = await errorMessage.getAttribute('aria-live');
        expect(ariaLive).toBe('polite');
        
        const role = await errorMessage.getAttribute('role');
        expect(role).toBe('alert');
      }
    });

    await test.step('Test required field indicators', async () => {
      // Check that required fields are properly marked
      const requiredInputs = page.locator('input[required], select[required]');
      const count = await requiredInputs.count();
      
      for (let i = 0; i < count; i++) {
        const input = requiredInputs.nth(i);
        const ariaRequired = await input.getAttribute('aria-required');
        expect(ariaRequired).toBe('true');
      }
    });

    await test.step('Test field descriptions', async () => {
      // Check that complex fields have helpful descriptions
      const deductibleSelect = page.locator('select').nth(2); // Current deductible
      const describedBy = await deductibleSelect.getAttribute('aria-describedby');
      
      if (describedBy) {
        const description = page.locator(`#${describedBy}`);
        await expect(description).toBeVisible();
      }
    });
  });

  test('Mobile accessibility', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await dashboardPage.clickTab('healthcare');

    await test.step('Test mobile touch targets', async () => {
      // Check that touch targets are large enough (minimum 44px)
      const buttons = page.locator('button');
      const count = await buttons.count();
      
      for (let i = 0; i < count; i++) {
        const button = buttons.nth(i);
        const boundingBox = await button.boundingBox();
        
        if (boundingBox) {
          expect(boundingBox.height).toBeGreaterThanOrEqual(44);
          expect(boundingBox.width).toBeGreaterThanOrEqual(44);
        }
      }
    });

    await test.step('Test mobile form accessibility', async () => {
      // Test that form is usable on mobile
      await healthcarePage.fillHealthProfile(healthcareTestScenarios.youngProfessionalZurich.profile);
      
      // Check that all form elements are accessible
      const accessibilityResults = await new AxeBuilder({ page })
        .withTags(['wcag2a', 'wcag2aa'])
        .analyze();

      expect(accessibilityResults.violations).toEqual([]);
    });

    await test.step('Test mobile navigation accessibility', async () => {
      // Test tab navigation on mobile
      await page.click('button:has-text("Optimization")');
      await expect(page.locator('[data-testid="optimization-results"]')).toBeVisible();
      
      await page.click('button:has-text("FIRE Integration")');
      await expect(page.locator('[data-testid="fire-analysis"]')).toBeVisible();
    });
  });

  test('Internationalization accessibility', async ({ page }) => {
    await dashboardPage.clickTab('healthcare');

    await test.step('Test language attributes', async () => {
      // Check that language is properly set
      const htmlLang = await page.getAttribute('html', 'lang');
      expect(htmlLang).toBeTruthy();
      
      // Check for any content in different languages
      const foreignContent = page.locator('[lang]:not([lang="en"]):not([lang=""])');
      const count = await foreignContent.count();
      
      for (let i = 0; i < count; i++) {
        const element = foreignContent.nth(i);
        const lang = await element.getAttribute('lang');
        expect(lang).toBeTruthy();
      }
    });

    await test.step('Test RTL language support', async () => {
      // If RTL languages are supported, test accessibility
      await page.evaluate(() => {
        document.documentElement.setAttribute('dir', 'rtl');
      });
      
      // Check that layout still works
      await expect(page.locator('h1:has-text("Swiss Healthcare Cost Optimizer")')).toBeVisible();
      
      // Reset direction
      await page.evaluate(() => {
        document.documentElement.setAttribute('dir', 'ltr');
      });
    });
  });

  test('Assistive technology compatibility', async ({ page }) => {
    await dashboardPage.clickTab('healthcare');

    await test.step('Test with simulated screen reader', async () => {
      // Test reading order by checking tab sequence
      const tabbableElements = await page.locator('input, select, button, a[href], [tabindex]:not([tabindex="-1"])').all();
      
      // Verify logical reading order
      for (let i = 0; i < Math.min(tabbableElements.length, 10); i++) {
        await page.keyboard.press('Tab');
        const focusedElement = page.locator(':focus');
        await expect(focusedElement).toBeVisible();
      }
    });

    await test.step('Test with voice control simulation', async () => {
      // Test that elements can be activated by voice commands
      // This simulates clicking elements by their accessible names
      
      await page.click('button:has-text("Optimization")');
      await expect(page.locator('[data-testid="optimization-results"]')).toBeVisible();
      
      await page.click('button:has-text("Health Profile")');
      await expect(page.locator('input[type="number"]').first()).toBeVisible();
    });
  });
});
