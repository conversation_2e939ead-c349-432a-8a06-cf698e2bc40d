import { test, expect } from '@playwright/test';

test.describe('Risk Assessment Metrics - Advanced Feature', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Set up financial data for risk assessment
    await page.fill('[data-testid="current-age"]', '40');
    await page.fill('[data-testid="retirement-age"]', '65');
    await page.fill('[data-testid="current-savings"]', '200000');
    await page.fill('[data-testid="monthly-income"]', '10000');
    await page.fill('[data-testid="monthly-expenses"]', '6000');
    
    // Navigate to Advanced tab
    await page.click('[data-testid="tab-advanced"]');
    await page.waitForTimeout(1000);
  });

  test('should display Risk Assessment Metrics component', async ({ page }) => {
    // Check if Risk Assessment section is visible
    await expect(page.locator('h3:has-text("⚠️ Risk Assessment Metrics")')).toBeVisible();
    
    // Check for description text
    await expect(page.locator('text=Comprehensive analysis of financial risks')).toBeVisible();
  });

  test('should generate comprehensive risk analysis', async ({ page }) => {
    // Wait for risk analysis to complete
    await page.waitForSelector('[data-testid="risk-analysis-complete"]', { timeout: 10000 });
    
    // Check if overall risk profile is displayed
    await expect(page.locator('h3:has-text("📊 Overall Risk Profile")')).toBeVisible();
    
    // Verify risk metrics are displayed
    await expect(page.locator('[data-testid="risk-score"]')).toBeVisible();
    await expect(page.locator('[data-testid="risk-level"]')).toBeVisible();
    await expect(page.locator('[data-testid="risk-factors-count"]')).toBeVisible();
  });

  test('should display all 8 risk metrics', async ({ page }) => {
    // Wait for analysis to complete
    await page.waitForSelector('[data-testid="risk-analysis-complete"]', { timeout: 10000 });
    
    // Check for all risk metrics
    const expectedMetrics = [
      'Emergency Fund Coverage',
      'Savings Rate Sustainability',
      'Investment Concentration',
      'Time Horizon Risk',
      'Income Stability',
      'Inflation Protection',
      'Withdrawal Rate Safety',
      'Sequence of Returns Risk',
    ];
    
    for (const metric of expectedMetrics) {
      await expect(page.locator(`text=${metric}`)).toBeVisible();
    }
  });

  test('should categorize risks correctly', async ({ page }) => {
    // Wait for analysis to complete
    await page.waitForSelector('[data-testid="risk-analysis-complete"]', { timeout: 10000 });
    
    // Test risk category filter
    const categorySelect = page.locator('[data-testid="risk-category-filter"]');
    await expect(categorySelect).toBeVisible();
    
    // Test different categories
    await categorySelect.selectOption('financial');
    await page.waitForTimeout(500);
    await expect(page.locator('text=Emergency Fund Coverage')).toBeVisible();
    
    await categorySelect.selectOption('market');
    await page.waitForTimeout(500);
    await expect(page.locator('text=Investment Concentration')).toBeVisible();
    
    await categorySelect.selectOption('personal');
    await page.waitForTimeout(500);
    await expect(page.locator('text=Time Horizon Risk')).toBeVisible();
    
    await categorySelect.selectOption('economic');
    await page.waitForTimeout(500);
    await expect(page.locator('text=Inflation Protection')).toBeVisible();
  });

  test('should show risk severity levels', async ({ page }) => {
    // Wait for analysis to complete
    await page.waitForSelector('[data-testid="risk-analysis-complete"]', { timeout: 10000 });
    
    // Check for severity indicators
    const riskMetrics = page.locator('[data-testid="risk-metric"]');
    await expect(riskMetrics.first()).toBeVisible();
    
    // Verify severity levels are displayed
    const severityLevels = ['LOW RISK', 'MEDIUM RISK', 'HIGH RISK', 'CRITICAL RISK'];
    let foundSeverity = false;
    
    for (const level of severityLevels) {
      const severityElement = page.locator(`text=${level}`);
      if (await severityElement.isVisible()) {
        foundSeverity = true;
        break;
      }
    }
    
    expect(foundSeverity).toBe(true);
  });

  test('should provide actionable recommendations', async ({ page }) => {
    // Wait for analysis to complete
    await page.waitForSelector('[data-testid="risk-analysis-complete"]', { timeout: 10000 });
    
    // Check if recommendations section is visible
    await expect(page.locator('h3:has-text("💡 Risk Management Recommendations")')).toBeVisible();
    
    // Verify recommendations are numbered and actionable
    const recommendations = page.locator('[data-testid="recommendation-step"]');
    await expect(recommendations).toHaveCount(4); // Should have 4 recommendations
    
    // Check if first recommendation contains actionable advice
    await expect(recommendations.first()).toBeVisible();
  });

  test('should display strengths and weaknesses', async ({ page }) => {
    // Wait for analysis to complete
    await page.waitForSelector('[data-testid="risk-analysis-complete"]', { timeout: 10000 });
    
    // Check for strengths section
    await expect(page.locator('h4:has-text("✅ Strengths")')).toBeVisible();
    
    // Check for weaknesses section
    await expect(page.locator('h4:has-text("⚠️ Areas for Improvement")')).toBeVisible();
  });

  test('should handle different risk scenarios', async ({ page }) => {
    // Test high-risk scenario (low savings, high expenses)
    await page.fill('[data-testid="current-savings"]', '10000');
    await page.fill('[data-testid="monthly-income"]', '5000');
    await page.fill('[data-testid="monthly-expenses"]', '4800');
    
    // Refresh analysis
    await page.click('[data-testid="refresh-risk-analysis"]');
    await page.waitForSelector('[data-testid="risk-analysis-complete"]', { timeout: 10000 });
    
    // Should show high risk indicators
    await expect(page.locator('text=CRITICAL RISK')).toBeVisible();
    
    // Test low-risk scenario (high savings, low expenses)
    await page.fill('[data-testid="current-savings"]', '500000');
    await page.fill('[data-testid="monthly-income"]', '12000');
    await page.fill('[data-testid="monthly-expenses"]', '4000');
    
    // Refresh analysis
    await page.click('[data-testid="refresh-risk-analysis"]');
    await page.waitForSelector('[data-testid="risk-analysis-complete"]', { timeout: 10000 });
    
    // Should show lower risk
    await expect(page.locator('text=LOW RISK')).toBeVisible();
  });

  test('should show risk scores with proper formatting', async ({ page }) => {
    // Wait for analysis to complete
    await page.waitForSelector('[data-testid="risk-analysis-complete"]', { timeout: 10000 });
    
    // Check if risk scores are displayed with proper format
    const riskMetrics = page.locator('[data-testid="risk-metric"]');
    const firstMetric = riskMetrics.first();
    
    // Should show score out of 100
    await expect(firstMetric.locator('text=/\\d+\/100/')).toBeVisible();
    
    // Should show formatted values
    await expect(firstMetric).toContainText(/CHF|%|months|years/);
  });

  test('should handle emergency fund calculations', async ({ page }) => {
    // Set specific values for emergency fund testing
    await page.fill('[data-testid="monthly-expenses"]', '5000');
    await page.fill('[data-testid="current-savings"]', '30000'); // 6 months of expenses
    
    // Refresh analysis
    await page.click('[data-testid="refresh-risk-analysis"]');
    await page.waitForSelector('[data-testid="risk-analysis-complete"]', { timeout: 10000 });
    
    // Should show adequate emergency fund
    await expect(page.locator('text=Emergency Fund Coverage')).toBeVisible();
    await expect(page.locator('text=6.0 months')).toBeVisible();
  });

  test('should assess time horizon risk correctly', async ({ page }) => {
    // Test short time horizon (high risk)
    await page.fill('[data-testid="current-age"]', '60');
    await page.fill('[data-testid="retirement-age"]', '65');
    
    // Refresh analysis
    await page.click('[data-testid="refresh-risk-analysis"]');
    await page.waitForSelector('[data-testid="risk-analysis-complete"]', { timeout: 10000 });
    
    // Should show higher time horizon risk
    await expect(page.locator('text=Time Horizon Risk')).toBeVisible();
    await expect(page.locator('text=5 years')).toBeVisible();
    
    // Test long time horizon (lower risk)
    await page.fill('[data-testid="current-age"]', '30');
    await page.fill('[data-testid="retirement-age"]', '65');
    
    // Refresh analysis
    await page.click('[data-testid="refresh-risk-analysis"]');
    await page.waitForSelector('[data-testid="risk-analysis-complete"]', { timeout: 10000 });
    
    // Should show lower time horizon risk
    await expect(page.locator('text=35 years')).toBeVisible();
  });

  test('should be responsive on mobile devices', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Wait for analysis to complete
    await page.waitForSelector('[data-testid="risk-analysis-complete"]', { timeout: 10000 });
    
    // Check if components are still visible and functional
    await expect(page.locator('h3:has-text("⚠️ Risk Assessment Metrics")')).toBeVisible();
    await expect(page.locator('[data-testid="risk-score"]')).toBeVisible();
    
    // Test category filter on mobile
    const categorySelect = page.locator('[data-testid="risk-category-filter"]');
    await expect(categorySelect).toBeVisible();
    await categorySelect.selectOption('financial');
    await page.waitForTimeout(500);
  });

  test('should handle edge cases gracefully', async ({ page }) => {
    // Test with zero values
    await page.fill('[data-testid="current-savings"]', '0');
    await page.fill('[data-testid="monthly-income"]', '0');
    
    // Refresh analysis
    await page.click('[data-testid="refresh-risk-analysis"]');
    
    // Should handle gracefully without crashing
    await page.waitForTimeout(3000);
    await expect(page.locator('h3:has-text("⚠️ Risk Assessment Metrics")')).toBeVisible();
  });

  test('should show confidence levels for metrics', async ({ page }) => {
    // Wait for analysis to complete
    await page.waitForSelector('[data-testid="risk-analysis-complete"]', { timeout: 10000 });
    
    // Check if confidence indicators are present
    await expect(page.locator('text=confidence')).toBeVisible();
    
    // Verify actionable indicators
    await expect(page.locator('text=Actionable')).toBeVisible();
  });

  test('should provide detailed metric descriptions', async ({ page }) => {
    // Wait for analysis to complete
    await page.waitForSelector('[data-testid="risk-analysis-complete"]', { timeout: 10000 });
    
    // Check if each metric has a description
    const riskMetrics = page.locator('[data-testid="risk-metric"]');
    const firstMetric = riskMetrics.first();
    
    // Should have description text
    await expect(firstMetric.locator('[data-testid="metric-description"]')).toBeVisible();
    
    // Should have recommendation text
    await expect(firstMetric.locator('[data-testid="metric-recommendation"]')).toBeVisible();
  });
});
