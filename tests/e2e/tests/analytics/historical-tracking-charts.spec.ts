import { test, expect } from '@playwright/test';

test.describe('Historical Tracking Charts - Analytics Feature', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Set up financial data
    await page.fill('[data-testid="current-age"]', '35');
    await page.fill('[data-testid="retirement-age"]', '65');
    await page.fill('[data-testid="current-savings"]', '150000');
    await page.fill('[data-testid="monthly-income"]', '9000');
    await page.fill('[data-testid="monthly-expenses"]', '5500');
    
    // Navigate to Analysis tab
    await page.click('[data-testid="tab-analysis"]');
    await page.waitForTimeout(1000);
  });

  test('should display Historical Tracking Charts component', async ({ page }) => {
    // Check if Historical Tracking section is visible
    await expect(page.locator('h3:has-text("📈 Historical Tracking & Analytics")')).toBeVisible();
    
    // Check for description text
    await expect(page.locator('text=Track your financial progress over time')).toBeVisible();
  });

  test('should show timeframe selection controls', async ({ page }) => {
    // Check if timeframe buttons are visible
    const timeframes = ['1M', '3M', '6M', '1Y', '2Y', 'ALL'];
    
    for (const timeframe of timeframes) {
      await expect(page.locator(`button:has-text("${timeframe}")`)).toBeVisible();
    }
    
    // Default should be 1Y
    await expect(page.locator('button:has-text("1Y")').first()).toHaveClass(/bg-blue-600/);
  });

  test('should show metric selection checkboxes', async ({ page }) => {
    // Check if metric selection is visible
    await expect(page.locator('text=Metrics to Display')).toBeVisible();
    
    // Check for metric checkboxes
    const metrics = ['Net Worth', 'Monthly Income', 'Monthly Expenses', 'Monthly Savings'];
    
    for (const metric of metrics) {
      await expect(page.locator(`text=${metric}`).locator('..').locator('input[type="checkbox"]')).toBeVisible();
    }
  });

  test('should generate sample data when button is clicked', async ({ page }) => {
    // Click generate sample data button
    await page.click('[data-testid="generate-sample-data"]');
    
    // Should show loading state
    await expect(page.locator('text=Generating...')).toBeVisible();
    
    // Wait for data generation to complete
    await page.waitForSelector('[data-testid="historical-data-complete"]', { timeout: 10000 });
    
    // Should show summary statistics
    await expect(page.locator('h3:has-text("📊 Summary Statistics")')).toBeVisible();
  });

  test('should display summary statistics after data generation', async ({ page }) => {
    // Generate sample data
    await page.click('[data-testid="generate-sample-data"]');
    await page.waitForSelector('[data-testid="historical-data-complete"]', { timeout: 10000 });
    
    // Check if all summary metrics are displayed
    const summaryMetrics = [
      'Net Worth Growth',
      'Current Net Worth',
      'Avg Savings Rate',
      'FIRE Progress',
    ];
    
    for (const metric of summaryMetrics) {
      await expect(page.locator(`text=${metric}`)).toBeVisible();
    }
    
    // Verify CHF formatting
    await expect(page.locator('text=CHF')).toBeVisible();
    
    // Verify percentage formatting
    await expect(page.locator('text=/%/')).toBeVisible();
  });

  test('should show financial trend charts', async ({ page }) => {
    // Generate sample data
    await page.click('[data-testid="generate-sample-data"]');
    await page.waitForSelector('[data-testid="historical-data-complete"]', { timeout: 10000 });
    
    // Check if charts section is visible
    await expect(page.locator('h3:has-text("📈 Financial Trends")')).toBeVisible();
    
    // Verify SVG charts are rendered
    const charts = page.locator('svg');
    await expect(charts.first()).toBeVisible();
    
    // Check for chart elements
    await expect(page.locator('svg polyline')).toBeVisible(); // Line charts
    await expect(page.locator('svg circle')).toBeVisible(); // Data points
  });

  test('should handle timeframe changes', async ({ page }) => {
    // Generate sample data
    await page.click('[data-testid="generate-sample-data"]');
    await page.waitForSelector('[data-testid="historical-data-complete"]', { timeout: 10000 });
    
    // Test different timeframes
    const timeframes = ['3M', '6M', '2Y', 'ALL'];
    
    for (const timeframe of timeframes) {
      await page.click(`button:has-text("${timeframe}")`);
      await page.waitForTimeout(500);
      
      // Verify timeframe is selected
      await expect(page.locator(`button:has-text("${timeframe}")`).first()).toHaveClass(/bg-blue-600/);
      
      // Charts should still be visible
      await expect(page.locator('svg')).toBeVisible();
    }
  });

  test('should handle metric selection changes', async ({ page }) => {
    // Generate sample data
    await page.click('[data-testid="generate-sample-data"]');
    await page.waitForSelector('[data-testid="historical-data-complete"]', { timeout: 10000 });
    
    // Test unchecking a metric
    const netWorthCheckbox = page.locator('text=Net Worth').locator('..').locator('input[type="checkbox"]');
    await netWorthCheckbox.uncheck();
    await page.waitForTimeout(500);
    
    // Test checking it back
    await netWorthCheckbox.check();
    await page.waitForTimeout(500);
    
    // Charts should still be visible
    await expect(page.locator('svg')).toBeVisible();
  });

  test('should display historical data table', async ({ page }) => {
    // Generate sample data
    await page.click('[data-testid="generate-sample-data"]');
    await page.waitForSelector('[data-testid="historical-data-complete"]', { timeout: 10000 });
    
    // Check if data table section is visible
    await expect(page.locator('h3:has-text("📋 Historical Data Table")')).toBeVisible();
    
    // Verify table headers
    const tableHeaders = [
      'Date',
      'Net Worth',
      'Monthly Income',
      'Monthly Expenses',
      'Savings Rate',
      'FIRE Progress',
    ];
    
    for (const header of tableHeaders) {
      await expect(page.locator(`th:has-text("${header}")`)).toBeVisible();
    }
    
    // Verify table has data rows
    const dataRows = page.locator('tbody tr');
    await expect(dataRows.first()).toBeVisible();
    
    // Check for Swiss date formatting
    await expect(page.locator('td').first()).toContainText(/\d{1,2}\.\d{1,2}\.\d{4}/);
  });

  test('should show chart tooltips on hover', async ({ page }) => {
    // Generate sample data
    await page.click('[data-testid="generate-sample-data"]');
    await page.waitForSelector('[data-testid="historical-data-complete"]', { timeout: 10000 });
    
    // Hover over a data point
    const dataPoint = page.locator('svg circle').first();
    await dataPoint.hover();
    
    // Should show tooltip with value
    await expect(page.locator('title')).toBeVisible();
  });

  test('should handle empty state properly', async ({ page }) => {
    // Should show empty state message
    await expect(page.locator('h3:has-text("No historical data available")')).toBeVisible();
    await expect(page.locator('text=Generate sample data to see your financial trends')).toBeVisible();
    
    // Should show generate button
    await expect(page.locator('[data-testid="generate-sample-data"]')).toBeVisible();
  });

  test('should show correct chart colors and labels', async ({ page }) => {
    // Generate sample data
    await page.click('[data-testid="generate-sample-data"]');
    await page.waitForSelector('[data-testid="historical-data-complete"]', { timeout: 10000 });
    
    // Check if chart labels are visible
    await expect(page.locator('text=Net Worth')).toBeVisible();
    
    // Verify color indicators in metric selection
    const colorIndicators = page.locator('span:has-text("●")');
    await expect(colorIndicators.first()).toBeVisible();
  });

  test('should handle data persistence', async ({ page }) => {
    // Generate sample data
    await page.click('[data-testid="generate-sample-data"]');
    await page.waitForSelector('[data-testid="historical-data-complete"]', { timeout: 10000 });
    
    // Navigate away and back
    await page.click('[data-testid="tab-dashboard"]');
    await page.waitForTimeout(1000);
    await page.click('[data-testid="tab-analysis"]');
    await page.waitForTimeout(1000);
    
    // Data should still be there
    await expect(page.locator('h3:has-text("📊 Summary Statistics")')).toBeVisible();
    await expect(page.locator('svg')).toBeVisible();
  });

  test('should be responsive on mobile devices', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Generate sample data
    await page.click('[data-testid="generate-sample-data"]');
    await page.waitForSelector('[data-testid="historical-data-complete"]', { timeout: 10000 });
    
    // Check if components are still visible and functional
    await expect(page.locator('h3:has-text("📈 Historical Tracking & Analytics")')).toBeVisible();
    await expect(page.locator('[data-testid="generate-sample-data"]')).toBeVisible();
    
    // Test timeframe selection on mobile
    await page.click('button:has-text("3M")');
    await page.waitForTimeout(500);
    await expect(page.locator('button:has-text("3M")').first()).toHaveClass(/bg-blue-600/);
  });

  test('should show proper data formatting', async ({ page }) => {
    // Generate sample data
    await page.click('[data-testid="generate-sample-data"]');
    await page.waitForSelector('[data-testid="historical-data-complete"]', { timeout: 10000 });
    
    // Check CHF formatting in summary
    const summaryValues = page.locator('[data-testid="summary-value"]');
    await expect(summaryValues.first()).toContainText('CHF');
    
    // Check percentage formatting
    await expect(page.locator('text=/%/')).toBeVisible();
    
    // Check table formatting
    const tableData = page.locator('tbody td');
    await expect(tableData).toContainText('CHF');
  });

  test('should handle chart interactions', async ({ page }) => {
    // Generate sample data
    await page.click('[data-testid="generate-sample-data"]');
    await page.waitForSelector('[data-testid="historical-data-complete"]', { timeout: 10000 });
    
    // Test chart hover interactions
    const chartArea = page.locator('svg').first();
    await chartArea.hover();
    
    // Should not crash or show errors
    await page.waitForTimeout(1000);
    await expect(page.locator('svg')).toBeVisible();
  });

  test('should show loading states during data generation', async ({ page }) => {
    // Click generate button
    await page.click('[data-testid="generate-sample-data"]');
    
    // Should show loading text
    await expect(page.locator('text=Generating Sample Data...')).toBeVisible();
    
    // Button should be disabled
    await expect(page.locator('[data-testid="generate-sample-data"]')).toBeDisabled();
    
    // Wait for completion
    await page.waitForSelector('[data-testid="historical-data-complete"]', { timeout: 10000 });
    
    // Loading should be gone
    await expect(page.locator('text=Generating Sample Data...')).not.toBeVisible();
    
    // Button should be enabled again
    await expect(page.locator('[data-testid="generate-sample-data"]')).toBeEnabled();
  });
});
