import { expect, test } from '@playwright/test';
import { DashboardPage } from '../../pages/dashboard-page';
import { swissTestScenarios } from '../../fixtures/swiss-scenarios';

test.describe('Advanced Performance Tests', () => {
  let dashboardPage: DashboardPage;

  test.beforeEach(async ({ page }) => {
    dashboardPage = new DashboardPage(page);
    await dashboardPage.goto();
    await dashboardPage.clearLocalStorage();
  });

  test.describe('Core Web Vitals', () => {
    test('measures Largest Contentful Paint (LCP)', async ({ page }) => {
      console.log('📊 Testing Largest Contentful Paint');
      
      // Navigate to page and measure LCP
      const startTime = Date.now();
      await dashboardPage.goto();
      
      // Wait for page to be fully loaded
      await page.waitForLoadState('networkidle');
      
      // Measure LCP using Performance API
      const lcp = await page.evaluate(() => {
        return new Promise((resolve) => {
          new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const lastEntry = entries[entries.length - 1];
            resolve(lastEntry.startTime);
          }).observe({ entryTypes: ['largest-contentful-paint'] });
          
          // Fallback timeout
          setTimeout(() => resolve(0), 5000);
        });
      });
      
      console.log(`LCP: ${lcp}ms`);
      
      // LCP should be under 2.5 seconds (2500ms) for good performance
      expect(lcp).toBeLessThan(2500);
    });

    test('measures First Input Delay (FID)', async ({ page }) => {
      console.log('⚡ Testing First Input Delay');
      
      await dashboardPage.goto();
      await page.waitForLoadState('domcontentloaded');
      
      // Measure time from first interaction to response
      const startTime = Date.now();
      
      // First user interaction
      await dashboardPage.navigateToIncomeSubTab();
      const incomeInput = page.locator('[data-testid="monthly-income"]');
      await incomeInput.click();
      
      const inputDelay = Date.now() - startTime;
      console.log(`First Input Delay: ${inputDelay}ms`);
      
      // FID should be under 100ms for good performance
      expect(inputDelay).toBeLessThan(100);
    });

    test('measures Cumulative Layout Shift (CLS)', async ({ page }) => {
      console.log('📐 Testing Cumulative Layout Shift');
      
      await dashboardPage.goto();
      
      // Set up scenario to trigger potential layout shifts
      const scenario = swissTestScenarios.zurichProfessional;
      await dashboardPage.inputScenario(scenario);
      
      // Measure CLS
      const cls = await page.evaluate(() => {
        return new Promise((resolve) => {
          let clsValue = 0;
          
          new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
              if (!(entry as any).hadRecentInput) {
                clsValue += (entry as any).value;
              }
            }
            resolve(clsValue);
          }).observe({ entryTypes: ['layout-shift'] });
          
          // Measure for 3 seconds
          setTimeout(() => resolve(clsValue), 3000);
        });
      });
      
      console.log(`CLS: ${cls}`);
      
      // CLS should be under 0.1 for good performance
      expect(cls).toBeLessThan(0.1);
    });
  });

  test.describe('Calculation Performance', () => {
    test('measures FIRE calculation performance', async ({ page }) => {
      console.log('🧮 Testing FIRE calculation performance');
      
      await dashboardPage.navigateToIncomeSubTab();
      
      // Measure calculation time for various income levels
      const incomeValues = [5000, 8000, 12000, 20000, 50000];
      const calculationTimes: number[] = [];
      
      for (const income of incomeValues) {
        const startTime = performance.now();
        
        await dashboardPage.enterMonthlyIncome(income);
        await dashboardPage.waitForCalculations();
        
        const endTime = performance.now();
        const calculationTime = endTime - startTime;
        calculationTimes.push(calculationTime);
        
        console.log(`Income ${income}: ${calculationTime.toFixed(2)}ms`);
      }
      
      // All calculations should complete within 1 second
      const maxTime = Math.max(...calculationTimes);
      expect(maxTime).toBeLessThan(1000);
      
      // Average calculation time should be reasonable
      const avgTime = calculationTimes.reduce((a, b) => a + b, 0) / calculationTimes.length;
      expect(avgTime).toBeLessThan(500);
    });

    test('measures tax calculation performance across cantons', async ({ page }) => {
      console.log('🏔️ Testing tax calculation performance across cantons');
      
      const scenario = swissTestScenarios.genevaExecutive;
      await dashboardPage.inputScenario(scenario);
      
      // Test calculation performance for different cantons
      const cantons = ['ZH', 'GE', 'VD', 'BE', 'BS', 'ZG'];
      const taxCalculationTimes: number[] = [];
      
      for (const canton of cantons) {
        await dashboardPage.navigateToTaxOptimizationSubTab();
        
        const startTime = performance.now();
        
        await dashboardPage.selectCanton(canton);
        await dashboardPage.waitForCalculations();
        
        const endTime = performance.now();
        const calculationTime = endTime - startTime;
        taxCalculationTimes.push(calculationTime);
        
        console.log(`Canton ${canton}: ${calculationTime.toFixed(2)}ms`);
      }
      
      // Tax calculations should be fast
      const maxTime = Math.max(...taxCalculationTimes);
      expect(maxTime).toBeLessThan(800);
    });

    test('measures complex scenario calculation performance', async ({ page }) => {
      console.log('🔄 Testing complex scenario calculation performance');
      
      // Test performance with multiple rapid changes
      await dashboardPage.navigateToIncomeSubTab();
      
      const startTime = performance.now();
      
      // Rapid succession of changes
      await dashboardPage.enterMonthlyIncome(10000);
      await dashboardPage.navigateToGoalsSubTab();
      await dashboardPage.enterAge(35);
      await dashboardPage.navigateToTaxOptimizationSubTab();
      await dashboardPage.selectCanton('ZH');
      await dashboardPage.selectCivilStatus('married');
      
      // Wait for all calculations to settle
      await dashboardPage.waitForCalculations();
      await page.waitForTimeout(1000);
      
      const endTime = performance.now();
      const totalTime = endTime - startTime;
      
      console.log(`Complex scenario calculation: ${totalTime.toFixed(2)}ms`);
      
      // Complex calculations should complete within 2 seconds
      expect(totalTime).toBeLessThan(2000);
    });
  });

  test.describe('Memory Performance', () => {
    test('monitors memory usage during extended session', async ({ page }) => {
      console.log('🧠 Testing memory usage during extended session');
      
      // Check if performance.memory is available
      const hasMemoryAPI = await page.evaluate(() => {
        return 'memory' in performance;
      });
      
      if (!hasMemoryAPI) {
        console.log('⚠️ Performance.memory API not available, skipping memory test');
        return;
      }
      
      // Get initial memory usage
      const initialMemory = await page.evaluate(() => {
        return (performance as any).memory.usedJSHeapSize;
      });
      
      console.log(`Initial memory: ${(initialMemory / 1024 / 1024).toFixed(2)} MB`);
      
      // Perform many operations
      for (let i = 0; i < 20; i++) {
        const scenario = swissTestScenarios.zurichProfessional;
        await dashboardPage.inputScenario(scenario);
        
        // Navigate through tabs
        await dashboardPage.navigateToDashboardTab();
        await dashboardPage.navigateToIncomeSubTab();
        await dashboardPage.navigateToGoalsSubTab();
        await dashboardPage.navigateToTaxOptimizationSubTab();
        
        // Modify data
        await dashboardPage.enterMonthlyIncome(8000 + i * 100);
        await dashboardPage.waitForCalculations();
      }
      
      // Check final memory usage
      const finalMemory = await page.evaluate(() => {
        return (performance as any).memory.usedJSHeapSize;
      });
      
      console.log(`Final memory: ${(finalMemory / 1024 / 1024).toFixed(2)} MB`);
      
      const memoryIncrease = finalMemory - initialMemory;
      const memoryIncreaseMB = memoryIncrease / 1024 / 1024;
      
      console.log(`Memory increase: ${memoryIncreaseMB.toFixed(2)} MB`);
      
      // Memory increase should be reasonable (less than 50MB)
      expect(memoryIncreaseMB).toBeLessThan(50);
    });

    test('checks for memory leaks in chart rendering', async ({ page }) => {
      console.log('📊 Testing memory leaks in chart rendering');
      
      const scenario = swissTestScenarios.baselTechWorker;
      await dashboardPage.inputScenario(scenario);
      
      // Navigate to visualizations multiple times
      for (let i = 0; i < 10; i++) {
        await dashboardPage.navigateToVisualizationsSubTab();
        await page.waitForTimeout(1000);
        
        await dashboardPage.navigateToDashboardTab();
        await page.waitForTimeout(500);
      }
      
      // Force garbage collection if available
      await page.evaluate(() => {
        if ('gc' in window) {
          (window as any).gc();
        }
      });
      
      // Check memory after chart operations
      const hasMemoryAPI = await page.evaluate(() => {
        return 'memory' in performance;
      });
      
      if (hasMemoryAPI) {
        const memoryAfterCharts = await page.evaluate(() => {
          return (performance as any).memory.usedJSHeapSize;
        });
        
        console.log(`Memory after chart operations: ${(memoryAfterCharts / 1024 / 1024).toFixed(2)} MB`);
        
        // Memory should be reasonable
        expect(memoryAfterCharts).toBeLessThan(100 * 1024 * 1024); // 100MB
      }
    });
  });

  test.describe('Network Performance', () => {
    test('measures resource loading performance', async ({ page }) => {
      console.log('🌐 Testing resource loading performance');
      
      // Clear cache to ensure fresh load
      await page.context().clearCookies();
      
      const startTime = Date.now();
      await dashboardPage.goto();
      await page.waitForLoadState('networkidle');
      const loadTime = Date.now() - startTime;
      
      console.log(`Total load time: ${loadTime}ms`);
      
      // Get resource timing information
      const resourceTiming = await page.evaluate(() => {
        const resources = performance.getEntriesByType('resource');
        return resources.map(resource => ({
          name: resource.name,
          duration: resource.duration,
          size: (resource as any).transferSize || 0,
        }));
      });
      
      // Log largest resources
      const largestResources = resourceTiming
        .sort((a, b) => b.duration - a.duration)
        .slice(0, 5);
      
      console.log('Slowest resources:');
      largestResources.forEach(resource => {
        console.log(`  ${resource.name}: ${resource.duration.toFixed(2)}ms`);
      });
      
      // Total load time should be reasonable
      expect(loadTime).toBeLessThan(5000);
    });

    test('tests performance under slow network conditions', async ({ page }) => {
      console.log('🐌 Testing performance under slow network');
      
      // Simulate slow 3G network
      await page.context().route('**/*', async route => {
        await new Promise(resolve => setTimeout(resolve, 100)); // 100ms delay
        await route.continue();
      });
      
      const startTime = Date.now();
      await dashboardPage.goto();
      await page.waitForSelector('h1', { timeout: 15000 });
      const loadTime = Date.now() - startTime;
      
      console.log(`Load time on slow network: ${loadTime}ms`);
      
      // Should still load within reasonable time
      expect(loadTime).toBeLessThan(10000);
      
      // Test functionality under slow network
      await dashboardPage.navigateToIncomeSubTab();
      const inputStartTime = Date.now();
      await dashboardPage.enterMonthlyIncome(8000);
      await dashboardPage.waitForCalculations();
      const inputTime = Date.now() - inputStartTime;
      
      console.log(`Input response time on slow network: ${inputTime}ms`);
      
      // Interactions should remain responsive
      expect(inputTime).toBeLessThan(3000);
      
      // Clean up
      await page.context().unroute('**/*');
    });
  });

  test.describe('Rendering Performance', () => {
    test('measures tab switching performance', async ({ page }) => {
      console.log('🔄 Testing tab switching performance');
      
      const scenario = swissTestScenarios.vaudFamily;
      await dashboardPage.inputScenario(scenario);
      
      const tabs = [
        () => dashboardPage.navigateToDashboardTab(),
        () => dashboardPage.navigateToIncomeSubTab(),
        () => dashboardPage.navigateToGoalsSubTab(),
        () => dashboardPage.navigateToTaxOptimizationSubTab(),
      ];
      
      const switchTimes: number[] = [];
      
      for (let i = 0; i < tabs.length; i++) {
        const startTime = performance.now();
        await tabs[i]();
        await page.waitForTimeout(100); // Wait for transition
        const endTime = performance.now();
        
        const switchTime = endTime - startTime;
        switchTimes.push(switchTime);
        
        console.log(`Tab switch ${i}: ${switchTime.toFixed(2)}ms`);
      }
      
      // Tab switches should be fast
      const maxSwitchTime = Math.max(...switchTimes);
      expect(maxSwitchTime).toBeLessThan(500);
      
      const avgSwitchTime = switchTimes.reduce((a, b) => a + b, 0) / switchTimes.length;
      expect(avgSwitchTime).toBeLessThan(300);
    });

    test('measures form rendering performance', async ({ page }) => {
      console.log('📝 Testing form rendering performance');
      
      // Measure time to render complex forms
      const startTime = performance.now();
      
      await dashboardPage.navigateToIncomeSubTab();
      await page.waitForSelector('[data-testid="monthly-income"]');
      
      const renderTime = performance.now() - startTime;
      console.log(`Form render time: ${renderTime.toFixed(2)}ms`);
      
      // Form should render quickly
      expect(renderTime).toBeLessThan(200);
      
      // Test form interaction performance
      const interactionStartTime = performance.now();
      
      await dashboardPage.enterMonthlyIncome(8500);
      await dashboardPage.enterAge(32);
      
      const interactionTime = performance.now() - interactionStartTime;
      console.log(`Form interaction time: ${interactionTime.toFixed(2)}ms`);
      
      // Form interactions should be responsive
      expect(interactionTime).toBeLessThan(1000);
    });
  });
});
