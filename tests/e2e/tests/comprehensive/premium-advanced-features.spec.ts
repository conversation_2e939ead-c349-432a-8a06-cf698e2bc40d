import { test, expect } from '@playwright/test';

test.describe('Comprehensive Premium & Advanced Features Integration', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Set up comprehensive financial data
    await page.fill('[data-testid="current-age"]', '40');
    await page.fill('[data-testid="retirement-age"]', '65');
    await page.fill('[data-testid="current-savings"]', '250000');
    await page.fill('[data-testid="monthly-income"]', '12000');
    await page.fill('[data-testid="monthly-expenses"]', '7000');
    await page.fill('[data-testid="expected-return"]', '7');
    await page.fill('[data-testid="inflation-rate"]', '2.5');
    await page.fill('[data-testid="safe-withdrawal-rate"]', '4');
  });

  test('should navigate through all premium features successfully', async ({ page }) => {
    // Navigate to Premium tab
    await page.click('[data-testid="tab-premium"]');
    await page.waitForTimeout(1000);
    
    // Verify Premium tab header
    await expect(page.locator('h2:has-text("💎 Premium Features")')).toBeVisible();
    
    // Check all premium feature sections are visible
    const premiumFeatures = [
      '₿ Cryptocurrency Portfolio Manager',
      '🏠 Real Estate Investment Tracker',
      '⚖️ Advanced Portfolio Rebalancing',
      '🤖 AI Financial Advisor',
      '🏛️ Canton Relocation Tax Optimizer',
    ];
    
    for (const feature of premiumFeatures) {
      await expect(page.locator(`h3:has-text("${feature}")`)).toBeVisible();
    }
  });

  test('should navigate through all advanced features successfully', async ({ page }) => {
    // Navigate to Advanced tab
    await page.click('[data-testid="tab-advanced"]');
    await page.waitForTimeout(1000);
    
    // Check all advanced feature sections are visible
    const advancedFeatures = [
      '🎲 Monte Carlo Simulation',
      '🏛️ Advanced Tax Calculator',
      '💾 Data Export & Import',
      '⚡ Performance Optimizer',
      '⚠️ Risk Assessment Metrics',
      '📊 Safe Withdrawal Rate Analysis',
    ];
    
    for (const feature of advancedFeatures) {
      await expect(page.locator(`h3:has-text("${feature}")`)).toBeVisible();
    }
  });

  test('should verify AI Financial Advisor integration with other features', async ({ page }) => {
    // Navigate to Premium tab
    await page.click('[data-testid="tab-premium"]');
    await page.waitForTimeout(1000);
    
    // Wait for AI analysis to complete
    await page.waitForSelector('[data-testid="ai-analysis-complete"]', { timeout: 15000 });
    
    // Verify AI insights reference other features
    await expect(page.locator('text=Pillar 3a')).toBeVisible();
    await expect(page.locator('text=emergency fund')).toBeVisible();
    await expect(page.locator('text=diversification')).toBeVisible();
    
    // Test AI chat with feature-specific questions
    const chatInput = page.locator('[data-testid="ai-chat-input"]');
    await chatInput.fill('Should I use the Monte Carlo simulation?');
    await page.click('[data-testid="ai-chat-send"]');
    
    await page.waitForSelector('[data-testid="ai-message"]', { timeout: 5000 });
    await expect(page.locator('[data-testid="ai-message"]').last()).toBeVisible();
  });

  test('should verify risk assessment integration with withdrawal rate analysis', async ({ page }) => {
    // Navigate to Advanced tab
    await page.click('[data-testid="tab-advanced"]');
    await page.waitForTimeout(1000);
    
    // Wait for both analyses to complete
    await page.waitForSelector('[data-testid="risk-analysis-complete"]', { timeout: 15000 });
    await page.waitForSelector('[data-testid="withdrawal-analysis-complete"]', { timeout: 15000 });
    
    // Verify risk assessment shows withdrawal rate risk
    await expect(page.locator('text=Withdrawal Rate Safety')).toBeVisible();
    
    // Verify withdrawal rate analysis shows risk factors
    await expect(page.locator('text=Risk Factors')).toBeVisible();
    
    // Both should reference similar concepts
    await expect(page.locator('text=sequence')).toBeVisible();
    await expect(page.locator('text=volatility')).toBeVisible();
  });

  test('should verify data consistency across features', async ({ page }) => {
    // Set specific values
    await page.fill('[data-testid="monthly-expenses"]', '8000');
    await page.fill('[data-testid="safe-withdrawal-rate"]', '3.5');
    
    // Check Premium features
    await page.click('[data-testid="tab-premium"]');
    await page.waitForTimeout(1000);
    await page.waitForSelector('[data-testid="ai-analysis-complete"]', { timeout: 15000 });
    
    // AI should reference the 3.5% rate
    await expect(page.locator('text=3.5%')).toBeVisible();
    
    // Check Advanced features
    await page.click('[data-testid="tab-advanced"]');
    await page.waitForTimeout(1000);
    await page.waitForSelector('[data-testid="withdrawal-analysis-complete"]', { timeout: 15000 });
    
    // Withdrawal analysis should show 3.5% as current rate
    await expect(page.locator('text=Your current 3.5% withdrawal rate')).toBeVisible();
  });

  test('should handle feature interactions without conflicts', async ({ page }) => {
    // Navigate to Premium tab and interact with multiple features
    await page.click('[data-testid="tab-premium"]');
    await page.waitForTimeout(1000);
    
    // Interact with AI Financial Advisor
    await page.waitForSelector('[data-testid="ai-analysis-complete"]', { timeout: 15000 });
    await page.click('[data-testid="refresh-ai-analysis"]');
    
    // Navigate to Advanced tab while AI is processing
    await page.click('[data-testid="tab-advanced"]');
    await page.waitForTimeout(1000);
    
    // Interact with Risk Assessment
    await page.waitForSelector('[data-testid="risk-analysis-complete"]', { timeout: 15000 });
    await page.selectOption('[data-testid="risk-category-filter"]', 'financial');
    
    // Navigate back to Premium
    await page.click('[data-testid="tab-premium"]');
    await page.waitForTimeout(1000);
    
    // Features should still work
    await expect(page.locator('h2:has-text("💎 Premium Features")')).toBeVisible();
  });

  test('should verify performance across all features', async ({ page }) => {
    // Measure navigation performance
    const startTime = Date.now();
    
    // Navigate through all tabs with features
    const tabs = ['premium', 'advanced', 'analysis'];
    
    for (const tab of tabs) {
      await page.click(`[data-testid="tab-${tab}"]`);
      await page.waitForTimeout(1000);
      
      // Verify tab loads without errors
      await expect(page.locator(`[data-testid="tab-${tab}"]`)).toHaveClass(/bg-blue-600/);
    }
    
    const endTime = Date.now();
    const totalTime = endTime - startTime;
    
    // Should complete navigation in reasonable time (less than 10 seconds)
    expect(totalTime).toBeLessThan(10000);
  });

  test('should verify mobile responsiveness across all features', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Test Premium features on mobile
    await page.click('[data-testid="tab-premium"]');
    await page.waitForTimeout(1000);
    
    await expect(page.locator('h2:has-text("💎 Premium Features")')).toBeVisible();
    
    // Test Advanced features on mobile
    await page.click('[data-testid="tab-advanced"]');
    await page.waitForTimeout(1000);
    
    await expect(page.locator('h3:has-text("🎲 Monte Carlo Simulation")')).toBeVisible();
    
    // Test Analysis features on mobile
    await page.click('[data-testid="tab-analysis"]');
    await page.waitForTimeout(1000);
    
    await expect(page.locator('h3:has-text("📈 Historical Tracking")')).toBeVisible();
  });

  test('should verify error handling across features', async ({ page }) => {
    // Set invalid data
    await page.fill('[data-testid="current-age"]', '150');
    await page.fill('[data-testid="retirement-age"]', '50');
    await page.fill('[data-testid="monthly-income"]', '-1000');
    
    // Navigate to Premium tab
    await page.click('[data-testid="tab-premium"]');
    await page.waitForTimeout(3000);
    
    // Should not crash
    await expect(page.locator('h2:has-text("💎 Premium Features")')).toBeVisible();
    
    // Navigate to Advanced tab
    await page.click('[data-testid="tab-advanced"]');
    await page.waitForTimeout(3000);
    
    // Should not crash
    await expect(page.locator('h3:has-text("🎲 Monte Carlo Simulation")')).toBeVisible();
  });

  test('should verify accessibility across all features', async ({ page }) => {
    // Test keyboard navigation
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab');
    
    // Navigate to Premium tab with keyboard
    await page.keyboard.press('Enter');
    await page.waitForTimeout(1000);
    
    // Should be able to navigate with keyboard
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab');
    
    // Test screen reader compatibility
    const premiumHeader = page.locator('h2:has-text("💎 Premium Features")');
    await expect(premiumHeader).toHaveAttribute('role', 'heading');
  });

  test('should verify data export includes all feature data', async ({ page }) => {
    // Navigate to Advanced tab
    await page.click('[data-testid="tab-advanced"]');
    await page.waitForTimeout(1000);
    
    // Find and test data export functionality
    await expect(page.locator('h3:has-text("💾 Data Export & Import")')).toBeVisible();
    
    // Should include comprehensive data from all features
    const exportButton = page.locator('[data-testid="export-data"]');
    if (await exportButton.isVisible()) {
      await exportButton.click();
      await page.waitForTimeout(1000);
    }
  });

  test('should verify Swiss-specific features integration', async ({ page }) => {
    // Navigate to Premium tab
    await page.click('[data-testid="tab-premium"]');
    await page.waitForTimeout(1000);
    
    // Wait for AI analysis
    await page.waitForSelector('[data-testid="ai-analysis-complete"]', { timeout: 15000 });
    
    // Verify Swiss-specific content across features
    await expect(page.locator('text=CHF')).toBeVisible();
    await expect(page.locator('text=Pillar 3a')).toBeVisible();
    await expect(page.locator('text=canton')).toBeVisible();
    
    // Navigate to Advanced tab
    await page.click('[data-testid="tab-advanced"]');
    await page.waitForTimeout(1000);
    
    // Verify Swiss tax integration
    await expect(page.locator('h3:has-text("🏛️ Advanced Tax Calculator")')).toBeVisible();
    await expect(page.locator('text=Swiss tax')).toBeVisible();
  });
});
