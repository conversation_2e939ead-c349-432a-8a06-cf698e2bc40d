import { expect, test } from '@playwright/test';
import { DashboardPage } from '../../pages/dashboard-page';

test.describe('Error Handling and Edge Cases', () => {
  let dashboardPage: DashboardPage;

  test.beforeEach(async ({ page }) => {
    dashboardPage = new DashboardPage(page);
    await dashboardPage.goto();
    await dashboardPage.clearLocalStorage();
  });

  test.describe('Network Error Handling', () => {
    test('handles offline mode gracefully', async ({ page }) => {
      console.log('🌐 Testing offline mode handling');
      
      // Set up initial data
      await dashboardPage.navigateToIncomeSubTab();
      await dashboardPage.enterMonthlyIncome(8000);
      
      // Simulate offline mode
      await page.context().setOffline(true);
      
      // Try to perform actions that might require network
      await dashboardPage.navigateToEconomicDataSubTab();
      
      // Should show offline indicator or graceful degradation
      const offlineIndicator = page.locator('[data-testid="offline-indicator"]');
      const errorMessage = page.locator('[data-testid="network-error"]');
      
      // Either offline indicator should be shown or error handled gracefully
      const hasOfflineHandling = await offlineIndicator.count() > 0 || await errorMessage.count() > 0;
      
      if (hasOfflineHandling) {
        console.log('✅ Offline mode handling detected');
      } else {
        // App should still function for basic calculations
        await dashboardPage.navigateToDashboardTab();
        const fireProjection = page.locator('[data-testid="fire-projection"]');
        await expect(fireProjection).toBeVisible();
      }
      
      // Restore online mode
      await page.context().setOffline(false);
    });

    test('recovers from temporary network failures', async ({ page }) => {
      console.log('🔄 Testing network failure recovery');
      
      // Set up scenario
      await dashboardPage.navigateToIncomeSubTab();
      await dashboardPage.enterMonthlyIncome(9000);
      
      // Simulate network failure
      await page.route('**/*', route => route.abort());
      
      // Try to perform network-dependent action
      await dashboardPage.navigateToEconomicDataSubTab();
      await page.waitForTimeout(2000);
      
      // Restore network
      await page.unroute('**/*');
      
      // Should recover automatically or provide retry mechanism
      const retryButton = page.locator('[data-testid="retry-button"]');
      if (await retryButton.count() > 0) {
        await retryButton.click();
        await page.waitForTimeout(1000);
      }
      
      // Verify functionality is restored
      await dashboardPage.navigateToDashboardTab();
      const fireProjection = page.locator('[data-testid="fire-projection"]');
      await expect(fireProjection).toBeVisible();
    });
  });

  test.describe('Browser Compatibility Edge Cases', () => {
    test('handles localStorage quota exceeded', async ({ page }) => {
      console.log('💾 Testing localStorage quota handling');
      
      // Fill localStorage to near capacity
      await page.evaluate(() => {
        try {
          const largeData = 'x'.repeat(1000000); // 1MB string
          for (let i = 0; i < 5; i++) {
            localStorage.setItem(`large-data-${i}`, largeData);
          }
        } catch (e) {
          console.log('localStorage quota reached during setup');
        }
      });
      
      // Try to save financial data
      await dashboardPage.navigateToIncomeSubTab();
      await dashboardPage.enterMonthlyIncome(8500);
      
      // Should handle storage quota gracefully
      const storageError = page.locator('[data-testid="storage-error"]');
      const storageWarning = page.locator('[data-testid="storage-warning"]');
      
      // Either show error/warning or handle silently
      if (await storageError.count() > 0) {
        await expect(storageError).toBeVisible();
        await expect(storageError).toContainText('storage');
      } else if (await storageWarning.count() > 0) {
        await expect(storageWarning).toBeVisible();
      }
      
      // Clean up
      await page.evaluate(() => {
        for (let i = 0; i < 5; i++) {
          localStorage.removeItem(`large-data-${i}`);
        }
      });
    });

    test('handles disabled JavaScript gracefully', async ({ page }) => {
      console.log('🚫 Testing JavaScript disabled scenario');
      
      // This test verifies the app shows appropriate fallback content
      // when JavaScript is disabled (noscript tags, etc.)
      
      const noscriptContent = page.locator('noscript');
      if (await noscriptContent.count() > 0) {
        // Verify noscript content is meaningful
        const noscriptText = await noscriptContent.textContent();
        expect(noscriptText).toContain('JavaScript');
        expect(noscriptText).toContain('enable');
      }
    });

    test('handles very slow network conditions', async ({ page }) => {
      console.log('🐌 Testing slow network handling');
      
      // Simulate very slow network
      await page.route('**/*', async route => {
        await new Promise(resolve => setTimeout(resolve, 2000)); // 2s delay
        await route.continue();
      });
      
      // Navigate and verify loading states
      await dashboardPage.goto();
      
      // Should show loading indicators
      const loadingIndicator = page.locator('[data-testid="loading"]');
      if (await loadingIndicator.count() > 0) {
        await expect(loadingIndicator).toBeVisible();
      }
      
      // Eventually should load
      await page.waitForSelector('h1', { timeout: 15000 });
      
      // Clean up
      await page.unroute('**/*');
    });
  });

  test.describe('Data Corruption and Recovery', () => {
    test('handles corrupted localStorage data', async ({ page }) => {
      console.log('🔧 Testing corrupted data recovery');
      
      // Inject corrupted data into localStorage
      await page.evaluate(() => {
        localStorage.setItem('swiss-budget-pro-data', '{ corrupted json data');
        localStorage.setItem('swiss-budget-pro-settings', 'invalid data');
      });
      
      // Reload page
      await page.reload();
      await page.waitForSelector('h1', { timeout: 10000 });
      
      // Should handle corruption gracefully
      const corruptionError = page.locator('[data-testid="data-corruption-error"]');
      const resetPrompt = page.locator('[data-testid="reset-data-prompt"]');
      
      if (await corruptionError.count() > 0) {
        await expect(corruptionError).toBeVisible();
      }
      
      if (await resetPrompt.count() > 0) {
        await resetPrompt.click();
        await page.waitForTimeout(1000);
      }
      
      // App should recover to clean state
      const incomeInput = page.locator('input[type="number"]').first();
      await expect(incomeInput).toBeVisible();
    });

    test('recovers from calculation errors', async ({ page }) => {
      console.log('🧮 Testing calculation error recovery');
      
      // Input extreme values that might cause calculation errors
      await dashboardPage.navigateToIncomeSubTab();
      await dashboardPage.enterMonthlyIncome(999999999);
      
      await dashboardPage.navigateToGoalsSubTab();
      await dashboardPage.enterAge(25);
      
      // Navigate to dashboard to trigger calculations
      await dashboardPage.navigateToDashboardTab();
      
      // Should handle calculation overflow/errors gracefully
      const calculationError = page.locator('[data-testid="calculation-error"]');
      const fireProjection = page.locator('[data-testid="fire-projection"]');
      
      if (await calculationError.count() > 0) {
        await expect(calculationError).toBeVisible();
        await expect(calculationError).toContainText('calculation');
      } else {
        // Should show some reasonable result or error message
        await expect(fireProjection).toBeVisible();
      }
    });

    test('handles memory leaks in long sessions', async ({ page }) => {
      console.log('🧠 Testing memory leak prevention');
      
      // Simulate long session with many operations
      for (let i = 0; i < 10; i++) {
        await dashboardPage.navigateToIncomeSubTab();
        await dashboardPage.enterMonthlyIncome(8000 + i * 100);
        
        await dashboardPage.navigateToGoalsSubTab();
        await dashboardPage.enterAge(30 + i);
        
        await dashboardPage.navigateToDashboardTab();
        await dashboardPage.waitForCalculations();
        
        // Check memory usage periodically
        if (i % 5 === 0) {
          const memoryInfo = await page.evaluate(() => {
            return (performance as any).memory ? {
              usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
              totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
            } : null;
          });
          
          if (memoryInfo) {
            console.log(`Memory usage at iteration ${i}:`, memoryInfo);
            // Memory shouldn't grow excessively
            expect(memoryInfo.usedJSHeapSize).toBeLessThan(100 * 1024 * 1024); // 100MB limit
          }
        }
      }
    });
  });

  test.describe('User Input Edge Cases', () => {
    test('handles rapid successive input changes', async ({ page }) => {
      console.log('⚡ Testing rapid input changes');
      
      await dashboardPage.navigateToIncomeSubTab();
      const incomeInput = page.locator('[data-testid="monthly-income"]');
      
      // Rapidly change input values
      for (let i = 0; i < 10; i++) {
        await incomeInput.fill((5000 + i * 500).toString());
        await page.waitForTimeout(50); // Very short delay
      }
      
      // Wait for debouncing to settle
      await page.waitForTimeout(2000);
      
      // Should handle the final value correctly
      const finalValue = await incomeInput.inputValue();
      expect(parseInt(finalValue)).toBe(9500);
      
      // Calculations should be stable
      await dashboardPage.navigateToDashboardTab();
      const fireProjection = page.locator('[data-testid="fire-projection"]');
      await expect(fireProjection).toBeVisible();
    });

    test('handles special characters in input fields', async ({ page }) => {
      console.log('🔤 Testing special character handling');
      
      await dashboardPage.navigateToIncomeSubTab();
      const incomeInput = page.locator('[data-testid="monthly-income"]');
      
      // Try various special characters
      const specialInputs = ['abc', '!@#$%', '8,500.50', '8\'500', '8 500', '8e3'];
      
      for (const input of specialInputs) {
        await incomeInput.clear();
        await incomeInput.type(input);
        await page.waitForTimeout(500);
        
        // Should either sanitize input or show validation error
        const value = await incomeInput.inputValue();
        const errorMessage = page.locator('[data-testid="monthly-income-error"]');
        
        // Either value is sanitized to valid number or error is shown
        if (await errorMessage.count() === 0) {
          // If no error, value should be a valid number or empty
          expect(value === '' || !isNaN(parseFloat(value))).toBeTruthy();
        }
      }
    });

    test('handles copy-paste of large data', async ({ page }) => {
      console.log('📋 Testing large data paste handling');
      
      await dashboardPage.navigateToIncomeSubTab();
      const incomeInput = page.locator('[data-testid="monthly-income"]');
      
      // Paste very large string
      const largeString = '1'.repeat(1000);
      await incomeInput.clear();
      await incomeInput.fill(largeString);
      
      // Should handle gracefully (truncate, show error, or sanitize)
      const value = await incomeInput.inputValue();
      const errorMessage = page.locator('[data-testid="monthly-income-error"]');
      
      // Either value is reasonable length or error is shown
      if (await errorMessage.count() === 0) {
        expect(value.length).toBeLessThan(20); // Reasonable input length
      } else {
        await expect(errorMessage).toBeVisible();
      }
    });
  });

  test.describe('Concurrent User Actions', () => {
    test('handles multiple tabs with same app', async ({ context }) => {
      console.log('🗂️ Testing multiple tab handling');
      
      // Open second tab
      const page2 = await context.newPage();
      const dashboardPage2 = new DashboardPage(page2);
      
      // Set different data in each tab
      await dashboardPage.navigateToIncomeSubTab();
      await dashboardPage.enterMonthlyIncome(8000);
      
      await dashboardPage2.goto();
      await dashboardPage2.navigateToIncomeSubTab();
      await dashboardPage2.enterMonthlyIncome(9000);
      
      // Check data synchronization or isolation
      await dashboardPage.page.bringToFront();
      await dashboardPage.page.reload();
      await dashboardPage.navigateToIncomeSubTab();
      
      const value1 = await dashboardPage.page.locator('[data-testid="monthly-income"]').inputValue();
      
      await page2.bringToFront();
      await page2.reload();
      await dashboardPage2.navigateToIncomeSubTab();
      
      const value2 = await page2.locator('[data-testid="monthly-income"]').inputValue();
      
      // Data should be consistent (last write wins) or properly isolated
      console.log(`Tab 1 value: ${value1}, Tab 2 value: ${value2}`);
      
      await page2.close();
    });

    test('handles rapid tab switching', async ({ page }) => {
      console.log('🔄 Testing rapid tab switching');
      
      // Set up initial data
      await dashboardPage.navigateToIncomeSubTab();
      await dashboardPage.enterMonthlyIncome(8500);
      
      // Rapidly switch between tabs
      const tabs = [
        () => dashboardPage.navigateToDashboardTab(),
        () => dashboardPage.navigateToIncomeSubTab(),
        () => dashboardPage.navigateToGoalsSubTab(),
        () => dashboardPage.navigateToTaxOptimizationSubTab(),
      ];
      
      for (let i = 0; i < 20; i++) {
        const randomTab = tabs[Math.floor(Math.random() * tabs.length)];
        await randomTab();
        await page.waitForTimeout(100);
      }
      
      // Should end up in stable state
      await dashboardPage.navigateToDashboardTab();
      const fireProjection = page.locator('[data-testid="fire-projection"]');
      await expect(fireProjection).toBeVisible();
    });
  });

  test.describe('Resource Exhaustion', () => {
    test('handles excessive DOM manipulation', async ({ page }) => {
      console.log('🏗️ Testing excessive DOM manipulation');
      
      // Trigger many DOM updates
      await dashboardPage.navigateToIncomeSubTab();
      
      for (let i = 0; i < 50; i++) {
        await dashboardPage.enterMonthlyIncome(5000 + i * 100);
        await page.waitForTimeout(10);
      }
      
      // Should remain responsive
      const incomeInput = page.locator('[data-testid="monthly-income"]');
      await expect(incomeInput).toBeVisible();
      
      // Final calculations should work
      await dashboardPage.navigateToDashboardTab();
      const fireProjection = page.locator('[data-testid="fire-projection"]');
      await expect(fireProjection).toBeVisible();
    });

    test('handles large calculation datasets', async ({ page }) => {
      console.log('📊 Testing large dataset calculations');
      
      // Set up scenario that might generate large datasets
      await dashboardPage.navigateToIncomeSubTab();
      await dashboardPage.enterMonthlyIncome(15000);
      
      await dashboardPage.navigateToGoalsSubTab();
      await dashboardPage.enterAge(25); // Long projection period
      
      // Navigate to visualizations which might process large datasets
      await dashboardPage.navigateToVisualizationsSubTab();
      
      // Should handle large calculations without freezing
      await page.waitForTimeout(5000);
      
      // Page should remain responsive
      const title = page.locator('h1');
      await expect(title).toBeVisible();
    });
  });
});
