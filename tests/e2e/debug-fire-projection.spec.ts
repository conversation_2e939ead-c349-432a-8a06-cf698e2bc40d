import { test, expect } from '@playwright/test';

test.describe('Debug FIRE Projection', () => {
  test('Check if FIRE projection element exists', async ({ page }) => {
    // Navigate to the app
    await page.goto('/');

    // Wait for the app to load
    await page.waitForSelector('h1:has-text("Swiss Budget Pro")', {
      timeout: 30000,
    });

    // Navigate to dashboard tab
    await page.click('button:has-text("Dashboard")');

    // Wait a bit for calculations to complete
    await page.waitForTimeout(2000);

    // Check for FIRE projection element
    const fireProjectionElement = page.locator(
      '[data-testid="fire-projection"]',
    );
    const fireProjectionCount = await fireProjectionElement.count();

    console.log(`🔍 FIRE projection element count: ${fireProjectionCount}`);

    if (fireProjectionCount > 0) {
      const fireProjectionText = await fireProjectionElement.textContent();
      console.log(`📊 FIRE projection text: ${fireProjectionText}`);
    }

    // Check for any element containing "FIRE @ Age"
    const fireAgeElements = page.locator('text=FIRE @ Age');
    const fireAgeCount = await fireAgeElements.count();
    console.log(`🎯 Elements containing "FIRE @ Age": ${fireAgeCount}`);

    if (fireAgeCount > 0) {
      const fireAgeText = await fireAgeElements.first().textContent();
      console.log(`🎯 FIRE @ Age text: ${fireAgeText}`);
    }

    // Check all elements with data-testid attributes
    const allTestIds = await page.locator('[data-testid]').all();
    console.log(`🔍 All elements with data-testid: ${allTestIds.length}`);

    for (const element of allTestIds) {
      const testId = await element.getAttribute('data-testid');
      const text = await element.textContent();
      console.log(`  - data-testid="${testId}": "${text?.slice(0, 50)}..."`);
    }

    // Take a screenshot for debugging
    await page.screenshot({
      path: 'debug-fire-projection.png',
      fullPage: true,
    });
  });

  test('Check if monthly tax element exists', async ({ page }) => {
    // Navigate to the app
    await page.goto('/');

    // Wait for the app to load
    await page.waitForSelector('h1:has-text("Swiss Budget Pro")', {
      timeout: 30000,
    });

    // Navigate to Analysis > Tax Optimizer sub-tab
    await page.click('button:has-text("Analysis")');
    await page.waitForTimeout(500);
    await page.click('button:has-text("Tax Optimizer")');
    await page.waitForTimeout(2000);

    // Check for monthly tax element
    const monthlyTaxElement = page.locator('[data-testid="monthly-tax"]');
    const monthlyTaxCount = await monthlyTaxElement.count();

    console.log(`💰 Monthly tax element count: ${monthlyTaxCount}`);

    if (monthlyTaxCount > 0) {
      const monthlyTaxText = await monthlyTaxElement.textContent();
      console.log(`💰 Monthly tax text: ${monthlyTaxText}`);
    } else {
      // Check if the tax optimization section is visible
      const taxOptSection = page.locator('text=Tax Optimization');
      const taxOptCount = await taxOptSection.count();
      console.log(`📊 Tax Optimization sections found: ${taxOptCount}`);

      // Check for any elements containing "tax"
      const taxElements = page.locator('text=/tax/i');
      const taxElementsCount = await taxElements.count();
      console.log(`🔍 Elements containing "tax": ${taxElementsCount}`);

      // List first few tax-related elements
      const allTaxElements = await taxElements.all();
      for (let i = 0; i < Math.min(5, allTaxElements.length); i++) {
        const text = await allTaxElements[i].textContent();
        console.log(`  - Tax element ${i + 1}: "${text?.slice(0, 50)}..."`);
      }
    }

    // Take a screenshot for debugging
    await page.screenshot({ path: 'debug-monthly-tax.png', fullPage: true });
  });
});
