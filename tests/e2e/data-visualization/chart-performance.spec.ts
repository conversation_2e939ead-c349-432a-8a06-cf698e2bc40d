/**
 * E2E Performance Tests for Data Visualization
 * Tests chart rendering performance, memory usage, and optimization
 */

import { test, expect } from '@playwright/test';
import { SwissBudgetProPage } from '../../page-objects/pages/SwissBudgetProPage';
import { DataVisualizationPage } from '../../page-objects/pages/DataVisualizationPage';

test.describe('Data Visualization Performance', () => {
  let swissBudgetPage: SwissBudgetProPage;
  let dataVizPage: DataVisualizationPage;

  test.beforeEach(async ({ page }) => {
    swissBudgetPage = new SwissBudgetProPage(page);
    dataVizPage = new DataVisualizationPage(page);
    
    await swissBudgetPage.goto();
    await swissBudgetPage.verifyPageLoaded();
    
    // Set up comprehensive test data
    await swissBudgetPage.fillBasicFinancialInfo({
      monthlyIncome: 10000,
      monthlyExpenses: 6000,
      currentSavings: 200000,
      canton: 'ZH',
      age: 35,
      retirementAge: 60,
    });
  });

  test('E2E-PERF-VIZ-001: Chart Rendering Performance', async ({ page }) => {
    test.step('Measure initial chart load time', async () => {
      const startTime = Date.now();
      
      await dataVizPage.navigateToVisualization();
      await dataVizPage.waitForChartsToLoad();
      
      const loadTime = Date.now() - startTime;
      
      // Charts should load within 2 seconds
      expect(loadTime).toBeLessThan(2000);
      console.log(`Initial chart load time: ${loadTime}ms`);
      
      // Verify charts are actually rendered
      const svgCount = await page.locator('svg').count();
      expect(svgCount).toBeGreaterThan(0);
    });

    test.step('Measure chart update performance', async () => {
      await dataVizPage.waitForChartsToLoad();
      
      // Measure timeframe change performance
      const startTime = Date.now();
      await dataVizPage.selectTimeframe('1Y');
      await dataVizPage.waitForChartUpdate();
      const updateTime = Date.now() - startTime;
      
      // Chart updates should be under 500ms
      expect(updateTime).toBeLessThan(500);
      console.log(`Chart update time: ${updateTime}ms`);
    });

    test.step('Measure chart type change performance', async () => {
      const chartTypes = ['line', 'area', 'bar'];
      const performanceResults: Record<string, number> = {};
      
      for (const chartType of chartTypes) {
        const startTime = Date.now();
        await dataVizPage.selectChartType(chartType);
        await dataVizPage.waitForChartUpdate();
        const changeTime = Date.now() - startTime;
        
        performanceResults[chartType] = changeTime;
        
        // Each chart type change should be under 800ms
        expect(changeTime).toBeLessThan(800);
      }
      
      console.log('Chart type change performance:', performanceResults);
    });

    test.step('Measure animation performance', async () => {
      // Enable performance monitoring
      await dataVizPage.togglePerformanceMonitor(true);
      
      // Trigger animations by changing chart type
      await dataVizPage.selectChartType('area');
      await page.waitForTimeout(1500); // Wait for animation to complete
      
      // Check performance metrics
      const metrics = await dataVizPage.getPerformanceMetrics();
      if (metrics) {
        expect(metrics.renderTime).toBeLessThan(500);
        expect(metrics.frameRate).toBeGreaterThan(30);
        console.log('Animation performance metrics:', metrics);
      }
    });
  });

  test('E2E-PERF-VIZ-002: Memory Usage and Optimization', async ({ page }) => {
    test.step('Measure baseline memory usage', async () => {
      await dataVizPage.navigateToVisualization();
      await dataVizPage.waitForChartsToLoad();
      
      const initialMemory = await page.evaluate(() => {
        const memory = (performance as any).memory;
        return memory ? {
          used: memory.usedJSHeapSize,
          total: memory.totalJSHeapSize,
          limit: memory.jsHeapSizeLimit
        } : null;
      });
      
      if (initialMemory) {
        console.log('Initial memory usage:', initialMemory);
        
        // Baseline memory should be reasonable
        expect(initialMemory.used).toBeLessThan(50 * 1024 * 1024); // Less than 50MB
      }
    });

    test.step('Test memory usage with large datasets', async () => {
      // Generate multiple datasets
      for (let i = 0; i < 5; i++) {
        await dataVizPage.generateSampleData();
        await page.waitForTimeout(200);
      }
      
      await dataVizPage.waitForChartsToLoad();
      
      const memoryAfterData = await page.evaluate(() => {
        const memory = (performance as any).memory;
        return memory ? memory.usedJSHeapSize : 0;
      });
      
      if (memoryAfterData > 0) {
        console.log('Memory after large dataset:', memoryAfterData);
        
        // Should handle large datasets efficiently
        expect(memoryAfterData).toBeLessThan(100 * 1024 * 1024); // Less than 100MB
      }
    });

    test.step('Test memory cleanup after chart destruction', async () => {
      const beforeMemory = await page.evaluate(() => {
        const memory = (performance as any).memory;
        return memory ? memory.usedJSHeapSize : 0;
      });
      
      // Navigate away and back to trigger cleanup
      await page.goto('/');
      await page.waitForTimeout(1000);
      
      // Force garbage collection if available
      await page.evaluate(() => {
        if ((window as any).gc) {
          (window as any).gc();
        }
      });
      
      await dataVizPage.navigateToVisualization();
      await dataVizPage.waitForChartsToLoad();
      
      const afterMemory = await page.evaluate(() => {
        const memory = (performance as any).memory;
        return memory ? memory.usedJSHeapSize : 0;
      });
      
      if (beforeMemory > 0 && afterMemory > 0) {
        console.log('Memory before/after navigation:', { beforeMemory, afterMemory });
        
        // Memory should not continuously grow
        const memoryGrowth = afterMemory - beforeMemory;
        expect(memoryGrowth).toBeLessThan(20 * 1024 * 1024); // Less than 20MB growth
      }
    });
  });

  test('E2E-PERF-VIZ-003: Interaction Performance', async ({ page }) => {
    test.step('Measure hover response time', async () => {
      await dataVizPage.navigateToVisualization();
      await dataVizPage.waitForChartsToLoad();
      
      const dataPoint = page.locator('svg circle.data-point, svg circle').first();
      await expect(dataPoint).toBeVisible();
      
      // Measure hover response time
      const startTime = Date.now();
      await dataPoint.hover();
      
      // Wait for tooltip to appear
      const tooltip = page.locator('.tooltip, [data-testid="tooltip"]');
      await expect(tooltip).toBeVisible({ timeout: 1000 });
      
      const hoverTime = Date.now() - startTime;
      
      // Hover response should be under 100ms
      expect(hoverTime).toBeLessThan(100);
      console.log(`Hover response time: ${hoverTime}ms`);
    });

    test.step('Measure click response time', async () => {
      const dataPoint = page.locator('svg circle.data-point, svg circle').first();
      
      const startTime = Date.now();
      await dataPoint.click();
      await page.waitForTimeout(50); // Wait for any visual feedback
      const clickTime = Date.now() - startTime;
      
      // Click response should be immediate
      expect(clickTime).toBeLessThan(50);
      console.log(`Click response time: ${clickTime}ms`);
    });

    test.step('Measure export performance', async () => {
      await dataVizPage.openExportMenu();
      
      const startTime = Date.now();
      
      // Set up download handler
      const downloadPromise = page.waitForEvent('download');
      await dataVizPage.exportAs('png');
      
      const download = await downloadPromise;
      const exportTime = Date.now() - startTime;
      
      // Export should complete within 3 seconds
      expect(exportTime).toBeLessThan(3000);
      console.log(`Export time: ${exportTime}ms`);
      
      // Verify export file size
      const path = await download.path();
      if (path) {
        const fs = require('fs');
        const stats = fs.statSync(path);
        expect(stats.size).toBeGreaterThan(1000); // At least 1KB
        expect(stats.size).toBeLessThan(5 * 1024 * 1024); // Less than 5MB
      }
    });
  });

  test('E2E-PERF-VIZ-004: Scalability Testing', async ({ page }) => {
    test.step('Test with maximum data points', async () => {
      await dataVizPage.navigateToVisualization();
      
      // Generate maximum amount of sample data
      for (let i = 0; i < 10; i++) {
        await dataVizPage.generateSampleData();
        await page.waitForTimeout(100);
      }
      
      const startTime = Date.now();
      await dataVizPage.waitForChartsToLoad();
      const renderTime = Date.now() - startTime;
      
      // Should handle large datasets within 5 seconds
      expect(renderTime).toBeLessThan(5000);
      console.log(`Large dataset render time: ${renderTime}ms`);
      
      // Verify all data points are rendered
      const dataPointCount = await page.locator('svg circle.data-point, svg circle').count();
      expect(dataPointCount).toBeGreaterThan(100);
    });

    test.step('Test multiple chart instances', async () => {
      await dataVizPage.navigateToVisualization();
      await dataVizPage.waitForChartsToLoad();
      
      // Switch to view that shows multiple charts
      await dataVizPage.switchView('Enhanced');
      await page.waitForTimeout(500);
      
      const svgCount = await page.locator('svg').count();
      console.log(`Number of chart instances: ${svgCount}`);
      
      // Verify performance with multiple charts
      const startTime = Date.now();
      await dataVizPage.selectTimeframe('ALL');
      await dataVizPage.waitForChartUpdate();
      const updateTime = Date.now() - startTime;
      
      // Multiple charts should still update efficiently
      expect(updateTime).toBeLessThan(1000);
      console.log(`Multiple chart update time: ${updateTime}ms`);
    });

    test.step('Test rapid interaction stress', async () => {
      await dataVizPage.waitForChartsToLoad();
      
      const startTime = Date.now();
      
      // Rapidly change timeframes
      const timeframes = ['1M', '3M', '6M', '1Y', '2Y', 'ALL'];
      for (const timeframe of timeframes) {
        await dataVizPage.selectTimeframe(timeframe);
        await page.waitForTimeout(50); // Minimal wait
      }
      
      // Rapidly change chart types
      const chartTypes = ['line', 'area', 'bar'];
      for (const chartType of chartTypes) {
        await dataVizPage.selectChartType(chartType);
        await page.waitForTimeout(50);
      }
      
      await dataVizPage.waitForChartUpdate();
      const stressTime = Date.now() - startTime;
      
      // Should handle rapid interactions gracefully
      expect(stressTime).toBeLessThan(3000);
      console.log(`Stress test completion time: ${stressTime}ms`);
      
      // Verify charts are still functional
      await dataVizPage.verifyChartData();
    });
  });

  test('E2E-PERF-VIZ-005: Performance Monitoring Integration', async ({ page }) => {
    test.step('Enable and verify performance monitor', async () => {
      await dataVizPage.navigateToVisualization();
      await dataVizPage.togglePerformanceMonitor(true);
      
      // Verify performance monitor is active
      const performanceMonitor = page.locator('[data-testid="performance-monitor"]');
      if (await performanceMonitor.isVisible()) {
        await expect(performanceMonitor).toBeVisible();
        
        // Verify metrics are being collected
        const metrics = await dataVizPage.getPerformanceMetrics();
        if (metrics) {
          expect(metrics.renderTime).toBeGreaterThan(0);
          console.log('Performance monitor metrics:', metrics);
        }
      }
    });

    test.step('Test performance alert system', async () => {
      await dataVizPage.togglePerformanceMonitor(true);
      
      // Generate heavy load to trigger alerts
      for (let i = 0; i < 15; i++) {
        await dataVizPage.generateSampleData();
        await page.waitForTimeout(50);
      }
      
      await dataVizPage.waitForChartsToLoad();
      
      // Check for performance alerts
      const hasAlerts = await dataVizPage.hasPerformanceAlerts();
      if (hasAlerts) {
        console.log('Performance alerts detected');
        
        const alertElement = page.locator('[data-testid="performance-alerts"]');
        await expect(alertElement).toBeVisible();
      }
    });

    test.step('Verify performance thresholds', async () => {
      await dataVizPage.togglePerformanceMonitor(true);
      await dataVizPage.waitForChartsToLoad();
      
      // Trigger various interactions to collect metrics
      await dataVizPage.selectChartType('area');
      await page.waitForTimeout(500);
      
      await dataVizPage.selectTimeframe('1Y');
      await page.waitForTimeout(500);
      
      const metrics = await dataVizPage.getPerformanceMetrics();
      if (metrics) {
        // Verify metrics meet performance thresholds
        expect(metrics.renderTime).toBeLessThan(500); // 500ms threshold
        expect(metrics.frameRate).toBeGreaterThan(30); // 30fps threshold
        
        if (metrics.memoryUsage) {
          expect(metrics.memoryUsage).toBeLessThan(50); // 50MB threshold
        }
        
        console.log('Performance threshold verification:', metrics);
      }
    });
  });

  test('E2E-PERF-VIZ-006: Browser-Specific Performance', async ({ page, browserName }) => {
    test.step(`Test ${browserName} specific optimizations`, async () => {
      await dataVizPage.navigateToVisualization();
      await dataVizPage.waitForChartsToLoad();
      
      // Browser-specific performance characteristics
      const browserThresholds = {
        chromium: { loadTime: 1500, updateTime: 400 },
        firefox: { loadTime: 2000, updateTime: 500 },
        webkit: { loadTime: 2500, updateTime: 600 }
      };
      
      const thresholds = browserThresholds[browserName as keyof typeof browserThresholds] || 
                        browserThresholds.chromium;
      
      // Test load performance
      const startTime = Date.now();
      await dataVizPage.generateSampleData();
      await dataVizPage.waitForChartsToLoad();
      const loadTime = Date.now() - startTime;
      
      expect(loadTime).toBeLessThan(thresholds.loadTime);
      console.log(`${browserName} load time: ${loadTime}ms`);
      
      // Test update performance
      const updateStart = Date.now();
      await dataVizPage.selectTimeframe('6M');
      await dataVizPage.waitForChartUpdate();
      const updateTime = Date.now() - updateStart;
      
      expect(updateTime).toBeLessThan(thresholds.updateTime);
      console.log(`${browserName} update time: ${updateTime}ms`);
    });
  });
});
