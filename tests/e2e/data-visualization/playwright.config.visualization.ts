/**
 * Playwright Configuration for Data Visualization Tests
 * Specialized configuration for comprehensive chart testing
 */

import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './tests/e2e/data-visualization',
  
  /* Run tests in files in parallel */
  fullyParallel: true,
  
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: !!process.env.CI,
  
  /* Retry on CI only */
  retries: process.env.CI ? 2 : 0,
  
  /* Opt out of parallel tests on CI. */
  workers: process.env.CI ? 1 : undefined,
  
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  reporter: [
    ['html', { outputFolder: 'test-results/visualization-report' }],
    ['json', { outputFile: 'test-results/visualization-results.json' }],
    ['junit', { outputFile: 'test-results/visualization-junit.xml' }],
    ['list'],
  ],
  
  /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
  use: {
    /* Base URL to use in actions like `await page.goto('/')`. */
    baseURL: 'http://localhost:3000',
    
    /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
    trace: 'on-first-retry',
    
    /* Take screenshot on failure */
    screenshot: 'only-on-failure',
    
    /* Record video on failure */
    video: 'retain-on-failure',
    
    /* Timeout for each action */
    actionTimeout: 10000,
    
    /* Timeout for navigation */
    navigationTimeout: 30000,
  },

  /* Configure projects for major browsers */
  projects: [
    // Desktop browsers
    {
      name: 'chromium-desktop',
      use: { 
        ...devices['Desktop Chrome'],
        viewport: { width: 1280, height: 720 },
      },
      testMatch: [
        'chart-interactions.spec.ts',
        'chart-performance.spec.ts',
        'chart-accessibility.spec.ts',
        'test-suite-runner.spec.ts',
      ],
    },
    
    {
      name: 'firefox-desktop',
      use: { 
        ...devices['Desktop Firefox'],
        viewport: { width: 1280, height: 720 },
      },
      testMatch: [
        'chart-interactions.spec.ts',
        'chart-performance.spec.ts',
        'test-suite-runner.spec.ts',
      ],
    },
    
    {
      name: 'webkit-desktop',
      use: { 
        ...devices['Desktop Safari'],
        viewport: { width: 1280, height: 720 },
      },
      testMatch: [
        'chart-interactions.spec.ts',
        'test-suite-runner.spec.ts',
      ],
    },

    // Mobile devices
    {
      name: 'mobile-chrome',
      use: { 
        ...devices['Pixel 5'],
      },
      testMatch: [
        'mobile-chart-interactions.spec.ts',
        'chart-accessibility.spec.ts',
      ],
    },
    
    {
      name: 'mobile-safari',
      use: { 
        ...devices['iPhone 12'],
      },
      testMatch: [
        'mobile-chart-interactions.spec.ts',
      ],
    },

    // Tablet devices
    {
      name: 'tablet-chrome',
      use: { 
        ...devices['iPad Pro'],
      },
      testMatch: [
        'chart-interactions.spec.ts',
        'mobile-chart-interactions.spec.ts',
      ],
    },

    // High DPI displays
    {
      name: 'high-dpi',
      use: {
        ...devices['Desktop Chrome'],
        viewport: { width: 1920, height: 1080 },
        deviceScaleFactor: 2,
      },
      testMatch: [
        'chart-interactions.spec.ts',
        'chart-performance.spec.ts',
      ],
    },

    // Accessibility testing
    {
      name: 'accessibility-chromium',
      use: {
        ...devices['Desktop Chrome'],
        // Enable accessibility features
        extraHTTPHeaders: {
          'Accept-Language': 'en-US,en;q=0.9',
        },
      },
      testMatch: [
        'chart-accessibility.spec.ts',
      ],
    },

    // Performance testing
    {
      name: 'performance-chromium',
      use: {
        ...devices['Desktop Chrome'],
        // Enable performance monitoring
        launchOptions: {
          args: [
            '--enable-precise-memory-info',
            '--enable-gpu-benchmarking',
            '--enable-threaded-compositing',
          ],
        },
      },
      testMatch: [
        'chart-performance.spec.ts',
      ],
    },

    // Dark mode testing
    {
      name: 'dark-mode',
      use: {
        ...devices['Desktop Chrome'],
        colorScheme: 'dark',
      },
      testMatch: [
        'chart-interactions.spec.ts',
        'chart-accessibility.spec.ts',
      ],
    },

    // Reduced motion testing
    {
      name: 'reduced-motion',
      use: {
        ...devices['Desktop Chrome'],
        reducedMotion: 'reduce',
      },
      testMatch: [
        'chart-accessibility.spec.ts',
      ],
    },

    // Slow network simulation
    {
      name: 'slow-network',
      use: {
        ...devices['Desktop Chrome'],
        // Simulate slow 3G
        launchOptions: {
          args: ['--force-effective-connection-type=slow-2g'],
        },
      },
      testMatch: [
        'chart-performance.spec.ts',
      ],
    },
  ],

  /* Global test timeout */
  timeout: 60000,

  /* Expect timeout */
  expect: {
    timeout: 10000,
  },

  /* Run your local dev server before starting the tests */
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
    timeout: 120000,
  },

  /* Global setup and teardown */
  globalSetup: require.resolve('./global-setup.ts'),
  globalTeardown: require.resolve('./global-teardown.ts'),
});

// Test categories configuration
export const testCategories = {
  smoke: [
    'chart-interactions.spec.ts:E2E-VIZ-001',
    'mobile-chart-interactions.spec.ts:E2E-MOBILE-VIZ-001',
  ],
  
  regression: [
    'chart-interactions.spec.ts',
    'chart-performance.spec.ts',
    'chart-accessibility.spec.ts',
  ],
  
  performance: [
    'chart-performance.spec.ts',
    'mobile-chart-interactions.spec.ts:E2E-MOBILE-VIZ-004',
  ],
  
  accessibility: [
    'chart-accessibility.spec.ts',
  ],
  
  mobile: [
    'mobile-chart-interactions.spec.ts',
  ],
  
  comprehensive: [
    'test-suite-runner.spec.ts',
  ],
};

// Performance thresholds
export const performanceThresholds = {
  chartLoadTime: 2000, // ms
  chartUpdateTime: 500, // ms
  exportTime: 3000, // ms
  memoryUsage: 100 * 1024 * 1024, // 100MB
  frameRate: 30, // fps
};

// Accessibility standards
export const accessibilityStandards = {
  wcagLevel: 'AA',
  colorContrastRatio: 4.5,
  focusIndicatorMinSize: 2, // px
  touchTargetMinSize: 44, // px
};

// Browser compatibility matrix
export const browserSupport = {
  chrome: {
    minVersion: 90,
    features: ['webgl', 'svg', 'canvas', 'touch'],
  },
  firefox: {
    minVersion: 88,
    features: ['svg', 'canvas', 'touch'],
  },
  safari: {
    minVersion: 14,
    features: ['svg', 'canvas', 'touch'],
  },
  edge: {
    minVersion: 90,
    features: ['webgl', 'svg', 'canvas', 'touch'],
  },
};

// Test data profiles
export const testDataProfiles = {
  minimal: {
    monthlyIncome: 3000,
    monthlyExpenses: 2500,
    currentSavings: 10000,
  },
  typical: {
    monthlyIncome: 8000,
    monthlyExpenses: 5000,
    currentSavings: 100000,
  },
  high: {
    monthlyIncome: 20000,
    monthlyExpenses: 12000,
    currentSavings: 500000,
  },
  extreme: {
    monthlyIncome: 50000,
    monthlyExpenses: 30000,
    currentSavings: 2000000,
  },
};
