import { describe, expect, it } from 'vitest';
import {
  calculateMonthsToCompletion,
  calculateProgress,
  calculateProjectedCompletionDate,
  getGoalTypeInfo,
  SWISS_SAVINGS_GOAL_TYPES,
  type SavingsGoal,
  type SavingsGoalPriority,
  type SavingsGoalStatus,
  type SavingsGoalType,
} from '../../../src/types/savings';

describe('Savings Types and Utilities', () => {
  describe('calculateProgress', () => {
    it('should calculate progress percentage correctly', () => {
      const goal: SavingsGoal = {
        id: '1',
        name: 'Emergency Fund',
        type: 'emergency-fund',
        targetAmount: 10000,
        currentAmount: 2500,
        priority: 'critical',
        status: 'active',
        targetDate: '2024-12-31',
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      };

      expect(calculateProgress(goal)).toBe(25); // 2500 / 10000 * 100
    });

    it('should return 0 for goals with no current amount', () => {
      const goal: SavingsGoal = {
        id: '1',
        name: 'New Goal',
        type: 'vacation',
        targetAmount: 5000,
        currentAmount: 0,
        priority: 'medium',
        status: 'active',
        targetDate: '2024-12-31',
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      };

      expect(calculateProgress(goal)).toBe(0);
    });

    it('should cap progress at 100%', () => {
      const goal: SavingsGoal = {
        id: '1',
        name: 'Overfunded Goal',
        type: 'other',
        targetAmount: 1000,
        currentAmount: 1500,
        priority: 'low',
        status: 'completed',
        targetDate: '2024-12-31',
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      };

      expect(calculateProgress(goal)).toBe(100);
    });

    it('should handle zero target amount', () => {
      const goal: SavingsGoal = {
        id: '1',
        name: 'Invalid Goal',
        type: 'other',
        targetAmount: 0,
        currentAmount: 100,
        priority: 'low',
        status: 'active',
        targetDate: '2024-12-31',
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      };

      expect(calculateProgress(goal)).toBe(0);
    });
  });

  describe('calculateMonthsToCompletion', () => {
    it('should calculate months to completion correctly', () => {
      const goal: SavingsGoal = {
        id: '1',
        name: 'Test Goal',
        type: 'house_deposit',
        targetAmount: 12000,
        currentAmount: 0,
        monthlyContribution: 1000,
        priority: 'high',
        status: 'active',
        autoContribute: true,
        swissSpecific: true,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      };

      const months = calculateMonthsToCompletion(goal);
      expect(months).toBe(12); // 12000 / 1000 = 12 months
    });

    it('should account for current amount in calculation', () => {
      const goal: SavingsGoal = {
        id: '1',
        name: 'Partial Goal',
        type: 'vacation',
        targetAmount: 5000,
        currentAmount: 1000,
        monthlyContribution: 400,
        priority: 'medium',
        status: 'active',
        autoContribute: true,
        swissSpecific: false,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      };

      const months = calculateMonthsToCompletion(goal);
      expect(months).toBe(10); // (5000 - 1000) / 400 = 10 months
    });

    it('should return 0 for completed goals', () => {
      const goal: SavingsGoal = {
        id: '1',
        name: 'Completed Goal',
        type: 'emergency_fund',
        targetAmount: 5000,
        currentAmount: 5000,
        monthlyContribution: 500,
        priority: 'critical',
        status: 'completed',
        autoContribute: true,
        swissSpecific: false,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      };

      expect(calculateMonthsToCompletion(goal)).toBe(0);
    });

    it('should return null for zero monthly contribution', () => {
      const goal: SavingsGoal = {
        id: '1',
        name: 'No Contribution Goal',
        type: 'vacation',
        targetAmount: 5000,
        currentAmount: 2000,
        monthlyContribution: 0,
        priority: 'medium',
        status: 'active',
        autoContribute: false,
        swissSpecific: false,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      };

      expect(calculateMonthsToCompletion(goal)).toBe(null);
    });
  });

  describe('calculateProjectedCompletionDate', () => {
    it('should calculate projected completion date', () => {
      const goal: SavingsGoal = {
        id: '1',
        name: 'Time Goal',
        type: 'house_deposit',
        targetAmount: 10000,
        currentAmount: 4000,
        monthlyContribution: 1000,
        priority: 'high',
        status: 'active',
        autoContribute: true,
        swissSpecific: true,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      };

      const completionDate = calculateProjectedCompletionDate(goal);
      expect(completionDate).toBeInstanceOf(Date);
    });

    it('should handle completed goals', () => {
      const goal: SavingsGoal = {
        id: '1',
        name: 'Completed Goal',
        type: 'emergency_fund',
        targetAmount: 5000,
        currentAmount: 5000,
        monthlyContribution: 500,
        priority: 'critical',
        status: 'completed',
        autoContribute: true,
        swissSpecific: false,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      };

      const completionDate = calculateProjectedCompletionDate(goal);
      expect(completionDate).toBeInstanceOf(Date);
    });

    it('should handle zero monthly contribution', () => {
      const goal: SavingsGoal = {
        id: '1',
        name: 'No Contribution Goal',
        type: 'vacation',
        targetAmount: 5000,
        currentAmount: 1000,
        monthlyContribution: 0,
        priority: 'medium',
        status: 'active',
        autoContribute: false,
        swissSpecific: false,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      };

      const completionDate = calculateProjectedCompletionDate(goal);
      // Should return null for zero contribution
      expect(completionDate).toBe(null);
    });

    it('should handle fractional months correctly', () => {
      const goal: SavingsGoal = {
        id: '1',
        name: 'Fractional Goal',
        type: 'other',
        targetAmount: 2500,
        currentAmount: 1000,
        monthlyContribution: 600,
        priority: 'medium',
        status: 'active',
        autoContribute: true,
        swissSpecific: false,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      };

      const months = calculateMonthsToCompletion(goal);
      expect(months).toBe(3); // Math.ceil((2500 - 1000) / 600) = 3
    });
  });

  describe('getGoalTypeInfo', () => {
    it('should return correct info for emergency fund', () => {
      const info = getGoalTypeInfo('emergency_fund');

      expect(info.id).toBe('emergency_fund');
      expect(info.name).toBe('Emergency Fund');
      expect(info.icon).toBe('🛡️');
      expect(info.swissSpecific).toBe(false);
      expect(info.suggestedAmount).toBeGreaterThan(0);
    });

    it('should return correct info for Pillar 3a', () => {
      const info = getGoalTypeInfo('pillar_3a');

      expect(info.id).toBe('pillar_3a');
      expect(info.name).toBe('Pillar 3a');
      expect(info.icon).toBe('🏛️');
      expect(info.swissSpecific).toBe(true);
      expect(info.maxAnnualContribution).toBe(7056); // 2024 limit
    });

    it('should return correct info for house deposit', () => {
      const info = getGoalTypeInfo('house_deposit');

      expect(info.id).toBe('house_deposit');
      expect(info.name).toBe('House Deposit');
      expect(info.icon).toBe('🏠');
      expect(info.swissSpecific).toBe(true);
    });

    it('should handle unknown goal types gracefully', () => {
      const info = getGoalTypeInfo('unknown' as SavingsGoalType);

      expect(info.id).toBe('other');
      expect(info.name).toBe('Other Goal');
      expect(info.icon).toBe('🎯');
    });
  });

  describe('SWISS_SAVINGS_GOAL_TYPES', () => {
    it('should contain all expected goal types', () => {
      const typeIds = SWISS_SAVINGS_GOAL_TYPES.map(type => type.id);

      expect(typeIds).toContain('emergency_fund');
      expect(typeIds).toContain('pillar_3a');
      expect(typeIds).toContain('house_deposit');
      expect(typeIds).toContain('vacation');
      expect(typeIds).toContain('car');
      expect(typeIds).toContain('education');
      expect(typeIds).toContain('wedding');
      expect(typeIds).toContain('retirement');
      expect(typeIds).toContain('investment_portfolio');
      expect(typeIds).toContain('debt_payoff');
      expect(typeIds).toContain('business');
      expect(typeIds).toContain('other');
    });

    it('should have 12 goal types total', () => {
      expect(SWISS_SAVINGS_GOAL_TYPES).toHaveLength(12);
    });

    it('should have Swiss-specific types marked correctly', () => {
      const swissSpecific = SWISS_SAVINGS_GOAL_TYPES.filter(
        type => type.swissSpecific,
      );
      const nonSwissSpecific = SWISS_SAVINGS_GOAL_TYPES.filter(
        type => !type.swissSpecific,
      );

      expect(swissSpecific.length).toBeGreaterThan(0);
      expect(nonSwissSpecific.length).toBeGreaterThan(0);
    });

    it('should have all types with required properties', () => {
      SWISS_SAVINGS_GOAL_TYPES.forEach(type => {
        expect(type).toHaveProperty('id');
        expect(type).toHaveProperty('name');
        expect(type).toHaveProperty('icon');
        expect(type).toHaveProperty('swissSpecific');
        expect(type).toHaveProperty('description');

        expect(typeof type.id).toBe('string');
        expect(typeof type.name).toBe('string');
        expect(typeof type.icon).toBe('string');
        expect(typeof type.swissSpecific).toBe('boolean');
        expect(typeof type.description).toBe('string');
      });
    });

    it('should have unique goal type IDs', () => {
      const ids = SWISS_SAVINGS_GOAL_TYPES.map(type => type.id);
      const uniqueIds = [...new Set(ids)];

      expect(ids).toHaveLength(uniqueIds.length);
    });

    it('should have meaningful descriptions', () => {
      SWISS_SAVINGS_GOAL_TYPES.forEach(type => {
        expect(type.description.length).toBeGreaterThan(10);
      });
    });
  });

  describe('Type Safety', () => {
    it('should enforce correct savings goal status types', () => {
      const validStatuses: SavingsGoalStatus[] = [
        'active',
        'completed',
        'paused',
        'cancelled',
      ];

      validStatuses.forEach(status => {
        const goal: SavingsGoal = {
          id: '1',
          name: 'Test Goal',
          type: 'emergency-fund',
          targetAmount: 5000,
          currentAmount: 1000,
          priority: 'medium',
          status,
          targetDate: '2024-12-31',
          createdAt: '2024-01-01',
          updatedAt: '2024-01-01',
        };

        expect(goal.status).toBe(status);
      });
    });

    it('should enforce correct savings goal priority types', () => {
      const validPriorities: SavingsGoalPriority[] = [
        'critical',
        'high',
        'medium',
        'low',
      ];

      validPriorities.forEach(priority => {
        const goal: SavingsGoal = {
          id: '1',
          name: 'Test Goal',
          type: 'emergency-fund',
          targetAmount: 5000,
          currentAmount: 1000,
          priority,
          status: 'active',
          targetDate: '2024-12-31',
          createdAt: '2024-01-01',
          updatedAt: '2024-01-01',
        };

        expect(goal.priority).toBe(priority);
      });
    });

    it('should enforce correct savings goal type types', () => {
      const validTypes: SavingsGoalType[] = [
        'emergency_fund',
        'pillar_3a',
        'investment_portfolio',
        'house_deposit',
        'retirement',
        'vacation',
        'education',
        'car',
        'wedding',
        'debt_payoff',
        'business',
        'other',
      ];

      validTypes.forEach(type => {
        const goal: SavingsGoal = {
          id: '1',
          name: 'Test Goal',
          type,
          targetAmount: 5000,
          currentAmount: 1000,
          priority: 'medium',
          status: 'active',
          targetDate: '2024-12-31',
          createdAt: '2024-01-01',
          updatedAt: '2024-01-01',
        };

        expect(goal.type).toBe(type);
      });
    });
  });
});
