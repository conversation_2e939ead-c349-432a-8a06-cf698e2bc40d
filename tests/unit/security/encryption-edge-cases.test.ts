/**
 * Swiss Budget Pro - Encryption Edge Cases & Behavioral Tests
 *
 * Comprehensive edge case testing for encryption system including
 * error conditions, boundary cases, and concurrent operations.
 */

import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
  EncryptedData,
  encryption,
  SecurityUtils,
} from '../../../src/security/encryption';

// Mock Web Crypto API for testing
const mockCrypto = {
  getRandomValues: vi.fn((array: Uint8Array) => {
    for (let i = 0; i < array.length; i++) {
      array[i] = Math.floor(Math.random() * 256);
    }
    return array;
  }),
  subtle: {
    importKey: vi.fn(),
    deriveKey: vi.fn(),
    encrypt: vi.fn(),
    decrypt: vi.fn(),
    sign: vi.fn(),
  },
};

// Setup global crypto mock
Object.defineProperty(global, 'crypto', {
  value: mockCrypto,
  writable: true,
});

describe('Encryption Edge Cases', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Error Handling', () => {
    it('should handle Web Crypto API failures gracefully', async () => {
      mockCrypto.subtle.importKey.mockRejectedValue(
        new Error('Crypto API unavailable'),
      );

      const passphrase = 'test-passphrase';

      await expect(encryption.deriveKey(passphrase)).rejects.toThrow(
        'Crypto API unavailable',
      );
    });

    it('should handle encryption failures', async () => {
      const mockKey = {
        key: { type: 'secret' } as CryptoKey,
        salt: new Uint8Array(32),
      };

      mockCrypto.subtle.encrypt.mockRejectedValue(
        new Error('Encryption failed'),
      );

      await expect(encryption.encrypt('test data', mockKey)).rejects.toThrow(
        'Encryption failed',
      );
    });

    it('should handle decryption failures', async () => {
      const mockKey = { type: 'secret' } as CryptoKey;
      mockCrypto.subtle.importKey.mockResolvedValue({});
      mockCrypto.subtle.deriveKey.mockResolvedValue(mockKey);
      mockCrypto.subtle.decrypt.mockRejectedValue(
        new Error('Decryption failed'),
      );

      const mockEncryptedData: EncryptedData = {
        data: 'dGVzdA==',
        iv: 'aXY=',
        salt: 'c2FsdA==',
        tag: 'dGFn',
        version: '1.0',
      };

      await expect(
        encryption.decrypt(mockEncryptedData, 'wrong-passphrase'),
      ).rejects.toThrow('Decryption failed');
    });

    it('should handle corrupted encrypted data', async () => {
      const mockKey = { type: 'secret' } as CryptoKey;
      mockCrypto.subtle.importKey.mockResolvedValue({});
      mockCrypto.subtle.deriveKey.mockResolvedValue(mockKey);

      const corruptedData: EncryptedData = {
        data: 'corrupted-base64-data!!!',
        iv: 'invalid-iv',
        salt: 'invalid-salt',
        tag: 'invalid-tag',
        version: '1.0',
      };

      await expect(
        encryption.decrypt(corruptedData, 'passphrase'),
      ).rejects.toThrow();
    });

    it('should handle missing Web Crypto API', () => {
      // Temporarily remove crypto
      const originalCrypto = global.crypto;
      delete (global as any).crypto;

      expect(SecurityUtils.isWebCryptoAvailable()).toBe(false);

      // Restore crypto
      global.crypto = originalCrypto;
    });
  });

  describe('Boundary Cases', () => {
    it('should handle empty data encryption', async () => {
      const mockEncryptedBuffer = new ArrayBuffer(0);
      mockCrypto.subtle.encrypt.mockResolvedValue(mockEncryptedBuffer);

      const mockKey = {
        key: { type: 'secret' } as CryptoKey,
        salt: new Uint8Array(32),
      };

      const result = await encryption.encrypt('', mockKey);
      expect(result).toHaveProperty('data');
      expect(result).toHaveProperty('iv');
    });

    it('should handle large data encryption', async () => {
      const largeData = 'x'.repeat(10000); // 10KB of data (reduced from 1MB)
      const mockEncryptedBuffer = new ArrayBuffer(10000);
      mockCrypto.subtle.encrypt.mockResolvedValue(mockEncryptedBuffer);

      const mockKey = {
        key: { type: 'secret' } as CryptoKey,
        salt: new Uint8Array(32),
      };

      const result = await encryption.encrypt(largeData, mockKey);
      expect(result).toHaveProperty('data');
      expect(result).toHaveProperty('iv');
    });

    it('should handle minimum passphrase length', async () => {
      const mockKey = { type: 'secret' } as CryptoKey;
      mockCrypto.subtle.importKey.mockResolvedValue({});
      mockCrypto.subtle.deriveKey.mockResolvedValue(mockKey);

      const shortPassphrase = 'a'; // Very short passphrase
      const result = await encryption.deriveKey(shortPassphrase);

      expect(result).toHaveProperty('key');
      expect(result).toHaveProperty('salt');
    });

    it('should handle maximum passphrase length', async () => {
      const mockKey = { type: 'secret' } as CryptoKey;
      mockCrypto.subtle.importKey.mockResolvedValue({});
      mockCrypto.subtle.deriveKey.mockResolvedValue(mockKey);

      const longPassphrase = 'a'.repeat(10000); // Very long passphrase
      const result = await encryption.deriveKey(longPassphrase);

      expect(result).toHaveProperty('key');
      expect(result).toHaveProperty('salt');
    });

    it('should handle special characters in data', async () => {
      const specialData = '🔐💰🇨🇭 Special chars: àáâãäåæçèéêë ñòóôõö ùúûüý';
      const mockEncryptedBuffer = new ArrayBuffer(64);
      mockCrypto.subtle.encrypt.mockResolvedValue(mockEncryptedBuffer);

      const mockKey = {
        key: { type: 'secret' } as CryptoKey,
        salt: new Uint8Array(32),
      };

      const result = await encryption.encrypt(specialData, mockKey);
      expect(result).toHaveProperty('data');
    });
  });

  describe('Concurrent Operations', () => {
    it('should handle multiple simultaneous encryptions', async () => {
      const mockEncryptedBuffer = new ArrayBuffer(32);
      mockCrypto.subtle.encrypt.mockResolvedValue(mockEncryptedBuffer);

      const mockKey = {
        key: { type: 'secret' } as CryptoKey,
        salt: new Uint8Array(32),
      };

      const promises = Array.from({ length: 10 }, (_, i) =>
        encryption.encrypt(`test data ${i}`, mockKey),
      );

      const results = await Promise.all(promises);

      expect(results).toHaveLength(10);
      results.forEach((result, i) => {
        expect(result).toHaveProperty('data');
        expect(result).toHaveProperty('iv');
        // Each encryption should have unique IV
        if (i > 0) {
          expect(result.iv).not.toBe(results[i - 1].iv);
        }
      });
    });

    it('should handle multiple simultaneous key derivations', async () => {
      const mockKey = { type: 'secret' } as CryptoKey;
      mockCrypto.subtle.importKey.mockResolvedValue({});
      mockCrypto.subtle.deriveKey.mockResolvedValue(mockKey);

      const promises = Array.from({ length: 5 }, (_, i) =>
        encryption.deriveKey(`passphrase-${i}`),
      );

      const results = await Promise.all(promises);

      expect(results).toHaveLength(5);
      results.forEach((result, i) => {
        expect(result).toHaveProperty('key');
        expect(result).toHaveProperty('salt');
        // Each derivation should have unique salt
        if (i > 0) {
          expect(result.salt).not.toEqual(results[i - 1].salt);
        }
      });
    });
  });

  describe('Memory Management', () => {
    it('should not leak sensitive data in memory', async () => {
      const sensitiveData = 'very-secret-financial-data';
      const mockEncryptedBuffer = new ArrayBuffer(32);
      mockCrypto.subtle.encrypt.mockResolvedValue(mockEncryptedBuffer);

      const mockKey = {
        key: { type: 'secret' } as CryptoKey,
        salt: new Uint8Array(32),
      };

      await encryption.encrypt(sensitiveData, mockKey);

      // Verify that the original data is not stored in the result
      // This is a basic check - in real scenarios, you'd use memory analysis tools
      expect(mockCrypto.subtle.encrypt).toHaveBeenCalled();
    });

    it('should handle garbage collection during operations', async () => {
      const mockKey = { type: 'secret' } as CryptoKey;
      mockCrypto.subtle.importKey.mockResolvedValue({});
      mockCrypto.subtle.deriveKey.mockResolvedValue(mockKey);

      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }

      const result = await encryption.deriveKey('test-passphrase');
      expect(result).toHaveProperty('key');
    });
  });

  describe('Version Compatibility', () => {
    it('should handle unsupported encryption versions', async () => {
      const mockKey = { type: 'secret' } as CryptoKey;
      mockCrypto.subtle.importKey.mockResolvedValue({});
      mockCrypto.subtle.deriveKey.mockResolvedValue(mockKey);

      const futureVersionData: EncryptedData = {
        data: 'dGVzdA==',
        iv: 'aXY=',
        salt: 'c2FsdA==',
        tag: 'dGFn',
        version: '2.0', // Future version
      };

      await expect(
        encryption.decrypt(futureVersionData, 'passphrase'),
      ).rejects.toThrow();
    });

    it('should handle legacy encryption versions', async () => {
      const mockKey = { type: 'secret' } as CryptoKey;
      const mockDecryptedBuffer = new TextEncoder().encode('test data');

      mockCrypto.subtle.importKey.mockResolvedValue({});
      mockCrypto.subtle.deriveKey.mockResolvedValue(mockKey);
      mockCrypto.subtle.decrypt.mockResolvedValue(mockDecryptedBuffer.buffer);

      const legacyData: EncryptedData = {
        data: 'dGVzdA==',
        iv: 'aXY=',
        salt: 'c2FsdA==',
        tag: 'dGFn',
        version: '0.9', // Legacy version
      };

      // Should still work with legacy versions
      const result = await encryption.decrypt(legacyData, 'passphrase');
      expect(result).toBe('test data');
    });
  });

  describe('Performance Edge Cases', () => {
    it('should handle encryption timeout scenarios', async () => {
      // Mock a slow encryption operation (but not too slow for tests)
      mockCrypto.subtle.encrypt.mockImplementation(
        () =>
          new Promise(resolve =>
            setTimeout(() => resolve(new ArrayBuffer(32)), 100),
          ),
      );

      const mockKey = {
        key: { type: 'secret' } as CryptoKey,
        salt: new Uint8Array(32),
      };

      // This test verifies the operation completes
      const startTime = Date.now();
      await encryption.encrypt('test data', mockKey);
      const endTime = Date.now();

      // Should complete within reasonable time
      expect(endTime - startTime).toBeGreaterThan(90);
      expect(endTime - startTime).toBeLessThan(1000);
    });

    it('should handle high-frequency encryption requests', async () => {
      const mockEncryptedBuffer = new ArrayBuffer(32);
      mockCrypto.subtle.encrypt.mockResolvedValue(mockEncryptedBuffer);

      const mockKey = {
        key: { type: 'secret' } as CryptoKey,
        salt: new Uint8Array(32),
      };

      // Simulate rapid-fire encryption requests
      const startTime = Date.now();
      const promises = Array.from({ length: 100 }, (_, i) =>
        encryption.encrypt(`data-${i}`, mockKey),
      );

      await Promise.all(promises);
      const endTime = Date.now();

      // Should handle high frequency without significant delay
      expect(endTime - startTime).toBeLessThan(1000); // Less than 1 second for 100 operations
    });
  });
});

describe('SecurityUtils Edge Cases', () => {
  describe('Passphrase Generation Edge Cases', () => {
    it('should handle zero length passphrase request', () => {
      expect(() => {
        SecurityUtils.generateSecurePassphrase(0);
      }).toThrow('Passphrase length must be positive');
    });

    it('should handle negative length passphrase request', () => {
      expect(() => {
        SecurityUtils.generateSecurePassphrase(-5);
      }).toThrow('Passphrase length must be positive');
    });

    it('should handle extremely large length request', () => {
      expect(() => {
        SecurityUtils.generateSecurePassphrase(10000);
      }).toThrow('Passphrase length too large');
    });

    it('should generate cryptographically secure random values', () => {
      const passphrases = Array.from({ length: 100 }, () =>
        SecurityUtils.generateSecurePassphrase(32),
      );

      // All passphrases should be unique
      const uniquePassphrases = new Set(passphrases);
      expect(uniquePassphrases.size).toBe(100);
    });
  });

  describe('Passphrase Strength Edge Cases', () => {
    it('should handle null/undefined passphrase', () => {
      const result1 = SecurityUtils.estimatePassphraseStrength(null as any);
      const result2 = SecurityUtils.estimatePassphraseStrength(
        undefined as any,
      );

      expect(result1.score).toBe(0);
      expect(result1.feedback).toContain('Passphrase is required');
      expect(result2.score).toBe(0);
      expect(result2.feedback).toContain('Passphrase is required');
    });

    it('should handle non-string input', () => {
      const result1 = SecurityUtils.estimatePassphraseStrength(123 as any);
      const result2 = SecurityUtils.estimatePassphraseStrength({} as any);
      const result3 = SecurityUtils.estimatePassphraseStrength([] as any);

      // These should be converted to strings and evaluated
      expect(result1.score).toBeGreaterThanOrEqual(0);
      expect(result2.score).toBeGreaterThanOrEqual(0);
      expect(result3.score).toBeGreaterThanOrEqual(0);
    });

    it('should handle extremely long passphrases', () => {
      const longPassphrase = 'a'.repeat(10000);
      const result = SecurityUtils.estimatePassphraseStrength(longPassphrase);

      expect(result.score).toBeGreaterThan(0);
      expect(result.feedback).toBeDefined();
    });

    it('should handle passphrases with only whitespace', () => {
      const whitespacePassphrase = '   \t\n   ';
      const result =
        SecurityUtils.estimatePassphraseStrength(whitespacePassphrase);

      expect(result.score).toBe(0);
      expect(result.feedback).toContain('Passphrase is too short');
    });

    it('should handle passphrases with unicode characters', () => {
      const unicodePassphrase = '🔐💰🇨🇭àáâãäåæçèéêëñòóôõöùúûüý';
      const result =
        SecurityUtils.estimatePassphraseStrength(unicodePassphrase);

      expect(result.score).toBeGreaterThan(0);
      expect(result.feedback).toBeDefined();
    });
  });
});
