import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import HistoricalTrackingCharts from '../../../src/components/HistoricalTrackingCharts';

// Mock the useTranslation hook
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string, defaultValue?: string) => defaultValue || key,
  }),
}));

// Mock the useLocalStorage hook
jest.mock('../../../src/hooks/useLocalStorage', () => ({
  useLocalStorage: jest.fn((key: string, defaultValue: any) => [defaultValue, jest.fn()]),
}));

const mockUserData = {
  currentAge: 35,
  retirementAge: 65,
  currentSavings: 150000,
  monthlyIncome: 9000,
  monthlyExpenses: 5500,
  expectedReturn: 7,
  inflationRate: 2.5,
  safeWithdrawalRate: 4,
};

const mockExpenses = [
  { id: '1', name: 'Housing', amount: 2500, category: 'housing' },
  { id: '2', name: 'Food', amount: 800, category: 'food' },
];

const mockInvestments = [
  { id: '1', name: 'Swiss ETF', currentValue: 80000, type: 'etf' },
  { id: '2', name: 'Bonds', currentValue: 40000, type: 'bonds' },
];

const mockSavingsGoals = [
  { id: '1', name: 'Emergency Fund', currentAmount: 20000, targetAmount: 30000 },
  { id: '2', name: 'Pillar 3a', currentAmount: 15000, targetAmount: 7056 },
];

describe('HistoricalTrackingCharts', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the Historical Tracking Charts component', () => {
    render(
      <HistoricalTrackingCharts
        darkMode={false}
        userData={mockUserData}
        expenses={mockExpenses}
        investments={mockInvestments}
        savingsGoals={mockSavingsGoals}
      />,
    );

    expect(screen.getByText('📈 Historical Tracking & Analytics')).toBeInTheDocument();
    expect(screen.getByText(/Track your financial progress over time/)).toBeInTheDocument();
  });

  it('displays timeframe selection controls', () => {
    render(
      <HistoricalTrackingCharts
        darkMode={false}
        userData={mockUserData}
        expenses={mockExpenses}
        investments={mockInvestments}
        savingsGoals={mockSavingsGoals}
      />,
    );

    expect(screen.getByText('Time Period')).toBeInTheDocument();

    // Check for timeframe buttons
    const timeframes = ['1M', '3M', '6M', '1Y', '2Y', 'ALL'];
    for (const timeframe of timeframes) {
      expect(screen.getByText(timeframe)).toBeInTheDocument();
    }
  });

  it('shows metric selection checkboxes', () => {
    render(
      <HistoricalTrackingCharts
        darkMode={false}
        userData={mockUserData}
        expenses={mockExpenses}
        investments={mockInvestments}
        savingsGoals={mockSavingsGoals}
      />,
    );

    expect(screen.getByText('Metrics to Display')).toBeInTheDocument();

    // Check for metric checkboxes
    const metrics = ['Net Worth', 'Monthly Income', 'Monthly Expenses', 'Monthly Savings'];
    for (const metric of metrics) {
      expect(screen.getByText(metric)).toBeInTheDocument();
    }
  });

  it('shows empty state when no data is available', () => {
    render(
      <HistoricalTrackingCharts
        darkMode={false}
        userData={mockUserData}
        expenses={mockExpenses}
        investments={mockInvestments}
        savingsGoals={mockSavingsGoals}
      />,
    );

    expect(screen.getByText('No historical data available')).toBeInTheDocument();
    expect(screen.getByText(/Generate sample data to see your financial trends/)).toBeInTheDocument();
    expect(screen.getByText('📊 Generate Sample Data')).toBeInTheDocument();
  });

  it('generates sample data when button is clicked', async () => {
    render(
      <HistoricalTrackingCharts
        darkMode={false}
        userData={mockUserData}
        expenses={mockExpenses}
        investments={mockInvestments}
        savingsGoals={mockSavingsGoals}
      />,
    );

    const generateButton = screen.getByText('📊 Generate Sample Data');
    fireEvent.click(generateButton);

    // Should show loading state
    expect(screen.getByText('Generating...')).toBeInTheDocument();

    // Wait for data generation to complete
    await waitFor(() => {
      expect(screen.getByText('📊 Summary Statistics (1Y)')).toBeInTheDocument();
    }, { timeout: 5000 });
  });

  it('displays summary statistics after data generation', async () => {
    render(
      <HistoricalTrackingCharts
        darkMode={false}
        userData={mockUserData}
        expenses={mockExpenses}
        investments={mockInvestments}
        savingsGoals={mockSavingsGoals}
      />,
    );

    const generateButton = screen.getByText('📊 Generate Sample Data');
    fireEvent.click(generateButton);

    await waitFor(() => {
      expect(screen.getByText('📊 Summary Statistics (1Y)')).toBeInTheDocument();
    }, { timeout: 5000 });

    // Check for all summary metrics
    expect(screen.getByText('Net Worth Growth')).toBeInTheDocument();
    expect(screen.getByText('Current Net Worth')).toBeInTheDocument();
    expect(screen.getByText('Avg Savings Rate')).toBeInTheDocument();
    expect(screen.getByText('FIRE Progress')).toBeInTheDocument();
  });

  it('shows financial trend charts after data generation', async () => {
    render(
      <HistoricalTrackingCharts
        darkMode={false}
        userData={mockUserData}
        expenses={mockExpenses}
        investments={mockInvestments}
        savingsGoals={mockSavingsGoals}
      />,
    );

    const generateButton = screen.getByText('📊 Generate Sample Data');
    fireEvent.click(generateButton);

    await waitFor(() => {
      expect(screen.getByText('📈 Financial Trends')).toBeInTheDocument();
    }, { timeout: 5000 });

    // Should show SVG charts
    const svgElements = screen.getAllByRole('img', { hidden: true });
    expect(svgElements.length).toBeGreaterThan(0);
  });

  it('handles timeframe changes', async () => {
    render(
      <HistoricalTrackingCharts
        darkMode={false}
        userData={mockUserData}
        expenses={mockExpenses}
        investments={mockInvestments}
        savingsGoals={mockSavingsGoals}
      />,
    );

    // Generate data first
    const generateButton = screen.getByText('📊 Generate Sample Data');
    fireEvent.click(generateButton);

    await waitFor(() => {
      expect(screen.getByText('📊 Summary Statistics (1Y)')).toBeInTheDocument();
    }, { timeout: 5000 });

    // Test different timeframes
    const timeframes = ['3M', '6M', '2Y', 'ALL'];
    for (const timeframe of timeframes) {
      const timeframeButton = screen.getByText(timeframe);
      fireEvent.click(timeframeButton);

      await waitFor(() => {
        expect(screen.getByText(`📊 Summary Statistics (${timeframe})`)).toBeInTheDocument();
      }, { timeout: 1000 });
    }
  });

  it('handles metric selection changes', async () => {
    render(
      <HistoricalTrackingCharts
        darkMode={false}
        userData={mockUserData}
        expenses={mockExpenses}
        investments={mockInvestments}
        savingsGoals={mockSavingsGoals}
      />,
    );

    // Generate data first
    const generateButton = screen.getByText('📊 Generate Sample Data');
    fireEvent.click(generateButton);

    await waitFor(() => {
      expect(screen.getByText('📈 Financial Trends')).toBeInTheDocument();
    }, { timeout: 5000 });

    // Test unchecking a metric
    const checkboxes = screen.getAllByRole('checkbox');
    expect(checkboxes.length).toBeGreaterThan(0);

    fireEvent.click(checkboxes[0]);
    fireEvent.click(checkboxes[0]); // Check it back
  });

  it('displays historical data table', async () => {
    render(
      <HistoricalTrackingCharts
        darkMode={false}
        userData={mockUserData}
        expenses={mockExpenses}
        investments={mockInvestments}
        savingsGoals={mockSavingsGoals}
      />,
    );

    const generateButton = screen.getByText('📊 Generate Sample Data');
    fireEvent.click(generateButton);

    await waitFor(() => {
      expect(screen.getByText('📋 Historical Data Table')).toBeInTheDocument();
    }, { timeout: 5000 });

    // Check for table headers
    expect(screen.getByText('Date')).toBeInTheDocument();
    expect(screen.getByText('Net Worth')).toBeInTheDocument();
    expect(screen.getByText('Monthly Income')).toBeInTheDocument();
    expect(screen.getByText('Monthly Expenses')).toBeInTheDocument();
    expect(screen.getByText('Savings Rate')).toBeInTheDocument();
    expect(screen.getByText('FIRE Progress')).toBeInTheDocument();
  });

  it('shows correct data formatting', async () => {
    render(
      <HistoricalTrackingCharts
        darkMode={false}
        userData={mockUserData}
        expenses={mockExpenses}
        investments={mockInvestments}
        savingsGoals={mockSavingsGoals}
      />,
    );

    const generateButton = screen.getByText('📊 Generate Sample Data');
    fireEvent.click(generateButton);

    await waitFor(() => {
      expect(screen.getByText('📊 Summary Statistics (1Y)')).toBeInTheDocument();
    }, { timeout: 5000 });

    // Should show CHF formatting
    await waitFor(() => {
      expect(screen.getByText(/CHF/)).toBeInTheDocument();
    }, { timeout: 2000 });

    // Should show percentage formatting
    await waitFor(() => {
      expect(screen.getByText(/%/)).toBeInTheDocument();
    }, { timeout: 2000 });
  });

  it('handles refresh functionality', async () => {
    render(
      <HistoricalTrackingCharts
        darkMode={false}
        userData={mockUserData}
        expenses={mockExpenses}
        investments={mockInvestments}
        savingsGoals={mockSavingsGoals}
      />,
    );

    // Generate data first
    const generateButton = screen.getByText('📊 Generate Sample Data');
    fireEvent.click(generateButton);

    await waitFor(() => {
      expect(screen.getByText('📊 Summary Statistics (1Y)')).toBeInTheDocument();
    }, { timeout: 5000 });

    // Click generate again to refresh
    const refreshButton = screen.getByText('📊 Generate Sample Data');
    fireEvent.click(refreshButton);

    expect(screen.getByText('Generating...')).toBeInTheDocument();
  });

  it('works in dark mode', () => {
    render(
      <HistoricalTrackingCharts
        darkMode={true}
        userData={mockUserData}
        expenses={mockExpenses}
        investments={mockInvestments}
        savingsGoals={mockSavingsGoals}
      />,
    );

    expect(screen.getByText('📈 Historical Tracking & Analytics')).toBeInTheDocument();
  });

  it('handles empty user data gracefully', async () => {
    const emptyUserData = {
      currentAge: 0,
      retirementAge: 0,
      currentSavings: 0,
      monthlyIncome: 0,
      monthlyExpenses: 0,
      expectedReturn: 0,
      inflationRate: 0,
      safeWithdrawalRate: 0,
    };

    render(
      <HistoricalTrackingCharts
        darkMode={false}
        userData={emptyUserData}
        expenses={[]}
        investments={[]}
        savingsGoals={[]}
      />,
    );

    const generateButton = screen.getByText('📊 Generate Sample Data');
    fireEvent.click(generateButton);

    // Should handle gracefully without crashing
    await waitFor(() => {
      expect(screen.getByText('📊 Summary Statistics (1Y)')).toBeInTheDocument();
    }, { timeout: 5000 });
  });

  it('shows loading states properly', async () => {
    render(
      <HistoricalTrackingCharts
        darkMode={false}
        userData={mockUserData}
        expenses={mockExpenses}
        investments={mockInvestments}
        savingsGoals={mockSavingsGoals}
      />,
    );

    const generateButton = screen.getByText('📊 Generate Sample Data');
    fireEvent.click(generateButton);

    // Should show loading text
    expect(screen.getByText('Generating Sample Data...')).toBeInTheDocument();

    // Button should be disabled
    expect(generateButton).toBeDisabled();

    // Wait for completion
    await waitFor(() => {
      expect(screen.getByText('📊 Summary Statistics (1Y)')).toBeInTheDocument();
    }, { timeout: 5000 });

    // Loading should be gone
    expect(screen.queryByText('Generating Sample Data...')).not.toBeInTheDocument();
  });
});
