import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import RiskAssessmentMetrics from '../../../src/components/RiskAssessmentMetrics';

// Mock the useTranslation hook
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string, defaultValue?: string) => defaultValue || key,
  }),
}));

const mockUserData = {
  currentAge: 40,
  retirementAge: 65,
  currentSavings: 200000,
  monthlyIncome: 10000,
  monthlyExpenses: 6000,
  expectedReturn: 7,
  inflationRate: 2.5,
  safeWithdrawalRate: 4,
};

const mockInvestments = [
  { id: '1', name: 'Swiss ETF', currentValue: 80000, type: 'etf' },
  { id: '2', name: 'Bonds', currentValue: 40000, type: 'bonds' },
  { id: '3', name: 'Real Estate', currentValue: 60000, type: 'reits' },
];

const mockExpenses = [
  { id: '1', name: 'Housing', amount: 2500, category: 'housing' },
  { id: '2', name: 'Food', amount: 1000, category: 'food' },
  { id: '3', name: 'Transport', amount: 800, category: 'transport' },
];

describe('RiskAssessmentMetrics', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the Risk Assessment Metrics component', () => {
    render(
      <RiskAssessmentMetrics
        darkMode={false}
        userData={mockUserData}
        investments={mockInvestments}
        expenses={mockExpenses}
      />,
    );

    expect(screen.getByText('⚠️ Risk Assessment Metrics')).toBeInTheDocument();
    expect(screen.getByText(/Comprehensive analysis of financial risks/)).toBeInTheDocument();
  });

  it('displays loading state during analysis', () => {
    render(
      <RiskAssessmentMetrics
        darkMode={false}
        userData={mockUserData}
        investments={mockInvestments}
        expenses={mockExpenses}
      />,
    );

    expect(screen.getByText(/Analyzing risk profile/)).toBeInTheDocument();
  });

  it('generates overall risk profile after analysis', async () => {
    render(
      <RiskAssessmentMetrics
        darkMode={false}
        userData={mockUserData}
        investments={mockInvestments}
        expenses={mockExpenses}
      />,
    );

    await waitFor(() => {
      expect(screen.getByText('📊 Overall Risk Profile')).toBeInTheDocument();
    }, { timeout: 5000 });

    expect(screen.getByText('Risk Score')).toBeInTheDocument();
    expect(screen.getByText('Risk Level')).toBeInTheDocument();
    expect(screen.getByText('Risk Factors')).toBeInTheDocument();
  });

  it('displays all 8 risk metrics', async () => {
    render(
      <RiskAssessmentMetrics
        darkMode={false}
        userData={mockUserData}
        investments={mockInvestments}
        expenses={mockExpenses}
      />,
    );

    await waitFor(() => {
      expect(screen.getByText('🎯 Risk Metrics Analysis')).toBeInTheDocument();
    }, { timeout: 5000 });

    // Check for all 8 risk metrics
    const expectedMetrics = [
      'Emergency Fund Coverage',
      'Savings Rate Sustainability',
      'Investment Concentration',
      'Time Horizon Risk',
      'Income Stability',
      'Inflation Protection',
      'Withdrawal Rate Safety',
      'Sequence of Returns Risk',
    ];

    for (const metric of expectedMetrics) {
      expect(screen.getByText(metric)).toBeInTheDocument();
    }
  });

  it('handles risk category filtering', async () => {
    render(
      <RiskAssessmentMetrics
        darkMode={false}
        userData={mockUserData}
        investments={mockInvestments}
        expenses={mockExpenses}
      />,
    );

    await waitFor(() => {
      expect(screen.getByText('🎯 Risk Metrics Analysis')).toBeInTheDocument();
    }, { timeout: 5000 });

    const categorySelect = screen.getByRole('combobox');
    expect(categorySelect).toBeInTheDocument();

    // Test different categories
    fireEvent.change(categorySelect, { target: { value: 'financial' } });
    expect(screen.getByText('Emergency Fund Coverage')).toBeInTheDocument();

    fireEvent.change(categorySelect, { target: { value: 'market' } });
    expect(screen.getByText('Investment Concentration')).toBeInTheDocument();

    fireEvent.change(categorySelect, { target: { value: 'personal' } });
    expect(screen.getByText('Time Horizon Risk')).toBeInTheDocument();

    fireEvent.change(categorySelect, { target: { value: 'economic' } });
    expect(screen.getByText('Inflation Protection')).toBeInTheDocument();
  });

  it('displays strengths and weaknesses', async () => {
    render(
      <RiskAssessmentMetrics
        darkMode={false}
        userData={mockUserData}
        investments={mockInvestments}
        expenses={mockExpenses}
      />,
    );

    await waitFor(() => {
      expect(screen.getByText('✅ Strengths')).toBeInTheDocument();
      expect(screen.getByText('⚠️ Areas for Improvement')).toBeInTheDocument();
    }, { timeout: 5000 });
  });

  it('shows risk management recommendations', async () => {
    render(
      <RiskAssessmentMetrics
        darkMode={false}
        userData={mockUserData}
        investments={mockInvestments}
        expenses={mockExpenses}
      />,
    );

    await waitFor(() => {
      expect(screen.getByText('💡 Risk Management Recommendations')).toBeInTheDocument();
    }, { timeout: 5000 });

    // Should have numbered recommendations
    expect(screen.getByText('1')).toBeInTheDocument();
    expect(screen.getByText('2')).toBeInTheDocument();
  });

  it('handles high-risk scenario correctly', async () => {
    const highRiskData = {
      ...mockUserData,
      currentSavings: 5000,
      monthlyIncome: 4000,
      monthlyExpenses: 3800,
      currentAge: 60,
      retirementAge: 65,
    };

    render(
      <RiskAssessmentMetrics
        darkMode={false}
        userData={highRiskData}
        investments={[]}
        expenses={mockExpenses}
      />,
    );

    await waitFor(() => {
      expect(screen.getByText('📊 Overall Risk Profile')).toBeInTheDocument();
    }, { timeout: 5000 });

    // Should show higher risk indicators
    await waitFor(() => {
      const riskElements = screen.queryAllByText(/CRITICAL|HIGH/);
      expect(riskElements.length).toBeGreaterThan(0);
    }, { timeout: 2000 });
  });

  it('handles low-risk scenario correctly', async () => {
    const lowRiskData = {
      ...mockUserData,
      currentSavings: 500000,
      monthlyIncome: 15000,
      monthlyExpenses: 5000,
      currentAge: 30,
      retirementAge: 65,
    };

    render(
      <RiskAssessmentMetrics
        darkMode={false}
        userData={lowRiskData}
        investments={mockInvestments}
        expenses={mockExpenses}
      />,
    );

    await waitFor(() => {
      expect(screen.getByText('📊 Overall Risk Profile')).toBeInTheDocument();
    }, { timeout: 5000 });

    // Should show lower risk indicators
    await waitFor(() => {
      const riskElements = screen.queryAllByText(/LOW|CONSERVATIVE/);
      expect(riskElements.length).toBeGreaterThan(0);
    }, { timeout: 2000 });
  });

  it('calculates emergency fund risk correctly', async () => {
    const emergencyFundData = {
      ...mockUserData,
      monthlyExpenses: 5000,
      currentSavings: 30000, // 6 months of expenses
    };

    render(
      <RiskAssessmentMetrics
        darkMode={false}
        userData={emergencyFundData}
        investments={mockInvestments}
        expenses={mockExpenses}
      />,
    );

    await waitFor(() => {
      expect(screen.getByText('Emergency Fund Coverage')).toBeInTheDocument();
    }, { timeout: 5000 });

    // Should show adequate emergency fund coverage
    await waitFor(() => {
      expect(screen.getByText(/6\.0 months/)).toBeInTheDocument();
    }, { timeout: 2000 });
  });

  it('assesses time horizon risk correctly', async () => {
    const shortTimeHorizonData = {
      ...mockUserData,
      currentAge: 60,
      retirementAge: 65,
    };

    render(
      <RiskAssessmentMetrics
        darkMode={false}
        userData={shortTimeHorizonData}
        investments={mockInvestments}
        expenses={mockExpenses}
      />,
    );

    await waitFor(() => {
      expect(screen.getByText('Time Horizon Risk')).toBeInTheDocument();
    }, { timeout: 5000 });

    // Should show 5 years to retirement
    await waitFor(() => {
      expect(screen.getByText(/5 years/)).toBeInTheDocument();
    }, { timeout: 2000 });
  });

  it('handles refresh analysis functionality', async () => {
    render(
      <RiskAssessmentMetrics
        darkMode={false}
        userData={mockUserData}
        investments={mockInvestments}
        expenses={mockExpenses}
      />,
    );

    await waitFor(() => {
      expect(screen.getByText('🔄 Refresh Risk Analysis')).toBeInTheDocument();
    }, { timeout: 5000 });

    const refreshButton = screen.getByText('🔄 Refresh Risk Analysis');
    fireEvent.click(refreshButton);

    expect(screen.getByText(/Analyzing.../)).toBeInTheDocument();
  });

  it('displays risk severity levels correctly', async () => {
    render(
      <RiskAssessmentMetrics
        darkMode={false}
        userData={mockUserData}
        investments={mockInvestments}
        expenses={mockExpenses}
      />,
    );

    await waitFor(() => {
      expect(screen.getByText('🎯 Risk Metrics Analysis')).toBeInTheDocument();
    }, { timeout: 5000 });

    // Should show severity levels
    const severityLevels = ['LOW RISK', 'MEDIUM RISK', 'HIGH RISK', 'CRITICAL RISK'];
    let foundSeverity = false;

    for (const level of severityLevels) {
      if (screen.queryByText(level)) {
        foundSeverity = true;
        break;
      }
    }

    expect(foundSeverity).toBe(true);
  });

  it('works in dark mode', () => {
    render(
      <RiskAssessmentMetrics
        darkMode={true}
        userData={mockUserData}
        investments={mockInvestments}
        expenses={mockExpenses}
      />,
    );

    expect(screen.getByText('⚠️ Risk Assessment Metrics')).toBeInTheDocument();
  });

  it('handles empty data gracefully', async () => {
    const emptyUserData = {
      currentAge: 0,
      retirementAge: 0,
      currentSavings: 0,
      monthlyIncome: 0,
      monthlyExpenses: 0,
      expectedReturn: 0,
      inflationRate: 0,
      safeWithdrawalRate: 0,
    };

    render(
      <RiskAssessmentMetrics
        darkMode={false}
        userData={emptyUserData}
        investments={[]}
        expenses={[]}
      />,
    );

    await waitFor(() => {
      expect(screen.getByText('📊 Overall Risk Profile')).toBeInTheDocument();
    }, { timeout: 5000 });

    // Should handle gracefully without crashing
    expect(screen.getByText('Risk Score')).toBeInTheDocument();
  });

  it('shows actionable recommendations', async () => {
    render(
      <RiskAssessmentMetrics
        darkMode={false}
        userData={mockUserData}
        investments={mockInvestments}
        expenses={mockExpenses}
      />,
    );

    await waitFor(() => {
      expect(screen.getByText('🎯 Risk Metrics Analysis')).toBeInTheDocument();
    }, { timeout: 5000 });

    // Should show actionable badges
    await waitFor(() => {
      expect(screen.getByText('Actionable')).toBeInTheDocument();
    }, { timeout: 2000 });
  });
});
