/**
 * Global Teardown for Swiss Budget Pro E2E Tests
 * Cleanup test environment and generate comprehensive reports
 */

import { FullConfig } from '@playwright/test';
import fs from 'fs';
import path from 'path';

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Cleaning up Swiss Budget Pro E2E Test Environment...');
  
  // Generate test summary report
  await generateTestSummaryReport();
  
  // Archive test artifacts
  await archiveTestArtifacts();
  
  // Cleanup temporary test data
  await cleanupTemporaryData();
  
  // Generate performance report
  await generatePerformanceReport();
  
  console.log('✅ Swiss Budget Pro E2E Test Environment Cleanup Complete');
}

/**
 * Generate comprehensive test summary report
 */
async function generateTestSummaryReport() {
  console.log('📊 Generating test summary report...');
  
  try {
    const testResultsPath = 'test-results/results.json';
    
    if (fs.existsSync(testResultsPath)) {
      const results = JSON.parse(fs.readFileSync(testResultsPath, 'utf8'));
      
      const summary = {
        timestamp: new Date().toISOString(),
        testSuite: 'Swiss Budget Pro E2E Tests',
        summary: {
          total: results.stats?.total || 0,
          passed: results.stats?.passed || 0,
          failed: results.stats?.failed || 0,
          skipped: results.stats?.skipped || 0,
          duration: results.stats?.duration || 0,
        },
        coverage: {
          criticalFlows: calculateCriticalFlowsCoverage(results),
          swissFeatures: calculateSwissFeaturesCoverage(results),
          browsers: calculateBrowserCoverage(results),
          mobile: calculateMobileCoverage(results),
        },
        performance: {
          averageTestDuration: calculateAverageTestDuration(results),
          slowestTests: findSlowestTests(results),
          failureRate: calculateFailureRate(results),
        },
        recommendations: generateRecommendations(results),
      };
      
      // Save summary report
      const summaryPath = 'test-results/reports/test-summary.json';
      fs.mkdirSync(path.dirname(summaryPath), { recursive: true });
      fs.writeFileSync(summaryPath, JSON.stringify(summary, null, 2));
      
      // Generate human-readable report
      const readableReport = generateReadableReport(summary);
      fs.writeFileSync('test-results/reports/test-summary.md', readableReport);
      
      console.log('✅ Test summary report generated');
      console.log(`📈 Tests: ${summary.summary.passed}/${summary.summary.total} passed`);
      console.log(`⏱️  Duration: ${(summary.summary.duration / 1000).toFixed(2)}s`);
      
    } else {
      console.log('⚠️  No test results found for summary generation');
    }
    
  } catch (error) {
    console.error('❌ Failed to generate test summary:', error);
  }
}

/**
 * Archive test artifacts for future reference
 */
async function archiveTestArtifacts() {
  console.log('📦 Archiving test artifacts...');
  
  try {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const archiveDir = `test-results/archives/${timestamp}`;
    
    // Create archive directory
    fs.mkdirSync(archiveDir, { recursive: true });
    
    // Archive important files
    const filesToArchive = [
      'test-results/results.json',
      'test-results/junit.xml',
      'test-results/reports/test-summary.json',
      'test-results/reports/test-summary.md',
    ];
    
    filesToArchive.forEach(file => {
      if (fs.existsSync(file)) {
        const fileName = path.basename(file);
        fs.copyFileSync(file, path.join(archiveDir, fileName));
      }
    });
    
    // Create archive manifest
    const manifest = {
      timestamp,
      testSuite: 'Swiss Budget Pro E2E Tests',
      archivedFiles: filesToArchive.filter(file => fs.existsSync(file)),
      environment: process.env.NODE_ENV || 'development',
    };
    
    fs.writeFileSync(
      path.join(archiveDir, 'manifest.json'),
      JSON.stringify(manifest, null, 2),
    );
    
    console.log(`✅ Test artifacts archived to ${archiveDir}`);
    
  } catch (error) {
    console.error('❌ Failed to archive test artifacts:', error);
  }
}

/**
 * Cleanup temporary test data
 */
async function cleanupTemporaryData() {
  console.log('🗑️  Cleaning up temporary test data...');
  
  try {
    const tempFiles = [
      'tests/fixtures/temp-*.json',
      'test-results/temp-*',
    ];
    
    // Note: In a real implementation, you would use glob patterns
    // For now, we'll just clean up known temporary files
    const knownTempFiles = [
      'tests/fixtures/temp-user-data.json',
      'tests/fixtures/temp-calculation-results.json',
    ];
    
    knownTempFiles.forEach(file => {
      if (fs.existsSync(file)) {
        fs.unlinkSync(file);
        console.log(`🗑️  Removed temporary file: ${file}`);
      }
    });
    
    console.log('✅ Temporary data cleanup complete');
    
  } catch (error) {
    console.error('❌ Failed to cleanup temporary data:', error);
  }
}

/**
 * Generate performance report
 */
async function generatePerformanceReport() {
  console.log('⚡ Generating performance report...');
  
  try {
    const performanceData = {
      timestamp: new Date().toISOString(),
      testSuite: 'Swiss Budget Pro E2E Tests',
      metrics: {
        averagePageLoadTime: 'N/A', // Would be calculated from actual test data
        averageCalculationTime: 'N/A',
        memoryUsage: 'N/A',
        networkRequests: 'N/A',
      },
      benchmarks: {
        fireCalculation: { target: '< 500ms', actual: 'N/A' },
        taxCalculation: { target: '< 300ms', actual: 'N/A' },
        healthcareOptimization: { target: '< 200ms', actual: 'N/A' },
        reportGeneration: { target: '< 2s', actual: 'N/A' },
      },
      recommendations: [
        'Monitor FIRE calculation performance for complex scenarios',
        'Optimize tax calculation for multiple cantons',
        'Consider caching for healthcare optimization results',
      ],
    };
    
    const performancePath = 'test-results/reports/performance-report.json';
    fs.writeFileSync(performancePath, JSON.stringify(performanceData, null, 2));
    
    console.log('✅ Performance report generated');
    
  } catch (error) {
    console.error('❌ Failed to generate performance report:', error);
  }
}

// Helper functions for report generation

function calculateCriticalFlowsCoverage(results: any): number {
  // Calculate coverage of critical flows (FIRE, Tax, Healthcare)
  const criticalTests = results.suites?.filter((suite: any) => 
    suite.title?.includes('critical-flows') || 
    suite.title?.includes('FIRE') ||
    suite.title?.includes('tax') ||
    suite.title?.includes('healthcare'),
  ) || [];
  
  const totalCritical = criticalTests.length;
  const passedCritical = criticalTests.filter((test: any) => test.outcome === 'passed').length;
  
  return totalCritical > 0 ? (passedCritical / totalCritical) * 100 : 0;
}

function calculateSwissFeaturesCoverage(results: any): number {
  // Calculate coverage of Swiss-specific features
  const swissTests = results.suites?.filter((suite: any) => 
    suite.title?.toLowerCase().includes('swiss') ||
    suite.title?.toLowerCase().includes('canton') ||
    suite.title?.toLowerCase().includes('pillar'),
  ) || [];
  
  const totalSwiss = swissTests.length;
  const passedSwiss = swissTests.filter((test: any) => test.outcome === 'passed').length;
  
  return totalSwiss > 0 ? (passedSwiss / totalSwiss) * 100 : 0;
}

function calculateBrowserCoverage(results: any): number {
  // Calculate browser compatibility coverage
  const browserProjects = ['chromium', 'firefox', 'webkit', 'edge'];
  const testedBrowsers = results.config?.projects?.filter((project: any) => 
    browserProjects.some(browser => project.name?.includes(browser)),
  ) || [];
  
  return (testedBrowsers.length / browserProjects.length) * 100;
}

function calculateMobileCoverage(results: any): number {
  // Calculate mobile device coverage
  const mobileTests = results.suites?.filter((suite: any) => 
    suite.title?.toLowerCase().includes('mobile') ||
    suite.title?.toLowerCase().includes('responsive'),
  ) || [];
  
  return mobileTests.length > 0 ? 100 : 0; // Simplified calculation
}

function calculateAverageTestDuration(results: any): number {
  const tests = results.suites?.flatMap((suite: any) => suite.specs || []) || [];
  const totalDuration = tests.reduce((sum: number, test: any) => sum + (test.duration || 0), 0);
  
  return tests.length > 0 ? totalDuration / tests.length : 0;
}

function findSlowestTests(results: any): any[] {
  const tests = results.suites?.flatMap((suite: any) => suite.specs || []) || [];
  
  return tests
    .sort((a: any, b: any) => (b.duration || 0) - (a.duration || 0))
    .slice(0, 5)
    .map((test: any) => ({
      title: test.title,
      duration: test.duration,
      file: test.file,
    }));
}

function calculateFailureRate(results: any): number {
  const total = results.stats?.total || 0;
  const failed = results.stats?.failed || 0;
  
  return total > 0 ? (failed / total) * 100 : 0;
}

function generateRecommendations(results: any): string[] {
  const recommendations: string[] = [];
  
  const failureRate = calculateFailureRate(results);
  if (failureRate > 5) {
    recommendations.push('High failure rate detected - review failing tests for stability issues');
  }
  
  const avgDuration = calculateAverageTestDuration(results);
  if (avgDuration > 30000) { // 30 seconds
    recommendations.push('Tests are running slowly - consider optimizing test scenarios');
  }
  
  const criticalCoverage = calculateCriticalFlowsCoverage(results);
  if (criticalCoverage < 95) {
    recommendations.push('Critical flow coverage below 95% - add more comprehensive tests');
  }
  
  return recommendations;
}

function generateReadableReport(summary: any): string {
  return `# Swiss Budget Pro E2E Test Summary

## Test Execution Summary
- **Total Tests**: ${summary.summary.total}
- **Passed**: ${summary.summary.passed}
- **Failed**: ${summary.summary.failed}
- **Skipped**: ${summary.summary.skipped}
- **Duration**: ${(summary.summary.duration / 1000).toFixed(2)} seconds

## Coverage Analysis
- **Critical Flows**: ${summary.coverage.criticalFlows.toFixed(1)}%
- **Swiss Features**: ${summary.coverage.swissFeatures.toFixed(1)}%
- **Browser Compatibility**: ${summary.coverage.browsers.toFixed(1)}%
- **Mobile Coverage**: ${summary.coverage.mobile.toFixed(1)}%

## Performance Metrics
- **Average Test Duration**: ${(summary.performance.averageTestDuration / 1000).toFixed(2)}s
- **Failure Rate**: ${summary.performance.failureRate.toFixed(1)}%

## Slowest Tests
${summary.performance.slowestTests.map((test: any, index: number) => 
  `${index + 1}. ${test.title} (${(test.duration / 1000).toFixed(2)}s)`,
).join('\n')}

## Recommendations
${summary.recommendations.map((rec: string) => `- ${rec}`).join('\n')}

---
Generated on: ${summary.timestamp}
`;
}

export default globalTeardown;
