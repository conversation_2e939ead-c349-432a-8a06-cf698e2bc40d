#!/usr/bin/env python3
"""
Documentation Structure Validator
Validates that all referenced documentation files exist and are properly structured.
"""

import os
import sys
from pathlib import Path

def validate_file_exists(file_path, context=""):
    """Check if a file exists and report status."""
    if os.path.exists(file_path):
        print(f"✅ {file_path} - EXISTS {context}")
        return True
    else:
        print(f"❌ {file_path} - MISSING {context}")
        return False

def validate_documentation_structure():
    """Validate the complete documentation structure."""
    print("🔍 Validating Swiss Budget Pro Documentation Structure")
    print("=" * 60)
    
    missing_files = []
    
    # Core documentation files
    core_files = [
        "index.md",
        "conf.py",
        "getting-started.md",
        "changelog.md",
        "roadmap.md",
        "contributing.md",
        "license.md",
    ]
    
    print("\n📚 Core Documentation Files:")
    for file in core_files:
        if not validate_file_exists(file, "(Core)"):
            missing_files.append(file)
    
    # User guide files
    user_guide_files = [
        "user-guide/index.md",
        "user-guide/fire-acceleration.md",
        "user-guide/smart-dashboard.md",
        "user-guide/data-visualization.md",
        "user-guide/internationalization.md",
        "user-guide/swiss-features.md",
        "user-guide/tax-optimization.md",
        "user-guide/pillar-3a-guide.md",
        "user-guide/cantonal-differences.md",
        "user-guide/healthcare-cost-optimizer-guide.md",
    ]
    
    print("\n👤 User Guide Files:")
    for file in user_guide_files:
        if not validate_file_exists(file, "(User Guide)"):
            missing_files.append(file)
    
    # Architecture documentation
    architecture_files = [
        "architecture/index.md",
        "architecture/data-flow.md",
        "architecture/components.md",
        "architecture/chart-components.md",
    ]
    
    print("\n🏗️ Architecture Documentation:")
    for file in architecture_files:
        if not validate_file_exists(file, "(Architecture)"):
            missing_files.append(file)
    
    # API documentation
    api_files = [
        "api-reference/index.md",
        "api/chart-components-api.md",
    ]
    
    print("\n📋 API Documentation:")
    for file in api_files:
        if not validate_file_exists(file, "(API)"):
            missing_files.append(file)
    
    # Testing documentation
    testing_files = [
        "testing-and-quality.md",
        "testing/data-visualization-testing.md",
    ]
    
    print("\n🧪 Testing Documentation:")
    for file in testing_files:
        if not validate_file_exists(file, "(Testing)"):
            missing_files.append(file)
    
    # PRD files
    prd_files = [
        "PRD_COMPREHENSIVE_FIRE_CALCULATOR.md",
        "PRD_DATA_VISUALIZATION.md",
        "PRD_PREMIUM_FEATURES.md",
    ]
    
    print("\n📋 PRD Documentation:")
    for file in prd_files:
        if not validate_file_exists(file, "(PRD)"):
            missing_files.append(file)
    
    # Quick start guides
    quickstart_files = [
        "quick-start/data-visualization-quickstart.md",
    ]
    
    print("\n🚀 Quick Start Guides:")
    for file in quickstart_files:
        if not validate_file_exists(file, "(Quick Start)"):
            missing_files.append(file)
    
    # Changelog files
    changelog_files = [
        "changelog/data-visualization-release.md",
    ]
    
    print("\n📝 Changelog Files:")
    for file in changelog_files:
        if not validate_file_exists(file, "(Changelog)"):
            missing_files.append(file)
    
    # Static assets
    static_files = [
        "_static/custom.css",
        "_static/custom.js",
        "_static/favicon.svg",
        "_static/logo-light.svg",
        "_static/logo-dark.svg",
    ]
    
    print("\n🎨 Static Assets:")
    for file in static_files:
        if not validate_file_exists(file, "(Static)"):
            missing_files.append(file)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 VALIDATION SUMMARY")
    print("=" * 60)
    
    total_files = len(core_files + user_guide_files + architecture_files + 
                     api_files + testing_files + prd_files + quickstart_files + 
                     changelog_files + static_files)
    existing_files = total_files - len(missing_files)
    
    print(f"📁 Total Files Checked: {total_files}")
    print(f"✅ Files Found: {existing_files}")
    print(f"❌ Files Missing: {len(missing_files)}")
    print(f"📈 Coverage: {(existing_files/total_files)*100:.1f}%")
    
    if missing_files:
        print(f"\n❌ MISSING FILES:")
        for file in missing_files:
            print(f"   - {file}")
        print(f"\n⚠️  Documentation structure is incomplete.")
        return False
    else:
        print(f"\n✅ ALL DOCUMENTATION FILES FOUND!")
        print(f"🎉 Documentation structure is complete and ready for build.")
        return True

def check_markdown_syntax():
    """Basic markdown syntax validation."""
    print("\n🔍 Checking Markdown Syntax...")
    
    markdown_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.md'):
                markdown_files.append(os.path.join(root, file))
    
    issues = []
    
    for file_path in markdown_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # Check for basic issues
                lines = content.split('\n')
                for i, line in enumerate(lines, 1):
                    # Check for unclosed code blocks
                    if line.strip().startswith('```') and not line.strip().endswith('```'):
                        # Count code block markers
                        code_blocks = content.count('```')
                        if code_blocks % 2 != 0:
                            issues.append(f"{file_path}:{i} - Unclosed code block")
                            break
                
        except Exception as e:
            issues.append(f"{file_path} - Error reading file: {e}")
    
    if issues:
        print("❌ Markdown Issues Found:")
        for issue in issues[:10]:  # Show first 10 issues
            print(f"   - {issue}")
        if len(issues) > 10:
            print(f"   ... and {len(issues) - 10} more issues")
        return False
    else:
        print("✅ No markdown syntax issues found")
        return True

def main():
    """Main validation function."""
    print("🔍 Swiss Budget Pro Documentation Validator")
    print("=" * 60)
    
    # Change to docs directory if not already there
    if not os.path.exists('conf.py'):
        if os.path.exists('docs/conf.py'):
            os.chdir('docs')
        else:
            print("❌ Cannot find documentation directory")
            sys.exit(1)
    
    # Run validations
    structure_valid = validate_documentation_structure()
    syntax_valid = check_markdown_syntax()
    
    print("\n" + "=" * 60)
    print("🎯 FINAL VALIDATION RESULT")
    print("=" * 60)
    
    if structure_valid and syntax_valid:
        print("✅ DOCUMENTATION IS READY FOR BUILD!")
        print("🚀 All files exist and syntax is valid.")
        sys.exit(0)
    else:
        print("❌ DOCUMENTATION NEEDS ATTENTION")
        if not structure_valid:
            print("📁 Missing files need to be created")
        if not syntax_valid:
            print("📝 Markdown syntax issues need to be fixed")
        sys.exit(1)

if __name__ == "__main__":
    main()
