# Data Visualization Guide

```{image} ../_static/logo-light.svg
:alt: Swiss Budget Pro Logo
:class: only-light
:align: center
:width: 200px
```

```{image} ../_static/logo-dark.svg
:alt: Swiss Budget Pro Logo
:class: only-dark
:align: center
:width: 200px
```

**Professional-Grade Financial Data Visualization**

Swiss Budget Pro features world-class data visualization capabilities that rival premium commercial financial planning tools. Our comprehensive visualization suite provides interactive charts, real-time analytics, and professional insights into your financial journey toward FIRE.

```{admonition} 🎯 Key Visualization Features
:class: tip

- **📈 Interactive D3.js Charts**: Professional-grade visualizations with smooth animations
- **📱 Mobile-Optimized**: Touch-friendly charts with gesture support
- **⚡ Real-time Performance**: Sub-500ms chart updates with memory optimization
- **♿ Accessibility Compliant**: WCAG AA standards with screen reader support
- **🌍 Swiss Localization**: CHF formatting and Swiss date conventions
- **📊 Multiple Chart Types**: Line, area, bar, and scatter plot visualizations
- **💾 Data Export**: PNG, SVG, CSV, and JSON export capabilities
- **🔍 Interactive Controls**: Timeframe selection, metric toggles, and zoom controls
```

## Getting Started with Data Visualization

### Accessing the Visualization Dashboard

1. **Navigate to Charts**: Click the "📊 Visualization" tab in the main interface
2. **Wait for Loading**: Charts will automatically render with your financial data
3. **Explore Controls**: Use the interactive controls to customize your view

```{admonition} 💡 Pro Tip
:class: tip

If you don't see any charts, make sure you have entered your basic financial information (income, expenses, savings) in the main calculator first.
```

### Understanding the Dashboard Layout

The Data Visualization Dashboard consists of several key components:

````{grid} 1 2 2 2
:gutter: 3

```{grid-item-card} 📊 Chart Container
Main visualization area displaying your financial charts with interactive data points.
```

```{grid-item-card} 🎛️ Chart Controls
Interactive controls for timeframe selection, chart type, and metric toggles.
```

```{grid-item-card} 📈 Performance Monitor
Real-time performance metrics showing chart render times and memory usage.
```

```{grid-item-card} ⚙️ Settings Panel
Dashboard configuration options for animations, mobile optimization, and accessibility.
```
````

## Interactive Chart Features

### Chart Types

Swiss Budget Pro supports multiple chart types optimized for different data visualization needs:

#### Line Charts
- **Best for**: Trend analysis and time-series data
- **Features**: Smooth curves, data point highlighting, zoom capabilities
- **Use cases**: Net worth growth, income trends, expense patterns

#### Area Charts
- **Best for**: Cumulative values and filled visualizations
- **Features**: Gradient fills, stacked data series, smooth animations
- **Use cases**: Savings accumulation, investment growth, total wealth

#### Bar Charts
- **Best for**: Comparative analysis and categorical data
- **Features**: Vertical/horizontal orientation, grouped series, sorting
- **Use cases**: Monthly comparisons, expense categories, canton analysis

#### Scatter Plots
- **Best for**: Correlation analysis and data distribution
- **Features**: Interactive data points, trend lines, clustering
- **Use cases**: Risk vs return analysis, efficiency metrics

### Timeframe Selection

Control the time period displayed in your charts:

```{list-table} Available Timeframes
:header-rows: 1
:widths: 20 80

* - Timeframe
  - Description
* - 1M
  - Last 30 days of data
* - 3M
  - Last 3 months of data
* - 6M
  - Last 6 months of data
* - 1Y
  - Last 12 months of data
* - 2Y
  - Last 24 months of data
* - ALL
  - Complete historical data
```

### Metric Toggles

Customize which financial metrics are displayed:

```{list-table} Available Metrics
:header-rows: 1
:widths: 25 75

* - Metric
  - Description
* - Net Worth
  - Total assets minus liabilities over time
* - Monthly Income
  - Income trends and variations
* - Monthly Expenses
  - Spending patterns and categories
* - Monthly Savings
  - Savings amount and consistency
* - Savings Rate
  - Percentage of income saved
* - FIRE Progress
  - Progress toward financial independence
* - Investment Value
  - Portfolio growth and performance
* - Emergency Fund
  - Emergency fund adequacy
```

## Advanced Visualization Features

### Interactive Data Points

Every chart includes interactive data points that provide detailed information:

- **Hover Effects**: Display tooltips with exact values and dates
- **Click Actions**: Select data points for detailed analysis
- **Keyboard Navigation**: Use arrow keys to navigate between points
- **Touch Support**: Tap and swipe gestures on mobile devices

### Chart Export Capabilities

Export your charts in multiple formats for presentations and reports:

#### Image Formats
- **PNG**: High-quality raster images for presentations
- **SVG**: Scalable vector graphics for professional documents

#### Data Formats
- **CSV**: Tabular data for spreadsheet analysis
- **JSON**: Structured data for technical analysis

```{admonition} 📤 Export Instructions
:class: note

1. Click the "📤 Export" button in the chart controls
2. Select your desired format from the dropdown menu
3. The file will automatically download to your device
4. Use the exported data in presentations, reports, or further analysis
```

### Performance Monitoring

Swiss Budget Pro includes real-time performance monitoring to ensure optimal chart performance:

#### Performance Metrics
- **Render Time**: Time taken to draw charts (target: <500ms)
- **Memory Usage**: RAM consumption (target: <50MB)
- **Frame Rate**: Animation smoothness (target: >30fps)
- **Data Points**: Number of visualized data points
- **Interaction Latency**: Response time for user interactions

#### Performance Optimization
- **Automatic Optimization**: Charts automatically optimize for your device
- **Memory Management**: Efficient data handling for large datasets
- **Animation Control**: Reduce animations on slower devices
- **Progressive Loading**: Load charts incrementally for better performance

## Mobile Visualization

### Touch-Optimized Interface

Swiss Budget Pro provides a fully optimized mobile experience:

#### Touch Gestures
- **Tap**: Select data points and activate controls
- **Swipe**: Pan across chart data horizontally
- **Pinch**: Zoom in and out of chart details
- **Long Press**: Access context menus and detailed information

#### Mobile-Specific Features
- **Larger Touch Targets**: 44px minimum for accessibility
- **Simplified Controls**: Streamlined interface for small screens
- **Optimized Performance**: Reduced complexity for mobile devices
- **Responsive Layout**: Adaptive design for all screen sizes

### Device Compatibility

Tested and optimized for all major mobile devices:

```{list-table} Supported Devices
:header-rows: 1
:widths: 30 70

* - Device Category
  - Specifications
* - Smartphones
  - iPhone 6+ and Android 5.0+ devices
* - Tablets
  - iPad Air+ and Android tablets 9"+ 
* - Desktop
  - Chrome 90+, Firefox 88+, Safari 14+
* - High DPI
  - Retina and 4K displays supported
```

## Accessibility Features

### WCAG AA Compliance

Swiss Budget Pro meets Web Content Accessibility Guidelines (WCAG) 2.1 AA standards:

#### Visual Accessibility
- **Color Contrast**: 4.5:1 minimum contrast ratio
- **Color Independence**: Information not conveyed by color alone
- **High Contrast Mode**: Support for system high contrast settings
- **Scalable Text**: Charts remain readable at 200% zoom

#### Motor Accessibility
- **Keyboard Navigation**: Full functionality without mouse
- **Touch Targets**: Minimum 44px touch target size
- **Focus Indicators**: Clear visual focus indicators
- **No Time Limits**: No automatic timeouts or animations

#### Cognitive Accessibility
- **Clear Labels**: Descriptive ARIA labels for all elements
- **Consistent Navigation**: Predictable interface patterns
- **Error Prevention**: Clear validation and error messages
- **Help Documentation**: Contextual help and instructions

### Screen Reader Support

Comprehensive screen reader compatibility:

- **ARIA Labels**: Descriptive labels for all chart elements
- **Data Tables**: Alternative tabular representation of chart data
- **Live Regions**: Announcements for dynamic content changes
- **Semantic Markup**: Proper heading structure and landmarks

```{admonition} ♿ Accessibility Tip
:class: tip

Use the Tab key to navigate through chart controls, and press Enter to activate buttons. Screen readers will announce chart data and provide alternative text descriptions.
```

## Troubleshooting

### Common Issues

#### Charts Not Loading
1. **Check Data**: Ensure you have entered financial information
2. **Browser Compatibility**: Use a supported modern browser
3. **JavaScript**: Verify JavaScript is enabled
4. **Network**: Check internet connection for data loading

#### Performance Issues
1. **Reduce Data**: Limit timeframe to improve performance
2. **Disable Animations**: Turn off animations in settings
3. **Close Tabs**: Free up browser memory
4. **Update Browser**: Use the latest browser version

#### Mobile Issues
1. **Orientation**: Try rotating device for better layout
2. **Touch Sensitivity**: Adjust device touch settings
3. **Zoom Level**: Reset browser zoom to 100%
4. **Cache**: Clear browser cache and reload

### Getting Help

If you encounter issues with data visualization:

1. **Check Documentation**: Review this guide for solutions
2. **Performance Monitor**: Enable performance monitoring to identify issues
3. **Browser Console**: Check for error messages (F12 key)
4. **Report Issues**: Submit bug reports with specific details

```{admonition} 🐛 Bug Reports
:class: note

When reporting visualization issues, please include:
- Browser and version
- Device type and screen size
- Steps to reproduce the issue
- Screenshot or screen recording
- Performance metrics if available
```

---

*Swiss Budget Pro's data visualization system provides professional-grade financial insights that help you make informed decisions on your journey to financial independence.*
