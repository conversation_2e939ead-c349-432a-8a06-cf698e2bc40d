# Swiss Budget Pro User Guide

Welcome to the comprehensive Swiss Budget Pro User Guide. This section covers everything you need to know to master Swiss FIRE planning with our advanced financial tool.

## Overview

Swiss Budget Pro is designed specifically for Swiss residents pursuing Financial Independence and Early Retirement (FIRE). Our user guide is organized into logical sections that build upon each other, from basic concepts to advanced optimization strategies.

```{admonition} 🆕 Latest Features (Version 4.1)
:class: tip

**🏥 Swiss Healthcare Cost Optimizer**: Complete healthcare cost optimization with deductible analysis, insurer comparison, and FIRE integration.

**🚀 FIRE Acceleration Engine**: AI-powered recommendations to accelerate your path to financial independence with timeline impact analysis.

**🎯 Enhanced User Experience**: Improved onboarding wizard, smart dashboard, and accessibility features.
```

```{admonition} 📚 How to Use This Guide
:class: tip

- **New Users**: Start with [Financial Planning Basics](financial-planning) and work through each section
- **Experienced Users**: Jump to specific topics using the navigation
- **Swiss Residents**: Pay special attention to [Swiss Features](swiss-features) and [Tax Optimization](tax-optimization)
- **Advanced Users**: Explore [Advanced Calculations](../advanced/calculations) and [Data Management](data-management)
```

## Guide Structure

### Core Functionality

````{grid} 1 2 2 2
:gutter: 3

```{grid-item-card} 💰 Financial Planning
:link: financial-planning
:link-type: doc

Master the fundamentals of FIRE planning, budgeting, and wealth accumulation strategies.
````

```{grid-item-card} 📊 Data Management
:link: data-management
:link-type: doc

Learn to save, organize, and analyze your financial data with scenarios and historical tracking.
```

```{grid-item-card} 🤖 Smart Dashboard
:link: smart-dashboard
:link-type: doc

Discover intelligent recommendations and personalized financial insights.
```

```{grid-item-card} 📈 Projections & Charts
:link: projections-charts
:link-type: doc

Understand and interpret the various charts, graphs, and financial projections.
```

```{grid-item-card} ⚙️ Settings & Configuration
:link: settings-configuration
:link-type: doc

Customize Swiss Budget Pro to match your specific financial situation and goals.
```

```{grid-item-card} 🧠 Advanced Analytics
:link: advanced-analytics
:link-type: doc

Professional Monte Carlo simulations and risk analysis for sophisticated FIRE planning.
```

```{grid-item-card} 📈 Economic Data Integration
:link: economic-data-integration
:link-type: doc

Real-time Swiss economic data integration with SNB and SIX market feeds.
```

```{grid-item-card} 🌍 Internationalization
:link: internationalization
:link-type: doc

Multi-language support with German, French, and Italian for the Swiss community.
```

### Premium Features

````{grid} 1 2 2 2
:gutter: 3

```{grid-item-card} 🤖 AI Financial Advisor
:link: ai-financial-advisor
:link-type: doc

AI-powered personalized financial insights and recommendations with interactive chat.
````

```{grid-item-card} ⚠️ Risk Assessment Metrics
:link: risk-assessment-metrics
:link-type: doc

Comprehensive financial risk analysis across 8 key metrics with mitigation strategies.
```

```{grid-item-card} 📊 Safe Withdrawal Rate Analysis
:link: safe-withdrawal-rate-analysis
:link-type: doc

Monte Carlo simulation for optimal withdrawal rates with 1,000 scenarios per rate.
```

```{grid-item-card} 📈 Historical Tracking Charts
:link: historical-tracking-charts
:link-type: doc

Interactive financial progress visualization with 8 key metrics and timeframe analysis.
```

```{grid-item-card} 🏛️ Canton Relocation Analysis
:link: canton-relocation-analysis
:link-type: doc

Strategic canton relocation analysis with tax optimization across all 26 Swiss cantons.
```

```{grid-item-card} 🏥 Healthcare Cost Optimizer
:link: healthcare-cost-optimizer-guide
:link-type: doc

Swiss healthcare deductible and premium optimization with FIRE integration.
```

### Swiss-Specific Features

````{grid} 1 2 2 2
:gutter: 3

```{grid-item-card} 🇨🇭 Swiss Features Overview
:link: swiss-features
:link-type: doc

Comprehensive guide to Swiss-specific financial planning features and regulations.
````

```{grid-item-card} 💸 Tax Optimization
:link: tax-optimization
:link-type: doc

Advanced strategies for minimizing your Swiss tax burden across all 26 cantons.
```

```{grid-item-card} 🏛️ Pillar 3a Guide
:link: pillar-3a-guide
:link-type: doc

Complete guide to maximizing your Pillar 3a benefits for tax-efficient retirement savings.
```

```{grid-item-card} 🗺️ Cantonal Differences
:link: cantonal-differences
:link-type: doc

Navigate the complex landscape of Swiss cantonal tax systems and regulations.
```

```{grid-item-card} 🏠 Swiss Relocation Calculator
:link: swiss-relocation-calculator
:link-type: doc

Comprehensive relocation analysis with financial impact, quality of life, and FIRE timeline assessment.
```

## Quick Reference

### Essential Concepts

```{glossary}

FIRE
    Financial Independence, Retire Early - a movement focused on extreme savings and investment to enable early retirement.

4% Rule
    A retirement planning rule of thumb that suggests you can safely withdraw 4% of your retirement portfolio annually.

Pillar 3a
    Swiss tax-advantaged retirement savings account with annual contribution limits and withdrawal restrictions.

BVG
    Swiss occupational pension system (2nd pillar) mandatory for employees earning above minimum threshold.

Savings Rate
    Percentage of your income that you save and invest, calculated as (Income - Expenses) / Income × 100.

Net Worth
    Total assets minus total liabilities, representing your overall financial position.

Compound Interest
    Interest calculated on the initial principal and accumulated interest from previous periods.

Real Return
    Investment return adjusted for inflation, representing actual purchasing power growth.
```

### Key Formulas

```{admonition} 📐 Important Calculations
:class: note

**FIRE Number Calculation:**
```

FIRE Number = Annual Expenses × 25

```

**Savings Rate:**
```

Savings Rate = (Income - Expenses) / Income × 100%

```

**Years to FIRE (simplified):**
```

Years = log(1 + (FIRE Number / Current Savings) × (Savings Rate / (1 - Savings Rate))) / log(1 + Real Return)

```

**Real Return:**
```

Real Return = ((1 + Nominal Return) / (1 + Inflation Rate)) - 1

```

```

## Navigation Tips

### Using the Interface

````{tabs}

```{tab} Tabs Navigation
Swiss Budget Pro uses a tabbed interface:
- **Overview**: High-level summary and key metrics
- **Budget Plan**: Income and expense management
- **Target Goal**: FIRE number and retirement planning
- **Company Growth**: Business and side income modeling
- **Data & History**: Scenario management and historical analysis
- **Projections**: Detailed financial forecasts
- **Visualizations**: Charts and graphs
- **Reports**: Exportable summaries
````

```{tab} Data Entry
Best practices for entering data:
- Use consistent currency (CHF)
- Enter monthly amounts unless specified otherwise
- Mark expenses as essential vs discretionary
- Save scenarios with descriptive names
- Review inputs regularly for accuracy
```

```{tab} Interpretation
Understanding your results:
- Focus on trends, not exact numbers
- Use multiple scenarios for sensitivity analysis
- Consider conservative assumptions
- Review projections quarterly
- Adjust plans based on life changes
```

## Common Workflows

### New User Setup

1. **Profile Creation**: Enter basic demographic and financial information
2. **Income Setup**: Configure all income sources and growth projections
3. **Expense Tracking**: Categorize and input monthly expenses
4. **Swiss Configuration**: Set canton, tax preferences, and Pillar 3a strategy
5. **Goal Setting**: Define FIRE number and retirement timeline
6. **Scenario Saving**: Save your baseline scenario

### Regular Review Process

1. **Data Update**: Refresh income, expenses, and asset values
2. **Progress Tracking**: Review savings rate and FIRE progress
3. **Assumption Review**: Validate return and inflation assumptions
4. **Optimization**: Identify areas for improvement
5. **Scenario Comparison**: Test different strategies
6. **Plan Adjustment**: Update goals and timelines as needed

### Advanced Analysis

1. **Sensitivity Analysis**: Test various market scenarios
2. **Tax Optimization**: Explore cantonal differences and strategies
3. **Withdrawal Planning**: Model retirement income strategies
4. **Estate Planning**: Consider wealth transfer implications
5. **Risk Assessment**: Evaluate portfolio volatility impact

## Feature Highlights

### Data Persistence

- **Auto-save**: Never lose your work with automatic saving every 30 seconds
- **Multiple Scenarios**: Compare different financial strategies side-by-side
- **Historical Tracking**: Monitor your progress over time with trend analysis
- **Export/Import**: Full data portability for backup and external analysis

### Swiss Integration

- **26 Cantonal Systems**: Accurate tax calculations for all Swiss cantons
- **Pillar 3a Optimization**: Advanced strategies for tax-deferred savings
- **BVG Integration**: Professional pension fund calculations
- **Real-time Data**: Integration with Swiss economic indicators (planned)

### Advanced Calculations

- **Compound Growth**: Sophisticated modeling of investment returns
- **Inflation Adjustment**: Real vs nominal value calculations
- **Tax Optimization**: Minimize lifetime tax burden
- **Monte Carlo**: Risk assessment and scenario planning (planned)

## Getting Support

````{grid} 1 2 2 2
:gutter: 3

```{grid-item-card} 🆘 Troubleshooting
:link: troubleshooting
:link-type: doc

Common issues and solutions
````

```{grid-item-card} ❓ FAQ
:link: faq
:link-type: doc

Frequently asked questions
```

```{grid-item-card} 💬 Community
:link: https://github.com/swiss-budget-pro/discussions

Join the discussion
```

```{grid-item-card} 📧 Contact
:link: https://github.com/swiss-budget-pro/issues

Report bugs or request features
```

## What's Next?

Ready to dive deeper? Here are some recommended next steps:

```{admonition} 🎯 Recommended Learning Path
:class: tip

1. **Start Here**: [Financial Planning Basics](financial-planning)
2. **Swiss Focus**: [Swiss Features Overview](swiss-features)
3. **Optimization**: [Tax Optimization Strategies](tax-optimization)
4. **Advanced**: [Data Management](data-management)
5. **Mastery**: [Advanced Calculations](../advanced/calculations)
```

---

_This user guide is continuously updated based on user feedback and new features. If you have suggestions for improvement, please [let us know](https://github.com/swiss-budget-pro/issues)!_
