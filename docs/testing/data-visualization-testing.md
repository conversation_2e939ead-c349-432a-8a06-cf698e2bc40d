# Data Visualization Testing Guide

**Comprehensive Testing Strategy for Chart Components**

This guide covers the complete testing infrastructure for Swiss Budget Pro's data visualization system, including unit tests, integration tests, end-to-end tests, and performance validation.

## Testing Overview

The data visualization testing suite consists of multiple layers ensuring comprehensive coverage:

```{mermaid}
graph TD
    A[Unit Tests] --> B[Component Tests]
    B --> C[Integration Tests]
    C --> D[E2E Tests]
    D --> E[Performance Tests]
    E --> F[Accessibility Tests]
    F --> G[Cross-browser Tests]
    G --> H[Mobile Tests]
```

## Test Coverage Statistics

```{admonition} 📊 Test Coverage Achievements
:class: tip

- **32+ Test Scenarios**: Comprehensive coverage across all visualization features
- **7 Test Files**: Specialized test suites for different aspects
- **5 Browser Configurations**: Chrome, Firefox, Safari, Mobile Chrome, Mobile Safari
- **4 Device Categories**: Desktop, Mobile, Tablet, High DPI
- **100% Feature Coverage**: All interactive chart features tested
- **WCAG AA Compliance**: Complete accessibility validation
- **Performance Thresholds**: Strict performance requirements validated
```

## Unit Testing

### Component Unit Tests

**Testing Framework**: Vitest with React Testing Library

```typescript
// Example unit test for EnhancedD3Chart
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import EnhancedD3Chart from '../EnhancedD3Chart';

describe('EnhancedD3Chart', () => {
  const mockData = [
    { date: new Date('2024-01-01'), value: 100000 },
    { date: new Date('2024-06-01'), value: 120000 },
    { date: new Date('2024-12-01'), value: 150000 },
  ];

  it('renders chart with correct data points', () => {
    render(
      <EnhancedD3Chart
        data={mockData}
        config={{ type: 'line', width: 500, height: 300 }}
        darkMode={false}
      />
    );

    // Verify SVG is rendered
    expect(screen.getByRole('img')).toBeInTheDocument();

    // Verify data points are present
    const dataPoints = screen.getAllByRole('button');
    expect(dataPoints).toHaveLength(3);
  });
});
```

### Hook Testing

**Testing Custom Hooks**: useD3Chart hook validation

```typescript
import { renderHook } from '@testing-library/react';
import { useD3Chart } from '../hooks/useD3Chart';

describe('useD3Chart', () => {
  it('provides correct formatting functions', () => {
    const { result } = renderHook(() =>
      useD3Chart([], { width: 500, height: 300 })
    );

    // Test Swiss currency formatting
    expect(result.current.formatCurrency(1234.56)).toBe('CHF 1,235');

    // Test Swiss date formatting
    const testDate = new Date('2024-12-25');
    expect(result.current.formatDate(testDate)).toBe('25. Dez. 2024');
  });
});
```

### Performance Unit Tests

**Performance Validation**: Component performance testing

```typescript
describe('Chart Performance', () => {
  it('renders large datasets within threshold', async () => {
    const largeDataset = generateMockData(1000); // 1000 data points

    const startTime = performance.now();
    render(<EnhancedD3Chart data={largeDataset} config={config} />);
    await waitForChartRender();
    const renderTime = performance.now() - startTime;

    expect(renderTime).toBeLessThan(500); // 500ms threshold
  });

  it('manages memory efficiently', () => {
    const initialMemory = getMemoryUsage();

    const { unmount } = render(<EnhancedD3Chart data={mockData} />);
    unmount();

    // Force garbage collection if available
    if (global.gc) global.gc();

    const finalMemory = getMemoryUsage();
    const memoryIncrease = finalMemory - initialMemory;

    expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024); // 10MB limit
  });
});
```

## End-to-End Testing

### Playwright Test Suite

**Comprehensive E2E Testing**: 7 specialized test files covering all aspects

#### Chart Interactions Testing

```typescript
// tests/e2e/data-visualization/chart-interactions.spec.ts
test('E2E-VIZ-001: Chart Loading and Initial Render', async ({ page }) => {
  await page.goto('/');
  await fillFinancialData(page);
  await navigateToVisualization(page);

  // Verify charts load correctly
  await expect(page.locator('svg')).toBeVisible({ timeout: 10000 });

  // Verify data points are rendered
  const dataPoints = await page.locator('svg circle.data-point').count();
  expect(dataPoints).toBeGreaterThan(0);

  // Measure performance
  const renderTime = await measureChartRenderTime(page);
  expect(renderTime).toBeLessThan(2000); // 2 second threshold
});
```

#### Mobile Testing

```typescript
// tests/e2e/data-visualization/mobile-chart-interactions.spec.ts
test('E2E-MOBILE-VIZ-001: Touch Interactions', async ({ page }) => {
  await page.setViewportSize({ width: 375, height: 667 }); // iPhone size

  await setupMobileTest(page);

  // Test tap interactions
  const dataPoint = page.locator('svg circle').first();
  await dataPoint.tap();

  // Verify tooltip appears
  await expect(page.locator('.tooltip')).toBeVisible();

  // Test swipe gestures
  await performSwipeGesture(page, 'right');
  await verifyChartPanning(page);
});
```

#### Performance Testing

```typescript
// tests/e2e/data-visualization/chart-performance.spec.ts
test('E2E-PERF-VIZ-001: Chart Rendering Performance', async ({ page }) => {
  await setupPerformanceTest(page);

  // Measure initial load time
  const loadTime = await measureChartLoadTime(page);
  expect(loadTime).toBeLessThan(2000);

  // Measure update performance
  const updateTime = await measureChartUpdateTime(page);
  expect(updateTime).toBeLessThan(500);

  // Verify memory usage
  const memoryUsage = await getMemoryUsage(page);
  expect(memoryUsage).toBeLessThan(100 * 1024 * 1024); // 100MB
});
```

#### Accessibility Testing

```typescript
// tests/e2e/data-visualization/chart-accessibility.spec.ts
test('E2E-A11Y-VIZ-001: ARIA Labels and Roles', async ({ page }) => {
  await setupAccessibilityTest(page);

  // Verify chart ARIA labels
  const charts = page.locator('svg[aria-label]');
  const chartCount = await charts.count();
  expect(chartCount).toBeGreaterThan(0);

  // Test keyboard navigation
  await page.keyboard.press('Tab');
  const focusedElement = page.locator(':focus');
  await expect(focusedElement).toBeVisible();

  // Verify screen reader compatibility
  await verifyScreenReaderSupport(page);
});
```

### Test Configuration

**Playwright Configuration**: Multi-browser and device testing

```typescript
// playwright.config.visualization.ts
export default defineConfig({
  projects: [
    // Desktop browsers
    {
      name: 'chromium-desktop',
      use: { ...devices['Desktop Chrome'] },
      testMatch: ['chart-interactions.spec.ts', 'chart-performance.spec.ts'],
    },
    {
      name: 'firefox-desktop',
      use: { ...devices['Desktop Firefox'] },
      testMatch: ['chart-interactions.spec.ts'],
    },
    {
      name: 'webkit-desktop',
      use: { ...devices['Desktop Safari'] },
      testMatch: ['chart-interactions.spec.ts'],
    },

    // Mobile devices
    {
      name: 'mobile-chrome',
      use: { ...devices['Pixel 5'] },
      testMatch: ['mobile-chart-interactions.spec.ts'],
    },
    {
      name: 'mobile-safari',
      use: { ...devices['iPhone 12'] },
      testMatch: ['mobile-chart-interactions.spec.ts'],
    },

    // Specialized testing
    {
      name: 'accessibility-chromium',
      use: { ...devices['Desktop Chrome'] },
      testMatch: ['chart-accessibility.spec.ts'],
    },
    {
      name: 'performance-chromium',
      use: {
        ...devices['Desktop Chrome'],
        launchOptions: {
          args: ['--enable-precise-memory-info'],
        },
      },
      testMatch: ['chart-performance.spec.ts'],
    },
  ],
});
```

## Performance Testing

### Performance Thresholds

```{list-table} Performance Requirements
:header-rows: 1
:widths: 30 20 50

* - Metric
  - Threshold
  - Description
* - Chart Load Time
  - <2000ms
  - Initial chart rendering time
* - Chart Update Time
  - <500ms
  - Time to update chart data
* - Export Time
  - <3000ms
  - Time to generate export files
* - Memory Usage
  - <100MB
  - JavaScript heap size limit
* - Frame Rate
  - >30fps
  - Animation smoothness requirement
* - Interaction Latency
  - <100ms
  - User interaction response time
```

### Performance Monitoring

**Real-time Performance Tracking**:

```typescript
class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];

  measureRenderTime(component: string): number {
    const startTime = performance.now();
    // Render operation
    const endTime = performance.now();

    const renderTime = endTime - startTime;
    this.recordMetric('renderTime', renderTime, 500); // 500ms threshold

    return renderTime;
  }

  measureMemoryUsage(): number {
    const memory = (performance as any).memory;
    if (memory) {
      const usedMB = memory.usedJSHeapSize / 1024 / 1024;
      this.recordMetric('memoryUsage', usedMB, 50); // 50MB threshold
      return usedMB;
    }
    return 0;
  }

  measureFrameRate(duration: number = 1000): Promise<number> {
    return new Promise(resolve => {
      let frameCount = 0;
      const startTime = performance.now();

      function countFrame() {
        frameCount++;
        const elapsed = performance.now() - startTime;

        if (elapsed < duration) {
          requestAnimationFrame(countFrame);
        } else {
          const fps = Math.round((frameCount * 1000) / elapsed);
          resolve(fps);
        }
      }

      requestAnimationFrame(countFrame);
    });
  }
}
```

## Accessibility Testing

### WCAG 2.1 AA Compliance

**Automated Accessibility Testing**:

```typescript
import { injectAxe, checkA11y } from 'axe-playwright';

test('Accessibility compliance', async ({ page }) => {
  await page.goto('/visualization');
  await injectAxe(page);

  // Check for WCAG violations
  await checkA11y(page, null, {
    detailedReport: true,
    detailedReportOptions: { html: true },
  });
});
```

**Manual Accessibility Tests**:

```{list-table} Accessibility Test Checklist
:header-rows: 1
:widths: 40 60

* - Test Category
  - Validation Points
* - Keyboard Navigation
  - Tab order, focus indicators, keyboard shortcuts
* - Screen Reader
  - ARIA labels, alternative text, semantic markup
* - Color Contrast
  - 4.5:1 ratio, color independence, high contrast mode
* - Motor Accessibility
  - Touch targets (44px min), gesture alternatives
* - Cognitive Accessibility
  - Clear labels, consistent patterns, error prevention
```

### Screen Reader Testing

**Screen Reader Compatibility**:

```typescript
test('Screen reader compatibility', async ({ page }) => {
  // Enable screen reader simulation
  await page.addInitScript(() => {
    window.speechSynthesis = {
      speak: utterance => console.log('Screen reader:', utterance.text),
      cancel: () => {},
      pause: () => {},
      resume: () => {},
    };
  });

  await page.goto('/visualization');

  // Verify ARIA announcements
  const chart = page.locator('svg[aria-label]');
  const ariaLabel = await chart.getAttribute('aria-label');
  expect(ariaLabel).toMatch(/chart|graph|visualization/i);

  // Test data table alternative
  const dataTable = page.locator('table[aria-label*="chart data"]');
  if (await dataTable.isVisible()) {
    const headers = await dataTable.locator('th').count();
    expect(headers).toBeGreaterThan(0);
  }
});
```

## Cross-Browser Testing

### Browser Compatibility Matrix

```{list-table} Supported Browsers
:header-rows: 1
:widths: 25 25 50

* - Browser
  - Minimum Version
  - Tested Features
* - Chrome
  - 90+
  - All features, WebGL, Performance APIs
* - Firefox
  - 88+
  - Core features, SVG rendering
* - Safari
  - 14+
  - Core features, Touch events
* - Edge
  - 90+
  - All features, Performance APIs
* - Mobile Chrome
  - 90+
  - Touch interactions, Mobile optimization
* - Mobile Safari
  - 14+
  - Touch interactions, iOS gestures
```

### Browser-Specific Testing

```typescript
test.describe('Cross-browser compatibility', () => {
  ['chromium', 'firefox', 'webkit'].forEach(browserName => {
    test(`Chart rendering in ${browserName}`, async ({ page }) => {
      // Browser-specific performance thresholds
      const thresholds = {
        chromium: { loadTime: 1500, updateTime: 400 },
        firefox: { loadTime: 2000, updateTime: 500 },
        webkit: { loadTime: 2500, updateTime: 600 },
      };

      const threshold = thresholds[browserName];

      await setupBrowserTest(page);

      const loadTime = await measureLoadTime(page);
      expect(loadTime).toBeLessThan(threshold.loadTime);

      const updateTime = await measureUpdateTime(page);
      expect(updateTime).toBeLessThan(threshold.updateTime);
    });
  });
});
```

## Test Execution

### Running Tests

**Command Line Interface**:

```bash
# Run all visualization tests
npm run test:visualization

# Run specific test categories
npm run test:visualization:smoke      # Quick validation tests
npm run test:visualization:regression # Core functionality tests
npm run test:visualization:performance # Performance validation
npm run test:visualization:accessibility # WCAG compliance tests
npm run test:visualization:mobile     # Mobile-specific tests

# Run with specific browser
npm run test:visualization -- --project=chromium-desktop

# Run in debug mode
npm run test:visualization -- --debug --headed

# Generate reports
npm run test:visualization -- --reporter=html
```

**Test Runner Script**:

```bash
# Professional test runner with advanced options
./scripts/run-visualization-tests.sh -m performance -b chromium -d desktop
./scripts/run-visualization-tests.sh -m accessibility -h
./scripts/run-visualization-tests.sh -m mobile -b webkit -d mobile
```

### Continuous Integration

**GitHub Actions Integration**:

```yaml
name: Data Visualization Tests
on: [push, pull_request]

jobs:
  visualization-tests:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        browser: [chromium, firefox, webkit]
        test-type: [smoke, regression, performance]

    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Install dependencies
        run: npm ci

      - name: Install Playwright browsers
        run: npx playwright install

      - name: Run visualization tests
        run: |
          npm run test:visualization -- \
            --project=${{ matrix.browser }}-desktop \
            --grep="${{ matrix.test-type }}"

      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: test-results-${{ matrix.browser }}-${{ matrix.test-type }}
          path: test-results/
```

## Test Reporting

### Comprehensive Reports

**HTML Report Generation**:

- Interactive test results with screenshots
- Performance metrics and trends
- Accessibility compliance status
- Cross-browser compatibility matrix
- Mobile testing results

**JSON Report Structure**:

```json
{
  "timestamp": "2024-12-19T10:30:00Z",
  "summary": {
    "total": 32,
    "passed": 31,
    "failed": 1,
    "skipped": 0,
    "duration": 45000
  },
  "coverage": {
    "chartInteractions": 100,
    "mobileSupport": 95,
    "accessibility": 100,
    "performance": 90,
    "crossBrowser": 85
  },
  "performance": {
    "averageLoadTime": 1200,
    "averageRenderTime": 350,
    "memoryUsage": 45,
    "frameRate": 58
  }
}
```

### Quality Gates

**Automated Quality Checks**:

- **Performance Gates**: All metrics must meet thresholds
- **Accessibility Gates**: Zero WCAG violations allowed
- **Coverage Gates**: 95% minimum test coverage required
- **Browser Gates**: All supported browsers must pass
- **Mobile Gates**: Touch interactions must work correctly

---

## Test Maintenance

### Test Data Management

**Mock Data Generation**:

```typescript
export const generateMockFinancialData = (months: number = 12) => {
  const data = [];
  const startDate = new Date();
  startDate.setMonth(startDate.getMonth() - months);

  for (let i = 0; i < months; i++) {
    const date = new Date(startDate);
    date.setMonth(date.getMonth() + i);

    data.push({
      date,
      netWorth: 100000 + i * 5000 + Math.random() * 10000,
      monthlyIncome: 8000 + Math.random() * 1000,
      monthlyExpenses: 5000 + Math.random() * 500,
      savingsRate: 30 + Math.random() * 10,
      fireProgress: (i / months) * 100,
    });
  }

  return data;
};
```

**Test Environment Setup**:

```typescript
export const setupTestEnvironment = async (page: Page) => {
  // Set up test data
  await page.addInitScript(() => {
    localStorage.setItem('test_mode', 'true');
    localStorage.setItem(
      'mock_data',
      JSON.stringify(generateMockFinancialData())
    );
  });

  // Mock external APIs
  await page.route('**/api/economic-data', route => {
    route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({ inflation: 2.1, interestRate: 1.5 }),
    });
  });
};
```

### Test Debugging

**Debug Utilities**:

```typescript
export const debugChart = async (page: Page) => {
  // Capture chart state
  const chartState = await page.evaluate(() => {
    const svg = document.querySelector('svg');
    return {
      dataPoints: svg?.querySelectorAll('circle').length || 0,
      paths: svg?.querySelectorAll('path').length || 0,
      dimensions: svg?.getBoundingClientRect(),
    };
  });

  console.log('Chart Debug Info:', chartState);

  // Take screenshot for visual debugging
  await page.screenshot({
    path: `debug-chart-${Date.now()}.png`,
    fullPage: true,
  });
};
```

_This comprehensive testing strategy ensures Swiss Budget Pro's data visualization system meets the highest standards of quality, performance, and accessibility across all platforms and devices._
