# 🚀 Local CI/CD Pipeline for Swiss Budget Pro

## Overview

This local CI/CD pipeline provides comprehensive error detection and prevention without relying on external services. It catches issues early and provides immediate feedback during development.

## 🛠️ Setup

### 1. Install Dependencies
```bash
npm install
```

### 2. Setup Git Hooks (Recommended)
```bash
npm run setup-hooks
```

### 3. Start File Watcher
```bash
npm run ci:watch
```

## 🔄 Pipeline Stages

### Stage 1: Quick Syntax Check ⚡
- **TypeScript Compilation**: Catches type errors and syntax issues
- **ESLint Validation**: Enforces code quality and catches potential bugs
- **Duration**: ~5-10 seconds

### Stage 2: Code Quality 📏
- **Code Formatting**: Ensures consistent style with Prettier
- **Issue Detection**: Custom script to catch Swiss Budget Pro specific issues
- **Duration**: ~3-5 seconds

### Stage 3: Fast Tests ⚡
- **Unit Tests**: Runs core business logic tests
- **Component Tests**: Validates React component behavior
- **Duration**: ~10-15 seconds

### Stage 4: Build Verification 🏗️
- **Production Build**: Ensures code compiles for production
- **Bundle Analysis**: Checks for build issues
- **Duration**: ~15-20 seconds

## 📋 Available Commands

### Manual Pipeline Execution
```bash
npm run ci              # Run complete pipeline once
npm run ci:quick        # Quick syntax check only
npm run health-check    # Quick validation + issue detection
```

### File Watching
```bash
npm run ci:watch        # Start file watcher (recommended for development)
```

### Individual Checks
```bash
npm run lint            # ESLint check
npm run lint:fix        # Auto-fix ESLint issues
npm run type-check      # TypeScript compilation check
npm run format          # Format code with Prettier
npm run format:check    # Check code formatting
npm run detect-issues   # Run custom issue detection
npm run validate        # Run all quick validations
```

### Git Integration
```bash
npm run setup-hooks     # Install Git hooks
```

## 🔍 Issue Detection

The pipeline includes advanced issue detection specifically for Swiss Budget Pro:

### Critical Issues (Block Commits)
- **Duplicate Exports**: Multiple default exports causing compilation errors
- **Orphaned JSX**: Code after export statements
- **Missing Imports**: Required imports not present
- **Circular Dependencies**: Import cycles

### Warnings (Don't Block)
- **Large Files**: Files over 5000 lines
- **Unused Imports**: Imports that aren't used
- **Naming Conventions**: Component naming issues
- **React Patterns**: Missing keys, inline styles

## 🎯 Real-Time Development

### File Watcher Features
- **Automatic Triggering**: Runs on file changes
- **Debouncing**: Prevents excessive runs (2-second delay)
- **Smart Filtering**: Only watches relevant files (.ts, .tsx, .js, .jsx, .json)
- **Exclusions**: Ignores node_modules, dist, coverage

### VS Code Integration
The pipeline works seamlessly with VS Code:
- **Error Lens**: Shows errors inline
- **ESLint Extension**: Real-time linting
- **Prettier Extension**: Auto-formatting on save
- **TypeScript**: Immediate type checking

## 🚨 Git Hooks

### Pre-commit Hook
Runs before each commit:
```bash
✅ ESLint validation
✅ TypeScript compilation
✅ Code formatting check
✅ Custom issue detection
```

### Pre-push Hook
Runs before pushing to remote:
```bash
✅ Full test suite
✅ Production build verification
```

### Commit Message Hook
Validates commit message format:
```
feat(ui): add dark mode toggle
fix(calc): correct tax calculation
docs: update README
```

## 🔧 Troubleshooting

### Common Issues

#### Pipeline Fails on TypeScript Errors
```bash
npm run type-check  # See detailed errors
```

#### Linting Errors
```bash
npm run lint:fix    # Auto-fix most issues
```

#### Formatting Issues
```bash
npm run format      # Auto-format all files
```

#### Build Failures
```bash
npm run build       # See detailed build errors
```

### Bypassing Hooks (Emergency Only)
```bash
git commit --no-verify    # Skip pre-commit hook
git push --no-verify      # Skip pre-push hook
```

## 📊 Performance

### Typical Pipeline Times
- **Quick Check**: 8-15 seconds
- **Full Pipeline**: 30-45 seconds
- **File Watcher**: 5-10 seconds per change

### Optimization Tips
- Use `npm run ci:quick` for rapid feedback
- Keep the file watcher running during development
- Fix issues as they appear rather than batching

## 🎉 Benefits

### Error Prevention
- **Catches 95%+ of compilation errors** before they reach production
- **Prevents orphaned code** like the recent JSX issue
- **Validates imports/exports** automatically

### Developer Experience
- **Immediate feedback** on file changes
- **Consistent code quality** across the team
- **Automated formatting** and linting

### Reliability
- **Local execution** - no external dependencies
- **Fast feedback loops** - issues caught in seconds
- **Comprehensive coverage** - syntax, types, tests, build

## 🚀 Getting Started

1. **Start the watcher**: `npm run ci:watch`
2. **Make changes** to your code
3. **See immediate feedback** in the terminal
4. **Fix issues** as they appear
5. **Commit with confidence** knowing code is validated

The pipeline will guide you through any issues and provide specific commands to fix them!
