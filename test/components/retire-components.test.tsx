import '@testing-library/jest-dom';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, expect, it, vi } from 'vitest';

// Mock i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
    i18n: {
      language: 'en',
      changeLanguage: vi.fn().mockResolvedValue(undefined),
    },
  }),
}));

// Mock D3
vi.mock('d3', () => {
  const mockSelection = {
    selectAll: vi.fn().mockReturnThis(),
    append: vi.fn().mockReturnThis(),
    attr: vi.fn().mockReturnThis(),
    style: vi.fn().mockReturnThis(),
    text: vi.fn().mockReturnThis(),
    data: vi.fn().mockReturnThis(),
    enter: vi.fn().mockReturnThis(),
    exit: vi.fn().mockReturnThis(),
    call: vi.fn().mockReturnThis(),
    datum: vi.fn().mockReturnThis(),
    remove: vi.fn().mockReturnThis(),
    on: vi.fn().mockReturnThis(),
    transition: vi.fn().mockReturnThis(),
    duration: vi.fn().mockReturnThis(),
    delay: vi.fn().mockReturnThis(),
    attrTween: vi.fn().mockReturnThis(),
  };

  const mockAxis = {
    tickFormat: vi.fn().mockReturnThis(),
    ticks: vi.fn().mockReturnThis(),
    tickSize: vi.fn().mockReturnThis(),
  };

  const mockPieFunction = vi.fn(() => []);
  const mockPieChain = {
    value: vi.fn().mockReturnThis(),
    sort: vi.fn(() => mockPieFunction),
  };
  const mockPie = {
    value: vi.fn(() => mockPieChain),
    sort: vi.fn(() => mockPieFunction),
  };

  const mockArc = {
    innerRadius: vi.fn().mockReturnThis(),
    outerRadius: vi.fn().mockReturnThis(),
  };

  return {
    select: vi.fn(() => mockSelection),
    scaleTime: vi.fn(() => ({
      domain: vi.fn().mockReturnThis(),
      range: vi.fn().mockReturnThis(),
    })),
    scaleLinear: vi.fn(() => ({
      domain: vi.fn().mockReturnThis(),
      range: vi.fn().mockReturnThis(),
    })),
    line: vi.fn(() => ({
      x: vi.fn().mockReturnThis(),
      y: vi.fn().mockReturnThis(),
      curve: vi.fn().mockReturnThis(),
    })),
    axisBottom: vi.fn(() => mockAxis),
    axisLeft: vi.fn(() => mockAxis),
    timeFormat: vi.fn(() => vi.fn()),
    extent: vi.fn(() => [new Date('2023-01-01'), new Date('2023-12-31')]),
    max: vi.fn(() => 10000),
    pie: vi.fn(() => mockPie),
    arc: vi.fn(() => mockArc),
    curveMonotoneX: {},
    interpolate: vi.fn(() => vi.fn()),
  };
});

// Mock useNumberFormat hook
vi.mock('../src/hooks/useNumberFormat', () => ({
  default: () => ({
    formatCurrency: (amount: number) => `CHF ${amount.toLocaleString()}`,
    formatNumber: (num: number) => num.toLocaleString(),
    formatPercentage: (num: number) => `${num.toFixed(1)}%`,
  }),
}));

// Import components after mocking
import { BudgetDonutChart, formatCurrency, HistoricalChart, LanguageSwitcher } from '../../retire';

describe('LanguageSwitcher Component', () => {
  const user = userEvent.setup();

  it('should render with default language', () => {
    render(<LanguageSwitcher />);

    expect(screen.getByText('English')).toBeInTheDocument();
    expect(screen.getByText('🇬🇧')).toBeInTheDocument();
  });

  it('should toggle dropdown when clicked', async () => {
    render(<LanguageSwitcher />);

    const button = screen.getByRole('button');
    await user.click(button);

    // Should show dropdown with language options
    expect(screen.getByText('Deutsch')).toBeInTheDocument();
    expect(screen.getByText('🇩🇪')).toBeInTheDocument();
  });

  it('should handle language change', async () => {
    render(<LanguageSwitcher />);

    const button = screen.getByRole('button');
    await user.click(button);

    // Just check that the dropdown appears and we can click on options
    const germanOption = screen.getByText('Deutsch');
    expect(germanOption).toBeInTheDocument();

    await user.click(germanOption);

    // Since the mock doesn't actually change language, just verify no errors
    expect(button).toBeInTheDocument();
  });

  it('should apply dark mode styles', () => {
    render(<LanguageSwitcher darkMode={true} />);

    const button = screen.getByRole('button');
    expect(button).toHaveClass('bg-gray-700');
  });

  it('should apply light mode styles', () => {
    render(<LanguageSwitcher darkMode={false} />);

    const button = screen.getByRole('button');
    expect(button).toHaveClass('bg-white');
  });

  it('should apply custom className', () => {
    render(<LanguageSwitcher className="custom-class" />);

    const container = screen.getByRole('button').parentElement;
    expect(container).toHaveClass('custom-class');
  });

  it('should handle language change errors gracefully', async () => {
    render(<LanguageSwitcher />);

    const button = screen.getByRole('button');
    await user.click(button);

    const germanOption = screen.getByText('Deutsch');
    await user.click(germanOption);

    // Just verify the component doesn't crash
    expect(button).toBeInTheDocument();
  });
});

describe('HistoricalChart Component', () => {
  const mockFormatCurrency = vi.fn((amount) => `CHF ${amount.toLocaleString()}`);

  const mockSnapshots = [
    {
      timestamp: '2023-01-01T00:00:00.000Z',
      totalMonthlyIncome: 8000,
      totalExpenses: 5000,
      totalSavings: 3000,
      savingsRate: 37.5,
      netWorth: 100000,
      fireProgress: 25,
    },
    {
      timestamp: '2023-02-01T00:00:00.000Z',
      totalMonthlyIncome: 8200,
      totalExpenses: 5100,
      totalSavings: 3100,
      savingsRate: 37.8,
      netWorth: 103000,
      fireProgress: 26,
    },
  ];

  it('should render chart with valid data', () => {
    render(
      <HistoricalChart
        darkMode={false}
        snapshots={mockSnapshots}
        formatCurrency={mockFormatCurrency}
      />,
    );

    // Should render SVG element
    const svg = document.querySelector('svg');
    expect(svg).toBeInTheDocument();
  });

  it('should handle empty snapshots', () => {
    render(
      <HistoricalChart
        darkMode={false}
        snapshots={[]}
        formatCurrency={mockFormatCurrency}
      />,
    );

    // Should not crash and should clear any existing chart
    expect(document.querySelector('svg')).toBeInTheDocument();
  });

  it('should handle insufficient data points', () => {
    const singleSnapshot = [mockSnapshots[0]];

    render(
      <HistoricalChart
        darkMode={false}
        snapshots={singleSnapshot}
        formatCurrency={mockFormatCurrency}
      />,
    );

    // Should handle gracefully with less than 2 data points
    expect(document.querySelector('svg')).toBeInTheDocument();
  });

  it('should apply dark mode styles', () => {
    render(
      <HistoricalChart
        darkMode={true}
        snapshots={mockSnapshots}
        formatCurrency={mockFormatCurrency}
      />,
    );

    // Chart should render (specific styling tested via D3 mocks)
    expect(document.querySelector('svg')).toBeInTheDocument();
  });
});

describe('BudgetDonutChart Component', () => {
  const defaultProps = {
    darkMode: false,
    totalExpenses: 5000,
    totalSavings: 3000,
    totalMonthlyIncome: 8000,
    essentialExpenses: 3000,
    nonEssentialExpenses: 2000,
    remaining: 0,
    savingsRate: 37.5,
  };

  it('should render chart with valid data', () => {
    render(<BudgetDonutChart {...defaultProps} />);

    // Should render SVG element
    const svg = document.querySelector('svg');
    expect(svg).toBeInTheDocument();
  });

  it('should handle zero income gracefully', () => {
    render(<BudgetDonutChart {...defaultProps} totalMonthlyIncome={0} />);

    // Should not crash with zero income
    const svg = document.querySelector('svg');
    expect(svg).toBeInTheDocument();
  });

  it('should apply dark mode styles', () => {
    render(<BudgetDonutChart {...defaultProps} darkMode={true} />);

    // Chart should render with dark mode
    const svg = document.querySelector('svg');
    expect(svg).toBeInTheDocument();
  });

  it('should handle negative remaining amount', () => {
    render(<BudgetDonutChart {...defaultProps} remaining={-500} />);

    // Should handle negative remaining gracefully
    const svg = document.querySelector('svg');
    expect(svg).toBeInTheDocument();
  });

  it('should filter out zero value segments', () => {
    render(
      <BudgetDonutChart
        {...defaultProps}
        essentialExpenses={0}
        nonEssentialExpenses={0}
        totalSavings={0}
        remaining={0}
      />,
    );

    // Should handle all zero values
    const svg = document.querySelector('svg');
    expect(svg).toBeInTheDocument();
  });
});

describe('formatCurrency utility', () => {
  it('should format positive numbers correctly', () => {
    // Test that the function returns a string with CHF and proper formatting
    const result1000 = formatCurrency(1000);
    expect(result1000).toMatch(/CHF.*1.*000/);
    expect(result1000).toContain('CHF');
    expect(result1000).toContain('1');
    expect(result1000).toContain('000');

    const result1234 = formatCurrency(1234.56);
    expect(result1234).toMatch(/CHF.*1.*235/);

    const resultZero = formatCurrency(0);
    expect(resultZero).toMatch(/CHF.*0/);
    expect(resultZero).toContain('CHF');
    expect(resultZero).toContain('0');
  });

  it('should handle negative numbers', () => {
    const resultNegative = formatCurrency(-1000);
    expect(resultNegative).toMatch(/CHF.*-.*1.*000/);
    expect(resultNegative).toContain('CHF');
    expect(resultNegative).toContain('-');
  });

  it('should handle invalid inputs', () => {
    const resultNull = formatCurrency(null);
    expect(resultNull).toMatch(/CHF.*0/);
    expect(resultNull).toContain('CHF');
    expect(resultNull).toContain('0');

    const resultUndefined = formatCurrency(undefined);
    expect(resultUndefined).toMatch(/CHF.*0/);

    const resultNaN = formatCurrency(NaN);
    expect(resultNaN).toMatch(/CHF.*0/);

    const resultInvalid = formatCurrency('invalid' as any);
    expect(resultInvalid).toMatch(/CHF.*0/);
  });

  it('should handle very large numbers', () => {
    const resultLarge = formatCurrency(1000000);
    expect(resultLarge).toMatch(/CHF.*1.*000.*000/);
    expect(resultLarge).toContain('CHF');
    expect(resultLarge).toContain('1');

    const resultVeryLarge = formatCurrency(1234567890);
    expect(resultVeryLarge).toMatch(/CHF.*1.*234.*567.*890/);
  });

  it('should round to nearest integer', () => {
    const result1234_4 = formatCurrency(1234.4);
    expect(result1234_4).toMatch(/CHF.*1.*234/);

    const result1234_6 = formatCurrency(1234.6);
    expect(result1234_6).toMatch(/CHF.*1.*235/);
  });
});
