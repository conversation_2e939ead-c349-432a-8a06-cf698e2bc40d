/**
 * Error Diagnostics Utility for Swiss Budget Pro
 * Helps diagnose and fix common runtime errors
 */

import { debugLogger } from './debug-logger';

export interface DiagnosticResult {
  issue: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  possibleCauses: string[];
  suggestedFixes: string[];
  affectedFeatures: string[];
  debugInfo?: Record<string, any>;
}

export class ErrorDiagnostics {
  private static instance: ErrorDiagnostics;

  public static getInstance(): ErrorDiagnostics {
    if (!ErrorDiagnostics.instance) {
      ErrorDiagnostics.instance = new ErrorDiagnostics();
    }
    return ErrorDiagnostics.instance;
  }

  /**
   * Diagnose common runtime errors
   */
  public diagnoseError(error: Error | string): DiagnosticResult[] {
    const errorMessage = typeof error === 'string' ? error : error.message;
    const errorStack = typeof error === 'string' ? undefined : error.stack;

    debugLogger.log('info', 'diagnostics', 'Running error diagnostics', {
      errorMessage,
      errorStack: errorStack?.substring(0, 500), // Truncate for logging
    });

    const results: DiagnosticResult[] = [];

    // Check for specific error patterns
    results.push(...this.checkReferenceErrors(errorMessage, errorStack));
    results.push(...this.checkTailwindErrors(errorMessage));
    results.push(...this.checkModuleErrors(errorMessage));
    results.push(...this.checkNetworkErrors(errorMessage));
    results.push(...this.checkLocalStorageErrors(errorMessage));
    results.push(...this.checkReactErrors(errorMessage, errorStack));

    return results;
  }

  /**
   * Run comprehensive system diagnostics
   */
  public runSystemDiagnostics(): DiagnosticResult[] {
    debugLogger.log('info', 'diagnostics', 'Running system diagnostics');

    const results: DiagnosticResult[] = [];

    // Check browser compatibility
    results.push(...this.checkBrowserCompatibility());

    // Check localStorage availability
    results.push(...this.checkLocalStorageHealth());

    // Check network connectivity
    results.push(...this.checkNetworkHealth());

    // Check performance issues
    results.push(...this.checkPerformanceIssues());

    // Check console errors
    results.push(...this.checkConsoleErrors());

    return results;
  }

  private checkReferenceErrors(
    message: string,
    stack?: string,
  ): DiagnosticResult[] {
    const results: DiagnosticResult[] = [];

    if (
      message.includes('is not defined') ||
      message.includes('ReferenceError')
    ) {
      const undefinedVar = this.extractUndefinedVariable(message);

      results.push({
        issue: 'Undefined Variable Reference',
        severity: 'critical',
        description: `Variable "${undefinedVar}" is being used but not defined`,
        possibleCauses: [
          'Variable declared after usage',
          'Typo in variable name',
          'Missing import statement',
          'Scope issue (variable not accessible)',
          'Module not loaded properly',
        ],
        suggestedFixes: [
          'Check variable spelling and case sensitivity',
          'Ensure variable is declared before usage',
          'Add missing import statement',
          'Check if variable is in correct scope',
          'Verify module loading order',
        ],
        affectedFeatures: ['Application initialization', 'Component rendering'],
        debugInfo: {
          undefinedVariable: undefinedVar,
          stackTrace: stack?.split('\n').slice(0, 5),
        },
      });
    }

    return results;
  }

  private checkTailwindErrors(message: string): DiagnosticResult[] {
    const results: DiagnosticResult[] = [];

    if (message.includes('Tailwind') || message.includes('tailwindcss')) {
      results.push({
        issue: 'Tailwind CSS Configuration Issue',
        severity: 'medium',
        description: 'Tailwind CSS is not loading or configured properly',
        possibleCauses: [
          'Tailwind CSS not installed',
          'Missing Tailwind configuration',
          'CSS not imported properly',
          'PostCSS configuration issue',
        ],
        suggestedFixes: [
          'Verify Tailwind CSS installation: npm list tailwindcss',
          'Check tailwind.config.js exists and is valid',
          'Ensure CSS imports include Tailwind directives',
          'Restart development server',
          'Clear build cache',
        ],
        affectedFeatures: ['UI styling', 'Component appearance'],
        debugInfo: {
          tailwindConfigExists: this.checkFileExists('tailwind.config.js'),
          cssImports: this.checkCSSImports(),
        },
      });
    }

    return results;
  }

  private checkModuleErrors(message: string): DiagnosticResult[] {
    const results: DiagnosticResult[] = [];

    if (
      message.includes('Cannot resolve module') ||
      message.includes('Module not found')
    ) {
      const moduleName = this.extractModuleName(message);

      results.push({
        issue: 'Module Resolution Error',
        severity: 'high',
        description: `Module "${moduleName}" cannot be found or loaded`,
        possibleCauses: [
          'Module not installed',
          'Incorrect import path',
          'Typo in module name',
          'Module version incompatibility',
          'Node modules corruption',
        ],
        suggestedFixes: [
          `Install missing module: npm install ${moduleName}`,
          'Check import path spelling and case',
          'Verify module exists in package.json',
          'Delete node_modules and reinstall: rm -rf node_modules && npm install',
          'Check for version conflicts',
        ],
        affectedFeatures: ['Module functionality', 'Component imports'],
        debugInfo: {
          moduleName,
          packageJsonExists: this.checkFileExists('package.json'),
          nodeModulesExists: this.checkFileExists('node_modules'),
        },
      });
    }

    return results;
  }

  private checkNetworkErrors(message: string): DiagnosticResult[] {
    const results: DiagnosticResult[] = [];

    if (
      message.includes('fetch') ||
      message.includes('network') ||
      message.includes('CORS')
    ) {
      results.push({
        issue: 'Network Request Error',
        severity: 'high',
        description: 'Network requests are failing',
        possibleCauses: [
          'Server not running',
          'CORS policy blocking request',
          'Network connectivity issue',
          'Incorrect API endpoint',
          'Authentication failure',
        ],
        suggestedFixes: [
          'Check if development server is running',
          'Verify API endpoint URLs',
          'Check network connectivity',
          'Review CORS configuration',
          'Check authentication tokens',
        ],
        affectedFeatures: ['Data fetching', 'API communication'],
        debugInfo: {
          onlineStatus: navigator.onLine,
          currentUrl: window.location.href,
          userAgent: navigator.userAgent,
        },
      });
    }

    return results;
  }

  private checkLocalStorageErrors(message: string): DiagnosticResult[] {
    const results: DiagnosticResult[] = [];

    if (
      message.includes('localStorage') ||
      message.includes('QuotaExceededError')
    ) {
      results.push({
        issue: 'Local Storage Error',
        severity: 'medium',
        description: 'Local storage is not available or quota exceeded',
        possibleCauses: [
          'Browser in private/incognito mode',
          'Local storage quota exceeded',
          'Local storage disabled',
          'Browser security settings',
          'Corrupted local storage data',
        ],
        suggestedFixes: [
          'Clear browser local storage',
          'Exit private/incognito mode',
          'Enable local storage in browser settings',
          'Clear application data',
          'Use alternative storage method',
        ],
        affectedFeatures: ['Data persistence', 'User preferences'],
        debugInfo: {
          localStorageAvailable: this.isLocalStorageAvailable(),
          localStorageUsage: this.getLocalStorageUsage(),
          isPrivateMode: this.isPrivateMode(),
        },
      });
    }

    return results;
  }

  private checkReactErrors(
    message: string,
    stack?: string,
  ): DiagnosticResult[] {
    const results: DiagnosticResult[] = [];

    if (message.includes('React') || stack?.includes('react')) {
      if (
        message.includes('Hook') ||
        message.includes('useState') ||
        message.includes('useEffect')
      ) {
        results.push({
          issue: 'React Hooks Error',
          severity: 'high',
          description: 'React hooks are being used incorrectly',
          possibleCauses: [
            'Hooks called outside component',
            'Hooks called conditionally',
            'Hooks called in wrong order',
            'Component not properly defined',
          ],
          suggestedFixes: [
            'Ensure hooks are called at top level of component',
            'Remove conditional hook calls',
            'Check component function definition',
            'Review React hooks rules',
          ],
          affectedFeatures: ['Component state', 'Component lifecycle'],
          debugInfo: {
            reactVersion: this.getReactVersion(),
            stackTrace: stack?.split('\n').slice(0, 3),
          },
        });
      }
    }

    return results;
  }

  private checkBrowserCompatibility(): DiagnosticResult[] {
    const results: DiagnosticResult[] = [];
    const userAgent = navigator.userAgent;

    // Check for old browsers
    if (userAgent.includes('MSIE') || userAgent.includes('Trident')) {
      results.push({
        issue: 'Unsupported Browser',
        severity: 'critical',
        description: 'Internet Explorer is not supported',
        possibleCauses: ['Using Internet Explorer browser'],
        suggestedFixes: [
          'Use a modern browser (Chrome, Firefox, Safari, Edge)',
          'Update to latest browser version',
        ],
        affectedFeatures: ['All application features'],
        debugInfo: { userAgent },
      });
    }

    return results;
  }

  private checkLocalStorageHealth(): DiagnosticResult[] {
    const results: DiagnosticResult[] = [];

    if (!this.isLocalStorageAvailable()) {
      results.push({
        issue: 'Local Storage Unavailable',
        severity: 'high',
        description: 'Local storage is not available in this browser',
        possibleCauses: [
          'Private/incognito browsing mode',
          'Browser security settings',
          'Local storage disabled',
        ],
        suggestedFixes: [
          'Exit private browsing mode',
          'Enable local storage in browser settings',
          'Use a different browser',
        ],
        affectedFeatures: ['Data persistence', 'Settings storage'],
        debugInfo: {
          isPrivateMode: this.isPrivateMode(),
          storageQuota: this.getLocalStorageUsage(),
        },
      });
    }

    return results;
  }

  private checkNetworkHealth(): DiagnosticResult[] {
    const results: DiagnosticResult[] = [];

    if (!navigator.onLine) {
      results.push({
        issue: 'Network Offline',
        severity: 'medium',
        description: 'No network connectivity detected',
        possibleCauses: [
          'Internet connection lost',
          'Network adapter disabled',
        ],
        suggestedFixes: [
          'Check internet connection',
          'Restart network adapter',
          'Contact network administrator',
        ],
        affectedFeatures: ['External data loading', 'API calls'],
        debugInfo: {
          onlineStatus: navigator.onLine,
          connectionType: (navigator as any).connection?.effectiveType,
        },
      });
    }

    return results;
  }

  private checkPerformanceIssues(): DiagnosticResult[] {
    const results: DiagnosticResult[] = [];

    if (
      performance.memory &&
      (performance as any).memory.usedJSHeapSize > 50 * 1024 * 1024
    ) {
      results.push({
        issue: 'High Memory Usage',
        severity: 'medium',
        description: 'Application is using excessive memory',
        possibleCauses: [
          'Memory leaks',
          'Large data sets',
          'Inefficient code',
          'Too many components',
        ],
        suggestedFixes: [
          'Refresh the page',
          'Close other browser tabs',
          'Clear browser cache',
          'Report performance issue',
        ],
        affectedFeatures: ['Application performance'],
        debugInfo: {
          memoryUsage: (performance as any).memory,
          performanceTiming: performance.timing,
        },
      });
    }

    return results;
  }

  private checkConsoleErrors(): DiagnosticResult[] {
    // This would require capturing console errors over time
    // For now, return empty array
    return [];
  }

  // Helper methods
  private extractUndefinedVariable(message: string): string {
    const match =
      message.match(/'([^']+)' is not defined/) ||
      message.match(/(\w+) is not defined/);
    return match ? match[1] : 'unknown';
  }

  private extractModuleName(message: string): string {
    const match =
      message.match(/Cannot resolve module '([^']+)'/) ||
      message.match(/Module not found: Error: Can't resolve '([^']+)'/);
    return match ? match[1] : 'unknown';
  }

  private checkFileExists(filename: string): boolean {
    // This is a placeholder - in a real implementation, you'd need a way to check file existence
    return true;
  }

  private checkCSSImports(): string[] {
    const stylesheets = Array.from(document.styleSheets);
    return stylesheets.map(sheet => sheet.href || 'inline').filter(Boolean);
  }

  private isLocalStorageAvailable(): boolean {
    try {
      const test = '__localStorage_test__';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch {
      return false;
    }
  }

  private getLocalStorageUsage(): { used: number; total: number } | null {
    try {
      let used = 0;
      for (const key in localStorage) {
        if (localStorage.hasOwnProperty(key)) {
          used += localStorage[key].length + key.length;
        }
      }
      return { used, total: 5 * 1024 * 1024 }; // 5MB typical limit
    } catch {
      return null;
    }
  }

  private isPrivateMode(): boolean {
    try {
      const test = '__private_mode_test__';
      sessionStorage.setItem(test, test);
      sessionStorage.removeItem(test);
      return false;
    } catch {
      return true;
    }
  }

  private getReactVersion(): string {
    try {
      return (React as any).version || 'unknown';
    } catch {
      return 'not available';
    }
  }
}

// Export singleton instance
export const errorDiagnostics = ErrorDiagnostics.getInstance();

// Global access for debugging
if (typeof window !== 'undefined') {
  (window as any).errorDiagnostics = errorDiagnostics;
}
