/**
 * Swiss Healthcare Cost Optimization Engine
 * Comprehensive healthcare cost calculation and optimization for Swiss residents
 */

import { CantonCode } from './swiss-tax-calculations';

// Core Types and Interfaces
export type HealthStatus = 'excellent' | 'good' | 'fair' | 'poor';
export type RiskTolerance = 'low' | 'medium' | 'high';
export type InsuranceModel = 'standard' | 'hmo' | 'family_doctor' | 'telmed';
export type CoverageType =
  | 'basic'
  | 'semi_private'
  | 'private'
  | 'dental'
  | 'alternative';
export type RecommendationLevel = 'optimal' | 'acceptable' | 'not_recommended';

export interface ChronicCondition {
  name: string;
  severity: 'mild' | 'moderate' | 'severe';
  annualCost: number;
  medicationRequired: boolean;
}

export interface RiskFactor {
  factor: string;
  multiplier: number; // Risk multiplier for medical expenses
  description: string;
}

export interface UserHealthProfile {
  age: number;
  canton: CantonCode;
  income: number;
  healthStatus: HealthStatus;
  chronicConditions: ChronicCondition[];
  historicalExpenses: number[];
  riskFactors: RiskFactor[];
  familySize: number;
  hasChildren: boolean;
  expectedMedicalExpenses: number;
  riskTolerance: RiskTolerance;
}

export interface HealthInsurer {
  id: string;
  name: string;
  rating: number; // Customer satisfaction rating 1-5
  marketShare: number;
  financialStability: 'A' | 'B' | 'C';
  claimProcessingSpeed: number; // Days
  customerServiceRating: number;
  digitalServices: boolean;
}

export interface PremiumData {
  insurerId: string;
  canton: CantonCode;
  ageGroup: '0-18' | '19-25' | '26+';
  deductible: 300 | 500 | 1000 | 1500 | 2000 | 2500;
  model: InsuranceModel;
  monthlyPremium: number;
  effectiveDate: Date;
  lastUpdated: Date;
}

export interface SupplementaryPlan {
  insurerId: string;
  planType: CoverageType;
  monthlyPremium: number;
  coverage: {
    hospitalPrivate: boolean;
    hospitalSemiPrivate: boolean;
    dental: boolean;
    alternativeMedicine: boolean;
    abroad: boolean;
    glasses: boolean;
  };
  waitingPeriods: number; // months
  exclusions: string[];
  deductible: number;
}

export interface InsuranceRecommendation {
  rank: number;
  insurerId: string;
  insurerName: string;
  planConfiguration: {
    deductible: number;
    model: InsuranceModel;
    supplementaryPlans: string[];
  };
  costs: {
    monthlyPremium: number;
    expectedOutOfPocket: number;
    totalAnnualCost: number;
    potentialSavings: number;
    taxDeductibleAmount: number;
  };
  qualityMetrics: {
    customerSatisfaction: number;
    claimProcessingSpeed: number;
    financialStability: string;
  };
  pros: string[];
  cons: string[];
  switchingEffort: 'easy' | 'moderate' | 'complex';
  confidenceLevel: number;
}

export interface DeductibleAnalysis {
  deductible: number;
  monthlyPremiumSavings: number;
  expectedOutOfPocket: number;
  totalExpectedCost: number;
  riskLevel: 'low' | 'medium' | 'high';
  recommendation: RecommendationLevel;
  breakEvenPoint: number; // Annual medical expenses where this deductible becomes optimal
  probabilityOptimal: number; // Probability this is the optimal choice
}

export interface DeductibleOptimization {
  userProfile: UserHealthProfile;
  deductibleAnalysis: DeductibleAnalysis[];
  optimalStrategy: {
    recommendedDeductible: number;
    expectedAnnualSavings: number;
    confidenceLevel: number;
    reasoning: string[];
    riskAssessment: string;
  };
}

export interface HealthcareFIREProjection {
  ageAtFIRE: number;
  yearsUntilAHV: number;
  projectedAnnualCosts: {
    year: number;
    age: number;
    premiums: number;
    outOfPocket: number;
    subsidyEligible: boolean;
    subsidyAmount: number;
    netCost: number;
    inflationAdjusted: boolean;
  }[];
  totalHealthcareFIRENumber: number;
  averageAnnualCost: number;
  subsidyOptimizationPotential: number;
}

// Swiss Health Insurance Database
export const SWISS_HEALTH_INSURERS: HealthInsurer[] = [
  {
    id: 'css',
    name: 'CSS Versicherung',
    rating: 4.2,
    marketShare: 15.8,
    financialStability: 'A',
    claimProcessingSpeed: 12,
    customerServiceRating: 4.1,
    digitalServices: true,
  },
  {
    id: 'swica',
    name: 'Swica',
    rating: 4.4,
    marketShare: 12.3,
    financialStability: 'A',
    claimProcessingSpeed: 10,
    customerServiceRating: 4.3,
    digitalServices: true,
  },
  {
    id: 'helsana',
    name: 'Helsana',
    rating: 4.0,
    marketShare: 14.2,
    financialStability: 'A',
    claimProcessingSpeed: 14,
    customerServiceRating: 3.9,
    digitalServices: true,
  },
  {
    id: 'concordia',
    name: 'Concordia',
    rating: 4.1,
    marketShare: 8.7,
    financialStability: 'A',
    claimProcessingSpeed: 11,
    customerServiceRating: 4.0,
    digitalServices: true,
  },
  {
    id: 'sanitas',
    name: 'Sanitas',
    rating: 3.9,
    marketShare: 9.4,
    financialStability: 'A',
    claimProcessingSpeed: 13,
    customerServiceRating: 3.8,
    digitalServices: true,
  },
  {
    id: 'assura',
    name: 'Assura',
    rating: 3.7,
    marketShare: 11.2,
    financialStability: 'B',
    claimProcessingSpeed: 15,
    customerServiceRating: 3.6,
    digitalServices: false,
  },
  // Add more insurers as needed
];

// Deductible options available in Switzerland
export const AVAILABLE_DEDUCTIBLES = [
  300, 500, 1000, 1500, 2000, 2500,
] as const;

// Insurance models and their characteristics
export const INSURANCE_MODELS = {
  standard: {
    name: 'Standard Model',
    description: 'Free choice of doctor and hospital',
    premiumDiscount: 0,
    restrictions: [],
  },
  hmo: {
    name: 'HMO Model',
    description: 'Health Maintenance Organization',
    premiumDiscount: 0.15, // 15% discount
    restrictions: [
      'Must visit HMO center first',
      'Referral required for specialists',
    ],
  },
  family_doctor: {
    name: 'Family Doctor Model',
    description: 'Must consult family doctor first',
    premiumDiscount: 0.1, // 10% discount
    restrictions: [
      'Must visit family doctor first',
      'Referral required for specialists',
    ],
  },
  telmed: {
    name: 'Telmed Model',
    description: 'Telephone consultation first',
    premiumDiscount: 0.08, // 8% discount
    restrictions: ['Must call hotline first', 'Emergency exceptions apply'],
  },
};

// Healthcare Cost Optimization Engine
export class HealthcareOptimizationEngine {
  /**
   * Calculate expected annual medical expenses based on health profile
   */
  calculateExpectedMedicalExpenses(profile: UserHealthProfile): number {
    let baseExpenses = 0;

    // Age-based baseline
    if (profile.age < 30) baseExpenses = 800;
    else if (profile.age < 50) baseExpenses = 1200;
    else if (profile.age < 65) baseExpenses = 1800;
    else baseExpenses = 2500;

    // Health status multiplier
    const healthMultipliers = {
      excellent: 0.7,
      good: 1.0,
      fair: 1.5,
      poor: 2.2,
    };
    baseExpenses *= healthMultipliers[profile.healthStatus];

    // Add chronic condition costs
    const chronicCosts = profile.chronicConditions.reduce(
      (total, condition) => total + condition.annualCost,
      0
    );

    // Apply risk factors
    const riskMultiplier = profile.riskFactors.reduce(
      (multiplier, factor) => multiplier * factor.multiplier,
      1.0
    );

    // Family size adjustment
    const familyAdjustment = profile.familySize > 1 ? 1.2 : 1.0;

    const totalExpected =
      (baseExpenses + chronicCosts) * riskMultiplier * familyAdjustment;

    // Use historical data if available
    if (profile.historicalExpenses.length > 0) {
      const historicalAverage =
        profile.historicalExpenses.reduce((sum, exp) => sum + exp, 0) /
        profile.historicalExpenses.length;
      // Weight historical data 70%, calculated 30%
      return historicalAverage * 0.7 + totalExpected * 0.3;
    }

    return Math.round(totalExpected);
  }

  /**
   * Optimize deductible selection based on health profile
   */
  optimizeDeductible(
    profile: UserHealthProfile,
    premiumData: PremiumData[]
  ): DeductibleOptimization {
    const expectedExpenses = this.calculateExpectedMedicalExpenses(profile);
    const analyses: DeductibleAnalysis[] = [];

    // Get premium data for user's canton and age group
    const userPremiums = premiumData.filter(
      p =>
        p.canton === profile.canton &&
        this.getAgeGroup(profile.age) === p.ageGroup
    );

    for (const deductible of AVAILABLE_DEDUCTIBLES) {
      const premiumForDeductible = userPremiums.find(
        p => p.deductible === deductible
      );
      if (!premiumForDeductible) continue;

      const monthlyPremium = premiumForDeductible.monthlyPremium;
      const annualPremium = monthlyPremium * 12;

      // Calculate expected out-of-pocket costs
      const expectedOutOfPocket = Math.min(
        Math.max(0, expectedExpenses - deductible) * 0.1 + // 10% co-pay after deductible
          Math.min(deductible, expectedExpenses), // Up to deductible amount
        deductible + 700 // Maximum out-of-pocket per year
      );

      const totalExpectedCost = annualPremium + expectedOutOfPocket;

      // Calculate savings compared to lowest deductible
      const lowestDeductiblePremium = userPremiums.find(
        p => p.deductible === 300
      );
      const lowestDeductibleCost = lowestDeductiblePremium
        ? lowestDeductiblePremium.monthlyPremium * 12 +
          Math.min(300, expectedExpenses) +
          Math.max(0, expectedExpenses - 300) * 0.1
        : 0;

      const monthlyPremiumSavings = lowestDeductiblePremium
        ? lowestDeductiblePremium.monthlyPremium - monthlyPremium
        : 0;

      // Risk assessment
      const riskLevel = this.assessDeductibleRisk(
        deductible,
        expectedExpenses,
        profile.riskTolerance
      );

      // Break-even analysis
      const breakEvenPoint = this.calculateBreakEvenPoint(
        deductible,
        monthlyPremiumSavings * 12
      );

      // Recommendation logic
      const recommendation = this.getDeductibleRecommendation(
        totalExpectedCost,
        lowestDeductibleCost,
        riskLevel,
        profile.riskTolerance
      );

      analyses.push({
        deductible,
        monthlyPremiumSavings,
        expectedOutOfPocket,
        totalExpectedCost,
        riskLevel,
        recommendation,
        breakEvenPoint,
        probabilityOptimal: this.calculateOptimalProbability(
          deductible,
          expectedExpenses
        ),
      });
    }

    // Find optimal strategy
    const optimalAnalysis = analyses
      .filter(a => a.recommendation !== 'not_recommended')
      .sort((a, b) => a.totalExpectedCost - b.totalExpectedCost)[0];

    const expectedSavings =
      analyses.find(a => a.deductible === 300)?.totalExpectedCost -
        optimalAnalysis?.totalExpectedCost || 0;

    return {
      userProfile: profile,
      deductibleAnalysis: analyses,
      optimalStrategy: {
        recommendedDeductible: optimalAnalysis?.deductible || 300,
        expectedAnnualSavings: Math.max(0, expectedSavings),
        confidenceLevel: optimalAnalysis?.probabilityOptimal || 0.5,
        reasoning: this.generateRecommendationReasoning(
          optimalAnalysis,
          profile
        ),
        riskAssessment: this.generateRiskAssessment(optimalAnalysis, profile),
      },
    };
  }

  /**
   * Generate insurance recommendations based on user profile
   */
  generateInsuranceRecommendations(
    profile: UserHealthProfile,
    premiumData: PremiumData[],
    supplementaryPlans: SupplementaryPlan[] = []
  ): InsuranceRecommendation[] {
    const recommendations: InsuranceRecommendation[] = [];
    const expectedExpenses = this.calculateExpectedMedicalExpenses(profile);
    const optimalDeductible = this.optimizeDeductible(profile, premiumData);

    // Get relevant premium data
    const userPremiums = premiumData.filter(
      p =>
        p.canton === profile.canton &&
        this.getAgeGroup(profile.age) === p.ageGroup
    );

    // Group by insurer and model
    const insurerModels = new Map<string, PremiumData[]>();
    userPremiums.forEach(premium => {
      const key = `${premium.insurerId}-${premium.model}`;
      if (!insurerModels.has(key)) {
        insurerModels.set(key, []);
      }
      insurerModels.get(key)!.push(premium);
    });

    let rank = 1;
    for (const [key, premiums] of Array.from(insurerModels.entries())) {
      const [insurerId, model] = key.split('-');
      const insurer = SWISS_HEALTH_INSURERS.find(i => i.id === insurerId);
      if (!insurer) continue;

      // Use optimal deductible for this insurer/model combination
      const optimalPremium =
        premiums.find(
          p =>
            p.deductible ===
            optimalDeductible.optimalStrategy.recommendedDeductible
        ) || premiums[0];

      const monthlyPremium = optimalPremium.monthlyPremium;
      const annualPremium = monthlyPremium * 12;

      // Calculate expected out-of-pocket
      const expectedOutOfPocket = this.calculateExpectedOutOfPocket(
        expectedExpenses,
        optimalPremium.deductible
      );

      const totalAnnualCost = annualPremium + expectedOutOfPocket;

      // Calculate potential savings vs. most expensive option
      const maxCost = Math.max(
        ...Array.from(insurerModels.values())
          .flat()
          .map(p => p.monthlyPremium * 12)
      );
      const potentialSavings = maxCost - annualPremium;

      // Tax deductible amount (premiums are tax deductible in Switzerland)
      const taxDeductibleAmount = annualPremium;

      recommendations.push({
        rank: rank++,
        insurerId,
        insurerName: insurer.name,
        planConfiguration: {
          deductible: optimalPremium.deductible,
          model: optimalPremium.model as InsuranceModel,
          supplementaryPlans: [], // TODO: Add supplementary plan optimization
        },
        costs: {
          monthlyPremium,
          expectedOutOfPocket,
          totalAnnualCost,
          potentialSavings,
          taxDeductibleAmount,
        },
        qualityMetrics: {
          customerSatisfaction: insurer.rating,
          claimProcessingSpeed: insurer.claimProcessingSpeed,
          financialStability: insurer.financialStability,
        },
        pros: this.generatePros(
          insurer,
          model as InsuranceModel,
          potentialSavings
        ),
        cons: this.generateCons(insurer, model as InsuranceModel),
        switchingEffort: this.assessSwitchingEffort(
          insurer,
          model as InsuranceModel
        ),
        confidenceLevel: this.calculateRecommendationConfidence(
          insurer,
          totalAnnualCost
        ),
      });
    }

    // Sort by total annual cost and quality metrics
    return recommendations
      .sort((a, b) => {
        const costDiff = a.costs.totalAnnualCost - b.costs.totalAnnualCost;
        if (Math.abs(costDiff) < 200) {
          // If costs are similar, prioritize quality
          return (
            b.qualityMetrics.customerSatisfaction -
            a.qualityMetrics.customerSatisfaction
          );
        }
        return costDiff;
      })
      .map((rec, index) => ({ ...rec, rank: index + 1 }));
  }

  // Helper methods
  private getAgeGroup(age: number): '0-18' | '19-25' | '26+' {
    if (age <= 18) return '0-18';
    if (age <= 25) return '19-25';
    return '26+';
  }

  private assessDeductibleRisk(
    deductible: number,
    expectedExpenses: number,
    riskTolerance: RiskTolerance
  ): 'low' | 'medium' | 'high' {
    const riskRatio = deductible / expectedExpenses;

    if (riskTolerance === 'low') {
      return riskRatio > 2 ? 'high' : riskRatio > 1 ? 'medium' : 'low';
    } else if (riskTolerance === 'medium') {
      return riskRatio > 3 ? 'high' : riskRatio > 1.5 ? 'medium' : 'low';
    } else {
      return riskRatio > 4 ? 'high' : riskRatio > 2 ? 'medium' : 'low';
    }
  }

  private calculateBreakEvenPoint(
    deductible: number,
    annualPremiumSavings: number
  ): number {
    // Break-even point is where higher deductible becomes cost-effective
    return deductible + annualPremiumSavings / 0.1; // Assuming 10% co-pay
  }

  private calculateOptimalProbability(
    deductible: number,
    expectedExpenses: number
  ): number {
    // Simplified probability calculation based on expected expenses
    const ratio = expectedExpenses / deductible;
    if (ratio < 0.5) return 0.8; // Low expenses, high deductible likely optimal
    if (ratio < 1) return 0.6;
    if (ratio < 2) return 0.4;
    return 0.2; // High expenses, low deductible likely optimal
  }

  private getDeductibleRecommendation(
    totalCost: number,
    lowestDeductibleCost: number,
    riskLevel: 'low' | 'medium' | 'high',
    riskTolerance: RiskTolerance
  ): RecommendationLevel {
    const savings = lowestDeductibleCost - totalCost;

    if (savings > 500 && (riskLevel === 'low' || riskTolerance === 'high')) {
      return 'optimal';
    } else if (savings > 200 && riskLevel !== 'high') {
      return 'acceptable';
    } else if (riskLevel === 'high' && riskTolerance === 'low') {
      return 'not_recommended';
    }

    return 'acceptable';
  }

  private calculateExpectedOutOfPocket(
    expectedExpenses: number,
    deductible: number
  ): number {
    return Math.min(
      Math.max(0, expectedExpenses - deductible) * 0.1 +
        Math.min(deductible, expectedExpenses),
      deductible + 700 // Maximum out-of-pocket per year
    );
  }

  private generateRecommendationReasoning(
    analysis: DeductibleAnalysis | undefined,
    profile: UserHealthProfile
  ): string[] {
    if (!analysis) return ['Unable to generate recommendation'];

    const reasoning = [];

    if (analysis.recommendation === 'optimal') {
      reasoning.push(
        `Optimal choice based on your expected medical expenses of CHF ${profile.expectedMedicalExpenses}`
      );
      reasoning.push(
        `Saves CHF ${analysis.monthlyPremiumSavings * 12} annually in premiums`
      );
    }

    if (analysis.riskLevel === 'low') {
      reasoning.push('Low financial risk given your health profile');
    } else if (analysis.riskLevel === 'high') {
      reasoning.push(
        'Higher financial risk - consider your emergency fund capacity'
      );
    }

    return reasoning;
  }

  private generateRiskAssessment(
    analysis: DeductibleAnalysis | undefined,
    profile: UserHealthProfile
  ): string {
    if (!analysis) return 'Unable to assess risk';

    if (analysis.riskLevel === 'low') {
      return 'Low risk strategy suitable for your health profile and risk tolerance';
    } else if (analysis.riskLevel === 'medium') {
      return 'Moderate risk - ensure you have adequate emergency savings';
    } else {
      return 'High risk strategy - only recommended if you have substantial emergency funds';
    }
  }

  private generatePros(
    insurer: HealthInsurer,
    model: InsuranceModel,
    savings: number
  ): string[] {
    const pros = [];

    if (savings > 1000) pros.push(`Save CHF ${Math.round(savings)} annually`);
    if (insurer.rating >= 4.0) pros.push('High customer satisfaction rating');
    if (insurer.claimProcessingSpeed <= 12) pros.push('Fast claim processing');
    if (insurer.digitalServices) pros.push('Modern digital services');
    if (model !== 'standard')
      pros.push(
        `${INSURANCE_MODELS[model].premiumDiscount * 100}% premium discount`
      );

    return pros;
  }

  private generateCons(
    insurer: HealthInsurer,
    model: InsuranceModel
  ): string[] {
    const cons = [];

    if (insurer.rating < 3.8) cons.push('Below average customer satisfaction');
    if (insurer.claimProcessingSpeed > 14) cons.push('Slower claim processing');
    if (!insurer.digitalServices) cons.push('Limited digital services');
    if (model !== 'standard') {
      cons.push(...INSURANCE_MODELS[model].restrictions);
    }

    return cons;
  }

  private assessSwitchingEffort(
    insurer: HealthInsurer,
    model: InsuranceModel
  ): 'easy' | 'moderate' | 'complex' {
    if (insurer.digitalServices && model === 'standard') return 'easy';
    if (model !== 'standard') return 'moderate';
    return 'easy';
  }

  private calculateRecommendationConfidence(
    insurer: HealthInsurer,
    totalCost: number
  ): number {
    let confidence = 0.5;

    if (insurer.rating >= 4.0) confidence += 0.2;
    if (insurer.financialStability === 'A') confidence += 0.2;
    if (insurer.claimProcessingSpeed <= 12) confidence += 0.1;

    return Math.min(confidence, 0.95);
  }
}

// Export singleton instance
export const healthcareOptimizer = new HealthcareOptimizationEngine();
