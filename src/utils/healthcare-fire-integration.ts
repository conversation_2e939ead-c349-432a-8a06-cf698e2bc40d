/**
 * Healthcare FIRE Integration Module
 * Integrates healthcare cost optimization with FIRE planning
 */

import { CantonCode } from './swiss-tax-calculations';
import {
  UserHealthProfile,
  HealthcareFIREProjection,
} from './healthcare-calculations';

export interface FIREGoals {
  targetFIREAge: number;
  currentAge: number;
  targetAnnualExpenses: number;
  currentSavings: number;
  monthlyContribution: number;
  expectedReturn: number;
  withdrawalRate: number;
  targetLocation?: CantonCode;
}

export interface HealthcareSubsidyEligibility {
  canton: CantonCode;
  incomeThresholds: {
    single: number;
    couple: number;
    family: number;
  };
  maxSubsidy: {
    single: number;
    couple: number;
    family: number;
  };
  subsidyFormula: (income: number, premiums: number) => number;
}

export interface HealthcareFIREAnalysis {
  currentHealthcareCosts: {
    annualPremiums: number;
    outOfPocketExpenses: number;
    supplementaryInsurance: number;
    totalAnnualCost: number;
    taxDeductibleAmount: number;
  };
  fireHealthcareProjections: HealthcareFIREProjection;
  optimizationStrategies: {
    cantonOptimization: {
      currentCanton: CantonCode;
      optimalCantons: {
        canton: CantonCode;
        annualSavings: number;
        qualityOfCare: number;
        costOfLiving: number;
      }[];
    };
    subsidyOptimization: {
      incomeThresholds: number[];
      subsidyAmounts: number[];
      optimizationStrategies: string[];
    };
    deductibleStrategy: {
      currentDeductible: number;
      optimalDeductible: number;
      annualSavings: number;
      riskAssessment: string;
    };
  };
  fireImpact: {
    additionalFIRENumber: number;
    delayInMonths: number;
    healthcareAsPercentageOfFIRE: number;
    recommendations: string[];
  };
}

// Swiss cantonal healthcare subsidy data
export const CANTONAL_HEALTHCARE_SUBSIDIES: Record<
  CantonCode,
  HealthcareSubsidyEligibility
> = {
  ZH: {
    canton: 'ZH',
    incomeThresholds: { single: 36000, couple: 54000, family: 72000 },
    maxSubsidy: { single: 4980, couple: 9960, family: 14940 },
    subsidyFormula: (income, premiums) =>
      Math.max(0, Math.min(premiums * 0.8, 4980 - (income - 36000) * 0.06)),
  },
  GE: {
    canton: 'GE',
    incomeThresholds: { single: 35000, couple: 52500, family: 70000 },
    maxSubsidy: { single: 5200, couple: 10400, family: 15600 },
    subsidyFormula: (income, premiums) =>
      Math.max(0, Math.min(premiums * 0.85, 5200 - (income - 35000) * 0.07)),
  },
  BS: {
    canton: 'BS',
    incomeThresholds: { single: 38000, couple: 57000, family: 76000 },
    maxSubsidy: { single: 4800, couple: 9600, family: 14400 },
    subsidyFormula: (income, premiums) =>
      Math.max(0, Math.min(premiums * 0.75, 4800 - (income - 38000) * 0.05)),
  },
  VD: {
    canton: 'VD',
    incomeThresholds: { single: 34000, couple: 51000, family: 68000 },
    maxSubsidy: { single: 5400, couple: 10800, family: 16200 },
    subsidyFormula: (income, premiums) =>
      Math.max(0, Math.min(premiums * 0.9, 5400 - (income - 34000) * 0.08)),
  },
  BE: {
    canton: 'BE',
    incomeThresholds: { single: 33000, couple: 49500, family: 66000 },
    maxSubsidy: { single: 4500, couple: 9000, family: 13500 },
    subsidyFormula: (income, premiums) =>
      Math.max(0, Math.min(premiums * 0.7, 4500 - (income - 33000) * 0.04)),
  },
  // Add more cantons as needed
  AG: {
    canton: 'AG',
    incomeThresholds: { single: 35000, couple: 52500, family: 70000 },
    maxSubsidy: { single: 4200, couple: 8400, family: 12600 },
    subsidyFormula: (income, premiums) =>
      Math.max(0, Math.min(premiums * 0.65, 4200 - (income - 35000) * 0.03)),
  },
  LU: {
    canton: 'LU',
    incomeThresholds: { single: 32000, couple: 48000, family: 64000 },
    maxSubsidy: { single: 3900, couple: 7800, family: 11700 },
    subsidyFormula: (income, premiums) =>
      Math.max(0, Math.min(premiums * 0.6, 3900 - (income - 32000) * 0.025)),
  },
  SG: {
    canton: 'SG',
    incomeThresholds: { single: 34000, couple: 51000, family: 68000 },
    maxSubsidy: { single: 4100, couple: 8200, family: 12300 },
    subsidyFormula: (income, premiums) =>
      Math.max(0, Math.min(premiums * 0.62, 4100 - (income - 34000) * 0.03)),
  },
  TI: {
    canton: 'TI',
    incomeThresholds: { single: 31000, couple: 46500, family: 62000 },
    maxSubsidy: { single: 4600, couple: 9200, family: 13800 },
    subsidyFormula: (income, premiums) =>
      Math.max(0, Math.min(premiums * 0.8, 4600 - (income - 31000) * 0.06)),
  },
  BL: {
    canton: 'BL',
    incomeThresholds: { single: 36000, couple: 54000, family: 72000 },
    maxSubsidy: { single: 4300, couple: 8600, family: 12900 },
    subsidyFormula: (income, premiums) =>
      Math.max(0, Math.min(premiums * 0.68, 4300 - (income - 36000) * 0.035)),
  },
  // Simplified entries for remaining cantons
  SO: {
    canton: 'SO',
    incomeThresholds: { single: 33000, couple: 49500, family: 66000 },
    maxSubsidy: { single: 3800, couple: 7600, family: 11400 },
    subsidyFormula: (income, premiums) =>
      Math.max(0, Math.min(premiums * 0.55, 3800 - (income - 33000) * 0.02)),
  },
  TG: {
    canton: 'TG',
    incomeThresholds: { single: 34000, couple: 51000, family: 68000 },
    maxSubsidy: { single: 3700, couple: 7400, family: 11100 },
    subsidyFormula: (income, premiums) =>
      Math.max(0, Math.min(premiums * 0.58, 3700 - (income - 34000) * 0.025)),
  },
  GR: {
    canton: 'GR',
    incomeThresholds: { single: 30000, couple: 45000, family: 60000 },
    maxSubsidy: { single: 4000, couple: 8000, family: 12000 },
    subsidyFormula: (income, premiums) =>
      Math.max(0, Math.min(premiums * 0.7, 4000 - (income - 30000) * 0.04)),
  },
  SH: {
    canton: 'SH',
    incomeThresholds: { single: 35000, couple: 52500, family: 70000 },
    maxSubsidy: { single: 3600, couple: 7200, family: 10800 },
    subsidyFormula: (income, premiums) =>
      Math.max(0, Math.min(premiums * 0.6, 3600 - (income - 35000) * 0.03)),
  },
  AR: {
    canton: 'AR',
    incomeThresholds: { single: 32000, couple: 48000, family: 64000 },
    maxSubsidy: { single: 3500, couple: 7000, family: 10500 },
    subsidyFormula: (income, premiums) =>
      Math.max(0, Math.min(premiums * 0.55, 3500 - (income - 32000) * 0.02)),
  },
  AI: {
    canton: 'AI',
    incomeThresholds: { single: 31000, couple: 46500, family: 62000 },
    maxSubsidy: { single: 3400, couple: 6800, family: 10200 },
    subsidyFormula: (income, premiums) =>
      Math.max(0, Math.min(premiums * 0.52, 3400 - (income - 31000) * 0.02)),
  },
  SZ: {
    canton: 'SZ',
    incomeThresholds: { single: 37000, couple: 55500, family: 74000 },
    maxSubsidy: { single: 3300, couple: 6600, family: 9900 },
    subsidyFormula: (income, premiums) =>
      Math.max(0, Math.min(premiums * 0.5, 3300 - (income - 37000) * 0.025)),
  },
  UR: {
    canton: 'UR',
    incomeThresholds: { single: 29000, couple: 43500, family: 58000 },
    maxSubsidy: { single: 3800, couple: 7600, family: 11400 },
    subsidyFormula: (income, premiums) =>
      Math.max(0, Math.min(premiums * 0.65, 3800 - (income - 29000) * 0.03)),
  },
  OW: {
    canton: 'OW',
    incomeThresholds: { single: 30000, couple: 45000, family: 60000 },
    maxSubsidy: { single: 3700, couple: 7400, family: 11100 },
    subsidyFormula: (income, premiums) =>
      Math.max(0, Math.min(premiums * 0.63, 3700 - (income - 30000) * 0.03)),
  },
  NW: {
    canton: 'NW',
    incomeThresholds: { single: 31000, couple: 46500, family: 62000 },
    maxSubsidy: { single: 3600, couple: 7200, family: 10800 },
    subsidyFormula: (income, premiums) =>
      Math.max(0, Math.min(premiums * 0.6, 3600 - (income - 31000) * 0.025)),
  },
  GL: {
    canton: 'GL',
    incomeThresholds: { single: 28000, couple: 42000, family: 56000 },
    maxSubsidy: { single: 3900, couple: 7800, family: 11700 },
    subsidyFormula: (income, premiums) =>
      Math.max(0, Math.min(premiums * 0.7, 3900 - (income - 28000) * 0.04)),
  },
  ZG: {
    canton: 'ZG',
    incomeThresholds: { single: 40000, couple: 60000, family: 80000 },
    maxSubsidy: { single: 3000, couple: 6000, family: 9000 },
    subsidyFormula: (income, premiums) =>
      Math.max(0, Math.min(premiums * 0.45, 3000 - (income - 40000) * 0.02)),
  },
  FR: {
    canton: 'FR',
    incomeThresholds: { single: 32000, couple: 48000, family: 64000 },
    maxSubsidy: { single: 4200, couple: 8400, family: 12600 },
    subsidyFormula: (income, premiums) =>
      Math.max(0, Math.min(premiums * 0.72, 4200 - (income - 32000) * 0.04)),
  },
  VS: {
    canton: 'VS',
    incomeThresholds: { single: 29000, couple: 43500, family: 58000 },
    maxSubsidy: { single: 4400, couple: 8800, family: 13200 },
    subsidyFormula: (income, premiums) =>
      Math.max(0, Math.min(premiums * 0.78, 4400 - (income - 29000) * 0.05)),
  },
  NE: {
    canton: 'NE',
    incomeThresholds: { single: 33000, couple: 49500, family: 66000 },
    maxSubsidy: { single: 4300, couple: 8600, family: 12900 },
    subsidyFormula: (income, premiums) =>
      Math.max(0, Math.min(premiums * 0.75, 4300 - (income - 33000) * 0.045)),
  },
  JU: {
    canton: 'JU',
    incomeThresholds: { single: 30000, couple: 45000, family: 60000 },
    maxSubsidy: { single: 4500, couple: 9000, family: 13500 },
    subsidyFormula: (income, premiums) =>
      Math.max(0, Math.min(premiums * 0.8, 4500 - (income - 30000) * 0.05)),
  },
};

// Healthcare inflation rates (historically higher than general inflation)
export const HEALTHCARE_INFLATION_RATE = 0.035; // 3.5% annually
export const GENERAL_INFLATION_RATE = 0.02; // 2% annually

export class HealthcareFIREIntegration {
  /**
   * Calculate comprehensive healthcare FIRE analysis
   */
  calculateHealthcareFIREAnalysis(
    healthProfile: UserHealthProfile,
    fireGoals: FIREGoals,
    currentHealthcareCosts: {
      annualPremiums: number;
      outOfPocketExpenses: number;
      supplementaryInsurance: number;
    },
  ): HealthcareFIREAnalysis {
    const totalCurrentCost =
      currentHealthcareCosts.annualPremiums +
      currentHealthcareCosts.outOfPocketExpenses +
      currentHealthcareCosts.supplementaryInsurance;

    // Project healthcare costs for FIRE period
    const fireProjections = this.projectHealthcareCostsForFIRE(
      healthProfile,
      fireGoals,
      currentHealthcareCosts,
    );

    // Calculate optimization strategies
    const optimizationStrategies = this.calculateOptimizationStrategies(
      healthProfile,
      fireGoals,
      currentHealthcareCosts,
    );

    // Calculate FIRE impact
    const fireImpact = this.calculateFIREImpact(fireProjections, fireGoals);

    return {
      currentHealthcareCosts: {
        ...currentHealthcareCosts,
        totalAnnualCost: totalCurrentCost,
        taxDeductibleAmount: currentHealthcareCosts.annualPremiums, // Premiums are tax deductible
      },
      fireHealthcareProjections: fireProjections,
      optimizationStrategies,
      fireImpact,
    };
  }

  /**
   * Project healthcare costs during FIRE period
   */
  private projectHealthcareCostsForFIRE(
    healthProfile: UserHealthProfile,
    fireGoals: FIREGoals,
    currentCosts: {
      annualPremiums: number;
      outOfPocketExpenses: number;
      supplementaryInsurance: number;
    },
  ): HealthcareFIREProjection {
    const yearsToFIRE = fireGoals.targetFIREAge - fireGoals.currentAge;
    const yearsUntilAHV = Math.max(0, 65 - fireGoals.targetFIREAge); // AHV starts at 65
    const projectionYears = Math.min(yearsUntilAHV, 30); // Project up to 30 years or until AHV

    const projectedCosts = [];
    let cumulativeHealthcareCost = 0;

    for (let year = 0; year < projectionYears; year++) {
      const age = fireGoals.targetFIREAge + year;
      const inflationMultiplier = Math.pow(
        1 + HEALTHCARE_INFLATION_RATE,
        yearsToFIRE + year,
      );

      // Age-based cost increases
      const ageMultiplier = this.calculateAgeMultiplier(age);

      // Base costs adjusted for inflation and age
      const basePremiums =
        currentCosts.annualPremiums * inflationMultiplier * ageMultiplier;
      const baseOutOfPocket =
        currentCosts.outOfPocketExpenses * inflationMultiplier * ageMultiplier;
      const baseSupplementary =
        currentCosts.supplementaryInsurance *
        inflationMultiplier *
        ageMultiplier;

      // Calculate subsidy eligibility based on FIRE income
      const fireIncome = fireGoals.currentSavings * fireGoals.withdrawalRate; // Simplified FIRE income
      const subsidyInfo = this.calculateSubsidyEligibility(
        fireIncome,
        basePremiums,
        healthProfile.canton,
        healthProfile.familySize,
      );

      const netPremiums = basePremiums - subsidyInfo.subsidyAmount;
      const totalNetCost = netPremiums + baseOutOfPocket + baseSupplementary;

      projectedCosts.push({
        year: year + 1,
        age,
        premiums: basePremiums,
        outOfPocket: baseOutOfPocket,
        subsidyEligible: subsidyInfo.eligible,
        subsidyAmount: subsidyInfo.subsidyAmount,
        netCost: totalNetCost,
        inflationAdjusted: true,
      });

      cumulativeHealthcareCost += totalNetCost;
    }

    const averageAnnualCost =
      projectedCosts.length > 0
        ? cumulativeHealthcareCost / projectedCosts.length
        : 0;

    // Calculate additional FIRE number needed for healthcare
    const totalHealthcareFIRENumber = cumulativeHealthcareCost;

    // Calculate subsidy optimization potential
    const subsidyOptimizationPotential =
      this.calculateSubsidyOptimizationPotential(
        fireGoals,
        healthProfile.canton,
        projectedCosts,
      );

    return {
      ageAtFIRE: fireGoals.targetFIREAge,
      yearsUntilAHV,
      projectedAnnualCosts: projectedCosts,
      totalHealthcareFIRENumber,
      averageAnnualCost,
      subsidyOptimizationPotential,
    };
  }

  /**
   * Calculate age-based healthcare cost multiplier
   */
  private calculateAgeMultiplier(age: number): number {
    if (age < 30) return 0.8;
    if (age < 40) return 0.9;
    if (age < 50) return 1.0;
    if (age < 60) return 1.2;
    if (age < 70) return 1.5;
    return 2.0;
  }

  /**
   * Calculate subsidy eligibility and amount
   */
  private calculateSubsidyEligibility(
    income: number,
    premiums: number,
    canton: CantonCode,
    familySize: number,
  ): { eligible: boolean; subsidyAmount: number } {
    const subsidyData = CANTONAL_HEALTHCARE_SUBSIDIES[canton];
    if (!subsidyData) return { eligible: false, subsidyAmount: 0 };

    const threshold =
      familySize === 1
        ? subsidyData.incomeThresholds.single
        : familySize === 2
          ? subsidyData.incomeThresholds.couple
          : subsidyData.incomeThresholds.family;

    if (income > threshold) {
      return { eligible: false, subsidyAmount: 0 };
    }

    const subsidyAmount = subsidyData.subsidyFormula(income, premiums);
    return {
      eligible: subsidyAmount > 0,
      subsidyAmount: Math.max(0, subsidyAmount),
    };
  }

  /**
   * Calculate optimization strategies
   */
  private calculateOptimizationStrategies(
    healthProfile: UserHealthProfile,
    fireGoals: FIREGoals,
    currentCosts: {
      annualPremiums: number;
      outOfPocketExpenses: number;
      supplementaryInsurance: number;
    },
  ) {
    // Canton optimization analysis
    const cantonOptimization = this.analyzeCantonOptimization(
      healthProfile,
      fireGoals,
    );

    // Subsidy optimization strategies
    const subsidyOptimization = this.analyzeSubsidyOptimization(
      fireGoals,
      healthProfile.canton,
    );

    // Deductible strategy for FIRE
    const deductibleStrategy = this.analyzeDeductibleStrategy(
      healthProfile,
      fireGoals,
    );

    return {
      cantonOptimization,
      subsidyOptimization,
      deductibleStrategy,
    };
  }

  /**
   * Analyze canton optimization opportunities
   */
  private analyzeCantonOptimization(
    healthProfile: UserHealthProfile,
    fireGoals: FIREGoals,
  ) {
    const currentCanton = healthProfile.canton;
    const optimalCantons = [];

    // Analyze top cantons for FIRE healthcare costs
    const topCantons: CantonCode[] = ['JU', 'VS', 'GR', 'TI', 'VD']; // Known for good subsidies

    for (const canton of topCantons) {
      if (canton === currentCanton) continue;

      const subsidyData = CANTONAL_HEALTHCARE_SUBSIDIES[canton];
      if (!subsidyData) continue;

      // Estimate savings potential
      const fireIncome = fireGoals.currentSavings * fireGoals.withdrawalRate;
      const currentSubsidy = this.calculateSubsidyEligibility(
        fireIncome,
        4800, // Average premium
        currentCanton,
        healthProfile.familySize,
      );
      const newSubsidy = this.calculateSubsidyEligibility(
        fireIncome,
        4800,
        canton,
        healthProfile.familySize,
      );

      const annualSavings =
        newSubsidy.subsidyAmount - currentSubsidy.subsidyAmount;

      if (annualSavings > 500) {
        optimalCantons.push({
          canton,
          annualSavings,
          qualityOfCare: this.getQualityOfCareRating(canton),
          costOfLiving: this.getCostOfLivingIndex(canton),
        });
      }
    }

    return {
      currentCanton,
      optimalCantons: optimalCantons.sort(
        (a, b) => b.annualSavings - a.annualSavings,
      ),
    };
  }

  /**
   * Analyze subsidy optimization strategies
   */
  private analyzeSubsidyOptimization(fireGoals: FIREGoals, canton: CantonCode) {
    const subsidyData = CANTONAL_HEALTHCARE_SUBSIDIES[canton];
    if (!subsidyData) {
      return {
        incomeThresholds: [],
        subsidyAmounts: [],
        optimizationStrategies: ['No subsidy data available for this canton'],
      };
    }

    const strategies = [];

    // Income threshold optimization
    const fireIncome = fireGoals.currentSavings * fireGoals.withdrawalRate;
    if (fireIncome > subsidyData.incomeThresholds.single * 1.1) {
      strategies.push(
        'Consider geographic arbitrage to a canton with higher income thresholds',
      );
    }

    if (fireIncome < subsidyData.incomeThresholds.single * 0.9) {
      strategies.push(
        'You may be eligible for significant healthcare premium subsidies',
      );
      strategies.push(
        'Consider timing of income realization to maximize subsidy eligibility',
      );
    }

    strategies.push('Monitor annual income to stay within subsidy thresholds');
    strategies.push(
      'Consider Roth conversion strategies to manage taxable income',
    );

    return {
      incomeThresholds: [
        subsidyData.incomeThresholds.single,
        subsidyData.incomeThresholds.couple,
        subsidyData.incomeThresholds.family,
      ],
      subsidyAmounts: [
        subsidyData.maxSubsidy.single,
        subsidyData.maxSubsidy.couple,
        subsidyData.maxSubsidy.family,
      ],
      optimizationStrategies: strategies,
    };
  }

  /**
   * Analyze deductible strategy for FIRE
   */
  private analyzeDeductibleStrategy(
    healthProfile: UserHealthProfile,
    fireGoals: FIREGoals,
  ) {
    // In FIRE, higher deductibles often make sense due to lower income and potential subsidies
    const currentDeductible = 1000; // Assume current deductible
    const optimalDeductible = 2500; // Higher deductible often optimal in FIRE

    const annualSavings = 1200; // Estimated savings from higher deductible

    return {
      currentDeductible,
      optimalDeductible,
      annualSavings,
      riskAssessment:
        'Higher deductible recommended for FIRE due to lower income and emergency fund availability',
    };
  }

  /**
   * Calculate FIRE impact of healthcare costs
   */
  private calculateFIREImpact(
    projections: HealthcareFIREProjection,
    fireGoals: FIREGoals,
  ) {
    const additionalFIRENumber =
      projections.totalHealthcareFIRENumber / fireGoals.withdrawalRate;
    const healthcareAsPercentageOfFIRE =
      (projections.averageAnnualCost / fireGoals.targetAnnualExpenses) * 100;

    // Estimate delay in months (simplified calculation)
    const delayInMonths = Math.round(
      (additionalFIRENumber / fireGoals.monthlyContribution) * 12,
    );

    const recommendations = [];

    if (healthcareAsPercentageOfFIRE > 20) {
      recommendations.push(
        'Healthcare costs represent a significant portion of your FIRE budget',
      );
      recommendations.push(
        'Consider geographic arbitrage to reduce healthcare costs',
      );
    }

    if (projections.subsidyOptimizationPotential > 2000) {
      recommendations.push(
        'Significant subsidy optimization potential available',
      );
      recommendations.push(
        'Plan income strategies to maximize healthcare subsidies',
      );
    }

    if (delayInMonths > 12) {
      recommendations.push('Healthcare costs may delay FIRE by over a year');
      recommendations.push('Focus on healthcare cost optimization strategies');
    }

    return {
      additionalFIRENumber,
      delayInMonths,
      healthcareAsPercentageOfFIRE,
      recommendations,
    };
  }

  /**
   * Calculate subsidy optimization potential
   */
  private calculateSubsidyOptimizationPotential(
    fireGoals: FIREGoals,
    canton: CantonCode,
    projectedCosts: any[],
  ): number {
    // Calculate potential savings from optimal subsidy strategies
    const currentSubsidies = projectedCosts.reduce(
      (sum, cost) => sum + cost.subsidyAmount,
      0,
    );
    const maxPossibleSubsidies = projectedCosts.length * 4000; // Estimated max subsidy per year

    return Math.max(0, maxPossibleSubsidies - currentSubsidies);
  }

  /**
   * Get quality of care rating for canton (simplified)
   */
  private getQualityOfCareRating(canton: CantonCode): number {
    const ratings: Record<CantonCode, number> = {
      ZH: 4.5,
      GE: 4.3,
      BS: 4.4,
      VD: 4.1,
      BE: 4.0,
      AG: 3.9,
      LU: 3.8,
      SG: 3.7,
      TI: 3.9,
      BL: 3.8,
      SO: 3.7,
      TG: 3.6,
      GR: 3.8,
      SH: 3.7,
      AR: 3.6,
      AI: 3.5,
      SZ: 3.8,
      UR: 3.6,
      OW: 3.5,
      NW: 3.5,
      GL: 3.6,
      ZG: 4.2,
      FR: 3.8,
      VS: 3.7,
      NE: 3.9,
      JU: 3.8,
    };
    return ratings[canton] || 3.5;
  }

  /**
   * Get cost of living index for canton (simplified)
   */
  private getCostOfLivingIndex(canton: CantonCode): number {
    const indices: Record<CantonCode, number> = {
      ZH: 120,
      GE: 115,
      BS: 110,
      VD: 105,
      BE: 100,
      AG: 105,
      LU: 95,
      SG: 90,
      TI: 95,
      BL: 100,
      SO: 95,
      TG: 90,
      GR: 100,
      SH: 95,
      AR: 85,
      AI: 85,
      SZ: 110,
      UR: 90,
      OW: 85,
      NW: 90,
      GL: 90,
      ZG: 125,
      FR: 95,
      VS: 90,
      NE: 95,
      JU: 85,
    };
    return indices[canton] || 100;
  }
}

// Export singleton instance
export const healthcareFIREIntegration = new HealthcareFIREIntegration();
