/**
 * Swiss Social Insurance Calculations
 * Comprehensive calculations for AHV/IV/EO, ALV, and other social insurance contributions
 */

export interface SocialInsuranceRates {
  ahv: {
    employee: number;
    employer: number;
    total: number;
    maxIncome: number;
  };
  alv: {
    employee: number;
    employer: number;
    total: number;
    maxIncome: number;
    additionalRate: number;
    additionalThreshold: number;
  };
  nbu: {
    employee: number;
    employer: number;
    total: number;
  };
  familyAllowances: {
    rate: number;
    employerOnly: boolean;
  };
}

export interface PensionFundRates {
  coordinationDeduction: number;
  minInsuredSalary: number;
  maxInsuredSalary: number;
  ageGroups: {
    age25to34: { employee: number; employer: number };
    age35to44: { employee: number; employer: number };
    age45to54: { employee: number; employer: number };
    age55to65: { employee: number; employer: number };
  };
  riskPremiums: {
    disability: number;
    death: number;
  };
  administrativeCosts: number;
}

export interface SocialInsuranceResult {
  ahv: {
    employee: number;
    employer: number;
    total: number;
  };
  alv: {
    employee: number;
    employer: number;
    total: number;
    additional: number;
  };
  nbu: {
    employee: number;
    employer: number;
    total: number;
  };
  pensionFund: {
    employee: number;
    employer: number;
    total: number;
    insuredSalary: number;
  };
  familyAllowances: {
    employer: number;
  };
  totalEmployee: number;
  totalEmployer: number;
  totalContributions: number;
  netSalary: number;
}

// 2024 Swiss Social Insurance Rates
export const SOCIAL_INSURANCE_RATES_2024: SocialInsuranceRates = {
  ahv: {
    employee: 4.35, // AHV/IV/EO employee rate
    employer: 4.35, // AHV/IV/EO employer rate
    total: 8.7,
    maxIncome: Infinity, // No maximum for AHV
  },
  alv: {
    employee: 1.1, // ALV employee rate
    employer: 1.1, // ALV employer rate
    total: 2.2,
    maxIncome: 148200, // 2024 maximum insured income
    additionalRate: 0.5, // Additional ALV rate for high earners
    additionalThreshold: 148200,
  },
  nbu: {
    employee: 1.0, // NBU employee rate (varies by employer)
    employer: 1.0, // NBU employer rate (varies by employer)
    total: 2.0,
  },
  familyAllowances: {
    rate: 1.2, // Family allowances rate (employer only)
    employerOnly: true,
  },
};

// Standard Pension Fund Rates (BVG minimum)
export const PENSION_FUND_RATES_2024: PensionFundRates = {
  coordinationDeduction: 25725, // 2024 coordination deduction
  minInsuredSalary: 22050, // Minimum insured salary
  maxInsuredSalary: 88200, // Maximum insured salary (3x coordination deduction)
  ageGroups: {
    age25to34: { employee: 3.5, employer: 3.5 }, // Age 25-34
    age35to44: { employee: 5.0, employer: 5.0 }, // Age 35-44
    age45to54: { employee: 7.5, employer: 7.5 }, // Age 45-54
    age55to65: { employee: 9.0, employer: 9.0 }, // Age 55-65
  },
  riskPremiums: {
    disability: 0.7, // Disability insurance premium
    death: 0.3, // Death benefit premium
  },
  administrativeCosts: 0.3, // Administrative costs
};

/**
 * Calculate AHV/IV/EO contributions
 */
export function calculateAHVContributions(
  grossSalary: number,
  rates: SocialInsuranceRates = SOCIAL_INSURANCE_RATES_2024
): { employee: number; employer: number; total: number } {
  const employee = (grossSalary * rates.ahv.employee) / 100;
  const employer = (grossSalary * rates.ahv.employer) / 100;

  return {
    employee,
    employer,
    total: employee + employer,
  };
}

/**
 * Calculate ALV (unemployment insurance) contributions
 */
export function calculateALVContributions(
  grossSalary: number,
  rates: SocialInsuranceRates = SOCIAL_INSURANCE_RATES_2024
): { employee: number; employer: number; total: number; additional: number } {
  // Standard ALV on income up to maximum
  const standardInsuredIncome = Math.min(grossSalary, rates.alv.maxIncome);
  const standardEmployee = (standardInsuredIncome * rates.alv.employee) / 100;
  const standardEmployer = (standardInsuredIncome * rates.alv.employer) / 100;

  // Additional ALV on income above threshold (employee only)
  const additionalInsuredIncome = Math.max(
    0,
    grossSalary - rates.alv.additionalThreshold
  );
  const additional = (additionalInsuredIncome * rates.alv.additionalRate) / 100;

  return {
    employee: standardEmployee + additional,
    employer: standardEmployer,
    total: standardEmployee + standardEmployer + additional,
    additional,
  };
}

/**
 * Calculate NBU (accident insurance) contributions
 */
export function calculateNBUContributions(
  grossSalary: number,
  rates: SocialInsuranceRates = SOCIAL_INSURANCE_RATES_2024
): { employee: number; employer: number; total: number } {
  const employee = (grossSalary * rates.nbu.employee) / 100;
  const employer = (grossSalary * rates.nbu.employer) / 100;

  return {
    employee,
    employer,
    total: employee + employer,
  };
}

/**
 * Calculate pension fund (BVG) contributions
 */
export function calculatePensionFundContributions(
  grossSalary: number,
  age: number,
  rates: PensionFundRates = PENSION_FUND_RATES_2024
): {
  employee: number;
  employer: number;
  total: number;
  insuredSalary: number;
} {
  // Calculate insured salary (gross salary minus coordination deduction)
  const insuredSalary = Math.max(
    0,
    Math.min(
      grossSalary - rates.coordinationDeduction,
      rates.maxInsuredSalary - rates.coordinationDeduction
    )
  );

  // Determine age group rates
  let ageGroupRates;
  if (age >= 25 && age <= 34) {
    ageGroupRates = rates.ageGroups.age25to34;
  } else if (age >= 35 && age <= 44) {
    ageGroupRates = rates.ageGroups.age35to44;
  } else if (age >= 45 && age <= 54) {
    ageGroupRates = rates.ageGroups.age45to54;
  } else if (age >= 55 && age <= 65) {
    ageGroupRates = rates.ageGroups.age55to65;
  } else {
    // No BVG contributions below age 25 or above 65
    return { employee: 0, employer: 0, total: 0, insuredSalary: 0 };
  }

  // Calculate contributions
  const savingsEmployee = (insuredSalary * ageGroupRates.employee) / 100;
  const savingsEmployer = (insuredSalary * ageGroupRates.employer) / 100;

  // Add risk premiums and administrative costs (split equally)
  const riskAndAdmin =
    (insuredSalary *
      (rates.riskPremiums.disability +
        rates.riskPremiums.death +
        rates.administrativeCosts)) /
    100;
  const riskAndAdminEmployee = riskAndAdmin / 2;
  const riskAndAdminEmployer = riskAndAdmin / 2;

  const employee = savingsEmployee + riskAndAdminEmployee;
  const employer = savingsEmployer + riskAndAdminEmployer;

  return {
    employee,
    employer,
    total: employee + employer,
    insuredSalary,
  };
}

/**
 * Calculate family allowances (employer only)
 */
export function calculateFamilyAllowances(
  grossSalary: number,
  rates: SocialInsuranceRates = SOCIAL_INSURANCE_RATES_2024
): { employer: number } {
  const employer = (grossSalary * rates.familyAllowances.rate) / 100;

  return { employer };
}

/**
 * Calculate complete social insurance contributions
 */
export function calculateSocialInsurance(
  grossSalary: number,
  age: number,
  socialRates: SocialInsuranceRates = SOCIAL_INSURANCE_RATES_2024,
  pensionRates: PensionFundRates = PENSION_FUND_RATES_2024
): SocialInsuranceResult {
  // Calculate each component
  const ahv = calculateAHVContributions(grossSalary, socialRates);
  const alv = calculateALVContributions(grossSalary, socialRates);
  const nbu = calculateNBUContributions(grossSalary, socialRates);
  const pensionFund = calculatePensionFundContributions(
    grossSalary,
    age,
    pensionRates
  );
  const familyAllowances = calculateFamilyAllowances(grossSalary, socialRates);

  // Calculate totals
  const totalEmployee =
    ahv.employee + alv.employee + nbu.employee + pensionFund.employee;
  const totalEmployer =
    ahv.employer +
    alv.employer +
    nbu.employer +
    pensionFund.employer +
    familyAllowances.employer;
  const totalContributions = totalEmployee + totalEmployer;
  const netSalary = grossSalary - totalEmployee;

  return {
    ahv,
    alv,
    nbu,
    pensionFund,
    familyAllowances,
    totalEmployee,
    totalEmployer,
    totalContributions,
    netSalary,
  };
}

/**
 * Calculate annual social insurance contributions
 */
export function calculateAnnualSocialInsurance(
  annualGrossSalary: number,
  age: number,
  monthlyBonus: number = 0,
  thirteenthSalary: boolean = true
): SocialInsuranceResult {
  // Calculate total annual compensation
  let totalAnnualSalary = annualGrossSalary;
  if (thirteenthSalary) {
    totalAnnualSalary += annualGrossSalary / 12; // Add 13th month salary
  }
  totalAnnualSalary += monthlyBonus * 12; // Add monthly bonuses

  return calculateSocialInsurance(totalAnnualSalary, age);
}

/**
 * Calculate social insurance for self-employed
 */
export function calculateSelfEmployedSocialInsurance(
  netBusinessIncome: number,
  age: number,
  voluntaryBVG: boolean = false
): Partial<SocialInsuranceResult> {
  // Self-employed pay double AHV rate (employee + employer portion)
  const ahvRate = SOCIAL_INSURANCE_RATES_2024.ahv.total;
  const ahvContribution = (netBusinessIncome * ahvRate) / 100;

  // No ALV for self-employed
  // No NBU (unless voluntarily insured)
  // Optional BVG (pension fund)

  let pensionFund = { employee: 0, employer: 0, total: 0, insuredSalary: 0 };
  if (voluntaryBVG) {
    // Self-employed can insure up to their net business income
    const insuredIncome = Math.min(
      netBusinessIncome,
      PENSION_FUND_RATES_2024.maxInsuredSalary
    );
    pensionFund = calculatePensionFundContributions(insuredIncome, age);
    // Self-employed pay both employee and employer portions
    pensionFund.total = pensionFund.employee + pensionFund.employer;
  }

  return {
    ahv: {
      employee: ahvContribution,
      employer: 0,
      total: ahvContribution,
    },
    pensionFund,
    totalEmployee: ahvContribution + pensionFund.total,
    totalEmployer: 0,
    totalContributions: ahvContribution + pensionFund.total,
    netSalary: netBusinessIncome - ahvContribution - pensionFund.total,
  };
}

/**
 * Get social insurance summary for different income levels
 */
export function getSocialInsuranceSummary(age: number): Array<{
  income: number;
  contributions: SocialInsuranceResult;
  effectiveRate: number;
}> {
  const incomelevels = [50000, 75000, 100000, 125000, 150000, 200000];

  return incomelevels.map(income => {
    const contributions = calculateSocialInsurance(income, age);
    const effectiveRate = (contributions.totalEmployee / income) * 100;

    return {
      income,
      contributions,
      effectiveRate,
    };
  });
}

export default {
  calculateAHVContributions,
  calculateALVContributions,
  calculateNBUContributions,
  calculatePensionFundContributions,
  calculateFamilyAllowances,
  calculateSocialInsurance,
  calculateAnnualSocialInsurance,
  calculateSelfEmployedSocialInsurance,
  getSocialInsuranceSummary,
  SOCIAL_INSURANCE_RATES_2024,
  PENSION_FUND_RATES_2024,
};
