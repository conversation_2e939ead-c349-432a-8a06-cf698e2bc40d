/**
 * Error Reporter for Swiss Budget Pro
 * Automatically captures, analyzes, and reports errors with context
 */

import { debugLogger } from './debug-logger';
import { performanceMonitor } from './performance-monitor';
import { errorDiagnostics } from './error-diagnostics';

export interface ErrorReport {
  id: string;
  timestamp: string;
  error: {
    message: string;
    stack?: string;
    name: string;
    type: 'javascript' | 'promise' | 'resource' | 'network' | 'react';
  };
  context: {
    url: string;
    userAgent: string;
    viewport: { width: number; height: number };
    timestamp: string;
    sessionId: string;
  };
  application: {
    version: string;
    environment: string;
    feature: string;
    component?: string;
  };
  user: {
    actions: UserAction[];
    sessionDuration: number;
    errorCount: number;
  };
  system: {
    memory: any;
    performance: any;
    localStorage: any;
    network: boolean;
  };
  diagnostics: any[];
  reproduction: {
    steps: string[];
    likelihood: 'low' | 'medium' | 'high';
    severity: 'low' | 'medium' | 'high' | 'critical';
  };
}

export interface UserAction {
  timestamp: string;
  type: 'click' | 'input' | 'navigation' | 'calculation' | 'error';
  target: string;
  details?: any;
}

class ErrorReporter {
  private userActions: UserAction[] = [];
  private sessionStart: number = Date.now();
  private errorCount: number = 0;
  private sessionId: string;
  private isTracking: boolean = false;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.setupUserActionTracking();
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  public startTracking(): void {
    if (this.isTracking) return;

    this.isTracking = true;
    debugLogger.log('info', 'error-reporter', 'Error reporting started', {
      sessionId: this.sessionId,
    });
  }

  public stopTracking(): void {
    this.isTracking = false;
    debugLogger.log('info', 'error-reporter', 'Error reporting stopped');
  }

  private setupUserActionTracking(): void {
    // Track clicks
    document.addEventListener('click', event => {
      if (!this.isTracking) return;

      const target = event.target as HTMLElement;
      this.recordUserAction('click', this.getElementSelector(target), {
        tagName: target.tagName,
        className: target.className,
        id: target.id,
        textContent: target.textContent?.substring(0, 50),
      });
    });

    // Track input changes
    document.addEventListener('input', event => {
      if (!this.isTracking) return;

      const target = event.target as HTMLInputElement;
      this.recordUserAction('input', this.getElementSelector(target), {
        type: target.type,
        name: target.name,
        valueLength: target.value?.length || 0,
      });
    });

    // Track navigation
    window.addEventListener('popstate', () => {
      if (!this.isTracking) return;

      this.recordUserAction('navigation', window.location.pathname, {
        url: window.location.href,
      });
    });

    // Track errors
    window.addEventListener('error', event => {
      this.errorCount++;
      this.recordUserAction('error', 'javascript_error', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
      });
    });

    window.addEventListener('unhandledrejection', event => {
      this.errorCount++;
      this.recordUserAction('error', 'promise_rejection', {
        reason: event.reason?.message || String(event.reason),
      });
    });
  }

  private recordUserAction(
    type: UserAction['type'],
    target: string,
    details?: any,
  ): void {
    const action: UserAction = {
      timestamp: new Date().toISOString(),
      type,
      target,
      details,
    };

    this.userActions.push(action);

    // Keep only last 50 actions
    if (this.userActions.length > 50) {
      this.userActions = this.userActions.slice(-50);
    }
  }

  private getElementSelector(element: HTMLElement): string {
    if (element.id) return `#${element.id}`;
    if (element.className) return `.${element.className.split(' ')[0]}`;
    if (element.getAttribute('data-testid'))
      return `[data-testid="${element.getAttribute('data-testid')}"]`;
    return element.tagName.toLowerCase();
  }

  public async generateErrorReport(
    error: Error | string,
    context?: any,
  ): Promise<ErrorReport> {
    const errorObj = typeof error === 'string' ? new Error(error) : error;

    debugLogger.log('info', 'error-reporter', 'Generating error report', {
      error: errorObj.message,
      context,
    });

    // Run diagnostics
    const diagnostics = errorDiagnostics.diagnoseError(errorObj);

    // Get system information
    const systemInfo = this.getSystemInfo();

    // Get performance data
    const performanceData = this.getPerformanceData();

    // Analyze reproduction steps
    const reproduction = this.analyzeReproduction(errorObj);

    const report: ErrorReport = {
      id: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      error: {
        message: errorObj.message,
        stack: errorObj.stack,
        name: errorObj.name,
        type: this.determineErrorType(errorObj, context),
      },
      context: {
        url: window.location.href,
        userAgent: navigator.userAgent,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight,
        },
        timestamp: new Date().toISOString(),
        sessionId: this.sessionId,
      },
      application: {
        version: this.getApplicationVersion(),
        environment: process.env.NODE_ENV || 'unknown',
        feature: this.determineFeatureContext(),
        component: context?.componentName,
      },
      user: {
        actions: [...this.userActions],
        sessionDuration: Date.now() - this.sessionStart,
        errorCount: this.errorCount,
      },
      system: systemInfo,
      diagnostics,
      reproduction,
    };

    // Store report locally
    this.storeReport(report);

    return report;
  }

  private determineErrorType(
    error: Error,
    context?: any,
  ): ErrorReport['error']['type'] {
    if (context?.type) return context.type;
    if (error.stack?.includes('React')) return 'react';
    if (error.message.includes('fetch') || error.message.includes('network'))
      return 'network';
    if (error.name === 'TypeError' && error.message.includes('Failed to fetch'))
      return 'network';
    return 'javascript';
  }

  private getSystemInfo(): ErrorReport['system'] {
    return {
      memory: (performance as any).memory
        ? {
            used: (performance as any).memory.usedJSHeapSize,
            total: (performance as any).memory.totalJSHeapSize,
            limit: (performance as any).memory.jsHeapSizeLimit,
          }
        : null,
      performance: {
        timing: performance.timing
          ? {
              loadEventEnd: performance.timing.loadEventEnd,
              navigationStart: performance.timing.navigationStart,
              domContentLoadedEventEnd:
                performance.timing.domContentLoadedEventEnd,
            }
          : null,
        navigation: performance.navigation
          ? {
              type: performance.navigation.type,
              redirectCount: performance.navigation.redirectCount,
            }
          : null,
      },
      localStorage: this.getLocalStorageInfo(),
      network: navigator.onLine,
    };
  }

  private getPerformanceData(): any {
    const metrics = performanceMonitor.getMetrics();
    const slowOperations = performanceMonitor.getSlowOperations();
    const memorySnapshots = performanceMonitor.getMemorySnapshots();

    return {
      totalMetrics: metrics.length,
      slowOperations: slowOperations.length,
      recentSlowOps: slowOperations.slice(-5),
      memoryTrend: memorySnapshots.slice(-5),
      averageRenderTime: this.calculateAverageRenderTime(metrics),
    };
  }

  private calculateAverageRenderTime(metrics: any[]): number {
    const renderMetrics = metrics.filter(m => m.type === 'render');
    if (renderMetrics.length === 0) return 0;

    const total = renderMetrics.reduce(
      (sum, metric) => sum + metric.duration,
      0,
    );
    return total / renderMetrics.length;
  }

  private getLocalStorageInfo(): any {
    try {
      const keys = Object.keys(localStorage).filter(key =>
        key.startsWith('swissBudgetPro_'),
      );
      const totalSize = keys.reduce((size, key) => {
        return size + (localStorage.getItem(key)?.length || 0);
      }, 0);

      return {
        available: true,
        keyCount: keys.length,
        totalSize,
        quota: this.estimateLocalStorageQuota(),
      };
    } catch {
      return {
        available: false,
        error: 'localStorage not accessible',
      };
    }
  }

  private estimateLocalStorageQuota(): number {
    try {
      const test = 'x'.repeat(1024); // 1KB
      let size = 0;

      while (size < 10 * 1024 * 1024) {
        // Max 10MB test
        try {
          localStorage.setItem(`quota_test_${size}`, test);
          size += 1024;
        } catch {
          // Clean up test data
          for (let i = 0; i < size; i += 1024) {
            localStorage.removeItem(`quota_test_${i}`);
          }
          return size;
        }
      }

      return size;
    } catch {
      return 0;
    }
  }

  private getApplicationVersion(): string {
    // Try to get version from package.json or environment
    return process.env.REACT_APP_VERSION || '4.1.0';
  }

  private determineFeatureContext(): string {
    const path = window.location.pathname;
    const hash = window.location.hash;

    if (path.includes('tax') || hash.includes('tax')) return 'tax-optimization';
    if (path.includes('healthcare') || hash.includes('healthcare'))
      return 'healthcare-optimizer';
    if (path.includes('fire') || hash.includes('fire'))
      return 'fire-calculation';
    if (path.includes('pillar') || hash.includes('pillar')) return 'pillar-3a';

    return 'general';
  }

  private analyzeReproduction(error: Error): ErrorReport['reproduction'] {
    const recentActions = this.userActions.slice(-10);
    const steps: string[] = [];

    // Generate reproduction steps from user actions
    recentActions.forEach((action, index) => {
      switch (action.type) {
        case 'click':
          steps.push(`${index + 1}. Click on ${action.target}`);
          break;
        case 'input':
          steps.push(`${index + 1}. Enter data in ${action.target}`);
          break;
        case 'navigation':
          steps.push(`${index + 1}. Navigate to ${action.target}`);
          break;
        case 'calculation':
          steps.push(`${index + 1}. Trigger calculation: ${action.target}`);
          break;
      }
    });

    // Determine likelihood and severity
    const likelihood = this.determineLikelihood(error, recentActions);
    const severity = this.determineSeverity(error);

    return {
      steps,
      likelihood,
      severity,
    };
  }

  private determineLikelihood(
    error: Error,
    actions: UserAction[],
  ): 'low' | 'medium' | 'high' {
    // If error occurred after specific user actions, it's more likely to be reproducible
    if (actions.length > 5) return 'high';
    if (actions.length > 2) return 'medium';
    return 'low';
  }

  private determineSeverity(
    error: Error,
  ): 'low' | 'medium' | 'high' | 'critical' {
    const message = error.message.toLowerCase();

    if (message.includes('referenceerror') || message.includes('typeerror'))
      return 'critical';
    if (message.includes('network') || message.includes('fetch')) return 'high';
    if (message.includes('warning') || message.includes('deprecated'))
      return 'medium';

    return 'low';
  }

  private storeReport(report: ErrorReport): void {
    try {
      const key = `swissBudgetPro_error_report_${report.id}`;
      localStorage.setItem(key, JSON.stringify(report));

      // Keep only last 10 error reports
      const errorKeys = Object.keys(localStorage)
        .filter(key => key.startsWith('swissBudgetPro_error_report_'))
        .sort()
        .slice(0, -10);

      errorKeys.forEach(key => localStorage.removeItem(key));
    } catch (error) {
      debugLogger.log(
        'warn',
        'error-reporter',
        'Failed to store error report',
        { error },
      );
    }
  }

  public getStoredReports(): ErrorReport[] {
    const reports: ErrorReport[] = [];

    try {
      Object.keys(localStorage)
        .filter(key => key.startsWith('swissBudgetPro_error_report_'))
        .forEach(key => {
          try {
            const report = JSON.parse(localStorage.getItem(key) || '');
            reports.push(report);
          } catch {
            // Remove corrupted report
            localStorage.removeItem(key);
          }
        });
    } catch (error) {
      debugLogger.log(
        'warn',
        'error-reporter',
        'Failed to retrieve stored reports',
        { error },
      );
    }

    return reports.sort(
      (a, b) =>
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime(),
    );
  }

  public exportReport(report: ErrorReport): void {
    const blob = new Blob([JSON.stringify(report, null, 2)], {
      type: 'application/json',
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `error-report-${report.id}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    debugLogger.log('info', 'error-reporter', 'Error report exported', {
      reportId: report.id,
    });
  }

  public clearReports(): void {
    const keys = Object.keys(localStorage).filter(key =>
      key.startsWith('swissBudgetPro_error_report_'),
    );

    keys.forEach(key => localStorage.removeItem(key));

    debugLogger.log('info', 'error-reporter', 'Error reports cleared', {
      count: keys.length,
    });
  }

  public getSessionStats(): any {
    return {
      sessionId: this.sessionId,
      sessionDuration: Date.now() - this.sessionStart,
      userActions: this.userActions.length,
      errorCount: this.errorCount,
      isTracking: this.isTracking,
    };
  }
}

// Export singleton instance
export const errorReporter = new ErrorReporter();

// Global access for debugging
if (typeof window !== 'undefined') {
  (window as any).errorReporter = errorReporter;
}

export default errorReporter;
