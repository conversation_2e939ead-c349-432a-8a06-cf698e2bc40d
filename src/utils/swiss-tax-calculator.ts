/**
 * Swiss Tax Calculator Engine
 * Comprehensive tax calculations for all 26 cantons
 */

import {
  TaxCalculationInput,
  TaxCalculationResult,
  TaxBracket,
  TaxBreakdown,
  FEDERAL_TAX_BRACKETS,
  CANTON_TAX_DATA,
  SwissCanton,
} from '../types/swiss-tax';

/**
 * Calculate tax based on progressive tax brackets
 */
export const calculateProgressiveTax = (
  income: number,
  brackets: TaxBracket[],
): { amount: number; breakdown: TaxBreakdown[] } => {
  let totalTax = 0;
  const breakdown: TaxBreakdown[] = [];

  for (const bracket of brackets) {
    if (income <= bracket.min) break;

    const taxableInBracket = Math.min(income, bracket.max) - bracket.min;
    if (taxableInBracket <= 0) continue;

    const taxInBracket = (taxableInBracket * bracket.rate) / 100;
    totalTax += taxInBracket;

    breakdown.push({
      base: bracket.min,
      rate: bracket.rate,
      amount: taxInBracket,
      bracket: `${bracket.min.toLocaleString()} - ${bracket.max === Infinity ? '∞' : bracket.max.toLocaleString()}`,
    });
  }

  return { amount: totalTax, breakdown };
};

/**
 * Calculate federal income tax
 */
export const calculateFederalTax = (taxableIncome: number): TaxBreakdown => {
  const result = calculateProgressiveTax(taxableIncome, FEDERAL_TAX_BRACKETS);

  return {
    base: taxableIncome,
    rate: taxableIncome > 0 ? (result.amount / taxableIncome) * 100 : 0,
    amount: result.amount,
    bracket: 'Federal Progressive',
  };
};

/**
 * Calculate cantonal income tax
 */
export const calculateCantonalTax = (
  taxableIncome: number,
  canton: SwissCanton,
): TaxBreakdown => {
  const cantonData = CANTON_TAX_DATA[canton];
  const result = calculateProgressiveTax(
    taxableIncome,
    cantonData.incomeTaxBrackets,
  );

  return {
    base: taxableIncome,
    rate: taxableIncome > 0 ? (result.amount / taxableIncome) * 100 : 0,
    amount: result.amount,
    bracket: `${canton} Cantonal`,
  };
};

/**
 * Calculate municipal tax
 */
export const calculateMunicipalTax = (
  cantonalTax: number,
  canton: SwissCanton,
  municipalMultiplier?: number,
): TaxBreakdown => {
  const cantonData = CANTON_TAX_DATA[canton];
  const multiplier = municipalMultiplier || cantonData.municipalTaxMultiplier;
  const municipalTax = (cantonalTax * multiplier) / 100;

  return {
    base: cantonalTax,
    rate: multiplier,
    amount: municipalTax,
    bracket: `Municipal (${multiplier}%)`,
  };
};

/**
 * Calculate church tax
 */
export const calculateChurchTax = (
  cantonalTax: number,
  canton: SwissCanton,
  isChurchMember: boolean,
): TaxBreakdown => {
  if (!isChurchMember) {
    return {
      base: 0,
      rate: 0,
      amount: 0,
      bracket: 'Not applicable',
    };
  }

  const cantonData = CANTON_TAX_DATA[canton];
  const churchTax = (cantonalTax * cantonData.churchTaxRate) / 100;

  return {
    base: cantonalTax,
    rate: cantonData.churchTaxRate,
    amount: churchTax,
    bracket: `Church Tax (${cantonData.churchTaxRate}%)`,
  };
};

/**
 * Calculate wealth tax
 */
export const calculateWealthTax = (
  wealth: number,
  canton: SwissCanton,
): TaxBreakdown => {
  const cantonData = CANTON_TAX_DATA[canton];

  // Most cantons have a minimum wealth threshold
  const taxableWealth = Math.max(0, wealth - 100000); // CHF 100k threshold
  const wealthTax = (taxableWealth * cantonData.wealthTaxRate) / 1000; // per mille

  return {
    base: taxableWealth,
    rate: cantonData.wealthTaxRate,
    amount: wealthTax,
    bracket: `Wealth Tax (${cantonData.wealthTaxRate}‰)`,
  };
};

/**
 * Calculate total deductions
 */
export const calculateDeductions = (
  input: TaxCalculationInput,
): {
  personal: number;
  married: number;
  children: number;
  pillar3a: number;
  professional: number;
  total: number;
} => {
  const cantonData = CANTON_TAX_DATA[input.canton];

  const personal = cantonData.deductions.personal;
  const married =
    input.maritalStatus === 'married' ? cantonData.deductions.married : 0;
  const children = input.children * cantonData.deductions.children;
  const pillar3a = Math.min(
    input.pillar3aContribution,
    cantonData.deductions.maxPillar3a,
  );
  const professional = Math.min(
    input.professionalExpenses,
    cantonData.deductions.maxProfessionalExpenses,
  );

  const total = personal + married + children + pillar3a + professional;

  return {
    personal,
    married,
    children,
    pillar3a,
    professional,
    total,
  };
};

/**
 * Main tax calculation function
 */
export const calculateSwissTax = (
  input: TaxCalculationInput,
): TaxCalculationResult => {
  // Calculate deductions
  const deductions = calculateDeductions(input);
  const taxableIncome = Math.max(0, input.grossIncome - deductions.total);

  // Calculate all taxes
  const federalTax = calculateFederalTax(taxableIncome);
  const cantonalTax = calculateCantonalTax(taxableIncome, input.canton);
  const municipalTax = calculateMunicipalTax(
    cantonalTax.amount,
    input.canton,
    input.municipalMultiplier,
  );
  const churchTax = calculateChurchTax(
    cantonalTax.amount,
    input.canton,
    input.churchMember,
  );
  const wealthTax = calculateWealthTax(input.wealth, input.canton);

  // Calculate totals
  const totalIncomeTax =
    federalTax.amount +
    cantonalTax.amount +
    municipalTax.amount +
    churchTax.amount;
  const totalTax = totalIncomeTax + wealthTax.amount;
  const netIncome = input.grossIncome - totalTax;

  // Calculate rates
  const effectiveTaxRate =
    input.grossIncome > 0 ? (totalTax / input.grossIncome) * 100 : 0;

  // Calculate marginal tax rate (approximate)
  const marginalIncome = input.grossIncome + 1000;
  const marginalTaxableIncome = Math.max(0, marginalIncome - deductions.total);
  const marginalFederalTax = calculateFederalTax(marginalTaxableIncome);
  const marginalCantonalTax = calculateCantonalTax(
    marginalTaxableIncome,
    input.canton,
  );
  const marginalMunicipalTax = calculateMunicipalTax(
    marginalCantonalTax.amount,
    input.canton,
    input.municipalMultiplier,
  );
  const marginalChurchTax = calculateChurchTax(
    marginalCantonalTax.amount,
    input.canton,
    input.churchMember,
  );

  const marginalTotalTax =
    marginalFederalTax.amount +
    marginalCantonalTax.amount +
    marginalMunicipalTax.amount +
    marginalChurchTax.amount;
  const marginalTaxRate = ((marginalTotalTax - totalIncomeTax) / 1000) * 100;

  return {
    grossIncome: input.grossIncome,
    taxableIncome,
    federalTax: federalTax.amount,
    cantonalTax: cantonalTax.amount,
    municipalTax: municipalTax.amount,
    churchTax: churchTax.amount,
    wealthTax: wealthTax.amount,
    totalTax,
    netIncome,
    effectiveTaxRate,
    marginalTaxRate,
    breakdown: {
      deductions,
      taxes: {
        federal: federalTax,
        cantonal: cantonalTax,
        municipal: municipalTax,
        church: churchTax,
        wealth: wealthTax,
      },
    },
  };
};

/**
 * Compare tax burden across multiple cantons
 */
export const compareCantonTaxes = (
  baseInput: Omit<TaxCalculationInput, 'canton'>,
  cantons: SwissCanton[],
): Record<SwissCanton, TaxCalculationResult> => {
  const results: Record<string, TaxCalculationResult> = {};

  cantons.forEach(canton => {
    results[canton] = calculateSwissTax({
      ...baseInput,
      canton,
    });
  });

  return results as Record<SwissCanton, TaxCalculationResult>;
};

/**
 * Find the most tax-efficient canton for given income
 */
export const findBestTaxCanton = (
  baseInput: Omit<TaxCalculationInput, 'canton'>,
): { canton: SwissCanton; result: TaxCalculationResult; savings: number } => {
  const allCantons = Object.keys(CANTON_TAX_DATA) as SwissCanton[];
  const results = compareCantonTaxes(baseInput, allCantons);

  let bestCanton: SwissCanton = 'ZG'; // Default to Zug
  let lowestTax = Infinity;

  allCantons.forEach(canton => {
    const result = results[canton];
    if (result.totalTax < lowestTax) {
      lowestTax = result.totalTax;
      bestCanton = canton;
    }
  });

  // Calculate savings compared to highest tax canton
  const highestTax = Math.max(...allCantons.map(c => results[c].totalTax));
  const savings = highestTax - lowestTax;

  return {
    canton: bestCanton,
    result: results[bestCanton],
    savings,
  };
};

/**
 * Calculate optimal Pillar 3a contribution for tax savings
 */
export const calculateOptimalPillar3a = (
  baseInput: TaxCalculationInput,
): { optimalContribution: number; taxSavings: number; netBenefit: number } => {
  const cantonData = CANTON_TAX_DATA[baseInput.canton];
  const maxContribution = cantonData.deductions.maxPillar3a;

  // Calculate tax with no Pillar 3a
  const noContribution = calculateSwissTax({
    ...baseInput,
    pillar3aContribution: 0,
  });

  // Calculate tax with maximum Pillar 3a
  const maxContribution_result = calculateSwissTax({
    ...baseInput,
    pillar3aContribution: maxContribution,
  });

  const taxSavings = noContribution.totalTax - maxContribution_result.totalTax;
  const netBenefit = taxSavings - maxContribution; // Negative means you pay more than you save

  return {
    optimalContribution: maxContribution,
    taxSavings,
    netBenefit,
  };
};
