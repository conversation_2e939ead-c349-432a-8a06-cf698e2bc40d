/**
 * Performance Monitor for Swiss Budget Pro
 * Tracks performance metrics, memory usage, and optimization opportunities
 */

import { debugLogger } from './debug-logger';

export interface PerformanceMetrics {
  id: string;
  timestamp: string;
  type: 'navigation' | 'component' | 'calculation' | 'render' | 'network';
  name: string;
  duration: number;
  startTime: number;
  endTime: number;
  metadata?: Record<string, any>;
}

export interface MemorySnapshot {
  timestamp: string;
  usedJSHeapSize: number;
  totalJSHeapSize: number;
  jsHeapSizeLimit: number;
  percentage: number;
}

export interface NetworkMetrics {
  url: string;
  method: string;
  duration: number;
  size: number;
  status: number;
  timestamp: string;
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics[] = [];
  private memorySnapshots: MemorySnapshot[] = [];
  private networkMetrics: NetworkMetrics[] = [];
  private activeMetrics: Map<string, { startTime: number; metadata?: any }> =
    new Map();
  private isMonitoring: boolean = false;
  private memoryCheckInterval?: number;

  constructor() {
    this.setupPerformanceObserver();
    this.setupMemoryMonitoring();
    this.setupNetworkMonitoring();
  }

  public startMonitoring(): void {
    if (this.isMonitoring) return;

    this.isMonitoring = true;
    debugLogger.log('info', 'performance', 'Performance monitoring started');

    // Start memory monitoring
    this.memoryCheckInterval = window.setInterval(() => {
      this.captureMemorySnapshot();
    }, 10000); // Every 10 seconds

    // Capture initial metrics
    this.captureNavigationMetrics();
  }

  public stopMonitoring(): void {
    if (!this.isMonitoring) return;

    this.isMonitoring = false;
    if (this.memoryCheckInterval) {
      clearInterval(this.memoryCheckInterval);
    }

    debugLogger.log('info', 'performance', 'Performance monitoring stopped');
  }

  public startMetric(
    name: string,
    type: PerformanceMetrics['type'],
    metadata?: any,
  ): string {
    const id = `${type}_${name}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    this.activeMetrics.set(id, {
      startTime: performance.now(),
      metadata,
    });

    debugLogger.log('debug', 'performance', `Started metric: ${name}`, {
      id,
      type,
      metadata,
    });
    return id;
  }

  public endMetric(id: string, name?: string): PerformanceMetrics | null {
    const activeMetric = this.activeMetrics.get(id);
    if (!activeMetric) {
      debugLogger.log('warn', 'performance', `Metric not found: ${id}`);
      return null;
    }

    const endTime = performance.now();
    const duration = endTime - activeMetric.startTime;

    const [type, metricName] = id.split('_');

    const metric: PerformanceMetrics = {
      id,
      timestamp: new Date().toISOString(),
      type: type as PerformanceMetrics['type'],
      name: name || metricName,
      duration,
      startTime: activeMetric.startTime,
      endTime,
      metadata: activeMetric.metadata,
    };

    this.metrics.push(metric);
    this.activeMetrics.delete(id);

    // Log slow operations
    if (duration > 100) {
      debugLogger.log(
        'warn',
        'performance',
        `Slow operation detected: ${metric.name}`,
        {
          duration: `${duration.toFixed(2)}ms`,
          type: metric.type,
          metadata: metric.metadata,
        },
      );
    }

    debugLogger.log(
      'debug',
      'performance',
      `Completed metric: ${metric.name}`,
      {
        duration: `${duration.toFixed(2)}ms`,
      },
    );

    return metric;
  }

  private setupPerformanceObserver(): void {
    if (!('PerformanceObserver' in window)) {
      debugLogger.log(
        'warn',
        'performance',
        'PerformanceObserver not supported',
      );
      return;
    }

    try {
      // Observe paint metrics
      const paintObserver = new PerformanceObserver(list => {
        for (const entry of list.getEntries()) {
          debugLogger.log(
            'info',
            'performance',
            `Paint metric: ${entry.name}`,
            {
              startTime: entry.startTime,
              duration: entry.duration,
            },
          );
        }
      });
      paintObserver.observe({ entryTypes: ['paint'] });

      // Observe navigation metrics
      const navigationObserver = new PerformanceObserver(list => {
        for (const entry of list.getEntries()) {
          this.processNavigationEntry(entry as PerformanceNavigationTiming);
        }
      });
      navigationObserver.observe({ entryTypes: ['navigation'] });

      // Observe resource metrics
      const resourceObserver = new PerformanceObserver(list => {
        for (const entry of list.getEntries()) {
          this.processResourceEntry(entry as PerformanceResourceTiming);
        }
      });
      resourceObserver.observe({ entryTypes: ['resource'] });
    } catch (error) {
      debugLogger.log(
        'error',
        'performance',
        'Failed to setup PerformanceObserver',
        { error },
      );
    }
  }

  private setupMemoryMonitoring(): void {
    if (!('memory' in performance)) {
      debugLogger.log('warn', 'performance', 'Memory API not available');
      return;
    }

    // Capture initial memory snapshot
    this.captureMemorySnapshot();
  }

  private setupNetworkMonitoring(): void {
    // Intercept fetch requests
    const originalFetch = window.fetch;
    window.fetch = async (...args) => {
      const startTime = performance.now();
      const url = args[0]?.toString() || 'unknown';

      try {
        const response = await originalFetch(...args);
        const endTime = performance.now();

        this.recordNetworkMetric({
          url,
          method: args[1]?.method || 'GET',
          duration: endTime - startTime,
          size: parseInt(response.headers.get('content-length') || '0'),
          status: response.status,
          timestamp: new Date().toISOString(),
        });

        return response;
      } catch (error) {
        const endTime = performance.now();

        this.recordNetworkMetric({
          url,
          method: args[1]?.method || 'GET',
          duration: endTime - startTime,
          size: 0,
          status: 0,
          timestamp: new Date().toISOString(),
        });

        throw error;
      }
    };
  }

  private captureNavigationMetrics(): void {
    if (!performance.timing) return;

    const timing = performance.timing;
    const navigationStart = timing.navigationStart;

    const metrics = {
      domContentLoaded: timing.domContentLoadedEventEnd - navigationStart,
      loadComplete: timing.loadEventEnd - navigationStart,
      domInteractive: timing.domInteractive - navigationStart,
      firstPaint: this.getFirstPaint(),
      firstContentfulPaint: this.getFirstContentfulPaint(),
      timeToInteractive: this.estimateTimeToInteractive(),
    };

    debugLogger.log(
      'info',
      'performance',
      'Navigation metrics captured',
      metrics,
    );

    // Store as performance metric
    this.metrics.push({
      id: `navigation_${Date.now()}`,
      timestamp: new Date().toISOString(),
      type: 'navigation',
      name: 'page_load',
      duration: metrics.loadComplete,
      startTime: 0,
      endTime: metrics.loadComplete,
      metadata: metrics,
    });
  }

  private processNavigationEntry(entry: PerformanceNavigationTiming): void {
    const metrics = {
      domContentLoaded:
        entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
      loadComplete: entry.loadEventEnd - entry.loadEventStart,
      domInteractive: entry.domInteractive - entry.fetchStart,
      redirectTime: entry.redirectEnd - entry.redirectStart,
      dnsTime: entry.domainLookupEnd - entry.domainLookupStart,
      connectTime: entry.connectEnd - entry.connectStart,
      requestTime: entry.responseStart - entry.requestStart,
      responseTime: entry.responseEnd - entry.responseStart,
    };

    debugLogger.log(
      'info',
      'performance',
      'Detailed navigation metrics',
      metrics,
    );
  }

  private processResourceEntry(entry: PerformanceResourceTiming): void {
    const duration = entry.responseEnd - entry.startTime;

    // Log slow resources
    if (duration > 1000) {
      debugLogger.log('warn', 'performance', 'Slow resource detected', {
        name: entry.name,
        duration: `${duration.toFixed(2)}ms`,
        size: entry.transferSize,
        type: this.getResourceType(entry.name),
      });
    }

    // Track resource metrics
    this.metrics.push({
      id: `resource_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      type: 'network',
      name: this.getResourceName(entry.name),
      duration,
      startTime: entry.startTime,
      endTime: entry.responseEnd,
      metadata: {
        url: entry.name,
        size: entry.transferSize,
        type: this.getResourceType(entry.name),
      },
    });
  }

  private captureMemorySnapshot(): void {
    if (!('memory' in performance)) return;

    const memory = (performance as any).memory;
    const snapshot: MemorySnapshot = {
      timestamp: new Date().toISOString(),
      usedJSHeapSize: memory.usedJSHeapSize,
      totalJSHeapSize: memory.totalJSHeapSize,
      jsHeapSizeLimit: memory.jsHeapSizeLimit,
      percentage: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100,
    };

    this.memorySnapshots.push(snapshot);

    // Keep only last 100 snapshots
    if (this.memorySnapshots.length > 100) {
      this.memorySnapshots = this.memorySnapshots.slice(-100);
    }

    // Warn about high memory usage
    if (snapshot.percentage > 80) {
      debugLogger.log('warn', 'performance', 'High memory usage detected', {
        percentage: `${snapshot.percentage.toFixed(1)}%`,
        used: `${(snapshot.usedJSHeapSize / 1024 / 1024).toFixed(1)}MB`,
        limit: `${(snapshot.jsHeapSizeLimit / 1024 / 1024).toFixed(1)}MB`,
      });
    }
  }

  private recordNetworkMetric(metric: NetworkMetrics): void {
    this.networkMetrics.push(metric);

    // Keep only last 100 network metrics
    if (this.networkMetrics.length > 100) {
      this.networkMetrics = this.networkMetrics.slice(-100);
    }

    // Log slow network requests
    if (metric.duration > 2000) {
      debugLogger.log('warn', 'performance', 'Slow network request', {
        url: metric.url,
        duration: `${metric.duration.toFixed(2)}ms`,
        status: metric.status,
      });
    }
  }

  private getFirstPaint(): number | null {
    try {
      const paintEntries = performance.getEntriesByType('paint');
      const firstPaint = paintEntries.find(
        entry => entry.name === 'first-paint',
      );
      return firstPaint ? firstPaint.startTime : null;
    } catch {
      return null;
    }
  }

  private getFirstContentfulPaint(): number | null {
    try {
      const paintEntries = performance.getEntriesByType('paint');
      const fcp = paintEntries.find(
        entry => entry.name === 'first-contentful-paint',
      );
      return fcp ? fcp.startTime : null;
    } catch {
      return null;
    }
  }

  private estimateTimeToInteractive(): number | null {
    // Simplified TTI estimation
    try {
      const timing = performance.timing;
      return timing.domContentLoadedEventEnd - timing.navigationStart;
    } catch {
      return null;
    }
  }

  private getResourceType(url: string): string {
    if (url.includes('.js')) return 'script';
    if (url.includes('.css')) return 'stylesheet';
    if (url.match(/\.(png|jpg|jpeg|gif|svg|webp)$/)) return 'image';
    if (url.includes('.woff') || url.includes('.ttf')) return 'font';
    return 'other';
  }

  private getResourceName(url: string): string {
    try {
      const urlObj = new URL(url);
      return urlObj.pathname.split('/').pop() || 'unknown';
    } catch {
      return url.substring(url.lastIndexOf('/') + 1) || 'unknown';
    }
  }

  // Public API methods
  public getMetrics(): PerformanceMetrics[] {
    return [...this.metrics];
  }

  public getMemorySnapshots(): MemorySnapshot[] {
    return [...this.memorySnapshots];
  }

  public getNetworkMetrics(): NetworkMetrics[] {
    return [...this.networkMetrics];
  }

  public getSlowOperations(threshold: number = 100): PerformanceMetrics[] {
    return this.metrics.filter(metric => metric.duration > threshold);
  }

  public getAverageMetric(name: string): number | null {
    const matchingMetrics = this.metrics.filter(metric => metric.name === name);
    if (matchingMetrics.length === 0) return null;

    const total = matchingMetrics.reduce(
      (sum, metric) => sum + metric.duration,
      0,
    );
    return total / matchingMetrics.length;
  }

  public exportPerformanceReport(): string {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalMetrics: this.metrics.length,
        slowOperations: this.getSlowOperations().length,
        averagePageLoad: this.getAverageMetric('page_load'),
        memorySnapshots: this.memorySnapshots.length,
        networkRequests: this.networkMetrics.length,
      },
      metrics: this.metrics,
      memorySnapshots: this.memorySnapshots.slice(-10), // Last 10 snapshots
      networkMetrics: this.networkMetrics.slice(-20), // Last 20 requests
      slowOperations: this.getSlowOperations(),
      recommendations: this.generateRecommendations(),
    };

    return JSON.stringify(report, null, 2);
  }

  private generateRecommendations(): string[] {
    const recommendations: string[] = [];

    const slowOps = this.getSlowOperations();
    if (slowOps.length > 0) {
      recommendations.push(
        `Found ${slowOps.length} slow operations. Consider optimizing these components.`,
      );
    }

    const lastMemory = this.memorySnapshots[this.memorySnapshots.length - 1];
    if (lastMemory && lastMemory.percentage > 70) {
      recommendations.push(
        'High memory usage detected. Consider implementing memory optimization strategies.',
      );
    }

    const slowNetworkRequests = this.networkMetrics.filter(
      req => req.duration > 2000,
    );
    if (slowNetworkRequests.length > 0) {
      recommendations.push(
        `${slowNetworkRequests.length} slow network requests detected. Consider caching or optimization.`,
      );
    }

    return recommendations;
  }

  public clearMetrics(): void {
    this.metrics = [];
    this.memorySnapshots = [];
    this.networkMetrics = [];
    debugLogger.log('info', 'performance', 'Performance metrics cleared');
  }
}

// Export singleton instance
export const performanceMonitor = new PerformanceMonitor();

// Global access for debugging
if (typeof window !== 'undefined') {
  (window as any).performanceMonitor = performanceMonitor;
}

export default performanceMonitor;
