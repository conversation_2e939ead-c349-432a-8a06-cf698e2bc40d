import { useCallback, useEffect, useRef, useState } from 'react';

// Enhanced error logging utility
const logError = (context: string, error: any, additionalData?: any) => {
  const timestamp = new Date().toISOString();
  const errorInfo = {
    timestamp,
    context,
    error: {
      message: error?.message || 'Unknown error',
      stack: error?.stack || 'No stack trace',
      name: error?.name || 'Unknown error type',
    },
    additionalData,
    userAgent: navigator.userAgent,
    url: window.location.href,
  };

  console.group(`🚨 localStorage Error - ${context}`);
  console.error('Error Details:', errorInfo);
  console.error('Original Error:', error);
  if (additionalData) {
    console.log('Additional Data:', additionalData);
  }
  console.groupEnd();

  // Store error in localStorage for debugging (with fallback)
  try {
    const existingErrors = JSON.parse(
      localStorage.getItem('fire-storage-errors') || '[]'
    );
    existingErrors.push(errorInfo);
    // Keep only last 5 errors to prevent storage bloat
    const recentErrors = existingErrors.slice(-5);
    localStorage.setItem('fire-storage-errors', JSON.stringify(recentErrors));
  } catch (storageError) {
    console.warn('Failed to store error in localStorage:', storageError);
  }
};

interface UseLocalStorageOptions {
  serialize?: (value: any) => string;
  deserialize?: (value: string) => any;
  debounceMs?: number;
  onError?: (error: Error, key: string) => void;
  fallbackValue?: any;
}

/**
 * Advanced localStorage hook with debouncing, error handling, and quota management
 *
 * @param key - The localStorage key
 * @param defaultValue - Default value if key doesn't exist
 * @param options - Configuration options
 * @returns [storedValue, setValue, { isLoading, error, clearError }]
 */
// Check if localStorage is available - defined outside component to avoid hoisting issues
const checkLocalStorageAvailable = () => {
  try {
    const testKey = '__localStorage_test__';
    localStorage.setItem(testKey, 'test');
    localStorage.removeItem(testKey);
    return true;
  } catch {
    return false;
  }
};

export const useLocalStorage = <T>(
  key: string,
  defaultValue: T,
  options: UseLocalStorageOptions = {}
) => {
  const {
    serialize = JSON.stringify,
    deserialize = JSON.parse,
    debounceMs = 500,
    onError,
    fallbackValue = defaultValue,
  } = options;

  // State for the stored value
  const [storedValue, setStoredValue] = useState<T>(() => {
    if (!checkLocalStorageAvailable()) {
      logError(
        'localStorage Unavailable',
        new Error('localStorage not available'),
        { key }
      );
      return fallbackValue;
    }

    try {
      const item = localStorage.getItem(key);
      if (item === null || item === 'undefined') {
        return defaultValue;
      }
      return deserialize(item);
    } catch (error) {
      logError('localStorage Read Error', error, { key, defaultValue });
      onError?.(error as Error, key);
      return fallbackValue;
    }
  });

  // State for loading and error tracking
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // Refs for debouncing
  const timeoutRef = useRef<NodeJS.Timeout>();
  const pendingValueRef = useRef<T>();

  // Check if localStorage is available
  const isLocalStorageAvailable = useCallback(() => {
    return checkLocalStorageAvailable();
  }, []);

  // Clear error function
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Cleanup quota exceeded errors by removing old data
  const cleanupStorage = useCallback(() => {
    try {
      const keys = Object.keys(localStorage);
      const fireKeys = keys.filter(k => k.startsWith('fire-'));

      // Remove oldest entries if we have too many
      if (fireKeys.length > 50) {
        const keysToRemove = fireKeys.slice(0, fireKeys.length - 40);
        keysToRemove.forEach(k => {
          try {
            localStorage.removeItem(k);
          } catch (e) {
            console.warn('Failed to remove key during cleanup:', k, e);
          }
        });
      }
    } catch (error) {
      console.warn('Storage cleanup failed:', error);
    }
  }, []);

  // Debounced save function
  const debouncedSave = useCallback(
    (value: T) => {
      if (!checkLocalStorageAvailable()) {
        logError(
          'localStorage Unavailable for Save',
          new Error('localStorage not available'),
          { key, value }
        );
        return;
      }

      // Clear existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Store pending value
      pendingValueRef.current = value;
      setIsLoading(true);

      // Set new timeout
      timeoutRef.current = setTimeout(() => {
        try {
          const serializedValue = serialize(value);
          localStorage.setItem(key, serializedValue);
          setError(null);
          console.log(`✅ Saved to localStorage: ${key}`, value);
        } catch (error) {
          // Handle quota exceeded error
          if (error instanceof Error && error.name === 'QuotaExceededError') {
            logError('localStorage Quota Exceeded', error, { key, value });

            // Try to cleanup and retry once
            cleanupStorage();
            try {
              const serializedValue = serialize(value);
              localStorage.setItem(key, serializedValue);
              setError(null);
              console.log(
                `✅ Saved to localStorage after cleanup: ${key}`,
                value
              );
            } catch (retryError) {
              logError('localStorage Save Failed After Cleanup', retryError, {
                key,
                value,
              });
              setError(retryError as Error);
              onError?.(retryError as Error, key);
            }
          } else {
            logError('localStorage Save Error', error, { key, value });
            setError(error as Error);
            onError?.(error as Error, key);
          }
        } finally {
          setIsLoading(false);
          pendingValueRef.current = undefined;
        }
      }, debounceMs);
    },
    [key, serialize, debounceMs, onError, cleanupStorage]
  );

  // Set value function
  const setValue = useCallback(
    (value: T | ((prevValue: T) => T)) => {
      try {
        // Allow value to be a function so we have the same API as useState
        setStoredValue(prevValue => {
          const valueToStore =
            value instanceof Function ? value(prevValue) : value;
          debouncedSave(valueToStore);
          return valueToStore;
        });
      } catch (error) {
        logError('setValue Error', error, { key, value });
        setError(error as Error);
        onError?.(error as Error, key);
      }
    },
    [key, debouncedSave, onError]
  );

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);

        // Save any pending value immediately on unmount
        if (pendingValueRef.current !== undefined) {
          try {
            const serializedValue = serialize(pendingValueRef.current);
            localStorage.setItem(key, serializedValue);
            console.log(
              `✅ Saved pending value on unmount: ${key}`,
              pendingValueRef.current
            );
          } catch (error) {
            logError('Unmount Save Error', error, {
              key,
              value: pendingValueRef.current,
            });
          }
        }
      }
    };
  }, [key, serialize]);

  // Listen for storage events from other tabs
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === key && e.newValue !== null) {
        try {
          const newValue = deserialize(e.newValue);
          setStoredValue(newValue);
          console.log(`🔄 Updated from storage event: ${key}`, newValue);
        } catch (error) {
          logError('Storage Event Parse Error', error, {
            key,
            newValue: e.newValue,
          });
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [key, deserialize]);

  return [
    storedValue,
    setValue,
    {
      isLoading,
      error,
      clearError,
      isAvailable: checkLocalStorageAvailable(),
    },
  ] as const;
};

export default useLocalStorage;
