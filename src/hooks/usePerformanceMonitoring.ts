import { useEffect, useRef, useCallback } from 'react';
import { performanceMonitor } from '../utils/performance-monitor';
import { debugLogger } from '../utils/debug-logger';

export interface UsePerformanceMonitoringOptions {
  componentName?: string;
  trackRenders?: boolean;
  trackEffects?: boolean;
  trackCalculations?: boolean;
  slowRenderThreshold?: number;
}

export const usePerformanceMonitoring = (
  options: UsePerformanceMonitoringOptions = {},
) => {
  const {
    componentName = 'UnknownComponent',
    trackRenders = true,
    trackEffects = true,
    trackCalculations = true,
    slowRenderThreshold = 16, // 16ms for 60fps
  } = options;

  const renderStartTime = useRef<number>(0);
  const renderCount = useRef<number>(0);
  const mountTime = useRef<number>(0);

  // Track component mount
  useEffect(() => {
    mountTime.current = performance.now();
    const mountMetricId = performanceMonitor.startMetric(
      `${componentName}_mount`,
      'component',
      { componentName, type: 'mount' },
    );

    debugLogger.log(
      'debug',
      'performance',
      `Component mounted: ${componentName}`,
    );

    return () => {
      // Track component unmount
      const unmountTime = performance.now();
      const totalMountTime = unmountTime - mountTime.current;

      performanceMonitor.endMetric(mountMetricId);

      debugLogger.log(
        'debug',
        'performance',
        `Component unmounted: ${componentName}`,
        {
          totalMountTime: `${totalMountTime.toFixed(2)}ms`,
          renderCount: renderCount.current,
        },
      );
    };
  }, [componentName]);

  // Track renders
  useEffect(() => {
    if (!trackRenders) return;

    const renderEndTime = performance.now();
    const renderDuration = renderEndTime - renderStartTime.current;
    renderCount.current++;

    if (renderStartTime.current > 0) {
      if (renderDuration > slowRenderThreshold) {
        debugLogger.log(
          'warn',
          'performance',
          `Slow render detected: ${componentName}`,
          {
            duration: `${renderDuration.toFixed(2)}ms`,
            threshold: `${slowRenderThreshold}ms`,
            renderCount: renderCount.current,
          },
        );
      }

      // Record render metric
      performanceMonitor.startMetric(
        `${componentName}_render_${renderCount.current}`,
        'render',
        {
          componentName,
          renderCount: renderCount.current,
          duration: renderDuration,
        },
      );
    }

    renderStartTime.current = performance.now();
  });

  // Performance tracking utilities
  const trackCalculation = useCallback(
    (calculationName: string, calculationFn: () => any) => {
      if (!trackCalculations) return calculationFn();

      const metricId = performanceMonitor.startMetric(
        `${componentName}_${calculationName}`,
        'calculation',
        { componentName, calculationName },
      );

      const startTime = performance.now();

      try {
        const result = calculationFn();
        const endTime = performance.now();
        const duration = endTime - startTime;

        performanceMonitor.endMetric(metricId);

        if (duration > 10) {
          // Log calculations taking more than 10ms
          debugLogger.log(
            'info',
            'performance',
            `Calculation completed: ${calculationName}`,
            {
              componentName,
              duration: `${duration.toFixed(2)}ms`,
            },
          );
        }

        return result;
      } catch (error) {
        performanceMonitor.endMetric(metricId);
        debugLogger.log(
          'error',
          'performance',
          `Calculation failed: ${calculationName}`,
          {
            componentName,
            error: (error as Error).message,
          },
        );
        throw error;
      }
    },
    [componentName, trackCalculations],
  );

  const trackAsyncOperation = useCallback(
    async (operationName: string, operationFn: () => Promise<any>) => {
      const metricId = performanceMonitor.startMetric(
        `${componentName}_${operationName}`,
        'component',
        { componentName, operationName, type: 'async' },
      );

      const startTime = performance.now();

      try {
        const result = await operationFn();
        const endTime = performance.now();
        const duration = endTime - startTime;

        performanceMonitor.endMetric(metricId);

        debugLogger.log(
          'info',
          'performance',
          `Async operation completed: ${operationName}`,
          {
            componentName,
            duration: `${duration.toFixed(2)}ms`,
          },
        );

        return result;
      } catch (error) {
        performanceMonitor.endMetric(metricId);
        debugLogger.log(
          'error',
          'performance',
          `Async operation failed: ${operationName}`,
          {
            componentName,
            error: (error as Error).message,
          },
        );
        throw error;
      }
    },
    [componentName],
  );

  const trackEffect = useCallback(
    (effectName: string, effectFn: () => void | (() => void)) => {
      if (!trackEffects) return effectFn();

      const metricId = performanceMonitor.startMetric(
        `${componentName}_effect_${effectName}`,
        'component',
        { componentName, effectName, type: 'effect' },
      );

      const startTime = performance.now();

      try {
        const cleanup = effectFn();
        const endTime = performance.now();
        const duration = endTime - startTime;

        performanceMonitor.endMetric(metricId);

        if (duration > 5) {
          // Log effects taking more than 5ms
          debugLogger.log(
            'info',
            'performance',
            `Effect completed: ${effectName}`,
            {
              componentName,
              duration: `${duration.toFixed(2)}ms`,
            },
          );
        }

        return cleanup;
      } catch (error) {
        performanceMonitor.endMetric(metricId);
        debugLogger.log(
          'error',
          'performance',
          `Effect failed: ${effectName}`,
          {
            componentName,
            error: (error as Error).message,
          },
        );
        throw error;
      }
    },
    [componentName, trackEffects],
  );

  const measureRenderTime = useCallback(() => {
    renderStartTime.current = performance.now();
  }, []);

  const getComponentStats = useCallback(() => {
    const metrics = performanceMonitor.getMetrics();
    const componentMetrics = metrics.filter(
      metric => metric.metadata?.componentName === componentName,
    );

    const renderMetrics = componentMetrics.filter(
      metric => metric.type === 'render',
    );
    const calculationMetrics = componentMetrics.filter(
      metric => metric.type === 'calculation',
    );
    const effectMetrics = componentMetrics.filter(
      metric =>
        metric.type === 'component' && metric.metadata?.type === 'effect',
    );

    const avgRenderTime =
      renderMetrics.length > 0
        ? renderMetrics.reduce((sum, metric) => sum + metric.duration, 0) /
          renderMetrics.length
        : 0;

    const slowRenders = renderMetrics.filter(
      metric => metric.duration > slowRenderThreshold,
    );

    return {
      componentName,
      totalRenders: renderCount.current,
      averageRenderTime: avgRenderTime,
      slowRenders: slowRenders.length,
      totalCalculations: calculationMetrics.length,
      totalEffects: effectMetrics.length,
      mountTime: mountTime.current,
      metrics: componentMetrics,
    };
  }, [componentName, slowRenderThreshold]);

  return {
    trackCalculation,
    trackAsyncOperation,
    trackEffect,
    measureRenderTime,
    getComponentStats,
    renderCount: renderCount.current,
  };
};

// Hook for tracking specific Swiss Budget Pro calculations
export const useSwissCalculationPerformance = (componentName: string) => {
  const { trackCalculation, trackAsyncOperation } = usePerformanceMonitoring({
    componentName,
    trackCalculations: true,
  });

  const trackTaxCalculation = useCallback(
    (canton: string, income: number, calculationFn: () => any) => {
      return trackCalculation(`tax_calculation_${canton}`, () => {
        debugLogger.log('debug', 'swiss-calc', 'Starting tax calculation', {
          canton,
          income,
          componentName,
        });

        const result = calculationFn();

        debugLogger.log('debug', 'swiss-calc', 'Tax calculation completed', {
          canton,
          income,
          result: result?.totalTax,
          componentName,
        });

        return result;
      });
    },
    [trackCalculation, componentName],
  );

  const trackFIRECalculation = useCallback(
    (scenario: any, calculationFn: () => any) => {
      return trackCalculation('fire_calculation', () => {
        debugLogger.log('debug', 'swiss-calc', 'Starting FIRE calculation', {
          scenario: {
            income: scenario.income,
            expenses: scenario.expenses,
            savings: scenario.savings,
          },
          componentName,
        });

        const result = calculationFn();

        debugLogger.log('debug', 'swiss-calc', 'FIRE calculation completed', {
          yearsToFIRE: result?.yearsToFIRE,
          fireNumber: result?.fireNumber,
          componentName,
        });

        return result;
      });
    },
    [trackCalculation, componentName],
  );

  const trackHealthcareOptimization = useCallback(
    (params: any, calculationFn: () => any) => {
      return trackCalculation('healthcare_optimization', () => {
        debugLogger.log(
          'debug',
          'swiss-calc',
          'Starting healthcare optimization',
          {
            canton: params.canton,
            age: params.age,
            riskProfile: params.riskProfile,
            componentName,
          },
        );

        const result = calculationFn();

        debugLogger.log(
          'debug',
          'swiss-calc',
          'Healthcare optimization completed',
          {
            recommendedDeductible: result?.recommendedDeductible,
            annualSavings: result?.annualSavings,
            componentName,
          },
        );

        return result;
      });
    },
    [trackCalculation, componentName],
  );

  const trackDataPersistence = useCallback(
    async (operation: string, operationFn: () => Promise<any>) => {
      return trackAsyncOperation(`data_${operation}`, async () => {
        debugLogger.log('debug', 'data-persistence', `Starting ${operation}`, {
          componentName,
        });

        const result = await operationFn();

        debugLogger.log('debug', 'data-persistence', `${operation} completed`, {
          componentName,
          dataSize: JSON.stringify(result || {}).length,
        });

        return result;
      });
    },
    [trackAsyncOperation, componentName],
  );

  return {
    trackTaxCalculation,
    trackFIRECalculation,
    trackHealthcareOptimization,
    trackDataPersistence,
  };
};

// Hook for tracking user interactions
export const useUserInteractionTracking = (componentName: string) => {
  const trackInteraction = useCallback(
    (interactionType: string, details?: any) => {
      const metricId = performanceMonitor.startMetric(
        `${componentName}_interaction_${interactionType}`,
        'component',
        {
          componentName,
          interactionType,
          type: 'interaction',
          ...details,
        },
      );

      debugLogger.log(
        'info',
        'user-interaction',
        `User interaction: ${interactionType}`,
        {
          componentName,
          details,
        },
      );

      // Return a function to end the interaction tracking
      return () => {
        performanceMonitor.endMetric(metricId);
      };
    },
    [componentName],
  );

  const trackFormSubmission = useCallback(
    (formName: string, formData?: any) => {
      return trackInteraction('form_submission', {
        formName,
        fieldCount: formData ? Object.keys(formData).length : 0,
      });
    },
    [trackInteraction],
  );

  const trackTabChange = useCallback(
    (fromTab: string, toTab: string) => {
      return trackInteraction('tab_change', {
        fromTab,
        toTab,
      });
    },
    [trackInteraction],
  );

  const trackCalculationTrigger = useCallback(
    (calculationType: string, inputChanges?: any) => {
      return trackInteraction('calculation_trigger', {
        calculationType,
        inputChanges,
      });
    },
    [trackInteraction],
  );

  return {
    trackInteraction,
    trackFormSubmission,
    trackTabChange,
    trackCalculationTrigger,
  };
};
