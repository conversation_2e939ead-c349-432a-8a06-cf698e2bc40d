import { useRef, useEffect, useCallback, RefObject } from 'react';
import * as d3 from 'd3';

export interface ChartDimensions {
  width: number;
  height: number;
  margin: { top: number; right: number; bottom: number; left: number };
}

export interface ChartScales {
  xScale: d3.ScaleTime<number, number> | d3.ScaleLinear<number, number>;
  yScale: d3.ScaleLinear<number, number>;
  innerWidth: number;
  innerHeight: number;
}

export interface UseD3ChartOptions {
  responsive?: boolean;
  animated?: boolean;
  darkMode?: boolean;
  onResize?: (dimensions: ChartDimensions) => void;
}

export const useD3Chart = <T>(
  data: T[],
  dimensions: ChartDimensions,
  options: UseD3ChartOptions = {}
) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const { responsive = true, animated = true, darkMode = false, onResize } = options;

  // Get responsive dimensions
  const getResponsiveDimensions = useCallback((): ChartDimensions => {
    if (!responsive || !containerRef.current) {
      return dimensions;
    }

    const containerWidth = containerRef.current.clientWidth;
    const responsiveWidth = Math.max(300, Math.min(containerWidth - 20, dimensions.width));
    
    return {
      ...dimensions,
      width: responsiveWidth,
    };
  }, [responsive, dimensions]);

  // Create SVG selection
  const createSVG = useCallback(() => {
    if (!svgRef.current) return null;
    
    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();
    
    const dims = getResponsiveDimensions();
    svg.attr('width', dims.width).attr('height', dims.height);
    
    return { svg, dimensions: dims };
  }, [getResponsiveDimensions]);

  // Create scales helper
  const createTimeScale = useCallback((
    domain: [Date, Date],
    range: [number, number]
  ): d3.ScaleTime<number, number> => {
    return d3.scaleTime().domain(domain).range(range);
  }, []);

  const createLinearScale = useCallback((
    domain: [number, number],
    range: [number, number],
    nice = true
  ): d3.ScaleLinear<number, number> => {
    const scale = d3.scaleLinear().domain(domain).range(range);
    return nice ? scale.nice() : scale;
  }, []);

  // Animation helpers
  const animatePathDraw = useCallback((
    path: d3.Selection<SVGPathElement, any, any, any>,
    duration = 1500
  ) => {
    if (!animated) return;

    const totalLength = path.node()?.getTotalLength() || 0;
    path
      .attr('stroke-dasharray', `${totalLength} ${totalLength}`)
      .attr('stroke-dashoffset', totalLength)
      .transition()
      .duration(duration)
      .ease(d3.easeLinear)
      .attr('stroke-dashoffset', 0);
  }, [animated]);

  const animateElementsSequentially = useCallback((
    selection: d3.Selection<any, any, any, any>,
    duration = 800,
    delay = 50
  ) => {
    if (!animated) return;

    selection
      .style('opacity', 0)
      .transition()
      .duration(duration)
      .delay((d, i) => i * delay)
      .style('opacity', 1);
  }, [animated]);

  // Tooltip helpers
  const createTooltip = useCallback(() => {
    return d3
      .select('body')
      .append('div')
      .attr('class', `tooltip ${darkMode ? 'dark' : ''}`)
      .style('position', 'absolute')
      .style('visibility', 'hidden')
      .style('background-color', darkMode ? '#1F2937' : 'white')
      .style('border', `1px solid ${darkMode ? '#374151' : '#E5E7EB'}`)
      .style('border-radius', '6px')
      .style('padding', '8px')
      .style('font-size', '12px')
      .style('box-shadow', '0 4px 6px -1px rgba(0, 0, 0, 0.1)')
      .style('z-index', '1000')
      .style('pointer-events', 'none');
  }, [darkMode]);

  const showTooltip = useCallback((
    tooltip: d3.Selection<HTMLDivElement, any, any, any>,
    content: string,
    event: MouseEvent
  ) => {
    tooltip
      .style('visibility', 'visible')
      .html(content)
      .style('left', `${event.pageX + 10}px`)
      .style('top', `${event.pageY - 10}px`);
  }, []);

  const hideTooltip = useCallback((
    tooltip: d3.Selection<HTMLDivElement, any, any, any>
  ) => {
    tooltip.style('visibility', 'hidden');
  }, []);

  // Axis helpers
  const createXAxis = useCallback((
    scale: d3.ScaleTime<number, number> | d3.ScaleLinear<number, number>,
    format?: string
  ) => {
    if (scale instanceof d3.ScaleTime || 'invert' in scale) {
      return d3.axisBottom(scale as d3.ScaleTime<number, number>)
        .tickFormat(format ? d3.timeFormat(format) : d3.timeFormat('%b %Y'));
    }
    return d3.axisBottom(scale as d3.ScaleLinear<number, number>);
  }, []);

  const createYAxis = useCallback((
    scale: d3.ScaleLinear<number, number>,
    format?: (value: number) => string
  ) => {
    return d3.axisLeft(scale)
      .tickFormat(format || (d => d.toString()));
  }, []);

  const styleAxes = useCallback((
    g: d3.Selection<SVGGElement, any, any, any>
  ) => {
    g.selectAll('.domain, .tick line')
      .style('stroke', darkMode ? '#4B5563' : '#E5E7EB');
    
    g.selectAll('text')
      .style('fill', darkMode ? '#9CA3AF' : '#6B7280');
  }, [darkMode]);

  // Currency formatting for Swiss locale
  const formatCurrency = useCallback((value: number, compact = false) => {
    return new Intl.NumberFormat('de-CH', {
      style: 'currency',
      currency: 'CHF',
      minimumFractionDigits: 0,
      maximumFractionDigits: compact ? 0 : 2,
      notation: compact && value >= 1000 ? 'compact' : 'standard',
    }).format(value);
  }, []);

  // Date formatting for Swiss locale
  const formatDate = useCallback((date: Date, format: 'short' | 'long' = 'short') => {
    return new Intl.DateTimeFormat('de-CH', {
      year: 'numeric',
      month: format === 'short' ? 'short' : 'long',
      day: 'numeric',
    }).format(date);
  }, []);

  // Percentage formatting
  const formatPercentage = useCallback((value: number, decimals = 1) => {
    return new Intl.NumberFormat('de-CH', {
      style: 'percent',
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals,
    }).format(value / 100);
  }, []);

  // Handle resize
  useEffect(() => {
    if (!responsive) return;

    const handleResize = () => {
      const newDimensions = getResponsiveDimensions();
      onResize?.(newDimensions);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [responsive, getResponsiveDimensions, onResize]);

  // Cleanup tooltips on unmount
  useEffect(() => {
    return () => {
      d3.selectAll('.tooltip').remove();
    };
  }, []);

  return {
    svgRef,
    containerRef,
    createSVG,
    createTimeScale,
    createLinearScale,
    animatePathDraw,
    animateElementsSequentially,
    createTooltip,
    showTooltip,
    hideTooltip,
    createXAxis,
    createYAxis,
    styleAxes,
    formatCurrency,
    formatDate,
    formatPercentage,
    getResponsiveDimensions,
  };
};

export default useD3Chart;
