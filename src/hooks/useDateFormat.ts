import { useTranslation } from 'react-i18next';
import { useMemo } from 'react';

interface DateFormatOptions {
  dateStyle?: 'full' | 'long' | 'medium' | 'short';
  timeStyle?: 'full' | 'long' | 'medium' | 'short';
  year?: 'numeric' | '2-digit';
  month?: 'numeric' | '2-digit' | 'long' | 'short' | 'narrow';
  day?: 'numeric' | '2-digit';
  weekday?: 'long' | 'short' | 'narrow';
}

export const useDateFormat = () => {
  const { i18n } = useTranslation();

  const formatters = useMemo(() => {
    const locale = i18n.language === 'de' ? 'de-CH' : i18n.language;

    return {
      // Short date format (DD.MM.YYYY for German, MM/DD/YYYY for English)
      short: new Intl.DateTimeFormat(locale, {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
      }),

      // Medium date format with month name
      medium: new Intl.DateTimeFormat(locale, {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      }),

      // Long date format with full month name
      long: new Intl.DateTimeFormat(locale, {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      }),

      // Full date format with weekday
      full: new Intl.DateTimeFormat(locale, {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      }),

      // Month and year only
      monthYear: new Intl.DateTimeFormat(locale, {
        year: 'numeric',
        month: 'long',
      }),

      // Year only
      year: new Intl.DateTimeFormat(locale, {
        year: 'numeric',
      }),

      // Time format
      time: new Intl.DateTimeFormat(locale, {
        hour: '2-digit',
        minute: '2-digit',
      }),

      // Date and time
      dateTime: new Intl.DateTimeFormat(locale, {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
      }),
    };
  }, [i18n.language]);

  // Format date in short format (DD.MM.YYYY for German, MM/DD/YYYY for English)
  const formatDate = (date: Date | string | number): string => {
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) return '';

    return formatters.short.format(dateObj);
  };

  // Format date in medium format with month abbreviation
  const formatDateMedium = (date: Date | string | number): string => {
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) return '';

    return formatters.medium.format(dateObj);
  };

  // Format date in long format with full month name
  const formatDateLong = (date: Date | string | number): string => {
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) return '';

    return formatters.long.format(dateObj);
  };

  // Format date in full format with weekday
  const formatDateFull = (date: Date | string | number): string => {
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) return '';

    return formatters.full.format(dateObj);
  };

  // Format month and year
  const formatMonthYear = (date: Date | string | number): string => {
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) return '';

    return formatters.monthYear.format(dateObj);
  };

  // Format year only
  const formatYear = (date: Date | string | number): string => {
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) return '';

    return formatters.year.format(dateObj);
  };

  // Format time only
  const formatTime = (date: Date | string | number): string => {
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) return '';

    return formatters.time.format(dateObj);
  };

  // Format date and time
  const formatDateTime = (date: Date | string | number): string => {
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) return '';

    return formatters.dateTime.format(dateObj);
  };

  // Format relative time (e.g., "2 days ago", "in 3 months")
  const formatRelativeTime = (date: Date | string | number): string => {
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) return '';

    const now = new Date();
    const diffInSeconds = Math.floor(
      (dateObj.getTime() - now.getTime()) / 1000,
    );

    const rtf = new Intl.RelativeTimeFormat(
      i18n.language === 'de' ? 'de-CH' : i18n.language,
      {
        numeric: 'auto',
      },
    );

    const absDiff = Math.abs(diffInSeconds);

    if (absDiff < 60) {
      return rtf.format(diffInSeconds, 'second');
    } else if (absDiff < 3600) {
      return rtf.format(Math.floor(diffInSeconds / 60), 'minute');
    } else if (absDiff < 86400) {
      return rtf.format(Math.floor(diffInSeconds / 3600), 'hour');
    } else if (absDiff < 2592000) {
      return rtf.format(Math.floor(diffInSeconds / 86400), 'day');
    } else if (absDiff < 31536000) {
      return rtf.format(Math.floor(diffInSeconds / 2592000), 'month');
    } else {
      return rtf.format(Math.floor(diffInSeconds / 31536000), 'year');
    }
  };

  // Parse date from localized string
  const parseDate = (dateString: string): Date | null => {
    if (!dateString) return null;

    // Try different date formats based on locale
    if (i18n.language === 'de') {
      // Try DD.MM.YYYY format
      const parts = dateString.split('.');
      if (parts.length === 3) {
        const day = parseInt(parts[0], 10);
        const month = parseInt(parts[1], 10) - 1; // Month is 0-indexed
        const year = parseInt(parts[2], 10);

        const date = new Date(year, month, day);
        if (!isNaN(date.getTime())) {
          return date;
        }
      }
    } else {
      // Try MM/DD/YYYY format
      const parts = dateString.split('/');
      if (parts.length === 3) {
        const month = parseInt(parts[0], 10) - 1; // Month is 0-indexed
        const day = parseInt(parts[1], 10);
        const year = parseInt(parts[2], 10);

        const date = new Date(year, month, day);
        if (!isNaN(date.getTime())) {
          return date;
        }
      }
    }

    // Fallback to standard Date parsing
    const date = new Date(dateString);
    return isNaN(date.getTime()) ? null : date;
  };

  // Get date format pattern for input fields
  const getDatePattern = (): string => {
    return i18n.language === 'de' ? 'dd.mm.yyyy' : 'mm/dd/yyyy';
  };

  // Get date format placeholder for input fields
  const getDatePlaceholder = (): string => {
    return i18n.language === 'de' ? 'TT.MM.JJJJ' : 'MM/DD/YYYY';
  };

  return {
    formatDate,
    formatDateMedium,
    formatDateLong,
    formatDateFull,
    formatMonthYear,
    formatYear,
    formatTime,
    formatDateTime,
    formatRelativeTime,
    parseDate,
    getDatePattern,
    getDatePlaceholder,
    formatters,
  };
};

export default useDateFormat;
