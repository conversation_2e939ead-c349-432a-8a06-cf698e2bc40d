import { useTranslation } from 'react-i18next';
import { useMemo } from 'react';

interface NumberFormatOptions {
  style?: 'decimal' | 'currency' | 'percent';
  currency?: string;
  minimumFractionDigits?: number;
  maximumFractionDigits?: number;
  useGrouping?: boolean;
}

interface SwissNumberFormatOptions extends NumberFormatOptions {
  useSwissFormat?: boolean; // Use Swiss-specific formatting (apostrophe as thousands separator)
}

export const useNumberFormat = () => {
  const { i18n } = useTranslation();

  const formatters = useMemo(() => {
    const locale = i18n.language;

    // Swiss-specific formatting for German
    const getSwissLocale = (baseLocale: string): string => {
      if (baseLocale === 'de') {
        return 'de-CH'; // Swiss German
      }
      return baseLocale;
    };

    return {
      // Currency formatter
      currency: new Intl.NumberFormat(getSwissLocale(locale), {
        style: 'currency',
        currency: 'CHF',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }),

      // Currency formatter without decimals for large amounts
      currencyWhole: new Intl.NumberFormat(getSwissLocale(locale), {
        style: 'currency',
        currency: 'CHF',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }),

      // Percentage formatter
      percentage: new Intl.NumberFormat(getSwissLocale(locale), {
        style: 'percent',
        minimumFractionDigits: 1,
        maximumFractionDigits: 1,
      }),

      // Decimal number formatter
      decimal: new Intl.NumberFormat(getSwissLocale(locale), {
        style: 'decimal',
        minimumFractionDigits: 0,
        maximumFractionDigits: 2,
      }),

      // Integer formatter
      integer: new Intl.NumberFormat(getSwissLocale(locale), {
        style: 'decimal',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }),
    };
  }, [i18n.language]);

  // Format currency with CHF
  const formatCurrency = (
    amount: number | string,
    options?: SwissNumberFormatOptions,
  ): string => {
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;

    if (isNaN(numAmount)) return 'CHF 0.00';

    if (options?.useSwissFormat && i18n.language === 'de') {
      // Swiss German formatting with apostrophe
      const formatted = formatters.currency.format(numAmount);
      return formatted.replace(/,/g, "'"); // Replace comma with apostrophe for thousands
    }

    return formatters.currency.format(numAmount);
  };

  // Format currency without decimals for large amounts
  const formatCurrencyWhole = (amount: number | string): string => {
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;

    if (isNaN(numAmount)) return 'CHF 0';

    if (i18n.language === 'de') {
      const formatted = formatters.currencyWhole.format(numAmount);
      return formatted.replace(/,/g, "'");
    }

    return formatters.currencyWhole.format(numAmount);
  };

  // Format percentage
  const formatPercentage = (
    value: number | string,
    decimals: number = 1,
  ): string => {
    const numValue = typeof value === 'string' ? parseFloat(value) : value;

    if (isNaN(numValue)) return '0.0%';

    const formatter = new Intl.NumberFormat(
      i18n.language === 'de' ? 'de-CH' : i18n.language,
      {
        style: 'percent',
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals,
      },
    );

    // Convert to percentage (divide by 100 if it's already a percentage value)
    const percentValue = numValue > 1 ? numValue / 100 : numValue;

    if (i18n.language === 'de') {
      // German uses comma as decimal separator
      return formatter.format(percentValue).replace('.', ',');
    }

    return formatter.format(percentValue);
  };

  // Format decimal number
  const formatNumber = (
    value: number | string,
    decimals: number = 2,
  ): string => {
    const numValue = typeof value === 'string' ? parseFloat(value) : value;

    if (isNaN(numValue)) return '0';

    const formatter = new Intl.NumberFormat(
      i18n.language === 'de' ? 'de-CH' : i18n.language,
      {
        style: 'decimal',
        minimumFractionDigits: 0,
        maximumFractionDigits: decimals,
      },
    );

    if (i18n.language === 'de') {
      const formatted = formatter.format(numValue);
      return formatted.replace(/,/g, "'").replace('.', ',');
    }

    return formatter.format(numValue);
  };

  // Format integer
  const formatInteger = (value: number | string): string => {
    const numValue =
      typeof value === 'string' ? parseInt(value) : Math.round(value);

    if (isNaN(numValue)) return '0';

    if (i18n.language === 'de') {
      const formatted = formatters.integer.format(numValue);
      return formatted.replace(/,/g, "'");
    }

    return formatters.integer.format(numValue);
  };

  // Parse number from localized string
  const parseNumber = (value: string): number => {
    if (!value) return 0;

    let cleanValue = value.toString();

    if (i18n.language === 'de') {
      // Handle Swiss German format: replace apostrophe with nothing, comma with dot
      cleanValue = cleanValue.replace(/'/g, '').replace(',', '.');
    } else {
      // Handle English format: remove commas
      cleanValue = cleanValue.replace(/,/g, '');
    }

    // Remove currency symbols and percentage signs
    cleanValue = cleanValue.replace(/[CHF$€%\s]/g, '');

    const parsed = parseFloat(cleanValue);
    return isNaN(parsed) ? 0 : parsed;
  };

  // Get decimal separator for current locale
  const getDecimalSeparator = (): string => {
    return i18n.language === 'de' ? ',' : '.';
  };

  // Get thousands separator for current locale
  const getThousandsSeparator = (): string => {
    return i18n.language === 'de' ? "'" : ',';
  };

  return {
    formatCurrency,
    formatCurrencyWhole,
    formatPercentage,
    formatNumber,
    formatInteger,
    parseNumber,
    getDecimalSeparator,
    getThousandsSeparator,
    formatters,
  };
};

export default useNumberFormat;
