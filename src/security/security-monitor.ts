/**
 * Swiss Budget Pro - Security Monitoring & Audit Trail
 *
 * Provides comprehensive security event logging, threat detection,
 * and audit trail management for compliance and security monitoring.
 */

// Security event types
export enum SecurityEventType {
  // Authentication events
  ENCRYPTION_KEY_DERIVED = 'encryption_key_derived',
  ENCRYPTION_SUCCESS = 'encryption_success',
  ENCRYPTION_FAILURE = 'encryption_failure',
  DECRYPTION_SUCCESS = 'decryption_success',
  DECRYPTION_FAILURE = 'decryption_failure',

  // Data access events
  DATA_ACCESS = 'data_access',
  DATA_MODIFICATION = 'data_modification',
  DATA_EXPORT = 'data_export',
  DATA_IMPORT = 'data_import',
  DATA_DELETION = 'data_deletion',

  // Privacy events
  PRIVACY_SETTINGS_CHANGED = 'privacy_settings_changed',
  CONSENT_GRANTED = 'consent_granted',
  CONSENT_REVOKED = 'consent_revoked',
  USER_RIGHTS_REQUEST = 'user_rights_request',

  // Security threats
  SUSPICIOUS_ACTIVITY = 'suspicious_activity',
  INTEGRITY_VIOLATION = 'integrity_violation',
  UNAUTHORIZED_ACCESS_ATTEMPT = 'unauthorized_access_attempt',
  MALICIOUS_EXTENSION_DETECTED = 'malicious_extension_detected',

  // System events
  APPLICATION_START = 'application_start',
  APPLICATION_ERROR = 'application_error',
  PERFORMANCE_ANOMALY = 'performance_anomaly',
  BROWSER_SECURITY_WARNING = 'browser_security_warning',
}

// Security event severity levels
export enum SecuritySeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

// Security event record
export interface SecurityEvent {
  id: string;
  timestamp: Date;
  type: SecurityEventType;
  severity: SecuritySeverity;
  description: string;
  details: Record<string, any>;
  userAgent?: string;
  ipAddress?: string;
  sessionId?: string;
  resolved: boolean;
  resolvedAt?: Date;
  resolvedBy?: string;
}

// Security metrics
export interface SecurityMetrics {
  totalEvents: number;
  eventsByType: Record<SecurityEventType, number>;
  eventsBySeverity: Record<SecuritySeverity, number>;
  threatsDetected: number;
  threatsResolved: number;
  averageResponseTime: number;
  lastThreatDetected?: Date;
}

// Threat detection rules
export interface ThreatRule {
  id: string;
  name: string;
  description: string;
  eventTypes: SecurityEventType[];
  threshold: number;
  timeWindow: number; // in minutes
  severity: SecuritySeverity;
  enabled: boolean;
}

// Security status
export interface SecurityStatus {
  overall: 'secure' | 'warning' | 'critical';
  threats: number;
  lastScan: Date;
  recommendations: string[];
  complianceScore: number;
}

/**
 * Security Monitoring & Audit Trail Manager
 *
 * Provides comprehensive security event logging, threat detection,
 * and audit trail management for Swiss Budget Pro.
 */
export class SecurityMonitor {
  private static instance: SecurityMonitor;
  private events: SecurityEvent[] = [];
  private threatRules: ThreatRule[] = [];
  private sessionId: string;
  private isMonitoring: boolean = false;

  private constructor() {
    this.sessionId = this.generateSessionId();
    this.initializeThreatRules();

    // Only load events if not in test environment
    if (typeof process === 'undefined' || process.env.NODE_ENV !== 'test') {
      this.loadEvents();
      this.startMonitoring();
    }
  }

  public static getInstance(): SecurityMonitor {
    if (!SecurityMonitor.instance) {
      SecurityMonitor.instance = new SecurityMonitor();
    }
    return SecurityMonitor.instance;
  }

  /**
   * Start security monitoring
   */
  public startMonitoring(): void {
    if (this.isMonitoring) {
      return;
    }

    this.isMonitoring = true;
    this.logEvent(
      SecurityEventType.APPLICATION_START,
      SecuritySeverity.LOW,
      'Security monitoring started'
    );

    // Set up periodic threat detection
    setInterval(() => {
      this.runThreatDetection();
    }, 60000); // Run every minute

    // Set up performance monitoring
    this.setupPerformanceMonitoring();

    // Set up browser security monitoring
    this.setupBrowserSecurityMonitoring();
  }

  /**
   * Stop security monitoring
   */
  public stopMonitoring(): void {
    this.isMonitoring = false;
    this.logEvent(
      SecurityEventType.APPLICATION_START,
      SecuritySeverity.LOW,
      'Security monitoring stopped'
    );
  }

  /**
   * Log a security event
   */
  public logEvent(
    type: SecurityEventType,
    severity: SecuritySeverity,
    description: string,
    details: Record<string, any> = {}
  ): string {
    const event: SecurityEvent = {
      id: this.generateEventId(),
      timestamp: new Date(),
      type,
      severity,
      description,
      details,
      userAgent: navigator.userAgent,
      sessionId: this.sessionId,
      resolved: false,
    };

    this.events.push(event);
    this.saveEvents();

    // Trigger immediate threat detection for high/critical events
    if (
      severity === SecuritySeverity.HIGH ||
      severity === SecuritySeverity.CRITICAL
    ) {
      this.runThreatDetection();
    }

    // Log to console for development
    console.log(
      `[SECURITY] ${severity.toUpperCase()}: ${description}`,
      details
    );

    return event.id;
  }

  /**
   * Get security events with optional filtering
   */
  public getEvents(filter?: {
    type?: SecurityEventType;
    severity?: SecuritySeverity;
    startDate?: Date;
    endDate?: Date;
    resolved?: boolean;
  }): SecurityEvent[] {
    let filteredEvents = [...this.events];

    if (filter) {
      if (filter.type) {
        filteredEvents = filteredEvents.filter(e => e.type === filter.type);
      }
      if (filter.severity) {
        filteredEvents = filteredEvents.filter(
          e => e.severity === filter.severity
        );
      }
      if (filter.startDate) {
        filteredEvents = filteredEvents.filter(
          e => e.timestamp >= filter.startDate!
        );
      }
      if (filter.endDate) {
        filteredEvents = filteredEvents.filter(
          e => e.timestamp <= filter.endDate!
        );
      }
      if (filter.resolved !== undefined) {
        filteredEvents = filteredEvents.filter(
          e => e.resolved === filter.resolved
        );
      }
    }

    return filteredEvents.sort(
      (a, b) => b.timestamp.getTime() - a.timestamp.getTime()
    );
  }

  /**
   * Resolve a security event
   */
  public resolveEvent(eventId: string, resolvedBy: string = 'system'): boolean {
    const event = this.events.find(e => e.id === eventId);
    if (!event) {
      return false;
    }

    event.resolved = true;
    event.resolvedAt = new Date();
    event.resolvedBy = resolvedBy;

    this.saveEvents();
    return true;
  }

  /**
   * Get security metrics
   */
  public getMetrics(): SecurityMetrics {
    const eventsByType: Record<SecurityEventType, number> = {} as any;
    const eventsBySeverity: Record<SecuritySeverity, number> = {} as any;

    // Initialize counters
    Object.values(SecurityEventType).forEach(type => {
      eventsByType[type] = 0;
    });
    Object.values(SecuritySeverity).forEach(severity => {
      eventsBySeverity[severity] = 0;
    });

    // Count events
    this.events.forEach(event => {
      eventsByType[event.type]++;
      eventsBySeverity[event.severity]++;
    });

    const threats = this.events.filter(
      e =>
        e.type === SecurityEventType.SUSPICIOUS_ACTIVITY ||
        e.type === SecurityEventType.INTEGRITY_VIOLATION ||
        e.type === SecurityEventType.UNAUTHORIZED_ACCESS_ATTEMPT ||
        e.type === SecurityEventType.MALICIOUS_EXTENSION_DETECTED
    );

    const lastThreat =
      threats.length > 0
        ? threats.sort(
            (a, b) => b.timestamp.getTime() - a.timestamp.getTime()
          )[0].timestamp
        : undefined;

    return {
      totalEvents: this.events.length,
      eventsByType,
      eventsBySeverity,
      threatsDetected: threats.length,
      threatsResolved: threats.filter(t => t.resolved).length,
      averageResponseTime: this.calculateAverageResponseTime(),
      lastThreatDetected: lastThreat,
    };
  }

  /**
   * Get current security status
   */
  public getSecurityStatus(): SecurityStatus {
    const metrics = this.getMetrics();
    const unresolvedThreats = metrics.threatsDetected - metrics.threatsResolved;
    const criticalEvents = this.events.filter(
      e => e.severity === SecuritySeverity.CRITICAL && !e.resolved
    ).length;

    let overall: SecurityStatus['overall'] = 'secure';
    const recommendations: string[] = [];

    if (criticalEvents > 0) {
      overall = 'critical';
      recommendations.push(
        `${criticalEvents} critical security events require immediate attention`
      );
    } else if (unresolvedThreats > 0) {
      overall = 'warning';
      recommendations.push(
        `${unresolvedThreats} security threats need resolution`
      );
    }

    // Check for recent suspicious activity
    const recentSuspiciousActivity = this.events.filter(
      e =>
        e.type === SecurityEventType.SUSPICIOUS_ACTIVITY &&
        e.timestamp > new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
    ).length;

    if (recentSuspiciousActivity > 5) {
      overall = 'warning';
      recommendations.push(
        'High number of suspicious activities detected in the last 24 hours'
      );
    }

    // Calculate compliance score
    const complianceScore = this.calculateComplianceScore();
    if (complianceScore < 80) {
      recommendations.push(
        'Security compliance score is below recommended threshold'
      );
    }

    return {
      overall,
      threats: unresolvedThreats,
      lastScan: new Date(),
      recommendations,
      complianceScore,
    };
  }

  /**
   * Run threat detection algorithms
   */
  private runThreatDetection(): void {
    for (const rule of this.threatRules) {
      if (!rule.enabled) {
        continue;
      }

      const timeWindow = new Date(Date.now() - rule.timeWindow * 60 * 1000);
      const relevantEvents = this.events.filter(
        e => rule.eventTypes.includes(e.type) && e.timestamp > timeWindow
      );

      if (relevantEvents.length >= rule.threshold) {
        this.logEvent(
          SecurityEventType.SUSPICIOUS_ACTIVITY,
          rule.severity,
          `Threat rule triggered: ${rule.name}`,
          {
            ruleId: rule.id,
            eventCount: relevantEvents.length,
            threshold: rule.threshold,
            timeWindow: rule.timeWindow,
            events: relevantEvents.map(e => e.id),
          }
        );
      }
    }
  }

  /**
   * Initialize default threat detection rules
   */
  private initializeThreatRules(): void {
    this.threatRules = [
      {
        id: 'multiple-decryption-failures',
        name: 'Multiple Decryption Failures',
        description: 'Detect multiple failed decryption attempts',
        eventTypes: [SecurityEventType.DECRYPTION_FAILURE],
        threshold: 5,
        timeWindow: 10, // 10 minutes
        severity: SecuritySeverity.HIGH,
        enabled: true,
      },
      {
        id: 'rapid-data-access',
        name: 'Rapid Data Access',
        description: 'Detect unusually rapid data access patterns',
        eventTypes: [SecurityEventType.DATA_ACCESS],
        threshold: 50,
        timeWindow: 5, // 5 minutes
        severity: SecuritySeverity.MEDIUM,
        enabled: true,
      },
      {
        id: 'multiple-export-attempts',
        name: 'Multiple Export Attempts',
        description: 'Detect multiple data export attempts',
        eventTypes: [SecurityEventType.DATA_EXPORT],
        threshold: 10,
        timeWindow: 30, // 30 minutes
        severity: SecuritySeverity.MEDIUM,
        enabled: true,
      },
      {
        id: 'integrity-violations',
        name: 'Data Integrity Violations',
        description: 'Detect data integrity violations',
        eventTypes: [SecurityEventType.INTEGRITY_VIOLATION],
        threshold: 1,
        timeWindow: 60, // 1 hour
        severity: SecuritySeverity.CRITICAL,
        enabled: true,
      },
    ];
  }

  /**
   * Setup performance monitoring
   */
  private setupPerformanceMonitoring(): void {
    // Monitor for performance anomalies
    const observer = new PerformanceObserver(list => {
      for (const entry of list.getEntries()) {
        if (entry.duration > 5000) {
          // 5 seconds
          this.logEvent(
            SecurityEventType.PERFORMANCE_ANOMALY,
            SecuritySeverity.MEDIUM,
            'Performance anomaly detected',
            {
              entryType: entry.entryType,
              name: entry.name,
              duration: entry.duration,
            }
          );
        }
      }
    });

    try {
      observer.observe({ entryTypes: ['measure', 'navigation'] });
    } catch (error) {
      console.warn('Performance monitoring not available:', error);
    }
  }

  /**
   * Setup browser security monitoring
   */
  private setupBrowserSecurityMonitoring(): void {
    // Check for HTTPS
    if (location.protocol !== 'https:' && location.hostname !== 'localhost') {
      this.logEvent(
        SecurityEventType.BROWSER_SECURITY_WARNING,
        SecuritySeverity.HIGH,
        'Application not served over HTTPS',
        { protocol: location.protocol, hostname: location.hostname }
      );
    }

    // Check for Web Crypto API
    if (!crypto || !crypto.subtle) {
      this.logEvent(
        SecurityEventType.BROWSER_SECURITY_WARNING,
        SecuritySeverity.CRITICAL,
        'Web Crypto API not available',
        { userAgent: navigator.userAgent }
      );
    }

    // Monitor for suspicious extensions (basic check)
    if (window.chrome && window.chrome.runtime) {
      this.logEvent(
        SecurityEventType.BROWSER_SECURITY_WARNING,
        SecuritySeverity.LOW,
        'Chrome extension environment detected',
        { extensionDetected: true }
      );
    }
  }

  /**
   * Calculate average response time for resolved events
   */
  private calculateAverageResponseTime(): number {
    const resolvedEvents = this.events.filter(e => e.resolved && e.resolvedAt);
    if (resolvedEvents.length === 0) {
      return 0;
    }

    const totalTime = resolvedEvents.reduce((sum, event) => {
      const responseTime =
        event.resolvedAt!.getTime() - event.timestamp.getTime();
      return sum + responseTime;
    }, 0);

    return totalTime / resolvedEvents.length / 1000; // Convert to seconds
  }

  /**
   * Calculate security compliance score
   */
  private calculateComplianceScore(): number {
    let score = 100;

    // Deduct points for unresolved critical events
    const criticalEvents = this.events.filter(
      e => e.severity === SecuritySeverity.CRITICAL && !e.resolved
    ).length;
    score -= criticalEvents * 20;

    // Deduct points for unresolved high severity events
    const highEvents = this.events.filter(
      e => e.severity === SecuritySeverity.HIGH && !e.resolved
    ).length;
    score -= highEvents * 10;

    // Deduct points for old unresolved events
    const oldEvents = this.events.filter(
      e =>
        !e.resolved &&
        e.timestamp < new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Older than 7 days
    ).length;
    score -= oldEvents * 5;

    return Math.max(0, Math.min(100, score));
  }

  /**
   * Load events from localStorage
   */
  private loadEvents(): void {
    try {
      const savedEvents = localStorage.getItem('swiss-budget-security-events');
      if (savedEvents) {
        const parsed = JSON.parse(savedEvents);
        this.events = parsed.map((event: any) => ({
          ...event,
          timestamp: new Date(event.timestamp),
          resolvedAt: event.resolvedAt ? new Date(event.resolvedAt) : undefined,
        }));
      }
    } catch (error) {
      console.warn('Failed to load security events from localStorage:', error);
      this.events = [];
    }
  }

  /**
   * Reset all data (for testing purposes)
   */
  public resetForTesting(): void {
    // Stop monitoring to prevent new events
    this.stopMonitoring();

    // Clear all events
    this.events = [];

    // Reset state
    this.isMonitoring = false;
    this.sessionId = this.generateSessionId();
    this.initializeThreatRules();

    // Clear localStorage to prevent loading old events
    if (typeof localStorage !== 'undefined') {
      localStorage.removeItem('swiss-budget-security-events');
    }
  }

  /**
   * Disable threat detection for testing
   */
  public disableThreatDetectionForTesting(): void {
    this.threatRules.forEach(rule => {
      rule.enabled = false;
    });
  }

  // Utility methods
  private generateEventId(): string {
    return `evt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateSessionId(): string {
    return `sess_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Persistence methods
  private saveEvents(): void {
    // Keep only last 1000 events to prevent storage bloat
    if (this.events.length > 1000) {
      this.events = this.events.slice(-1000);
    }

    try {
      localStorage.setItem(
        'swiss-budget-security-events',
        JSON.stringify(this.events)
      );
    } catch (error) {
      console.error('Failed to save security events:', error);
    }
  }
}

// Export singleton instance
export const securityMonitor = SecurityMonitor.getInstance();
