/**
 * Swiss Budget Pro - Advanced Encryption Engine
 *
 * Provides bank-level AES-256-GCM encryption for all user data
 * with zero-knowledge architecture and local-only processing.
 */

// Encryption configuration
const ENCRYPTION_CONFIG = {
  algorithm: 'AES-GCM',
  keyLength: 256,
  ivLength: 12, // 96 bits for GCM
  saltLength: 32, // 256 bits
  tagLength: 16, // 128 bits
  iterations: 100000, // PBKDF2 iterations
} as const;

// Type definitions
export interface EncryptedData {
  data: string; // Base64 encoded encrypted data
  iv: string; // Base64 encoded initialization vector
  salt: string; // Base64 encoded salt
  tag: string; // Base64 encoded authentication tag
  version: string; // Encryption version for future compatibility
}

export interface EncryptionKey {
  key: CryptoKey;
  salt: Uint8Array;
}

export interface SecurityMetrics {
  encryptionTime: number;
  decryptionTime: number;
  keyDerivationTime: number;
  dataSize: number;
}

/**
 * Advanced Encryption Engine for Swiss Budget Pro
 *
 * Features:
 * - AES-256-GCM encryption with authenticated encryption
 * - PBKDF2 key derivation with configurable iterations
 * - Zero-knowledge architecture (local-only processing)
 * - Memory-safe key handling
 * - Performance monitoring and metrics
 */
export class SwissBudgetEncryption {
  private static instance: SwissBudgetEncryption;
  private metrics: SecurityMetrics[] = [];

  private constructor() {
    // Singleton pattern for security consistency
  }

  public static getInstance(): SwissBudgetEncryption {
    if (!SwissBudgetEncryption.instance) {
      SwissBudgetEncryption.instance = new SwissBudgetEncryption();
    }
    return SwissBudgetEncryption.instance;
  }

  /**
   * Generate a cryptographically secure salt
   */
  private generateSalt(): Uint8Array {
    return crypto.getRandomValues(new Uint8Array(ENCRYPTION_CONFIG.saltLength));
  }

  /**
   * Generate a cryptographically secure initialization vector
   */
  private generateIV(): Uint8Array {
    return crypto.getRandomValues(new Uint8Array(ENCRYPTION_CONFIG.ivLength));
  }

  /**
   * Derive encryption key from user passphrase using PBKDF2
   */
  public async deriveKey(
    passphrase: string,
    salt?: Uint8Array
  ): Promise<EncryptionKey> {
    const startTime = performance.now();

    const actualSalt = salt || this.generateSalt();
    const encoder = new TextEncoder();
    const passphraseBuffer = encoder.encode(passphrase);

    try {
      // Import passphrase as key material
      const keyMaterial = await crypto.subtle.importKey(
        'raw',
        passphraseBuffer,
        'PBKDF2',
        false,
        ['deriveKey']
      );

      // Derive AES-GCM key
      const key = await crypto.subtle.deriveKey(
        {
          name: 'PBKDF2',
          salt: actualSalt,
          iterations: ENCRYPTION_CONFIG.iterations,
          hash: 'SHA-256',
        },
        keyMaterial,
        {
          name: 'AES-GCM',
          length: ENCRYPTION_CONFIG.keyLength,
        },
        false, // Not extractable for security
        ['encrypt', 'decrypt']
      );

      const keyDerivationTime = performance.now() - startTime;

      // Clear sensitive data from memory
      passphraseBuffer.fill(0);

      return {
        key,
        salt: actualSalt,
      };
    } catch (error) {
      // Clear sensitive data on error
      passphraseBuffer.fill(0);
      throw new Error(
        `Key derivation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Encrypt data using AES-256-GCM
   */
  public async encrypt(
    data: string,
    encryptionKey: EncryptionKey
  ): Promise<EncryptedData> {
    const startTime = performance.now();

    try {
      const encoder = new TextEncoder();
      const dataBuffer = encoder.encode(data);
      const iv = this.generateIV();

      // Encrypt with AES-GCM (provides both confidentiality and authenticity)
      const encryptedBuffer = await crypto.subtle.encrypt(
        {
          name: 'AES-GCM',
          iv: iv,
          tagLength: ENCRYPTION_CONFIG.tagLength * 8, // Convert to bits
        },
        encryptionKey.key,
        dataBuffer
      );

      // Split encrypted data and authentication tag
      const encryptedData = new Uint8Array(
        encryptedBuffer.slice(0, -ENCRYPTION_CONFIG.tagLength)
      );
      const tag = new Uint8Array(
        encryptedBuffer.slice(-ENCRYPTION_CONFIG.tagLength)
      );

      const encryptionTime = performance.now() - startTime;

      // Record metrics
      this.recordMetrics({
        encryptionTime,
        decryptionTime: 0,
        keyDerivationTime: 0,
        dataSize: data.length,
      });

      // Clear sensitive data
      dataBuffer.fill(0);

      return {
        data: this.arrayBufferToBase64(encryptedData),
        iv: this.arrayBufferToBase64(iv),
        salt: this.arrayBufferToBase64(encryptionKey.salt),
        tag: this.arrayBufferToBase64(tag),
        version: '1.0',
      };
    } catch (error) {
      throw new Error(
        `Encryption failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Decrypt data using AES-256-GCM
   */
  public async decrypt(
    encryptedData: EncryptedData,
    passphrase: string
  ): Promise<string> {
    const startTime = performance.now();

    try {
      // Derive key using stored salt
      const salt = this.base64ToArrayBuffer(encryptedData.salt);
      const encryptionKey = await this.deriveKey(passphrase, salt);

      // Prepare encrypted data with tag
      const data = this.base64ToArrayBuffer(encryptedData.data);
      const tag = this.base64ToArrayBuffer(encryptedData.tag);
      const iv = this.base64ToArrayBuffer(encryptedData.iv);

      // Combine data and tag for GCM decryption
      const encryptedBuffer = new Uint8Array(data.length + tag.length);
      encryptedBuffer.set(data);
      encryptedBuffer.set(tag, data.length);

      // Decrypt with authentication verification
      const decryptedBuffer = await crypto.subtle.decrypt(
        {
          name: 'AES-GCM',
          iv: iv,
          tagLength: ENCRYPTION_CONFIG.tagLength * 8,
        },
        encryptionKey.key,
        encryptedBuffer
      );

      const decryptionTime = performance.now() - startTime;

      // Record metrics
      this.recordMetrics({
        encryptionTime: 0,
        decryptionTime,
        keyDerivationTime: 0,
        dataSize: decryptedBuffer.byteLength,
      });

      // Convert to string
      const decoder = new TextDecoder();
      return decoder.decode(decryptedBuffer);
    } catch (error) {
      throw new Error(
        `Decryption failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Verify data integrity using HMAC
   */
  public async verifyIntegrity(
    data: string,
    signature: string,
    key: CryptoKey
  ): Promise<boolean> {
    try {
      const encoder = new TextEncoder();
      const dataBuffer = encoder.encode(data);
      const signatureBuffer = this.base64ToArrayBuffer(signature);

      const computedSignature = await crypto.subtle.sign(
        'HMAC',
        key,
        dataBuffer
      );

      // Constant-time comparison to prevent timing attacks
      return this.constantTimeEqual(
        new Uint8Array(computedSignature),
        new Uint8Array(signatureBuffer)
      );
    } catch (error) {
      console.error('Integrity verification failed:', error);
      return false;
    }
  }

  /**
   * Generate HMAC signature for data integrity
   */
  public async generateSignature(
    data: string,
    key: CryptoKey
  ): Promise<string> {
    try {
      const encoder = new TextEncoder();
      const dataBuffer = encoder.encode(data);

      const signature = await crypto.subtle.sign('HMAC', key, dataBuffer);

      return this.arrayBufferToBase64(new Uint8Array(signature));
    } catch (error) {
      throw new Error(
        `Signature generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Secure memory clearing (best effort in JavaScript)
   */
  public clearSensitiveData(data: any): void {
    if (data instanceof Uint8Array || data instanceof ArrayBuffer) {
      new Uint8Array(data).fill(0);
    } else if (typeof data === 'string') {
      // JavaScript strings are immutable, but we can try to overwrite references
      data = '';
    }
  }

  /**
   * Get encryption performance metrics
   */
  public getMetrics(): SecurityMetrics[] {
    return [...this.metrics];
  }

  /**
   * Clear performance metrics
   */
  public clearMetrics(): void {
    this.metrics = [];
  }

  // Utility methods
  private arrayBufferToBase64(buffer: Uint8Array): string {
    const bytes = Array.from(buffer);
    return btoa(String.fromCharCode(...bytes));
  }

  private base64ToArrayBuffer(base64: string): Uint8Array {
    const binaryString = atob(base64);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    return bytes;
  }

  private constantTimeEqual(a: Uint8Array, b: Uint8Array): boolean {
    if (a.length !== b.length) {
      return false;
    }

    let result = 0;
    for (let i = 0; i < a.length; i++) {
      result |= a[i] ^ b[i];
    }

    return result === 0;
  }

  private recordMetrics(metrics: SecurityMetrics): void {
    this.metrics.push(metrics);

    // Keep only last 100 metrics to prevent memory bloat
    if (this.metrics.length > 100) {
      this.metrics = this.metrics.slice(-100);
    }
  }
}

// Export singleton instance
export const encryption = SwissBudgetEncryption.getInstance();

// Export utility functions
export const SecurityUtils = {
  /**
   * Generate a secure random passphrase
   */
  generateSecurePassphrase(length: number = 32): string {
    // Validate input
    if (length <= 0) {
      throw new Error('Passphrase length must be positive');
    }
    if (length > 1000) {
      throw new Error('Passphrase length too large');
    }

    const charset =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    const array = new Uint8Array(length);
    crypto.getRandomValues(array);

    return Array.from(array, byte => charset[byte % charset.length]).join('');
  },

  /**
   * Estimate passphrase strength
   */
  estimatePassphraseStrength(passphrase: string): {
    score: number;
    feedback: string[];
  } {
    // Handle null/undefined input
    if (!passphrase || typeof passphrase !== 'string') {
      return {
        score: 0,
        feedback: ['Passphrase is required'],
      };
    }

    const feedback: string[] = [];
    let score = 0;

    // Handle whitespace-only passphrases
    const trimmed = passphrase.trim();
    if (trimmed.length === 0) {
      return {
        score: 0,
        feedback: ['Passphrase is too short'],
      };
    }

    // Length check
    if (passphrase.length >= 12) score += 2;
    else if (passphrase.length >= 8) score += 1;
    else feedback.push('Use at least 8 characters');

    // Character variety
    if (/[a-z]/.test(passphrase)) score += 1;
    if (/[A-Z]/.test(passphrase)) score += 1;
    if (/[0-9]/.test(passphrase)) score += 1;
    if (/[^a-zA-Z0-9]/.test(passphrase)) score += 1;

    // Avoid common patterns
    if (!/(.)\1{2,}/.test(passphrase)) score += 1;
    else feedback.push('Avoid repeated characters');

    if (score < 3)
      feedback.push('Consider using a mix of letters, numbers, and symbols');

    return { score: Math.min(score, 6), feedback };
  },

  /**
   * Check if Web Crypto API is available
   */
  isWebCryptoAvailable(): boolean {
    return (
      typeof crypto !== 'undefined' &&
      typeof crypto.subtle !== 'undefined' &&
      typeof crypto.getRandomValues !== 'undefined'
    );
  },
};
