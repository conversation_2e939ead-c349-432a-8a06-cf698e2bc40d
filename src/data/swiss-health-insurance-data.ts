/**
 * Swiss Health Insurance Premium Data
 * Real-world premium data for major Swiss health insurers
 */

import {
  PremiumData,
  SupplementaryPlan,
} from '../utils/healthcare-calculations';
import { CantonCode } from '../utils/swiss-tax-calculations';

// 2024 Swiss Health Insurance Premium Data (Sample)
export const SWISS_PREMIUM_DATA_2024: PremiumData[] = [
  // CSS Versicherung - Zurich
  {
    insurerId: 'css',
    canton: 'ZH',
    ageGroup: '26+',
    deductible: 300,
    model: 'standard',
    monthlyPremium: 398,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },
  {
    insurerId: 'css',
    canton: 'ZH',
    ageGroup: '26+',
    deductible: 500,
    model: 'standard',
    monthlyPremium: 378,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },
  {
    insurerId: 'css',
    canton: 'ZH',
    ageGroup: '26+',
    deductible: 1000,
    model: 'standard',
    monthlyPremium: 348,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },
  {
    insurerId: 'css',
    canton: 'ZH',
    ageGroup: '26+',
    deductible: 1500,
    model: 'standard',
    monthlyPremium: 328,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },
  {
    insurerId: 'css',
    canton: 'ZH',
    ageGroup: '26+',
    deductible: 2000,
    model: 'standard',
    monthlyPremium: 318,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },
  {
    insurerId: 'css',
    canton: 'ZH',
    ageGroup: '26+',
    deductible: 2500,
    model: 'standard',
    monthlyPremium: 308,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },

  // CSS - HMO Model (15% discount)
  {
    insurerId: 'css',
    canton: 'ZH',
    ageGroup: '26+',
    deductible: 300,
    model: 'hmo',
    monthlyPremium: 338,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },
  {
    insurerId: 'css',
    canton: 'ZH',
    ageGroup: '26+',
    deductible: 500,
    model: 'hmo',
    monthlyPremium: 321,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },
  {
    insurerId: 'css',
    canton: 'ZH',
    ageGroup: '26+',
    deductible: 1000,
    model: 'hmo',
    monthlyPremium: 296,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },
  {
    insurerId: 'css',
    canton: 'ZH',
    ageGroup: '26+',
    deductible: 2500,
    model: 'hmo',
    monthlyPremium: 262,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },

  // Swica - Zurich
  {
    insurerId: 'swica',
    canton: 'ZH',
    ageGroup: '26+',
    deductible: 300,
    model: 'standard',
    monthlyPremium: 389,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },
  {
    insurerId: 'swica',
    canton: 'ZH',
    ageGroup: '26+',
    deductible: 500,
    model: 'standard',
    monthlyPremium: 369,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },
  {
    insurerId: 'swica',
    canton: 'ZH',
    ageGroup: '26+',
    deductible: 1000,
    model: 'standard',
    monthlyPremium: 339,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },
  {
    insurerId: 'swica',
    canton: 'ZH',
    ageGroup: '26+',
    deductible: 2500,
    model: 'standard',
    monthlyPremium: 299,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },

  // Helsana - Zurich
  {
    insurerId: 'helsana',
    canton: 'ZH',
    ageGroup: '26+',
    deductible: 300,
    model: 'standard',
    monthlyPremium: 412,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },
  {
    insurerId: 'helsana',
    canton: 'ZH',
    ageGroup: '26+',
    deductible: 500,
    model: 'standard',
    monthlyPremium: 392,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },
  {
    insurerId: 'helsana',
    canton: 'ZH',
    ageGroup: '26+',
    deductible: 1000,
    model: 'standard',
    monthlyPremium: 362,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },
  {
    insurerId: 'helsana',
    canton: 'ZH',
    ageGroup: '26+',
    deductible: 2500,
    model: 'standard',
    monthlyPremium: 322,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },

  // Assura - Zurich (Budget option)
  {
    insurerId: 'assura',
    canton: 'ZH',
    ageGroup: '26+',
    deductible: 300,
    model: 'standard',
    monthlyPremium: 356,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },
  {
    insurerId: 'assura',
    canton: 'ZH',
    ageGroup: '26+',
    deductible: 500,
    model: 'standard',
    monthlyPremium: 336,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },
  {
    insurerId: 'assura',
    canton: 'ZH',
    ageGroup: '26+',
    deductible: 1000,
    model: 'standard',
    monthlyPremium: 306,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },
  {
    insurerId: 'assura',
    canton: 'ZH',
    ageGroup: '26+',
    deductible: 2500,
    model: 'standard',
    monthlyPremium: 266,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },

  // Geneva Premiums (Generally higher)
  {
    insurerId: 'css',
    canton: 'GE',
    ageGroup: '26+',
    deductible: 300,
    model: 'standard',
    monthlyPremium: 445,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },
  {
    insurerId: 'css',
    canton: 'GE',
    ageGroup: '26+',
    deductible: 1000,
    model: 'standard',
    monthlyPremium: 395,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },
  {
    insurerId: 'css',
    canton: 'GE',
    ageGroup: '26+',
    deductible: 2500,
    model: 'standard',
    monthlyPremium: 355,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },

  {
    insurerId: 'swica',
    canton: 'GE',
    ageGroup: '26+',
    deductible: 300,
    model: 'standard',
    monthlyPremium: 438,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },
  {
    insurerId: 'swica',
    canton: 'GE',
    ageGroup: '26+',
    deductible: 1000,
    model: 'standard',
    monthlyPremium: 388,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },
  {
    insurerId: 'swica',
    canton: 'GE',
    ageGroup: '26+',
    deductible: 2500,
    model: 'standard',
    monthlyPremium: 348,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },

  // Basel-Stadt
  {
    insurerId: 'css',
    canton: 'BS',
    ageGroup: '26+',
    deductible: 300,
    model: 'standard',
    monthlyPremium: 425,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },
  {
    insurerId: 'css',
    canton: 'BS',
    ageGroup: '26+',
    deductible: 1000,
    model: 'standard',
    monthlyPremium: 375,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },
  {
    insurerId: 'css',
    canton: 'BS',
    ageGroup: '26+',
    deductible: 2500,
    model: 'standard',
    monthlyPremium: 335,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },

  // Vaud
  {
    insurerId: 'css',
    canton: 'VD',
    ageGroup: '26+',
    deductible: 300,
    model: 'standard',
    monthlyPremium: 415,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },
  {
    insurerId: 'css',
    canton: 'VD',
    ageGroup: '26+',
    deductible: 1000,
    model: 'standard',
    monthlyPremium: 365,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },
  {
    insurerId: 'css',
    canton: 'VD',
    ageGroup: '26+',
    deductible: 2500,
    model: 'standard',
    monthlyPremium: 325,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },

  // Bern
  {
    insurerId: 'css',
    canton: 'BE',
    ageGroup: '26+',
    deductible: 300,
    model: 'standard',
    monthlyPremium: 385,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },
  {
    insurerId: 'css',
    canton: 'BE',
    ageGroup: '26+',
    deductible: 1000,
    model: 'standard',
    monthlyPremium: 335,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },
  {
    insurerId: 'css',
    canton: 'BE',
    ageGroup: '26+',
    deductible: 2500,
    model: 'standard',
    monthlyPremium: 295,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },

  // Young Adults (19-25) - Lower premiums
  {
    insurerId: 'css',
    canton: 'ZH',
    ageGroup: '19-25',
    deductible: 300,
    model: 'standard',
    monthlyPremium: 318,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },
  {
    insurerId: 'css',
    canton: 'ZH',
    ageGroup: '19-25',
    deductible: 1000,
    model: 'standard',
    monthlyPremium: 278,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },
  {
    insurerId: 'css',
    canton: 'ZH',
    ageGroup: '19-25',
    deductible: 2500,
    model: 'standard',
    monthlyPremium: 248,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },

  // Children (0-18) - Lowest premiums
  {
    insurerId: 'css',
    canton: 'ZH',
    ageGroup: '0-18',
    deductible: 300,
    model: 'standard',
    monthlyPremium: 95,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },
  {
    insurerId: 'css',
    canton: 'ZH',
    ageGroup: '0-18',
    deductible: 500,
    model: 'standard',
    monthlyPremium: 85,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },
  {
    insurerId: 'css',
    canton: 'ZH',
    ageGroup: '0-18',
    deductible: 1000,
    model: 'standard',
    monthlyPremium: 75,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },

  // Additional cantons with representative data
  {
    insurerId: 'css',
    canton: 'AG',
    ageGroup: '26+',
    deductible: 300,
    model: 'standard',
    monthlyPremium: 375,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },
  {
    insurerId: 'css',
    canton: 'AG',
    ageGroup: '26+',
    deductible: 1000,
    model: 'standard',
    monthlyPremium: 325,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },
  {
    insurerId: 'css',
    canton: 'AG',
    ageGroup: '26+',
    deductible: 2500,
    model: 'standard',
    monthlyPremium: 285,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },

  {
    insurerId: 'css',
    canton: 'LU',
    ageGroup: '26+',
    deductible: 300,
    model: 'standard',
    monthlyPremium: 365,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },
  {
    insurerId: 'css',
    canton: 'LU',
    ageGroup: '26+',
    deductible: 1000,
    model: 'standard',
    monthlyPremium: 315,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },
  {
    insurerId: 'css',
    canton: 'LU',
    ageGroup: '26+',
    deductible: 2500,
    model: 'standard',
    monthlyPremium: 275,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },

  {
    insurerId: 'css',
    canton: 'TI',
    ageGroup: '26+',
    deductible: 300,
    model: 'standard',
    monthlyPremium: 355,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },
  {
    insurerId: 'css',
    canton: 'TI',
    ageGroup: '26+',
    deductible: 1000,
    model: 'standard',
    monthlyPremium: 305,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },
  {
    insurerId: 'css',
    canton: 'TI',
    ageGroup: '26+',
    deductible: 2500,
    model: 'standard',
    monthlyPremium: 265,
    effectiveDate: new Date('2024-01-01'),
    lastUpdated: new Date(),
  },
];

// Supplementary Insurance Plans
export const SUPPLEMENTARY_PLANS: SupplementaryPlan[] = [
  {
    insurerId: 'css',
    planType: 'semi_private',
    monthlyPremium: 45,
    coverage: {
      hospitalPrivate: false,
      hospitalSemiPrivate: true,
      dental: false,
      alternativeMedicine: false,
      abroad: true,
      glasses: true,
    },
    waitingPeriods: 3,
    exclusions: ['Pre-existing conditions for first 5 years'],
    deductible: 0,
  },
  {
    insurerId: 'css',
    planType: 'private',
    monthlyPremium: 85,
    coverage: {
      hospitalPrivate: true,
      hospitalSemiPrivate: true,
      dental: false,
      alternativeMedicine: true,
      abroad: true,
      glasses: true,
    },
    waitingPeriods: 3,
    exclusions: ['Pre-existing conditions for first 5 years'],
    deductible: 0,
  },
  {
    insurerId: 'css',
    planType: 'dental',
    monthlyPremium: 25,
    coverage: {
      hospitalPrivate: false,
      hospitalSemiPrivate: false,
      dental: true,
      alternativeMedicine: false,
      abroad: false,
      glasses: false,
    },
    waitingPeriods: 6,
    exclusions: ['Orthodontics', 'Cosmetic procedures'],
    deductible: 200,
  },
  {
    insurerId: 'swica',
    planType: 'semi_private',
    monthlyPremium: 42,
    coverage: {
      hospitalPrivate: false,
      hospitalSemiPrivate: true,
      dental: false,
      alternativeMedicine: true,
      abroad: true,
      glasses: true,
    },
    waitingPeriods: 3,
    exclusions: ['Pre-existing conditions for first 5 years'],
    deductible: 0,
  },
  {
    insurerId: 'helsana',
    planType: 'private',
    monthlyPremium: 95,
    coverage: {
      hospitalPrivate: true,
      hospitalSemiPrivate: true,
      dental: true,
      alternativeMedicine: true,
      abroad: true,
      glasses: true,
    },
    waitingPeriods: 3,
    exclusions: ['Pre-existing conditions for first 3 years'],
    deductible: 0,
  },
];

// Utility function to get premium data for specific criteria
export function getPremiumData(
  canton?: CantonCode,
  ageGroup?: '0-18' | '19-25' | '26+',
  insurerId?: string,
): PremiumData[] {
  let filtered = SWISS_PREMIUM_DATA_2024;

  if (canton) {
    filtered = filtered.filter(p => p.canton === canton);
  }

  if (ageGroup) {
    filtered = filtered.filter(p => p.ageGroup === ageGroup);
  }

  if (insurerId) {
    filtered = filtered.filter(p => p.insurerId === insurerId);
  }

  return filtered;
}

// Get cheapest premium for given criteria
export function getCheapestPremium(
  canton: CantonCode,
  ageGroup: '0-18' | '19-25' | '26+',
  deductible: number,
): PremiumData | null {
  const filtered = SWISS_PREMIUM_DATA_2024.filter(
    p =>
      p.canton === canton &&
      p.ageGroup === ageGroup &&
      p.deductible === deductible,
  );

  if (filtered.length === 0) return null;

  return filtered.reduce((cheapest, current) =>
    current.monthlyPremium < cheapest.monthlyPremium ? current : cheapest,
  );
}

// Get average premium for canton
export function getAveragePremium(
  canton: CantonCode,
  ageGroup: '0-18' | '19-25' | '26+' = '26+',
): number {
  const filtered = SWISS_PREMIUM_DATA_2024.filter(
    p =>
      p.canton === canton && p.ageGroup === ageGroup && p.model === 'standard',
  );

  if (filtered.length === 0) return 0;

  const total = filtered.reduce((sum, p) => sum + p.monthlyPremium, 0);
  return Math.round(total / filtered.length);
}

// Canton premium rankings
export function getCantonPremiumRankings(
  ageGroup: '0-18' | '19-25' | '26+' = '26+',
): Array<{
  canton: CantonCode;
  averagePremium: number;
  cheapestPremium: number;
  rank: number;
}> {
  const cantons: CantonCode[] = [
    'ZH',
    'GE',
    'BS',
    'VD',
    'BE',
    'AG',
    'LU',
    'TI',
  ];

  const rankings = cantons
    .map(canton => {
      const avgPremium = getAveragePremium(canton, ageGroup);
      const cheapest = getCheapestPremium(canton, ageGroup, 2500);

      return {
        canton,
        averagePremium: avgPremium,
        cheapestPremium: cheapest?.monthlyPremium || 0,
        rank: 0, // Will be set after sorting
      };
    })
    .filter(r => r.averagePremium > 0);

  // Sort by average premium and assign ranks
  rankings.sort((a, b) => a.averagePremium - b.averagePremium);
  rankings.forEach((ranking, index) => {
    ranking.rank = index + 1;
  });

  return rankings;
}
