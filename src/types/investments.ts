export type InvestmentType =
  | 'stock'
  | 'etf'
  | 'bond'
  | 'reit'
  | 'commodity'
  | 'crypto'
  | 'mutual_fund'
  | 'option'
  | 'future'
  | 'other';

export type InvestmentCategoryId =
  | 'stocks_etf'
  | 'bonds'
  | 'swiss_stocks'
  | 'real_estate'
  | 'commodities'
  | 'crypto';

export type RiskLevel = 'low' | 'medium' | 'high' | 'very_high';

export interface InvestmentCategory {
  id: InvestmentCategoryId;
  name: string;
  icon: string;
  swissSpecific: boolean;
  description: string;
  riskLevel: RiskLevel;
  expectedReturn: number; // Annual percentage
}

export interface Investment {
  id: string;
  name: string;
  symbol?: string;
  category: InvestmentCategoryId;
  type: InvestmentType;
  currentValue: number;
  purchasePrice: number;
  quantity: number;
  currency: string;
  broker?: string;
  isActive: boolean;
  swissWithholdingTax?: number; // Percentage
  dividendYield?: number; // Annual percentage
  lastUpdated?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface PortfolioAllocation {
  categoryId: InvestmentCategoryId;
  percentage: number;
  targetPercentage?: number;
  value: number;
  riskLevel: RiskLevel;
}

export interface PortfolioMetrics {
  totalValue: number;
  totalGainLoss: number;
  totalReturnPercentage: number;
  annualizedReturn: number;
  volatility: number;
  sharpeRatio: number;
  maxDrawdown: number;
  allocations: PortfolioAllocation[];
}

export interface InvestmentPerformance {
  investmentId: string;
  date: string;
  value: number;
  return1Day?: number;
  return1Week?: number;
  return1Month?: number;
  return3Month?: number;
  return1Year?: number;
  returnSinceInception?: number;
}

// Utility functions
export const calculateGainLoss = (investment: Investment): number => {
  return investment.currentValue - investment.purchasePrice;
};

export const calculateReturnPercentage = (investment: Investment): number => {
  if (investment.purchasePrice === 0) return 0;
  return (
    ((investment.currentValue - investment.purchasePrice) /
      investment.purchasePrice) *
    100
  );
};

export const calculatePortfolioValue = (investments: Investment[]): number => {
  return investments
    .filter(inv => inv.isActive)
    .reduce((sum, inv) => sum + inv.currentValue, 0);
};

export const calculatePortfolioGainLoss = (
  investments: Investment[],
): number => {
  return investments
    .filter(inv => inv.isActive)
    .reduce((sum, inv) => sum + calculateGainLoss(inv), 0);
};

export const calculatePortfolioReturnPercentage = (
  investments: Investment[],
): number => {
  const activeInvestments = investments.filter(inv => inv.isActive);
  const totalPurchasePrice = activeInvestments.reduce(
    (sum, inv) => sum + inv.purchasePrice,
    0,
  );

  if (totalPurchasePrice === 0) return 0;

  const totalGainLoss = calculatePortfolioGainLoss(investments);
  return (totalGainLoss / totalPurchasePrice) * 100;
};

export const calculatePortfolioAllocations = (
  investments: Investment[],
  categories: InvestmentCategory[],
): PortfolioAllocation[] => {
  const totalValue = calculatePortfolioValue(investments);
  const activeInvestments = investments.filter(inv => inv.isActive);

  return categories
    .map(category => {
      const categoryInvestments = activeInvestments.filter(
        inv => inv.category === category.id,
      );
      const categoryValue = categoryInvestments.reduce(
        (sum, inv) => sum + inv.currentValue,
        0,
      );
      const percentage =
        totalValue > 0 ? (categoryValue / totalValue) * 100 : 0;

      return {
        categoryId: category.id,
        percentage,
        value: categoryValue,
        riskLevel: category.riskLevel,
      };
    })
    .filter(allocation => allocation.value > 0);
};

export const calculateDiversificationScore = (
  allocations: PortfolioAllocation[],
): number => {
  // Calculate Herfindahl-Hirschman Index (HHI) for diversification
  // Lower HHI = more diversified (better)
  // Score is inverted: higher score = more diversified

  const hhi = allocations.reduce((sum, allocation) => {
    const marketShare = allocation.percentage / 100;
    return sum + marketShare * marketShare;
  }, 0);

  // Convert to 0-100 scale where 100 is perfectly diversified
  const maxHHI = 1; // Completely concentrated
  const minHHI = 1 / allocations.length; // Perfectly diversified

  if (allocations.length <= 1) return 0;

  return Math.max(0, Math.min(100, ((maxHHI - hhi) / (maxHHI - minHHI)) * 100));
};

export const calculateRiskScore = (
  allocations: PortfolioAllocation[],
): number => {
  // Calculate weighted average risk score
  const riskWeights = {
    low: 1,
    medium: 2,
    high: 3,
    very_high: 4,
  };

  const totalWeight = allocations.reduce(
    (sum, allocation) => sum + allocation.percentage,
    0,
  );

  if (totalWeight === 0) return 0;

  const weightedRisk = allocations.reduce((sum, allocation) => {
    const riskWeight = riskWeights[allocation.riskLevel];
    return sum + riskWeight * allocation.percentage;
  }, 0);

  return (weightedRisk / totalWeight) * 25; // Scale to 0-100
};

export const getInvestmentRecommendations = (
  allocations: PortfolioAllocation[],
  totalValue: number,
  riskTolerance: RiskLevel = 'medium',
): string[] => {
  const recommendations: string[] = [];

  // Check diversification
  const diversificationScore = calculateDiversificationScore(allocations);
  if (diversificationScore < 50) {
    recommendations.push(
      'Consider diversifying across more asset classes to reduce risk',
    );
  }

  // Check Swiss allocation
  const swissAllocation = allocations.find(
    a => a.categoryId === 'swiss_stocks',
  );
  if (!swissAllocation || swissAllocation.percentage < 10) {
    recommendations.push(
      'Consider adding Swiss stocks for local market exposure and tax benefits',
    );
  }

  // Check bond allocation based on age (assuming 30-40 age range)
  const bondAllocation = allocations.find(a => a.categoryId === 'bonds');
  const recommendedBondPercentage =
    riskTolerance === 'low' ? 40 : riskTolerance === 'medium' ? 20 : 10;

  if (
    !bondAllocation ||
    bondAllocation.percentage < recommendedBondPercentage
  ) {
    recommendations.push(
      `Consider increasing bond allocation to ${recommendedBondPercentage}% for stability`,
    );
  }

  // Check crypto allocation
  const cryptoAllocation = allocations.find(a => a.categoryId === 'crypto');
  if (cryptoAllocation && cryptoAllocation.percentage > 10) {
    recommendations.push(
      'Consider reducing cryptocurrency allocation to below 10% due to high volatility',
    );
  }

  // Check minimum investment amount
  if (totalValue < 10000) {
    recommendations.push(
      'Consider focusing on low-cost ETFs until you reach CHF 10,000 in investments',
    );
  }

  return recommendations;
};

export const SWISS_INVESTMENT_CATEGORIES: InvestmentCategory[] = [
  {
    id: 'stocks_etf',
    name: 'Global Stocks & ETFs',
    icon: '📈',
    swissSpecific: false,
    description: 'International stocks and exchange-traded funds',
    riskLevel: 'high',
    expectedReturn: 7.0,
  },
  {
    id: 'swiss_stocks',
    name: 'Swiss Stocks',
    icon: '🇨🇭',
    swissSpecific: true,
    description: 'Swiss Market Index (SMI) and Swiss equities',
    riskLevel: 'medium',
    expectedReturn: 6.5,
  },
  {
    id: 'bonds',
    name: 'Bonds',
    icon: '🏛️',
    swissSpecific: false,
    description: 'Government and corporate bonds',
    riskLevel: 'low',
    expectedReturn: 2.5,
  },
  {
    id: 'real_estate',
    name: 'Real Estate',
    icon: '🏠',
    swissSpecific: false,
    description: 'REITs and real estate investments',
    riskLevel: 'medium',
    expectedReturn: 5.5,
  },
  {
    id: 'commodities',
    name: 'Commodities',
    icon: '🥇',
    swissSpecific: false,
    description: 'Gold, silver, and commodity ETFs',
    riskLevel: 'high',
    expectedReturn: 4.0,
  },
  {
    id: 'crypto',
    name: 'Cryptocurrency',
    icon: '₿',
    swissSpecific: false,
    description: 'Bitcoin, Ethereum, and digital assets',
    riskLevel: 'very_high',
    expectedReturn: 15.0,
  },
];
