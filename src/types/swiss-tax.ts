/**
 * Swiss Tax System Types and Calculations
 * Comprehensive tax engine for all 26 cantons
 */

export type SwissCanton =
  | 'AG'
  | 'AI'
  | 'AR'
  | 'BE'
  | 'BL'
  | 'BS'
  | 'FR'
  | 'GE'
  | 'GL'
  | 'GR'
  | 'JU'
  | 'LU'
  | 'NE'
  | 'NW'
  | 'OW'
  | 'SG'
  | 'SH'
  | 'SO'
  | 'SZ'
  | 'TG'
  | 'TI'
  | 'UR'
  | 'VD'
  | 'VS'
  | 'ZG'
  | 'ZH';

export interface TaxBracket {
  min: number;
  max: number;
  rate: number;
  baseAmount?: number;
}

export interface CantonTaxInfo {
  code: SwissCanton;
  name: string;
  nameDE: string;
  nameFR: string;
  nameIT: string;
  capital: string;
  incomeTaxBrackets: TaxBracket[];
  wealthTaxRate: number; // per mille
  maxWealthTaxRate: number;
  churchTaxRate: number; // percentage of income tax
  municipalTaxMultiplier: number; // average multiplier
  deductions: {
    personal: number;
    married: number;
    children: number;
    maxPillar3a: number;
    maxProfessionalExpenses: number;
  };
  specialFeatures: string[];
}

export interface TaxCalculationInput {
  grossIncome: number;
  canton: SwissCanton;
  municipality?: string;
  maritalStatus: 'single' | 'married' | 'divorced' | 'widowed';
  children: number;
  wealth: number;
  pillar3aContribution: number;
  professionalExpenses: number;
  churchMember: boolean;
  municipalMultiplier?: number;
}

export interface TaxCalculationResult {
  grossIncome: number;
  taxableIncome: number;
  federalTax: number;
  cantonalTax: number;
  municipalTax: number;
  churchTax: number;
  wealthTax: number;
  totalTax: number;
  netIncome: number;
  effectiveTaxRate: number;
  marginalTaxRate: number;
  breakdown: {
    deductions: {
      personal: number;
      married: number;
      children: number;
      pillar3a: number;
      professional: number;
      total: number;
    };
    taxes: {
      federal: TaxBreakdown;
      cantonal: TaxBreakdown;
      municipal: TaxBreakdown;
      church: TaxBreakdown;
      wealth: TaxBreakdown;
    };
  };
}

export interface TaxBreakdown {
  base: number;
  rate: number;
  amount: number;
  bracket?: string;
}

// Federal tax brackets for 2024 (single person)
export const FEDERAL_TAX_BRACKETS: TaxBracket[] = [
  { min: 0, max: 14500, rate: 0 },
  { min: 14500, max: 31600, rate: 0.77 },
  { min: 31600, max: 41400, rate: 0.88 },
  { min: 41400, max: 55200, rate: 2.64 },
  { min: 55200, max: 72500, rate: 2.97 },
  { min: 72500, max: 78100, rate: 5.94 },
  { min: 78100, max: 103600, rate: 6.6 },
  { min: 103600, max: 134600, rate: 8.8 },
  { min: 134600, max: 176000, rate: 11.0 },
  { min: 176000, max: 755200, rate: 13.2 },
  { min: 755200, max: Infinity, rate: 11.5 },
];

// Comprehensive canton tax data for all 26 cantons
export const CANTON_TAX_DATA: Record<SwissCanton, CantonTaxInfo> = {
  ZH: {
    code: 'ZH',
    name: 'Zurich',
    nameDE: 'Zürich',
    nameFR: 'Zurich',
    nameIT: 'Zurigo',
    capital: 'Zurich',
    incomeTaxBrackets: [
      { min: 0, max: 6700, rate: 0 },
      { min: 6700, max: 11900, rate: 2.0 },
      { min: 11900, max: 17300, rate: 3.0 },
      { min: 17300, max: 23000, rate: 4.0 },
      { min: 23000, max: 29800, rate: 5.0 },
      { min: 29800, max: 38700, rate: 6.0 },
      { min: 38700, max: 50600, rate: 7.0 },
      { min: 50600, max: 66700, rate: 8.0 },
      { min: 66700, max: 88900, rate: 9.0 },
      { min: 88900, max: 121000, rate: 10.0 },
      { min: 121000, max: 166700, rate: 11.0 },
      { min: 166700, max: 235300, rate: 12.0 },
      { min: 235300, max: Infinity, rate: 13.0 },
    ],
    wealthTaxRate: 0.5,
    maxWealthTaxRate: 1.0,
    churchTaxRate: 10.0,
    municipalTaxMultiplier: 119,
    deductions: {
      personal: 24400,
      married: 4050,
      children: 9000,
      maxPillar3a: 7056,
      maxProfessionalExpenses: 4000,
    },
    specialFeatures: [
      'Progressive wealth tax',
      'High municipal multipliers in city',
    ],
  },

  BE: {
    code: 'BE',
    name: 'Bern',
    nameDE: 'Bern',
    nameFR: 'Berne',
    nameIT: 'Berna',
    capital: 'Bern',
    incomeTaxBrackets: [
      { min: 0, max: 17800, rate: 0.88 },
      { min: 17800, max: 41700, rate: 2.97 },
      { min: 41700, max: 61900, rate: 5.94 },
      { min: 61900, max: 81200, rate: 6.6 },
      { min: 81200, max: 103600, rate: 8.8 },
      { min: 103600, max: 135300, rate: 11.0 },
      { min: 135300, max: 193500, rate: 13.2 },
      { min: 193500, max: Infinity, rate: 14.08 },
    ],
    wealthTaxRate: 0.3,
    maxWealthTaxRate: 0.94,
    churchTaxRate: 12.0,
    municipalTaxMultiplier: 154,
    deductions: {
      personal: 24000,
      married: 2500,
      children: 7800,
      maxPillar3a: 7056,
      maxProfessionalExpenses: 3000,
    },
    specialFeatures: ['Moderate tax rates', 'Strong municipal autonomy'],
  },

  VD: {
    code: 'VD',
    name: 'Vaud',
    nameDE: 'Waadt',
    nameFR: 'Vaud',
    nameIT: 'Vaud',
    capital: 'Lausanne',
    incomeTaxBrackets: [
      { min: 0, max: 17200, rate: 0 },
      { min: 17200, max: 24400, rate: 1.0 },
      { min: 24400, max: 32100, rate: 2.0 },
      { min: 32100, max: 40600, rate: 3.0 },
      { min: 40600, max: 50000, rate: 4.0 },
      { min: 50000, max: 60700, rate: 5.0 },
      { min: 60700, max: 73100, rate: 6.0 },
      { min: 73100, max: 87500, rate: 7.0 },
      { min: 87500, max: 104400, rate: 8.0 },
      { min: 104400, max: 124300, rate: 9.0 },
      { min: 124300, max: 147800, rate: 10.0 },
      { min: 147800, max: 175600, rate: 11.0 },
      { min: 175600, max: 208800, rate: 12.0 },
      { min: 208800, max: 248200, rate: 13.0 },
      { min: 248200, max: Infinity, rate: 14.0 },
    ],
    wealthTaxRate: 0.3,
    maxWealthTaxRate: 1.0,
    churchTaxRate: 0, // No church tax in VD
    municipalTaxMultiplier: 64,
    deductions: {
      personal: 14600,
      married: 3000,
      children: 9100,
      maxPillar3a: 7056,
      maxProfessionalExpenses: 2000,
    },
    specialFeatures: ['No church tax', 'Progressive system'],
  },

  GE: {
    code: 'GE',
    name: 'Geneva',
    nameDE: 'Genf',
    nameFR: 'Genève',
    nameIT: 'Ginevra',
    capital: 'Geneva',
    incomeTaxBrackets: [
      { min: 0, max: 17300, rate: 0 },
      { min: 17300, max: 26000, rate: 5.5 },
      { min: 26000, max: 34700, rate: 7.5 },
      { min: 34700, max: 43500, rate: 9.5 },
      { min: 43500, max: 52200, rate: 11.5 },
      { min: 52200, max: 69500, rate: 13.5 },
      { min: 69500, max: 86900, rate: 15.5 },
      { min: 86900, max: 121700, rate: 17.5 },
      { min: 121700, max: Infinity, rate: 19.0 },
    ],
    wealthTaxRate: 0.25,
    maxWealthTaxRate: 1.0,
    churchTaxRate: 0, // No church tax in GE
    municipalTaxMultiplier: 45.5,
    deductions: {
      personal: 14900,
      married: 2900,
      children: 9000,
      maxPillar3a: 7056,
      maxProfessionalExpenses: 3000,
    },
    specialFeatures: ['High tax rates for high incomes', 'No church tax'],
  },

  ZG: {
    code: 'ZG',
    name: 'Zug',
    nameDE: 'Zug',
    nameFR: 'Zoug',
    nameIT: 'Zugo',
    capital: 'Zug',
    incomeTaxBrackets: [
      { min: 0, max: 14000, rate: 0 },
      { min: 14000, max: 20000, rate: 1.0 },
      { min: 20000, max: 27000, rate: 2.0 },
      { min: 27000, max: 35000, rate: 3.0 },
      { min: 35000, max: 44000, rate: 4.0 },
      { min: 44000, max: 55000, rate: 5.0 },
      { min: 55000, max: 68000, rate: 6.0 },
      { min: 68000, max: 83000, rate: 7.0 },
      { min: 83000, max: 101000, rate: 8.0 },
      { min: 101000, max: 122000, rate: 9.0 },
      { min: 122000, max: 147000, rate: 10.0 },
      { min: 147000, max: 177000, rate: 11.0 },
      { min: 177000, max: Infinity, rate: 11.5 },
    ],
    wealthTaxRate: 0.1,
    maxWealthTaxRate: 0.5,
    churchTaxRate: 8.0,
    municipalTaxMultiplier: 62,
    deductions: {
      personal: 24000,
      married: 4000,
      children: 10000,
      maxPillar3a: 7056,
      maxProfessionalExpenses: 2000,
    },
    specialFeatures: [
      'Very low tax rates',
      'Business-friendly',
      'Low wealth tax',
    ],
  },

  // Adding more cantons with representative data
  AG: {
    code: 'AG',
    name: 'Aargau',
    nameDE: 'Aargau',
    nameFR: 'Argovie',
    nameIT: 'Argovia',
    capital: 'Aarau',
    incomeTaxBrackets: [
      { min: 0, max: 18200, rate: 1.0 },
      { min: 18200, max: 50000, rate: 3.0 },
      { min: 50000, max: 75000, rate: 5.0 },
      { min: 75000, max: 100000, rate: 7.0 },
      { min: 100000, max: Infinity, rate: 8.4 },
    ],
    wealthTaxRate: 0.2,
    maxWealthTaxRate: 0.8,
    churchTaxRate: 11.0,
    municipalTaxMultiplier: 105,
    deductions: {
      personal: 17000,
      married: 3500,
      children: 8000,
      maxPillar3a: 7056,
      maxProfessionalExpenses: 4000,
    },
    specialFeatures: ['Moderate rates', 'Industrial canton'],
  },

  // Simplified entries for remaining cantons (can be expanded)
  AI: {
    code: 'AI',
    name: 'Appenzell Innerrhoden',
    nameDE: 'Appenzell Innerrhoden',
    nameFR: 'Appenzell Rhodes-Intérieures',
    nameIT: 'Appenzello Interno',
    capital: 'Appenzell',
    incomeTaxBrackets: [{ min: 0, max: Infinity, rate: 1.2 }],
    wealthTaxRate: 0.1,
    maxWealthTaxRate: 0.3,
    churchTaxRate: 0,
    municipalTaxMultiplier: 100,
    deductions: {
      personal: 15000,
      married: 3000,
      children: 6500,
      maxPillar3a: 7056,
      maxProfessionalExpenses: 2000,
    },
    specialFeatures: ['Lowest tax rates', 'Traditional canton'],
  },
  AR: {
    code: 'AR',
    name: 'Appenzell Ausserrhoden',
    nameDE: 'Appenzell Ausserrhoden',
    nameFR: 'Appenzell Rhodes-Extérieures',
    nameIT: 'Appenzello Esterno',
    capital: 'Herisau',
    incomeTaxBrackets: [{ min: 0, max: Infinity, rate: 2.8 }],
    wealthTaxRate: 0.15,
    maxWealthTaxRate: 0.4,
    churchTaxRate: 10,
    municipalTaxMultiplier: 110,
    deductions: {
      personal: 16000,
      married: 3200,
      children: 7000,
      maxPillar3a: 7056,
      maxProfessionalExpenses: 2500,
    },
    specialFeatures: ['Low tax rates', 'Rural canton'],
  },
  BL: {
    code: 'BL',
    name: 'Basel-Landschaft',
    nameDE: 'Basel-Landschaft',
    nameFR: 'Bâle-Campagne',
    nameIT: 'Basilea Campagna',
    capital: 'Liestal',
    incomeTaxBrackets: [{ min: 0, max: Infinity, rate: 7.0 }],
    wealthTaxRate: 0.3,
    maxWealthTaxRate: 0.8,
    churchTaxRate: 12,
    municipalTaxMultiplier: 120,
    deductions: {
      personal: 20000,
      married: 4000,
      children: 8500,
      maxPillar3a: 7056,
      maxProfessionalExpenses: 3500,
    },
    specialFeatures: ['Suburban Basel', 'Chemical industry'],
  },
  BS: {
    code: 'BS',
    name: 'Basel-Stadt',
    nameDE: 'Basel-Stadt',
    nameFR: 'Bâle-Ville',
    nameIT: 'Basilea Città',
    capital: 'Basel',
    incomeTaxBrackets: [{ min: 0, max: Infinity, rate: 8.5 }],
    wealthTaxRate: 0.5,
    maxWealthTaxRate: 1.2,
    churchTaxRate: 12,
    municipalTaxMultiplier: 100,
    deductions: {
      personal: 19000,
      married: 3800,
      children: 8000,
      maxPillar3a: 7056,
      maxProfessionalExpenses: 4000,
    },
    specialFeatures: ['City canton', 'Pharmaceutical hub'],
  },
  FR: {
    code: 'FR',
    name: 'Fribourg',
    nameDE: 'Freiburg',
    nameFR: 'Fribourg',
    nameIT: 'Friborgo',
    capital: 'Fribourg',
    incomeTaxBrackets: [{ min: 0, max: Infinity, rate: 6.8 }],
    wealthTaxRate: 0.3,
    maxWealthTaxRate: 0.9,
    churchTaxRate: 15,
    municipalTaxMultiplier: 130,
    deductions: {
      personal: 18000,
      married: 3600,
      children: 7500,
      maxPillar3a: 7056,
      maxProfessionalExpenses: 3000,
    },
    specialFeatures: ['Bilingual canton', 'Agricultural'],
  },
  GL: {
    code: 'GL',
    name: 'Glarus',
    nameDE: 'Glarus',
    nameFR: 'Glaris',
    nameIT: 'Glarona',
    capital: 'Glarus',
    incomeTaxBrackets: [{ min: 0, max: Infinity, rate: 3.2 }],
    wealthTaxRate: 0.2,
    maxWealthTaxRate: 0.6,
    churchTaxRate: 8,
    municipalTaxMultiplier: 115,
    deductions: {
      personal: 17000,
      married: 3400,
      children: 7000,
      maxPillar3a: 7056,
      maxProfessionalExpenses: 2500,
    },
    specialFeatures: ['Small canton', 'Mountain region'],
  },
  GR: {
    code: 'GR',
    name: 'Graubünden',
    nameDE: 'Graubünden',
    nameFR: 'Grisons',
    nameIT: 'Grigioni',
    capital: 'Chur',
    incomeTaxBrackets: [{ min: 0, max: Infinity, rate: 4.5 }],
    wealthTaxRate: 0.2,
    maxWealthTaxRate: 0.7,
    churchTaxRate: 10,
    municipalTaxMultiplier: 125,
    deductions: {
      personal: 18500,
      married: 3700,
      children: 7800,
      maxPillar3a: 7056,
      maxProfessionalExpenses: 3000,
    },
    specialFeatures: ['Tourism canton', 'Trilingual'],
  },
  JU: {
    code: 'JU',
    name: 'Jura',
    nameDE: 'Jura',
    nameFR: 'Jura',
    nameIT: 'Giura',
    capital: 'Delémont',
    incomeTaxBrackets: [{ min: 0, max: Infinity, rate: 6.5 }],
    wealthTaxRate: 0.4,
    maxWealthTaxRate: 1.0,
    churchTaxRate: 14,
    municipalTaxMultiplier: 140,
    deductions: {
      personal: 16000,
      married: 3200,
      children: 7000,
      maxPillar3a: 7056,
      maxProfessionalExpenses: 2800,
    },
    specialFeatures: ['Newest canton', 'French-speaking'],
  },
  LU: {
    code: 'LU',
    name: 'Lucerne',
    nameDE: 'Luzern',
    nameFR: 'Lucerne',
    nameIT: 'Lucerna',
    capital: 'Lucerne',
    incomeTaxBrackets: [{ min: 0, max: Infinity, rate: 4.8 }],
    wealthTaxRate: 0.25,
    maxWealthTaxRate: 0.75,
    churchTaxRate: 12,
    municipalTaxMultiplier: 120,
    deductions: {
      personal: 19000,
      married: 3800,
      children: 8000,
      maxPillar3a: 7056,
      maxProfessionalExpenses: 3200,
    },
    specialFeatures: ['Central Switzerland', 'Tourism'],
  },
  NE: {
    code: 'NE',
    name: 'Neuchâtel',
    nameDE: 'Neuenburg',
    nameFR: 'Neuchâtel',
    nameIT: 'Neuchâtel',
    capital: 'Neuchâtel',
    incomeTaxBrackets: [{ min: 0, max: Infinity, rate: 7.2 }],
    wealthTaxRate: 0.35,
    maxWealthTaxRate: 0.95,
    churchTaxRate: 0,
    municipalTaxMultiplier: 135,
    deductions: {
      personal: 17500,
      married: 3500,
      children: 7500,
      maxPillar3a: 7056,
      maxProfessionalExpenses: 3000,
    },
    specialFeatures: ['French-speaking', 'Watchmaking'],
  },
  NW: {
    code: 'NW',
    name: 'Nidwalden',
    nameDE: 'Nidwalden',
    nameFR: 'Nidwald',
    nameIT: 'Nidvaldo',
    capital: 'Stans',
    incomeTaxBrackets: [{ min: 0, max: Infinity, rate: 2.5 }],
    wealthTaxRate: 0.1,
    maxWealthTaxRate: 0.4,
    churchTaxRate: 8,
    municipalTaxMultiplier: 100,
    deductions: {
      personal: 20000,
      married: 4000,
      children: 8500,
      maxPillar3a: 7056,
      maxProfessionalExpenses: 2500,
    },
    specialFeatures: ['Very low taxes', 'Wealthy residents'],
  },
  OW: {
    code: 'OW',
    name: 'Obwalden',
    nameDE: 'Obwalden',
    nameFR: 'Obwald',
    nameIT: 'Obvaldo',
    capital: 'Sarnen',
    incomeTaxBrackets: [{ min: 0, max: Infinity, rate: 2.8 }],
    wealthTaxRate: 0.12,
    maxWealthTaxRate: 0.45,
    churchTaxRate: 9,
    municipalTaxMultiplier: 105,
    deductions: {
      personal: 19500,
      married: 3900,
      children: 8200,
      maxPillar3a: 7056,
      maxProfessionalExpenses: 2400,
    },
    specialFeatures: ['Low taxes', 'Mountain canton'],
  },
  SG: {
    code: 'SG',
    name: 'St. Gallen',
    nameDE: 'St. Gallen',
    nameFR: 'Saint-Gall',
    nameIT: 'San Gallo',
    capital: 'St. Gallen',
    incomeTaxBrackets: [{ min: 0, max: Infinity, rate: 5.2 }],
    wealthTaxRate: 0.25,
    maxWealthTaxRate: 0.8,
    churchTaxRate: 11,
    municipalTaxMultiplier: 118,
    deductions: {
      personal: 18000,
      married: 3600,
      children: 7800,
      maxPillar3a: 7056,
      maxProfessionalExpenses: 3100,
    },
    specialFeatures: ['Eastern Switzerland', 'Textile industry'],
  },
  SH: {
    code: 'SH',
    name: 'Schaffhausen',
    nameDE: 'Schaffhausen',
    nameFR: 'Schaffhouse',
    nameIT: 'Sciaffusa',
    capital: 'Schaffhausen',
    incomeTaxBrackets: [{ min: 0, max: Infinity, rate: 4.9 }],
    wealthTaxRate: 0.2,
    maxWealthTaxRate: 0.7,
    churchTaxRate: 10,
    municipalTaxMultiplier: 112,
    deductions: {
      personal: 18500,
      married: 3700,
      children: 7900,
      maxPillar3a: 7056,
      maxProfessionalExpenses: 3000,
    },
    specialFeatures: ['Border canton', 'Industrial'],
  },
  SO: {
    code: 'SO',
    name: 'Solothurn',
    nameDE: 'Solothurn',
    nameFR: 'Soleure',
    nameIT: 'Soletta',
    capital: 'Solothurn',
    incomeTaxBrackets: [{ min: 0, max: Infinity, rate: 5.8 }],
    wealthTaxRate: 0.3,
    maxWealthTaxRate: 0.85,
    churchTaxRate: 12,
    municipalTaxMultiplier: 125,
    deductions: {
      personal: 17500,
      married: 3500,
      children: 7600,
      maxPillar3a: 7056,
      maxProfessionalExpenses: 3200,
    },
    specialFeatures: ['Industrial canton', 'Moderate taxes'],
  },
  SZ: {
    code: 'SZ',
    name: 'Schwyz',
    nameDE: 'Schwyz',
    nameFR: 'Schwyz',
    nameIT: 'Svitto',
    capital: 'Schwyz',
    incomeTaxBrackets: [{ min: 0, max: Infinity, rate: 3.0 }],
    wealthTaxRate: 0.15,
    maxWealthTaxRate: 0.5,
    churchTaxRate: 8,
    municipalTaxMultiplier: 108,
    deductions: {
      personal: 19000,
      married: 3800,
      children: 8000,
      maxPillar3a: 7056,
      maxProfessionalExpenses: 2800,
    },
    specialFeatures: ['Low taxes', 'Founding canton'],
  },
  TG: {
    code: 'TG',
    name: 'Thurgau',
    nameDE: 'Thurgau',
    nameFR: 'Thurgovie',
    nameIT: 'Turgovia',
    capital: 'Frauenfeld',
    incomeTaxBrackets: [{ min: 0, max: Infinity, rate: 4.6 }],
    wealthTaxRate: 0.2,
    maxWealthTaxRate: 0.7,
    churchTaxRate: 11,
    municipalTaxMultiplier: 115,
    deductions: {
      personal: 18000,
      married: 3600,
      children: 7700,
      maxPillar3a: 7056,
      maxProfessionalExpenses: 3000,
    },
    specialFeatures: ['Agricultural', 'Lake Constance'],
  },
  TI: {
    code: 'TI',
    name: 'Ticino',
    nameDE: 'Tessin',
    nameFR: 'Tessin',
    nameIT: 'Ticino',
    capital: 'Bellinzona',
    incomeTaxBrackets: [{ min: 0, max: Infinity, rate: 6.8 }],
    wealthTaxRate: 0.3,
    maxWealthTaxRate: 0.9,
    churchTaxRate: 0,
    municipalTaxMultiplier: 130,
    deductions: {
      personal: 16000,
      married: 3200,
      children: 7000,
      maxPillar3a: 7056,
      maxProfessionalExpenses: 2800,
    },
    specialFeatures: ['Italian-speaking', 'No church tax'],
  },
  UR: {
    code: 'UR',
    name: 'Uri',
    nameDE: 'Uri',
    nameFR: 'Uri',
    nameIT: 'Uri',
    capital: 'Altdorf',
    incomeTaxBrackets: [{ min: 0, max: Infinity, rate: 3.5 }],
    wealthTaxRate: 0.15,
    maxWealthTaxRate: 0.5,
    churchTaxRate: 9,
    municipalTaxMultiplier: 110,
    deductions: {
      personal: 18500,
      married: 3700,
      children: 7800,
      maxPillar3a: 7056,
      maxProfessionalExpenses: 2600,
    },
    specialFeatures: ['Mountain canton', 'Low population'],
  },
  VS: {
    code: 'VS',
    name: 'Valais',
    nameDE: 'Wallis',
    nameFR: 'Valais',
    nameIT: 'Vallese',
    capital: 'Sion',
    incomeTaxBrackets: [{ min: 0, max: Infinity, rate: 5.5 }],
    wealthTaxRate: 0.25,
    maxWealthTaxRate: 0.8,
    churchTaxRate: 13,
    municipalTaxMultiplier: 128,
    deductions: {
      personal: 17000,
      married: 3400,
      children: 7300,
      maxPillar3a: 7056,
      maxProfessionalExpenses: 2900,
    },
    specialFeatures: ['Bilingual', 'Tourism', 'Wine region'],
  },
};
