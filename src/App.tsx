import React from 'react';
import {
  Navigate,
  Route,
  BrowserRouter as Router,
  Routes,
} from 'react-router-dom';
import { AdminPage } from './components/admin/AdminPage';
import { FireOrRetireCalculator } from './components/FireOrRetireCalculator';

const App: React.FC = () => {
  return (
    <Router>
      <Routes>
        <Route path='/admin' element={<AdminPage darkMode={true} />} />
        <Route path='/' element={<FireOrRetireCalculator darkMode={true} />} />
        <Route path='*' element={<Navigate to='/' replace />} />
      </Routes>
    </Router>
  );
};

export default App;
