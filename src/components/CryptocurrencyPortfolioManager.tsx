import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocalStorage } from '../hooks/useLocalStorage';

interface CryptocurrencyPortfolioManagerProps {
  darkMode: boolean;
}

interface CryptoHolding {
  id: string;
  symbol: string;
  name: string;
  amount: number;
  averageBuyPrice: number;
  currentPrice: number;
  purchaseDate: string;
  exchange: string;
  notes: string;
}

interface CryptoPortfolioSummary {
  totalValue: number;
  totalInvested: number;
  totalGainLoss: number;
  totalGainLossPercentage: number;
  topPerformer: CryptoHolding | null;
  worstPerformer: CryptoHolding | null;
}

interface SwissTaxInfo {
  isPrivateWealth: boolean;
  taxableGains: number;
  wealthTaxValue: number;
  recommendations: string[];
}

const CryptocurrencyPortfolioManager: React.FC<
  CryptocurrencyPortfolioManagerProps
> = ({ darkMode }) => {
  const { t } = useTranslation();
  const [holdings, setHoldings] = useLocalStorage<CryptoHolding[]>(
    'crypto_holdings',
    [],
  );
  const [newHolding, setNewHolding] = useState<Partial<CryptoHolding>>({
    symbol: '',
    name: '',
    amount: 0,
    averageBuyPrice: 0,
    purchaseDate: new Date().toISOString().split('T')[0],
    exchange: '',
    notes: '',
  });
  const [portfolioSummary, setPortfolioSummary] =
    useState<CryptoPortfolioSummary | null>(null);
  const [swissTaxInfo, setSwissTaxInfo] = useState<SwissTaxInfo | null>(null);
  const [isLoadingPrices, setIsLoadingPrices] = useState(false);

  // Popular cryptocurrencies with mock current prices
  const popularCryptos = [
    { symbol: 'BTC', name: 'Bitcoin', currentPrice: 43250 },
    { symbol: 'ETH', name: 'Ethereum', currentPrice: 2580 },
    { symbol: 'ADA', name: 'Cardano', currentPrice: 0.52 },
    { symbol: 'DOT', name: 'Polkadot', currentPrice: 7.85 },
    { symbol: 'LINK', name: 'Chainlink', currentPrice: 14.2 },
    { symbol: 'MATIC', name: 'Polygon', currentPrice: 0.89 },
    { symbol: 'AVAX', name: 'Avalanche', currentPrice: 38.5 },
    { symbol: 'SOL', name: 'Solana', currentPrice: 98.75 },
  ];

  // Swiss exchanges
  const swissExchanges = [
    'Bitpanda Pro',
    'Coinbase Pro',
    'Kraken',
    'Binance',
    'Bitcoin Suisse',
    'SwissBorg',
    'Relai',
    'Pocket Bitcoin',
  ];

  // Update current prices (mock implementation)
  const updateCurrentPrices = async () => {
    setIsLoadingPrices(true);

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1500));

      const updatedHoldings = holdings.map(holding => {
        const cryptoData = popularCryptos.find(
          crypto => crypto.symbol === holding.symbol,
        );
        if (cryptoData) {
          // Add some random price variation (±5%)
          const variation = (Math.random() - 0.5) * 0.1;
          const newPrice = cryptoData.currentPrice * (1 + variation);
          return { ...holding, currentPrice: newPrice };
        }
        return holding;
      });

      setHoldings(updatedHoldings);
    } catch (error) {
      console.error('Failed to update crypto prices:', error);
    } finally {
      setIsLoadingPrices(false);
    }
  };

  // Calculate portfolio summary
  useEffect(() => {
    if (holdings.length === 0) {
      setPortfolioSummary(null);
      return;
    }

    const totalValue = holdings.reduce(
      (sum, holding) => sum + holding.amount * holding.currentPrice,
      0,
    );
    const totalInvested = holdings.reduce(
      (sum, holding) => sum + holding.amount * holding.averageBuyPrice,
      0,
    );
    const totalGainLoss = totalValue - totalInvested;
    const totalGainLossPercentage =
      totalInvested > 0 ? (totalGainLoss / totalInvested) * 100 : 0;

    // Find top and worst performers
    const holdingsWithGains = holdings.map(holding => ({
      ...holding,
      gainLoss:
        ((holding.currentPrice - holding.averageBuyPrice) /
          holding.averageBuyPrice) *
        100,
    }));

    const topPerformer = holdingsWithGains.reduce(
      (best, current) => (current.gainLoss > best.gainLoss ? current : best),
      holdingsWithGains[0],
    );

    const worstPerformer = holdingsWithGains.reduce(
      (worst, current) => (current.gainLoss < worst.gainLoss ? current : worst),
      holdingsWithGains[0],
    );

    setPortfolioSummary({
      totalValue,
      totalInvested,
      totalGainLoss,
      totalGainLossPercentage,
      topPerformer: topPerformer || null,
      worstPerformer: worstPerformer || null,
    });

    // Calculate Swiss tax implications
    calculateSwissTaxInfo(totalValue, totalGainLoss);
  }, [holdings]);

  const calculateSwissTaxInfo = (totalValue: number, totalGainLoss: number) => {
    // Swiss crypto tax rules (simplified)
    const isPrivateWealth = totalValue < 100000; // Simplified threshold
    const taxableGains = isPrivateWealth ? 0 : Math.max(0, totalGainLoss);
    const wealthTaxValue = totalValue; // Full value subject to wealth tax

    const recommendations = [];

    if (isPrivateWealth) {
      recommendations.push(
        '✅ Holdings likely qualify as private wealth - no income tax on gains',
      );
      recommendations.push(
        '⚠️ Still subject to wealth tax on total portfolio value',
      );
    } else {
      recommendations.push(
        '⚠️ Large holdings may be considered business assets - gains taxable as income',
      );
      recommendations.push(
        '📋 Consider consulting a Swiss tax advisor for professional trading activities',
      );
    }

    if (totalValue > 50000) {
      recommendations.push(
        '📊 Maintain detailed records of all transactions for tax reporting',
      );
    }

    if (totalGainLoss < 0) {
      recommendations.push(
        '💡 Losses cannot be deducted if held as private wealth',
      );
    }

    setSwissTaxInfo({
      isPrivateWealth,
      taxableGains,
      wealthTaxValue,
      recommendations,
    });
  };

  const addHolding = () => {
    if (
      !newHolding.symbol ||
      !newHolding.amount ||
      !newHolding.averageBuyPrice
    ) {
      return;
    }

    const cryptoData = popularCryptos.find(
      crypto => crypto.symbol === newHolding.symbol,
    );
    const currentPrice =
      cryptoData?.currentPrice || newHolding.averageBuyPrice || 0;

    const holding: CryptoHolding = {
      id: Date.now().toString(),
      symbol: newHolding.symbol,
      name: newHolding.name || cryptoData?.name || newHolding.symbol,
      amount: newHolding.amount || 0,
      averageBuyPrice: newHolding.averageBuyPrice || 0,
      currentPrice: currentPrice,
      purchaseDate:
        newHolding.purchaseDate || new Date().toISOString().split('T')[0],
      exchange: newHolding.exchange || '',
      notes: newHolding.notes || '',
    };

    setHoldings([...holdings, holding]);
    setNewHolding({
      symbol: '',
      name: '',
      amount: 0,
      averageBuyPrice: 0,
      purchaseDate: new Date().toISOString().split('T')[0],
      exchange: '',
      notes: '',
    });
  };

  const removeHolding = (id: string) => {
    setHoldings(holdings.filter(holding => holding.id !== id));
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('de-CH', {
      style: 'currency',
      currency: 'CHF',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  const formatCrypto = (amount: number, decimals: number = 6) => {
    return amount.toFixed(decimals);
  };

  const formatPercentage = (percentage: number) => {
    const color = percentage >= 0 ? 'text-green-600' : 'text-red-600';
    const sign = percentage >= 0 ? '+' : '';
    return (
      <span className={color}>
        {sign}
        {percentage.toFixed(2)}%
      </span>
    );
  };

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div>
        <h2 className='text-2xl font-bold mb-2'>
          ₿ Cryptocurrency Portfolio Manager
        </h2>
        <p className='text-gray-600 dark:text-gray-400'>
          Track your cryptocurrency investments with Swiss tax compliance
          features
        </p>
      </div>

      {/* Portfolio Summary */}
      {portfolioSummary && (
        <div
          className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
        >
          <div className='flex items-center justify-between mb-4'>
            <h3 className='text-lg font-semibold'>📊 Portfolio Summary</h3>
            <button
              onClick={updateCurrentPrices}
              disabled={isLoadingPrices}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                isLoadingPrices
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-blue-600 hover:bg-blue-700 text-white'
              }`}
            >
              {isLoadingPrices ? '🔄 Updating...' : '🔄 Update Prices'}
            </button>
          </div>

          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
            <div className='text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg'>
              <div className='text-sm text-gray-600 dark:text-gray-400'>
                Total Value
              </div>
              <div className='text-2xl font-bold text-blue-600'>
                {formatCurrency(portfolioSummary.totalValue)}
              </div>
            </div>

            <div className='text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg'>
              <div className='text-sm text-gray-600 dark:text-gray-400'>
                Total Invested
              </div>
              <div className='text-2xl font-bold'>
                {formatCurrency(portfolioSummary.totalInvested)}
              </div>
            </div>

            <div className='text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg'>
              <div className='text-sm text-gray-600 dark:text-gray-400'>
                Total Gain/Loss
              </div>
              <div
                className={`text-2xl font-bold ${portfolioSummary.totalGainLoss >= 0 ? 'text-green-600' : 'text-red-600'}`}
              >
                {formatCurrency(portfolioSummary.totalGainLoss)}
              </div>
              <div className='text-sm'>
                {formatPercentage(portfolioSummary.totalGainLossPercentage)}
              </div>
            </div>

            <div className='text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg'>
              <div className='text-sm text-gray-600 dark:text-gray-400'>
                Holdings
              </div>
              <div className='text-2xl font-bold text-purple-600'>
                {holdings.length}
              </div>
              <div className='text-sm text-gray-500'>Assets</div>
            </div>
          </div>
        </div>
      )}

      {/* Swiss Tax Information */}
      {swissTaxInfo && (
        <div
          className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
        >
          <h3 className='text-lg font-semibold mb-4'>
            🇨🇭 Swiss Tax Information
          </h3>

          <div className='grid grid-cols-1 md:grid-cols-3 gap-4 mb-4'>
            <div className='text-center p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg'>
              <div className='text-sm text-gray-600 dark:text-gray-400'>
                Classification
              </div>
              <div className='text-lg font-bold text-yellow-600'>
                {swissTaxInfo.isPrivateWealth
                  ? 'Private Wealth'
                  : 'Business Assets'}
              </div>
            </div>

            <div className='text-center p-4 bg-red-50 dark:bg-red-900/20 rounded-lg'>
              <div className='text-sm text-gray-600 dark:text-gray-400'>
                Taxable Gains
              </div>
              <div className='text-lg font-bold text-red-600'>
                {formatCurrency(swissTaxInfo.taxableGains)}
              </div>
            </div>

            <div className='text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg'>
              <div className='text-sm text-gray-600 dark:text-gray-400'>
                Wealth Tax Value
              </div>
              <div className='text-lg font-bold text-blue-600'>
                {formatCurrency(swissTaxInfo.wealthTaxValue)}
              </div>
            </div>
          </div>

          <div className='space-y-2'>
            <h4 className='font-medium'>Tax Recommendations:</h4>
            {swissTaxInfo.recommendations.map((rec, index) => (
              <div
                key={index}
                className='text-sm text-gray-600 dark:text-gray-400'
              >
                {rec}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Add New Holding */}
      <div
        className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
      >
        <h3 className='text-lg font-semibold mb-4'>➕ Add New Holding</h3>

        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
          <div>
            <label className='block text-sm font-medium mb-2'>
              Cryptocurrency
            </label>
            <select
              value={newHolding.symbol}
              onChange={e => {
                const crypto = popularCryptos.find(
                  c => c.symbol === e.target.value,
                );
                setNewHolding(prev => ({
                  ...prev,
                  symbol: e.target.value,
                  name: crypto?.name || '',
                }));
              }}
              className={`w-full px-3 py-2 rounded-md border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
            >
              <option value=''>Select cryptocurrency</option>
              {popularCryptos.map(crypto => (
                <option key={crypto.symbol} value={crypto.symbol}>
                  {crypto.symbol} - {crypto.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className='block text-sm font-medium mb-2'>Amount</label>
            <input
              type='number'
              step='0.000001'
              value={newHolding.amount || ''}
              onChange={e =>
                setNewHolding(prev => ({
                  ...prev,
                  amount: parseFloat(e.target.value) || 0,
                }))
              }
              className={`w-full px-3 py-2 rounded-md border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
              placeholder='0.000000'
            />
          </div>

          <div>
            <label className='block text-sm font-medium mb-2'>
              Average Buy Price (CHF)
            </label>
            <input
              type='number'
              step='0.01'
              value={newHolding.averageBuyPrice || ''}
              onChange={e =>
                setNewHolding(prev => ({
                  ...prev,
                  averageBuyPrice: parseFloat(e.target.value) || 0,
                }))
              }
              className={`w-full px-3 py-2 rounded-md border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
              placeholder='0.00'
            />
          </div>

          <div>
            <label className='block text-sm font-medium mb-2'>Exchange</label>
            <select
              value={newHolding.exchange}
              onChange={e =>
                setNewHolding(prev => ({ ...prev, exchange: e.target.value }))
              }
              className={`w-full px-3 py-2 rounded-md border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
            >
              <option value=''>Select exchange</option>
              {swissExchanges.map(exchange => (
                <option key={exchange} value={exchange}>
                  {exchange}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className='mt-4 flex justify-end'>
          <button
            onClick={addHolding}
            className='px-6 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors'
          >
            Add Holding
          </button>
        </div>
      </div>

      {/* Holdings List */}
      {holdings.length > 0 && (
        <div
          className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
        >
          <h3 className='text-lg font-semibold mb-4'>💼 Your Holdings</h3>

          <div className='overflow-x-auto'>
            <table className='w-full text-sm'>
              <thead>
                <tr
                  className={`border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
                >
                  <th className='text-left py-2'>Asset</th>
                  <th className='text-right py-2'>Amount</th>
                  <th className='text-right py-2'>Avg. Buy Price</th>
                  <th className='text-right py-2'>Current Price</th>
                  <th className='text-right py-2'>Value</th>
                  <th className='text-right py-2'>Gain/Loss</th>
                  <th className='text-center py-2'>Actions</th>
                </tr>
              </thead>
              <tbody>
                {holdings.map(holding => {
                  const value = holding.amount * holding.currentPrice;
                  const invested = holding.amount * holding.averageBuyPrice;
                  const gainLoss = value - invested;
                  const gainLossPercentage =
                    invested > 0 ? (gainLoss / invested) * 100 : 0;

                  return (
                    <tr
                      key={holding.id}
                      className={`border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
                    >
                      <td className='py-3'>
                        <div>
                          <div className='font-medium'>{holding.symbol}</div>
                          <div className='text-xs text-gray-500'>
                            {holding.name}
                          </div>
                        </div>
                      </td>
                      <td className='text-right py-3'>
                        {formatCrypto(holding.amount)}
                      </td>
                      <td className='text-right py-3'>
                        {formatCurrency(holding.averageBuyPrice)}
                      </td>
                      <td className='text-right py-3'>
                        {formatCurrency(holding.currentPrice)}
                      </td>
                      <td className='text-right py-3 font-medium'>
                        {formatCurrency(value)}
                      </td>
                      <td className='text-right py-3'>
                        <div>{formatCurrency(gainLoss)}</div>
                        <div>{formatPercentage(gainLossPercentage)}</div>
                      </td>
                      <td className='text-center py-3'>
                        <button
                          onClick={() => removeHolding(holding.id)}
                          className='text-red-600 hover:text-red-800 transition-colors'
                          title='Remove holding'
                        >
                          🗑️
                        </button>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {holdings.length === 0 && (
        <div
          className={`p-8 rounded-lg text-center ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
        >
          <div className='text-4xl mb-4'>₿</div>
          <h3 className='text-lg font-semibold mb-2'>
            No cryptocurrency holdings yet
          </h3>
          <p className='text-gray-600 dark:text-gray-400'>
            Add your first cryptocurrency holding to start tracking your
            portfolio
          </p>
        </div>
      )}
    </div>
  );
};

export default CryptocurrencyPortfolioManager;
