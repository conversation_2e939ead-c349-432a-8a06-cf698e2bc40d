import React, { useEffect, useState } from 'react';
import { DebugLogEntry, debugLogger } from '../utils/debug-logger';

interface DebugPanelProps {
  isOpen: boolean;
  onClose: () => void;
}

const DebugPanel: React.FC<DebugPanelProps> = ({ isOpen, onClose }) => {
  const [activeTab, setActiveTab] = useState<
    'logs' | 'performance' | 'errors' | 'system'
  >('logs');
  const [logs, setLogs] = useState<DebugLogEntry[]>([]);
  const [filter, setFilter] = useState<string>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [stats, setStats] = useState<any>({});
  const [performanceData, setPerformanceData] = useState<any>({});
  const [errorReports, setErrorReports] = useState<any[]>([]);
  const [systemInfo, setSystemInfo] = useState<any>({});
  const [autoRefresh, setAutoRefresh] = useState<boolean>(true);

  useEffect(() => {
    if (isOpen) {
      refreshAllData();

      if (autoRefresh) {
        const interval = setInterval(refreshAllData, 2000);
        return () => clearInterval(interval);
      }
    }
  }, [isOpen, autoRefresh]);

  const refreshAllData = () => {
    // Refresh logs
    setLogs(debugLogger.getRecentLogs(100));
    setStats(debugLogger.getStats());

    // Refresh performance data
    const metrics = performanceMonitor.getMetrics();
    const memorySnapshots = performanceMonitor.getMemorySnapshots();
    const networkMetrics = performanceMonitor.getNetworkMetrics();

    setPerformanceData({
      metrics: metrics.slice(-20),
      slowOperations: performanceMonitor.getSlowOperations(),
      memorySnapshots: memorySnapshots.slice(-10),
      networkMetrics: networkMetrics.slice(-10),
      summary: {
        totalMetrics: metrics.length,
        slowOpsCount: performanceMonitor.getSlowOperations().length,
        avgRenderTime: calculateAverageRenderTime(metrics),
        memoryUsage: getLatestMemoryUsage(memorySnapshots),
      },
    });

    // Refresh error reports
    setErrorReports(errorReporter.getStoredReports());

    // Refresh system info
    setSystemInfo({
      session: errorReporter.getSessionStats(),
      browser: {
        userAgent: navigator.userAgent,
        language: navigator.language,
        platform: navigator.platform,
        onLine: navigator.onLine,
        cookieEnabled: navigator.cookieEnabled,
      },
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight,
      },
      memory: (performance as any).memory
        ? {
            used: Math.round(
              (performance as any).memory.usedJSHeapSize / 1024 / 1024,
            ),
            total: Math.round(
              (performance as any).memory.totalJSHeapSize / 1024 / 1024,
            ),
            limit: Math.round(
              (performance as any).memory.jsHeapSizeLimit / 1024 / 1024,
            ),
          }
        : null,
    });
  };

  const calculateAverageRenderTime = (metrics: any[]): number => {
    const renderMetrics = metrics.filter(m => m.type === 'render');
    if (renderMetrics.length === 0) return 0;
    return (
      renderMetrics.reduce((sum, m) => sum + m.duration, 0) /
      renderMetrics.length
    );
  };

  const getLatestMemoryUsage = (snapshots: any[]): number => {
    if (snapshots.length === 0) return 0;
    const latest = snapshots[snapshots.length - 1];
    return latest ? latest.percentage : 0;
  };

  const filteredLogs = logs.filter(log => {
    const levelMatch = filter === 'all' || log.level === filter;
    const categoryMatch =
      categoryFilter === 'all' || log.category === categoryFilter;
    return levelMatch && categoryMatch;
  });

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'error':
        return 'text-red-400 bg-red-900/20';
      case 'warn':
        return 'text-yellow-400 bg-yellow-900/20';
      case 'info':
        return 'text-blue-400 bg-blue-900/20';
      case 'debug':
        return 'text-green-400 bg-green-900/20';
      default:
        return 'text-gray-400 bg-gray-900/20';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const uniqueCategories = [...new Set(logs.map(log => log.category))];

  if (!isOpen) return null;

  return (
    <div className='fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4'>
      <div className='bg-gray-900 border border-gray-700 rounded-lg shadow-2xl w-full max-w-6xl h-5/6 flex flex-col'>
        {/* Header */}
        <div className='flex items-center justify-between p-4 border-b border-gray-700'>
          <div className='flex items-center space-x-4'>
            <h2 className='text-xl font-bold text-white'>🐛 Debug Panel</h2>
            <div className='text-sm text-gray-400'>
              Session: {stats.sessionId?.slice(-8)}
            </div>
          </div>
          <div className='flex items-center space-x-2'>
            <button
              onClick={() => setAutoRefresh(!autoRefresh)}
              className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                autoRefresh
                  ? 'bg-green-600 text-white'
                  : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
              }`}
            >
              {autoRefresh ? '⏸️ Pause' : '▶️ Resume'}
            </button>
            <button
              onClick={refreshAllData}
              className='px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm font-medium transition-colors'
            >
              🔄 Refresh
            </button>
            <button
              onClick={() => debugLogger.downloadLogs()}
              className='px-3 py-1 bg-purple-600 hover:bg-purple-700 text-white rounded text-sm font-medium transition-colors'
            >
              📥 Export
            </button>
            <button
              onClick={() => {
                debugLogger.clearLogs();
                performanceMonitor.clearMetrics();
                errorReporter.clearReports();
                refreshAllData();
              }}
              className='px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded text-sm font-medium transition-colors'
            >
              🗑️ Clear
            </button>
            <button
              onClick={onClose}
              className='px-3 py-1 bg-gray-600 hover:bg-gray-700 text-white rounded text-sm font-medium transition-colors'
            >
              ✕ Close
            </button>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className='flex border-b border-gray-700 bg-gray-800'>
          {[
            { id: 'logs', label: '📝 Logs', count: logs.length },
            {
              id: 'performance',
              label: '⚡ Performance',
              count: performanceData.summary?.slowOpsCount || 0,
            },
            { id: 'errors', label: '🚨 Errors', count: errorReports.length },
            { id: 'system', label: '🖥️ System', count: null },
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`px-4 py-3 text-sm font-medium transition-colors border-b-2 ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-400 bg-gray-700'
                  : 'border-transparent text-gray-400 hover:text-gray-300 hover:bg-gray-700'
              }`}
            >
              {tab.label}
              {tab.count !== null && tab.count > 0 && (
                <span className='ml-2 px-2 py-1 text-xs bg-red-600 text-white rounded-full'>
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </div>

        {/* Stats Bar */}
        <div className='p-4 bg-gray-800 border-b border-gray-700'>
          <div className='grid grid-cols-2 md:grid-cols-5 gap-4 text-sm'>
            <div className='text-center'>
              <div className='text-gray-400'>Total Logs</div>
              <div className='text-white font-bold'>{stats.totalLogs || 0}</div>
            </div>
            <div className='text-center'>
              <div className='text-red-400'>Errors</div>
              <div className='text-white font-bold'>
                {stats.logsByLevel?.error || 0}
              </div>
            </div>
            <div className='text-center'>
              <div className='text-yellow-400'>Warnings</div>
              <div className='text-white font-bold'>
                {stats.logsByLevel?.warn || 0}
              </div>
            </div>
            <div className='text-center'>
              <div className='text-blue-400'>Info</div>
              <div className='text-white font-bold'>
                {stats.logsByLevel?.info || 0}
              </div>
            </div>
            <div className='text-center'>
              <div className='text-green-400'>Debug</div>
              <div className='text-white font-bold'>
                {stats.logsByLevel?.debug || 0}
              </div>
            </div>
          </div>
        </div>

        {/* Tab Content */}
        <div className='flex-1 overflow-auto'>
          {activeTab === 'logs' && (
            <>
              {/* Filters */}
              <div className='p-4 bg-gray-800 border-b border-gray-700'>
                <div className='flex flex-wrap items-center gap-4'>
                  <div className='flex items-center space-x-2'>
                    <label className='text-sm text-gray-400'>Level:</label>
                    <select
                      value={filter}
                      onChange={e => setFilter(e.target.value)}
                      className='bg-gray-700 border border-gray-600 rounded px-2 py-1 text-white text-sm'
                    >
                      <option value='all'>All</option>
                      <option value='error'>Error</option>
                      <option value='warn'>Warning</option>
                      <option value='info'>Info</option>
                      <option value='debug'>Debug</option>
                    </select>
                  </div>
                  <div className='flex items-center space-x-2'>
                    <label className='text-sm text-gray-400'>Category:</label>
                    <select
                      value={categoryFilter}
                      onChange={e => setCategoryFilter(e.target.value)}
                      className='bg-gray-700 border border-gray-600 rounded px-2 py-1 text-white text-sm'
                    >
                      <option value='all'>All</option>
                      {uniqueCategories.map(category => (
                        <option key={category} value={category}>
                          {category}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div className='text-sm text-gray-400'>
                    Showing {filteredLogs.length} of {logs.length} logs
                  </div>
                </div>
              </div>

              {/* Logs */}
              <div className='p-4 space-y-2'>
                {filteredLogs.length === 0 ? (
                  <div className='text-center text-gray-500 py-8'>
                    No logs match the current filters
                  </div>
                ) : (
                  filteredLogs.map(log => (
                    <div
                      key={log.id}
                      className={`p-3 rounded border border-gray-700 ${getLevelColor(log.level)}`}
                    >
                      <div className='flex items-start justify-between'>
                        <div className='flex-1'>
                          <div className='flex items-center space-x-2 mb-1'>
                            <span className='text-xs font-mono bg-gray-800 px-2 py-1 rounded'>
                              {formatTimestamp(log.timestamp)}
                            </span>
                            <span className='text-xs font-bold uppercase'>
                              {log.level}
                            </span>
                            <span className='text-xs bg-gray-700 px-2 py-1 rounded'>
                              {log.category}
                            </span>
                          </div>
                          <div className='font-medium mb-2'>{log.message}</div>
                          {log.data && (
                            <details className='mt-2'>
                              <summary className='cursor-pointer text-sm text-gray-400 hover:text-gray-300'>
                                Show data
                              </summary>
                              <pre className='mt-2 text-xs bg-gray-800 p-2 rounded overflow-auto max-h-40'>
                                {JSON.stringify(log.data, null, 2)}
                              </pre>
                            </details>
                          )}
                          {log.stack && (
                            <details className='mt-2'>
                              <summary className='cursor-pointer text-sm text-gray-400 hover:text-gray-300'>
                                Show stack trace
                              </summary>
                              <pre className='mt-2 text-xs bg-gray-800 p-2 rounded overflow-auto max-h-40 text-red-300'>
                                {log.stack}
                              </pre>
                            </details>
                          )}
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </>
          )}

          {activeTab === 'performance' && (
            <div className='p-4 space-y-4'>
              <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
                <div className='bg-gray-800 p-4 rounded border border-gray-700'>
                  <div className='text-sm text-gray-400'>Total Metrics</div>
                  <div className='text-2xl font-bold text-white'>
                    {performanceData.summary?.totalMetrics || 0}
                  </div>
                </div>
                <div className='bg-gray-800 p-4 rounded border border-gray-700'>
                  <div className='text-sm text-gray-400'>Slow Operations</div>
                  <div className='text-2xl font-bold text-orange-400'>
                    {performanceData.summary?.slowOpsCount || 0}
                  </div>
                </div>
                <div className='bg-gray-800 p-4 rounded border border-gray-700'>
                  <div className='text-sm text-gray-400'>Avg Render Time</div>
                  <div className='text-2xl font-bold text-blue-400'>
                    {(performanceData.summary?.avgRenderTime || 0).toFixed(1)}ms
                  </div>
                </div>
                <div className='bg-gray-800 p-4 rounded border border-gray-700'>
                  <div className='text-sm text-gray-400'>Memory Usage</div>
                  <div className='text-2xl font-bold text-green-400'>
                    {(performanceData.summary?.memoryUsage || 0).toFixed(1)}%
                  </div>
                </div>
              </div>

              {performanceData.slowOperations?.length > 0 && (
                <div className='bg-gray-800 p-4 rounded border border-gray-700'>
                  <h4 className='text-lg font-bold text-white mb-3'>
                    🐌 Slow Operations
                  </h4>
                  <div className='space-y-2'>
                    {performanceData.slowOperations
                      .slice(0, 10)
                      .map((op: any, index: number) => (
                        <div
                          key={index}
                          className='flex justify-between items-center p-2 bg-gray-700 rounded'
                        >
                          <div>
                            <div className='font-medium text-white'>
                              {op.name}
                            </div>
                            <div className='text-sm text-gray-400'>
                              {op.type}
                            </div>
                          </div>
                          <div className='text-orange-400 font-bold'>
                            {op.duration.toFixed(2)}ms
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'errors' && (
            <div className='p-4 space-y-4'>
              {errorReports.length === 0 ? (
                <div className='text-center text-gray-500 py-8'>
                  No error reports found
                </div>
              ) : (
                errorReports.map((report: any) => (
                  <div
                    key={report.id}
                    className='bg-gray-800 p-4 rounded border border-red-700'
                  >
                    <div className='flex justify-between items-start mb-3'>
                      <div>
                        <div className='font-bold text-red-400'>
                          {report.error.message}
                        </div>
                        <div className='text-sm text-gray-400'>
                          {formatTimestamp(report.timestamp)}
                        </div>
                      </div>
                      <button
                        onClick={() => errorReporter.exportReport(report)}
                        className='px-3 py-1 bg-purple-600 hover:bg-purple-700 text-white rounded text-sm'
                      >
                        Export
                      </button>
                    </div>
                    <div className='grid grid-cols-2 gap-4 text-sm'>
                      <div>
                        <div className='text-gray-400'>Severity:</div>
                        <div className='text-white'>
                          {report.reproduction.severity}
                        </div>
                      </div>
                      <div>
                        <div className='text-gray-400'>Component:</div>
                        <div className='text-white'>
                          {report.application.component || 'Unknown'}
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          )}

          {activeTab === 'system' && (
            <div className='p-4 space-y-4'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div className='bg-gray-800 p-4 rounded border border-gray-700'>
                  <h4 className='text-lg font-bold text-white mb-3'>
                    🖥️ Browser Info
                  </h4>
                  <div className='space-y-2 text-sm'>
                    <div>
                      <span className='text-gray-400'>Platform:</span>{' '}
                      <span className='text-white'>
                        {systemInfo.browser?.platform}
                      </span>
                    </div>
                    <div>
                      <span className='text-gray-400'>Language:</span>{' '}
                      <span className='text-white'>
                        {systemInfo.browser?.language}
                      </span>
                    </div>
                    <div>
                      <span className='text-gray-400'>Online:</span>{' '}
                      <span className='text-white'>
                        {systemInfo.browser?.onLine ? 'Yes' : 'No'}
                      </span>
                    </div>
                    <div>
                      <span className='text-gray-400'>Cookies:</span>{' '}
                      <span className='text-white'>
                        {systemInfo.browser?.cookieEnabled
                          ? 'Enabled'
                          : 'Disabled'}
                      </span>
                    </div>
                  </div>
                </div>

                <div className='bg-gray-800 p-4 rounded border border-gray-700'>
                  <h4 className='text-lg font-bold text-white mb-3'>
                    📱 Viewport
                  </h4>
                  <div className='space-y-2 text-sm'>
                    <div>
                      <span className='text-gray-400'>Width:</span>{' '}
                      <span className='text-white'>
                        {systemInfo.viewport?.width}px
                      </span>
                    </div>
                    <div>
                      <span className='text-gray-400'>Height:</span>{' '}
                      <span className='text-white'>
                        {systemInfo.viewport?.height}px
                      </span>
                    </div>
                  </div>
                </div>

                {systemInfo.memory && (
                  <div className='bg-gray-800 p-4 rounded border border-gray-700'>
                    <h4 className='text-lg font-bold text-white mb-3'>
                      💾 Memory
                    </h4>
                    <div className='space-y-2 text-sm'>
                      <div>
                        <span className='text-gray-400'>Used:</span>{' '}
                        <span className='text-white'>
                          {systemInfo.memory.used}MB
                        </span>
                      </div>
                      <div>
                        <span className='text-gray-400'>Total:</span>{' '}
                        <span className='text-white'>
                          {systemInfo.memory.total}MB
                        </span>
                      </div>
                      <div>
                        <span className='text-gray-400'>Limit:</span>{' '}
                        <span className='text-white'>
                          {systemInfo.memory.limit}MB
                        </span>
                      </div>
                    </div>
                  </div>
                )}

                <div className='bg-gray-800 p-4 rounded border border-gray-700'>
                  <h4 className='text-lg font-bold text-white mb-3'>
                    📊 Session
                  </h4>
                  <div className='space-y-2 text-sm'>
                    <div>
                      <span className='text-gray-400'>Duration:</span>{' '}
                      <span className='text-white'>
                        {Math.round(
                          (systemInfo.session?.sessionDuration || 0) / 1000,
                        )}
                        s
                      </span>
                    </div>
                    <div>
                      <span className='text-gray-400'>Actions:</span>{' '}
                      <span className='text-white'>
                        {systemInfo.session?.userActions || 0}
                      </span>
                    </div>
                    <div>
                      <span className='text-gray-400'>Errors:</span>{' '}
                      <span className='text-white'>
                        {systemInfo.session?.errorCount || 0}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className='p-4 bg-gray-800 border-t border-gray-700 text-xs text-gray-400'>
          <div className='flex justify-between items-center'>
            <div>
              Debug logging is {stats.isEnabled ? 'enabled' : 'disabled'}
            </div>
            <div>Press Ctrl+Shift+D to toggle this panel</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DebugPanel;
