import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocalStorage } from '../hooks/useLocalStorage';

interface AIFinancialAdvisorProps {
  darkMode: boolean;
  userData: {
    currentAge: number;
    retirementAge: number;
    currentSavings: number;
    monthlyIncome: number;
    monthlyExpenses: number;
    expectedReturn: number;
    inflationRate: number;
    safeWithdrawalRate: number;
  };
  expenses?: any[];
  investments?: any[];
  savingsGoals?: any[];
}

interface AIInsight {
  id: string;
  category: 'optimization' | 'risk' | 'opportunity' | 'warning' | 'goal';
  priority: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  recommendation: string;
  impact: string;
  confidence: number; // 0-100
  timeframe: 'immediate' | 'short-term' | 'medium-term' | 'long-term';
  actionable: boolean;
  swissSpecific: boolean;
}

interface AIAnalysis {
  overallScore: number; // 0-100
  riskLevel: 'low' | 'medium' | 'high';
  fireReadiness: number; // 0-100
  optimizationPotential: number; // 0-100
  insights: AIInsight[];
  personalizedPlan: string[];
}

interface ConversationMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  category?: string;
}

const AIFinancialAdvisor: React.FC<AIFinancialAdvisorProps> = ({
  darkMode,
  userData,
  expenses = [],
  investments = [],
  savingsGoals = [],
}) => {
  const { t } = useTranslation();
  const [analysis, setAnalysis] = useState<AIAnalysis | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [conversation, setConversation] = useLocalStorage<
    ConversationMessage[]
  >('ai_conversation', []);
  const [userQuestion, setUserQuestion] = useState('');
  const [isProcessingQuestion, setIsProcessingQuestion] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // AI Analysis Categories
  const analysisCategories = [
    { value: 'all', label: '🎯 All Insights', icon: '🎯' },
    { value: 'optimization', label: '⚡ Optimization', icon: '⚡' },
    { value: 'risk', label: '⚠️ Risk Management', icon: '⚠️' },
    { value: 'opportunity', label: '🚀 Opportunities', icon: '🚀' },
    { value: 'goal', label: '🎯 Goal Planning', icon: '🎯' },
    { value: 'swiss', label: '🇨🇭 Swiss Specific', icon: '🇨🇭' },
  ];

  // Generate AI Financial Analysis
  const generateAIAnalysis = async () => {
    setIsAnalyzing(true);

    try {
      // Simulate AI processing delay
      await new Promise(resolve => setTimeout(resolve, 3000));

      const {
        currentAge,
        retirementAge,
        currentSavings,
        monthlyIncome,
        monthlyExpenses,
        expectedReturn,
      } = userData;

      // Calculate key metrics
      const yearsToRetirement = Math.max(0, retirementAge - currentAge);
      const monthlySavings = monthlyIncome - monthlyExpenses;
      const savingsRate =
        monthlyIncome > 0 ? (monthlySavings / monthlyIncome) * 100 : 0;
      const fireNumber = monthlyExpenses * 12 * 25; // 4% rule
      const fireProgress =
        fireNumber > 0 ? (currentSavings / fireNumber) * 100 : 0;

      // Generate AI insights based on financial data
      const insights: AIInsight[] = [];

      // Savings Rate Analysis
      if (savingsRate < 10) {
        insights.push({
          id: '1',
          category: 'warning',
          priority: 'high',
          title: 'Low Savings Rate Detected',
          description: `Your current savings rate of ${savingsRate.toFixed(1)}% is below the recommended 20% for FIRE goals.`,
          recommendation:
            'Increase your savings rate by reducing expenses or increasing income. Consider the 50/30/20 rule as a starting point.',
          impact: 'Could delay retirement by 10-15 years',
          confidence: 95,
          timeframe: 'immediate',
          actionable: true,
          swissSpecific: false,
        });
      } else if (savingsRate > 50) {
        insights.push({
          id: '2',
          category: 'optimization',
          priority: 'medium',
          title: 'Excellent Savings Rate',
          description: `Your savings rate of ${savingsRate.toFixed(1)}% is exceptional and puts you on track for early retirement.`,
          recommendation:
            'Consider diversifying your investment strategy and exploring tax-advantaged accounts like Pillar 3a.',
          impact:
            'On track for retirement in ' +
            Math.ceil(yearsToRetirement * 0.7) +
            ' years',
          confidence: 90,
          timeframe: 'long-term',
          actionable: true,
          swissSpecific: true,
        });
      }

      // Swiss-specific insights
      const pillar3aLimit = 7056; // 2024 limit
      const currentPillar3a =
        savingsGoals.find(goal => goal.name?.toLowerCase().includes('pillar'))
          ?.currentAmount || 0;

      if (currentPillar3a < pillar3aLimit * 0.8) {
        insights.push({
          id: '3',
          category: 'opportunity',
          priority: 'high',
          title: 'Pillar 3a Optimization Opportunity',
          description:
            'You have unused Pillar 3a contribution capacity that could provide significant tax benefits.',
          recommendation: `Maximize your Pillar 3a contributions to CHF ${pillar3aLimit} annually for tax savings of up to CHF 2,500.`,
          impact: 'Potential annual tax savings of CHF 1,500-2,500',
          confidence: 98,
          timeframe: 'immediate',
          actionable: true,
          swissSpecific: true,
        });
      }

      // Investment diversification analysis
      const totalInvestments = investments.reduce(
        (sum, inv) => sum + inv.currentValue,
        0,
      );
      if (totalInvestments < currentSavings * 0.6) {
        insights.push({
          id: '4',
          category: 'risk',
          priority: 'medium',
          title: 'Low Investment Allocation',
          description:
            'A large portion of your wealth is in cash or low-yield accounts, missing growth opportunities.',
          recommendation:
            'Consider increasing your stock allocation to 60-80% for long-term growth, using Swiss ETFs for tax efficiency.',
          impact: 'Could increase retirement wealth by 30-50%',
          confidence: 85,
          timeframe: 'medium-term',
          actionable: true,
          swissSpecific: true,
        });
      }

      // Emergency fund analysis
      const emergencyFund =
        savingsGoals.find(goal =>
          goal.name?.toLowerCase().includes('emergency'),
        )?.currentAmount || 0;
      const emergencyTarget = monthlyExpenses * 6;

      if (emergencyFund < emergencyTarget) {
        insights.push({
          id: '5',
          category: 'risk',
          priority: 'high',
          title: 'Insufficient Emergency Fund',
          description: `Your emergency fund of CHF ${emergencyFund.toLocaleString()} is below the recommended 6 months of expenses.`,
          recommendation: `Build your emergency fund to CHF ${emergencyTarget.toLocaleString()} before aggressive investing.`,
          impact:
            'Reduces financial stress and prevents early withdrawal penalties',
          confidence: 100,
          timeframe: 'immediate',
          actionable: true,
          swissSpecific: false,
        });
      }

      // FIRE timeline optimization
      if (yearsToRetirement > 15 && savingsRate > 20) {
        insights.push({
          id: '6',
          category: 'opportunity',
          priority: 'medium',
          title: 'Early Retirement Potential',
          description:
            'Your strong savings rate suggests you could retire earlier than planned.',
          recommendation:
            'Consider increasing your expected return through strategic asset allocation and geographic diversification.',
          impact: 'Could retire 5-10 years earlier',
          confidence: 75,
          timeframe: 'long-term',
          actionable: true,
          swissSpecific: false,
        });
      }

      // Swiss tax optimization
      insights.push({
        id: '7',
        category: 'optimization',
        priority: 'medium',
        title: 'Swiss Tax Optimization Strategy',
        description:
          'Switzerland offers unique tax optimization opportunities through cantonal differences and structured withdrawals.',
        recommendation:
          'Consider canton relocation for tax savings and plan staggered Pillar 3a withdrawals to minimize tax burden.',
        impact: 'Potential lifetime tax savings of CHF 50,000-200,000',
        confidence: 80,
        timeframe: 'long-term',
        actionable: true,
        swissSpecific: true,
      });

      // Calculate overall scores
      const overallScore = Math.min(
        100,
        Math.max(
          0,
          savingsRate * 1.5 +
            fireProgress * 0.3 +
            (emergencyFund >= emergencyTarget ? 20 : 0) +
            (totalInvestments > currentSavings * 0.5 ? 15 : 0),
        ),
      );

      const riskLevel =
        overallScore > 70 ? 'low' : overallScore > 40 ? 'medium' : 'high';
      const fireReadiness = Math.min(100, fireProgress + savingsRate * 2);
      const optimizationPotential = 100 - overallScore;

      // Generate personalized plan
      const personalizedPlan = [
        `🎯 Primary Goal: Achieve FIRE by age ${retirementAge} with CHF ${fireNumber.toLocaleString()} target`,
        `💰 Savings Strategy: Maintain ${savingsRate > 20 ? 'current excellent' : 'improved'} savings rate of ${Math.max(savingsRate, 25).toFixed(0)}%`,
        `📈 Investment Allocation: ${totalInvestments < currentSavings * 0.6 ? 'Increase stock allocation to 70%' : 'Maintain diversified portfolio'}`,
        '🇨🇭 Swiss Optimization: Maximize Pillar 3a contributions and consider tax-efficient canton strategies',
        `⚠️ Risk Management: ${emergencyFund < emergencyTarget ? 'Build emergency fund first' : 'Maintain adequate emergency reserves'}`,
        '📊 Monitoring: Review and rebalance quarterly, adjust strategy based on market conditions',
      ];

      const analysis: AIAnalysis = {
        overallScore,
        riskLevel,
        fireReadiness,
        optimizationPotential,
        insights,
        personalizedPlan,
      };

      setAnalysis(analysis);
    } catch (error) {
      console.error('AI analysis failed:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Process user question with AI
  const processUserQuestion = async (question: string) => {
    if (!question.trim()) return;

    setIsProcessingQuestion(true);

    // Add user message
    const userMessage: ConversationMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: question,
      timestamp: new Date(),
    };

    setConversation(prev => [...prev, userMessage]);
    setUserQuestion('');

    try {
      // Simulate AI processing
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Generate AI response based on question context
      let aiResponse = '';
      const lowerQuestion = question.toLowerCase();

      if (
        lowerQuestion.includes('pillar 3a') ||
        lowerQuestion.includes('pillar')
      ) {
        aiResponse =
          'Based on your current financial situation, I recommend maximizing your Pillar 3a contributions. With your income level, you can contribute up to CHF 7,056 annually, which could save you CHF 1,500-2,500 in taxes. This is one of the most tax-efficient investment strategies available in Switzerland.';
      } else if (
        lowerQuestion.includes('retire') ||
        lowerQuestion.includes('fire')
      ) {
        const yearsToRetirement = Math.max(
          0,
          userData.retirementAge - userData.currentAge,
        );
        aiResponse = `Based on your current savings rate of ${(((userData.monthlyIncome - userData.monthlyExpenses) / userData.monthlyIncome) * 100).toFixed(1)}%, you're on track to retire in approximately ${yearsToRetirement} years. To accelerate this, consider increasing your savings rate or optimizing your investment allocation.`;
      } else if (
        lowerQuestion.includes('investment') ||
        lowerQuestion.includes('portfolio')
      ) {
        aiResponse =
          'For Swiss residents, I recommend a diversified portfolio with 60-80% stocks (Swiss and international ETFs), 15-25% bonds, and 5-15% alternatives. Consider tax-efficient Swiss domiciled ETFs and utilize your Pillar 3a for additional tax benefits.';
      } else if (
        lowerQuestion.includes('tax') ||
        lowerQuestion.includes('canton')
      ) {
        aiResponse =
          'Switzerland offers significant tax optimization opportunities. Consider canton relocation if feasible - moving from a high-tax canton like Geneva to a low-tax canton like Zug could save thousands annually. Also, plan your Pillar 3a withdrawals strategically to minimize tax impact.';
      } else if (
        lowerQuestion.includes('emergency') ||
        lowerQuestion.includes('fund')
      ) {
        const emergencyTarget = userData.monthlyExpenses * 6;
        aiResponse = `Your emergency fund should cover 6 months of expenses, approximately CHF ${emergencyTarget.toLocaleString()}. Keep this in a high-yield savings account for immediate access. Only invest beyond this amount for long-term growth.`;
      } else {
        aiResponse =
          "That's a great question! Based on your financial profile, I'd recommend focusing on three key areas: 1) Maximizing tax-advantaged accounts like Pillar 3a, 2) Maintaining a diversified investment portfolio, and 3) Optimizing your savings rate. Would you like me to elaborate on any of these areas?";
      }

      const aiMessage: ConversationMessage = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: aiResponse,
        timestamp: new Date(),
        category: 'advice',
      };

      setConversation(prev => [...prev, aiMessage]);
    } catch (error) {
      console.error('AI question processing failed:', error);
    } finally {
      setIsProcessingQuestion(false);
    }
  };

  // Filter insights by category
  const filteredInsights =
    analysis?.insights.filter(insight => {
      if (selectedCategory === 'all') return true;
      if (selectedCategory === 'swiss') return insight.swissSpecific;
      return insight.category === selectedCategory;
    }) || [];

  useEffect(() => {
    generateAIAnalysis();
  }, [userData]);

  const formatScore = (score: number) => `${score.toFixed(0)}%`;

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'border-red-500 bg-red-50 dark:bg-red-900/20';
      case 'medium':
        return 'border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20';
      case 'low':
        return 'border-blue-500 bg-blue-50 dark:bg-blue-900/20';
      default:
        return 'border-gray-500 bg-gray-50 dark:bg-gray-900/20';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'optimization':
        return '⚡';
      case 'risk':
        return '⚠️';
      case 'opportunity':
        return '🚀';
      case 'warning':
        return '🚨';
      case 'goal':
        return '🎯';
      default:
        return '💡';
    }
  };

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div>
        <h2 className='text-2xl font-bold mb-2'>🤖 AI Financial Advisor</h2>
        <p className='text-gray-600 dark:text-gray-400'>
          Personalized financial insights and recommendations powered by
          advanced AI analysis
        </p>
      </div>

      {/* AI Analysis Overview */}
      {isAnalyzing ? (
        <div
          className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
        >
          <div className='flex items-center justify-center py-8'>
            <div className='animate-spin w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full mr-3'></div>
            <span>AI is analyzing your financial situation...</span>
          </div>
        </div>
      ) : (
        analysis && (
          <div
            className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
          >
            <h3 className='text-lg font-semibold mb-4'>
              📊 AI Financial Health Score
            </h3>

            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
              <div className='text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg'>
                <div className='text-sm text-gray-600 dark:text-gray-400'>
                  Overall Score
                </div>
                <div
                  className={`text-3xl font-bold ${getScoreColor(analysis.overallScore)}`}
                >
                  {formatScore(analysis.overallScore)}
                </div>
                <div className='text-xs text-gray-500'>Financial Health</div>
              </div>

              <div className='text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg'>
                <div className='text-sm text-gray-600 dark:text-gray-400'>
                  FIRE Readiness
                </div>
                <div
                  className={`text-3xl font-bold ${getScoreColor(analysis.fireReadiness)}`}
                >
                  {formatScore(analysis.fireReadiness)}
                </div>
                <div className='text-xs text-gray-500'>Retirement Progress</div>
              </div>

              <div className='text-center p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg'>
                <div className='text-sm text-gray-600 dark:text-gray-400'>
                  Risk Level
                </div>
                <div
                  className={`text-2xl font-bold ${
                    analysis.riskLevel === 'low'
                      ? 'text-green-600'
                      : analysis.riskLevel === 'medium'
                        ? 'text-yellow-600'
                        : 'text-red-600'
                  }`}
                >
                  {analysis.riskLevel.toUpperCase()}
                </div>
                <div className='text-xs text-gray-500'>Financial Risk</div>
              </div>

              <div className='text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg'>
                <div className='text-sm text-gray-600 dark:text-gray-400'>
                  Optimization
                </div>
                <div
                  className={`text-3xl font-bold ${getScoreColor(100 - analysis.optimizationPotential)}`}
                >
                  {formatScore(analysis.optimizationPotential)}
                </div>
                <div className='text-xs text-gray-500'>
                  Improvement Potential
                </div>
              </div>
            </div>
          </div>
        )
      )}

      {/* Personalized Plan */}
      {analysis && (
        <div
          className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
        >
          <h3 className='text-lg font-semibold mb-4'>
            🎯 Your Personalized FIRE Plan
          </h3>

          <div className='space-y-3'>
            {analysis.personalizedPlan.map((step, index) => (
              <div key={index} className='flex items-start gap-3'>
                <div className='flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold'>
                  {index + 1}
                </div>
                <p className='text-sm'>{step}</p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* AI Insights */}
      {analysis && (
        <div
          className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
        >
          <div className='flex items-center justify-between mb-4'>
            <h3 className='text-lg font-semibold'>
              💡 AI Insights & Recommendations
            </h3>
            <select
              value={selectedCategory}
              onChange={e => setSelectedCategory(e.target.value)}
              className={`px-3 py-2 rounded-md border text-sm ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
            >
              {analysisCategories.map(category => (
                <option key={category.value} value={category.value}>
                  {category.label}
                </option>
              ))}
            </select>
          </div>

          <div className='space-y-4'>
            {filteredInsights.map(insight => (
              <div
                key={insight.id}
                className={`p-4 rounded-lg border-l-4 ${getPriorityColor(insight.priority)}`}
              >
                <div className='flex items-start justify-between mb-2'>
                  <div className='flex items-center gap-2'>
                    <span className='text-lg'>
                      {getCategoryIcon(insight.category)}
                    </span>
                    <h4 className='font-semibold'>{insight.title}</h4>
                    {insight.swissSpecific && (
                      <span className='px-2 py-1 bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300 rounded-full text-xs font-medium'>
                        🇨🇭 Swiss
                      </span>
                    )}
                  </div>
                  <div className='text-right'>
                    <div
                      className={`text-sm font-medium ${
                        insight.priority === 'high'
                          ? 'text-red-600'
                          : insight.priority === 'medium'
                            ? 'text-yellow-600'
                            : 'text-blue-600'
                      }`}
                    >
                      {insight.priority.toUpperCase()}
                    </div>
                    <div className='text-xs text-gray-500'>
                      {insight.confidence}% confidence
                    </div>
                  </div>
                </div>

                <p className='text-sm text-gray-600 dark:text-gray-400 mb-3'>
                  {insight.description}
                </p>

                <div className='grid grid-cols-1 md:grid-cols-2 gap-3 text-sm'>
                  <div>
                    <span className='font-medium text-blue-600 dark:text-blue-400'>
                      Recommendation:
                    </span>
                    <p className='text-gray-600 dark:text-gray-400'>
                      {insight.recommendation}
                    </p>
                  </div>
                  <div>
                    <span className='font-medium text-green-600 dark:text-green-400'>
                      Impact:
                    </span>
                    <p className='text-gray-600 dark:text-gray-400'>
                      {insight.impact}
                    </p>
                  </div>
                </div>

                <div className='flex items-center justify-between mt-3 pt-3 border-t border-gray-200 dark:border-gray-600'>
                  <span className='text-xs text-gray-500'>
                    Timeframe: {insight.timeframe}
                  </span>
                  {insight.actionable && (
                    <span className='px-2 py-1 bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 rounded-full text-xs'>
                      Actionable
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* AI Chat Interface */}
      <div
        className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
      >
        <h3 className='text-lg font-semibold mb-4'>
          💬 Ask Your AI Financial Advisor
        </h3>

        {/* Chat Messages */}
        <div className='space-y-3 mb-4 max-h-64 overflow-y-auto'>
          {conversation.length === 0 ? (
            <div className='text-center py-4 text-gray-500'>
              <p>
                Ask me anything about your finances, Swiss tax optimization, or
                FIRE strategy!
              </p>
            </div>
          ) : (
            conversation.map(message => (
              <div
                key={message.id}
                className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                    message.type === 'user'
                      ? 'bg-blue-600 text-white'
                      : darkMode
                        ? 'bg-gray-700 text-gray-200'
                        : 'bg-gray-100 text-gray-800'
                  }`}
                >
                  <p className='text-sm'>{message.content}</p>
                  <p className='text-xs opacity-70 mt-1'>
                    {message.timestamp.toLocaleTimeString()}
                  </p>
                </div>
              </div>
            ))
          )}
        </div>

        {/* Chat Input */}
        <div className='flex gap-2'>
          <input
            type='text'
            value={userQuestion}
            onChange={e => setUserQuestion(e.target.value)}
            onKeyPress={e =>
              e.key === 'Enter' && processUserQuestion(userQuestion)
            }
            placeholder='Ask about investments, taxes, FIRE strategy...'
            className={`flex-1 px-3 py-2 rounded-md border ${
              darkMode
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            }`}
            disabled={isProcessingQuestion}
          />
          <button
            onClick={() => processUserQuestion(userQuestion)}
            disabled={isProcessingQuestion || !userQuestion.trim()}
            className={`px-4 py-2 rounded-md font-medium transition-colors ${
              isProcessingQuestion || !userQuestion.trim()
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700 text-white'
            }`}
          >
            {isProcessingQuestion ? '🤔' : '📤'}
          </button>
        </div>
      </div>

      {/* Refresh Analysis */}
      <div className='flex justify-center'>
        <button
          onClick={generateAIAnalysis}
          disabled={isAnalyzing}
          className={`px-6 py-3 rounded-lg font-medium transition-colors ${
            isAnalyzing
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-purple-600 hover:bg-purple-700 text-white'
          }`}
        >
          {isAnalyzing ? 'Analyzing...' : '🔄 Refresh AI Analysis'}
        </button>
      </div>
    </div>
  );
};

export default AIFinancialAdvisor;
