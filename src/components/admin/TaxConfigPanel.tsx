/**
 * Tax Configuration Panel for Swiss Budget Pro Admin
 * Allows configuration of Swiss federal and cantonal tax rates
 */

import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useAdminConfig } from '../../contexts/AdminConfigContext';

interface TaxConfigPanelProps {
  darkMode?: boolean;
}

export const TaxConfigPanel: React.FC<TaxConfigPanelProps> = ({
  darkMode = true,
}) => {
  const { t } = useTranslation();
  const { config, updateSection } = useAdminConfig();
  const [localConfig, setLocalConfig] = useState(config.tax);
  const [validationErrors, setValidationErrors] = useState<
    Record<string, string>
  >({});
  const [selectedCanton, setSelectedCanton] = useState<string>('ZH');

  // Update local config when global config changes
  useEffect(() => {
    setLocalConfig(config.tax);
  }, [config.tax]);

  const validateTaxRate = (rate: number): string | null => {
    if (rate < 0 || rate > 1) {
      return t(
        'admin.validation.taxRateRange',
        'Tax rate must be between 0% and 100%'
      );
    }
    return null;
  };

  const validateIncome = (income: number): string | null => {
    if (income < 0 || income > 10000000) {
      return t(
        'admin.validation.incomeRange',
        'Income must be between 0 and 10,000,000'
      );
    }
    return null;
  };

  const handleFieldChange = (
    section: string,
    field: string,
    value: string | number
  ) => {
    const numericValue = typeof value === 'string' ? parseFloat(value) : value;

    // Validate based on field type
    let error: string | null = null;
    if (field.includes('rate') || field.includes('Tax')) {
      error = validateTaxRate(numericValue);
    } else if (field.includes('Income') || field.includes('Threshold')) {
      error = validateIncome(numericValue);
    }

    setValidationErrors(prev => ({
      ...prev,
      [`${section}.${field}`]: error || '',
    }));

    let updatedConfig = { ...localConfig };

    if (section === 'federalRates') {
      // Handle federal tax brackets
      const bracketIndex = parseInt(field);
      const [subField, subValue] = value.toString().split(':');
      updatedConfig.federalRates[bracketIndex] = {
        ...updatedConfig.federalRates[bracketIndex],
        [subField]: parseFloat(subValue),
      };
    } else if (section === 'cantonalRates') {
      // Handle cantonal rates
      const [canton, rateField] = field.split('.');
      updatedConfig.cantonalRates[canton] = {
        ...updatedConfig.cantonalRates[canton],
        [rateField]: numericValue,
      };
    } else if (section === 'socialInsurance') {
      // Handle social insurance rates
      const [category, insuranceField] = field.split('.');
      updatedConfig.socialInsurance[
        category as keyof typeof updatedConfig.socialInsurance
      ] = {
        ...updatedConfig.socialInsurance[
          category as keyof typeof updatedConfig.socialInsurance
        ],
        [insuranceField]: numericValue,
      };
    } else if (section === 'pillar3a') {
      // Handle Pillar 3a settings
      updatedConfig.pillar3a = {
        ...updatedConfig.pillar3a,
        [field]: numericValue,
      };
    } else {
      // Handle direct field updates
      updatedConfig = {
        ...updatedConfig,
        [field]: value,
      };
    }

    updatedConfig.lastUpdated = new Date().toISOString();
    setLocalConfig(updatedConfig);
    updateSection('tax', updatedConfig);
  };

  const addFederalBracket = () => {
    const newBracket = {
      min: 0,
      max: 100000,
      rate: 0.1,
    };

    const updatedConfig = {
      ...localConfig,
      federalRates: [...localConfig.federalRates, newBracket],
      lastUpdated: new Date().toISOString(),
    };

    setLocalConfig(updatedConfig);
    updateSection('tax', updatedConfig);
  };

  const removeFederalBracket = (index: number) => {
    const updatedConfig = {
      ...localConfig,
      federalRates: localConfig.federalRates.filter((_, i) => i !== index),
      lastUpdated: new Date().toISOString(),
    };

    setLocalConfig(updatedConfig);
    updateSection('tax', updatedConfig);
  };

  const inputClasses = `w-full px-3 py-2 rounded-lg border ${
    darkMode
      ? 'bg-gray-700 border-gray-600 text-white focus:border-blue-500'
      : 'bg-white border-gray-300 text-gray-900 focus:border-blue-500'
  } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50`;

  const labelClasses = `block text-sm font-medium mb-2 ${
    darkMode ? 'text-gray-300' : 'text-gray-700'
  }`;

  const cardClasses = `rounded-lg border p-6 ${
    darkMode ? 'bg-gray-800 border-gray-700' : 'bg-gray-50 border-gray-200'
  }`;

  const renderNumberInput = (
    section: string,
    field: string,
    label: string,
    value: number,
    suffix: string = '',
    step: number = 0.001
  ) => {
    const fieldKey = `${section}.${field}`;
    const hasError = validationErrors[fieldKey];

    return (
      <div className='space-y-2'>
        <label className={labelClasses}>{label}</label>
        <div className='relative'>
          <input
            type='number'
            step={step}
            value={value}
            onChange={e => handleFieldChange(section, field, e.target.value)}
            className={`${inputClasses} ${hasError ? 'border-red-500' : ''} ${suffix ? 'pr-12' : ''}`}
          />
          {suffix && (
            <span
              className={`absolute right-3 top-1/2 transform -translate-y-1/2 text-sm ${
                darkMode ? 'text-gray-400' : 'text-gray-500'
              }`}
            >
              {suffix}
            </span>
          )}
        </div>
        {hasError && <p className='text-red-500 text-xs'>{hasError}</p>}
      </div>
    );
  };

  const cantonNames: Record<string, string> = {
    ZH: 'Zurich',
    GE: 'Geneva',
    VD: 'Vaud',
    BE: 'Bern',
    ZG: 'Zug',
    BS: 'Basel-Stadt',
    BL: 'Basel-Landschaft',
    AG: 'Aargau',
  };

  return (
    <div className='space-y-8'>
      {/* Header */}
      <div>
        <h2 className='text-2xl font-bold mb-2'>
          💰 {t('admin.tax.title', 'Tax Configuration')}
        </h2>
        <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          {t(
            'admin.tax.description',
            'Configure Swiss federal and cantonal tax rates, social insurance, and Pillar 3a settings'
          )}
        </p>
      </div>

      {/* Federal Tax Brackets */}
      <div className={cardClasses}>
        <div className='flex items-center justify-between mb-4'>
          <h3 className='text-lg font-semibold'>
            🏛️ {t('admin.tax.federalRates', 'Federal Tax Brackets')}
          </h3>
          <button
            onClick={addFederalBracket}
            className='px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors'
          >
            ➕ {t('admin.tax.addBracket', 'Add Bracket')}
          </button>
        </div>

        <div className='space-y-4'>
          {localConfig.federalRates.map((bracket, index) => (
            <div
              key={index}
              className={`p-4 rounded-lg border ${
                darkMode ? 'border-gray-600' : 'border-gray-300'
              }`}
            >
              <div className='flex items-center justify-between mb-4'>
                <h4 className='font-medium'>
                  {t('admin.tax.bracket', 'Bracket')} {index + 1}
                </h4>
                {localConfig.federalRates.length > 1 && (
                  <button
                    onClick={() => removeFederalBracket(index)}
                    className='px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded text-sm transition-colors'
                  >
                    🗑️ {t('admin.tax.remove', 'Remove')}
                  </button>
                )}
              </div>

              <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
                {renderNumberInput(
                  'federalRates',
                  `${index}.min`,
                  t('admin.tax.minIncome', 'Min Income'),
                  bracket.min,
                  'CHF',
                  1
                )}

                {renderNumberInput(
                  'federalRates',
                  `${index}.max`,
                  t('admin.tax.maxIncome', 'Max Income'),
                  bracket.max,
                  'CHF',
                  1
                )}

                {renderNumberInput(
                  'federalRates',
                  `${index}.rate`,
                  t('admin.tax.taxRate', 'Tax Rate'),
                  bracket.rate * 100,
                  '%',
                  0.1
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Cantonal Tax Rates */}
      <div className={cardClasses}>
        <h3 className='text-lg font-semibold mb-4'>
          🇨🇭 {t('admin.tax.cantonalRates', 'Cantonal Tax Rates')}
        </h3>

        <div className='mb-6'>
          <label className={labelClasses}>
            {t('admin.tax.selectCanton', 'Select Canton')}
          </label>
          <select
            value={selectedCanton}
            onChange={e => setSelectedCanton(e.target.value)}
            className={inputClasses}
          >
            {Object.entries(cantonNames).map(([code, name]) => (
              <option key={code} value={code}>
                {name} ({code})
              </option>
            ))}
          </select>
        </div>

        {selectedCanton && localConfig.cantonalRates[selectedCanton] && (
          <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
            {renderNumberInput(
              'cantonalRates',
              `${selectedCanton}.rate`,
              t('admin.tax.cantonalRate', 'Cantonal Tax Rate'),
              localConfig.cantonalRates[selectedCanton].rate * 100,
              '%',
              0.1
            )}

            {renderNumberInput(
              'cantonalRates',
              `${selectedCanton}.wealthTax`,
              t('admin.tax.wealthTax', 'Wealth Tax Rate'),
              localConfig.cantonalRates[selectedCanton].wealthTax * 100,
              '%',
              0.001
            )}

            {renderNumberInput(
              'cantonalRates',
              `${selectedCanton}.wealthTaxThreshold`,
              t('admin.tax.wealthTaxThreshold', 'Wealth Tax Threshold'),
              localConfig.cantonalRates[selectedCanton].wealthTaxThreshold,
              'CHF',
              1000
            )}
          </div>
        )}
      </div>

      {/* Social Insurance */}
      <div className={cardClasses}>
        <h3 className='text-lg font-semibold mb-4'>
          🛡️ {t('admin.tax.socialInsurance', 'Social Insurance Contributions')}
        </h3>

        <div className='space-y-6'>
          {/* AHV/IV/EO */}
          <div>
            <h4 className='font-medium mb-4'>
              {t('admin.tax.ahv', 'AHV/IV/EO (Old Age & Disability Insurance)')}
            </h4>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              {renderNumberInput(
                'socialInsurance',
                'ahv.rate',
                t('admin.tax.ahvRate', 'AHV Rate'),
                localConfig.socialInsurance.ahv.rate * 100,
                '%',
                0.01
              )}

              {renderNumberInput(
                'socialInsurance',
                'ahv.maxIncome',
                t('admin.tax.ahvMaxIncome', 'Max Income'),
                localConfig.socialInsurance.ahv.maxIncome,
                'CHF',
                100
              )}
            </div>
          </div>

          {/* ALV */}
          <div>
            <h4 className='font-medium mb-4'>
              {t('admin.tax.alv', 'ALV (Unemployment Insurance)')}
            </h4>
            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
              {renderNumberInput(
                'socialInsurance',
                'alv.rate',
                t('admin.tax.alvRate', 'ALV Rate'),
                localConfig.socialInsurance.alv.rate * 100,
                '%',
                0.01
              )}

              {renderNumberInput(
                'socialInsurance',
                'alv.maxIncome',
                t('admin.tax.alvMaxIncome', 'Max Income'),
                localConfig.socialInsurance.alv.maxIncome,
                'CHF',
                100
              )}

              {renderNumberInput(
                'socialInsurance',
                'alv.additionalRate',
                t('admin.tax.alvAdditionalRate', 'Additional Rate'),
                localConfig.socialInsurance.alv.additionalRate * 100,
                '%',
                0.01
              )}

              {renderNumberInput(
                'socialInsurance',
                'alv.additionalThreshold',
                t('admin.tax.alvAdditionalThreshold', 'Additional Threshold'),
                localConfig.socialInsurance.alv.additionalThreshold,
                'CHF',
                100
              )}
            </div>
          </div>

          {/* NBV */}
          <div>
            <h4 className='font-medium mb-4'>
              {t('admin.tax.nbv', 'NBV (Accident Insurance)')}
            </h4>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              {renderNumberInput(
                'socialInsurance',
                'nbv.rate',
                t('admin.tax.nbvRate', 'NBV Rate'),
                localConfig.socialInsurance.nbv.rate * 100,
                '%',
                0.01
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Pillar 3a */}
      <div className={cardClasses}>
        <h3 className='text-lg font-semibold mb-4'>
          🏛️ {t('admin.tax.pillar3a', 'Pillar 3a (Private Retirement Savings)')}
        </h3>

        <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
          {renderNumberInput(
            'pillar3a',
            'maxContribution',
            t('admin.tax.pillar3aMax', 'Max Annual Contribution'),
            localConfig.pillar3a.maxContribution,
            'CHF',
            1
          )}

          {renderNumberInput(
            'pillar3a',
            'maxContributionSelfEmployed',
            t(
              'admin.tax.pillar3aMaxSelfEmployed',
              'Max Self-Employed Contribution'
            ),
            localConfig.pillar3a.maxContributionSelfEmployed,
            'CHF',
            1
          )}

          {renderNumberInput(
            'pillar3a',
            'withdrawalAge',
            t('admin.tax.pillar3aWithdrawalAge', 'Withdrawal Age'),
            localConfig.pillar3a.withdrawalAge,
            'years',
            1
          )}

          {renderNumberInput(
            'pillar3a',
            'earlyWithdrawalPenalty',
            t('admin.tax.pillar3aPenalty', 'Early Withdrawal Penalty'),
            localConfig.pillar3a.earlyWithdrawalPenalty * 100,
            '%',
            0.1
          )}
        </div>
      </div>

      {/* Data Source */}
      <div className={cardClasses}>
        <h3 className='text-lg font-semibold mb-4'>
          📊 {t('admin.tax.dataSource', 'Data Source')}
        </h3>

        <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
          <div>
            <label className={labelClasses}>
              {t('admin.tax.source', 'Source')}
            </label>
            <select
              value={localConfig.source}
              onChange={e => handleFieldChange('', 'source', e.target.value)}
              className={inputClasses}
            >
              <option value='Swiss Federal Tax Administration'>
                Swiss Federal Tax Administration
              </option>
              <option value='Cantonal Tax Authorities'>
                Cantonal Tax Authorities
              </option>
              <option value='Manual Entry'>Manual Entry</option>
            </select>
          </div>

          <div>
            <label className={labelClasses}>
              {t('admin.tax.lastUpdated', 'Last Updated')}
            </label>
            <div
              className={`${inputClasses} bg-gray-100 ${darkMode ? 'bg-gray-600' : ''}`}
            >
              {new Date(localConfig.lastUpdated).toLocaleString()}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
