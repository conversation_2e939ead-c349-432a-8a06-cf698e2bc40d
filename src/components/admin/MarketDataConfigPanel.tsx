/**
 * Market Data Configuration Panel for Swiss Budget Pro Admin
 * Allows configuration of market indices, expected returns, and risk assumptions
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAdminConfig } from '../../contexts/AdminConfigContext';

interface MarketDataConfigPanelProps {
  darkMode?: boolean;
}

export const MarketDataConfigPanel: React.FC<MarketDataConfigPanelProps> = ({
  darkMode = true,
}) => {
  const { t } = useTranslation();
  const { config, updateSection } = useAdminConfig();
  const [localConfig, setLocalConfig] = useState(config.marketData);
  const [validationErrors, setValidationErrors] = useState<
    Record<string, string>
  >({});

  // Update local config when global config changes
  useEffect(() => {
    setLocalConfig(config.marketData);
  }, [config.marketData]);

  const validateField = (field: string, value: number): string | null => {
    switch (field) {
      case 'smiIndex':
      case 'spiIndex':
        if (value < 1000 || value > 50000) {
          return t(
            'admin.validation.indexRange',
            'Index value must be between 1,000 and 50,000',
          );
        }
        break;
      case 'smiChange':
        if (value < -50 || value > 50) {
          return t(
            'admin.validation.changeRange',
            'Change must be between -50% and 50%',
          );
        }
        break;
      case 'bondYield10Y':
      case 'bondYield2Y':
        if (value < -5 || value > 20) {
          return t(
            'admin.validation.yieldRange',
            'Yield must be between -5% and 20%',
          );
        }
        break;
      case 'volatilityIndex':
        if (value < 0 || value > 100) {
          return t(
            'admin.validation.volatilityRange',
            'Volatility must be between 0% and 100%',
          );
        }
        break;
      default:
        if (
          field.includes('expectedReturns') ||
          field.includes('riskPremiums')
        ) {
          if (value < -20 || value > 30) {
            return t(
              'admin.validation.returnRange',
              'Return must be between -20% and 30%',
            );
          }
        }
    }
    return null;
  };

  const handleFieldChange = (field: string, value: string | number) => {
    const numericValue = typeof value === 'string' ? parseFloat(value) : value;

    // Validate numeric fields
    if (typeof numericValue === 'number' && !isNaN(numericValue)) {
      const error = validateField(field, numericValue);
      setValidationErrors(prev => ({
        ...prev,
        [field]: error || '',
      }));
    }

    // Handle nested object updates
    if (field.includes('.')) {
      const [section, subField] = field.split('.');
      const updatedConfig = {
        ...localConfig,
        [section]: {
          ...localConfig[section as keyof typeof localConfig],
          [subField]: numericValue,
        },
        lastUpdated: new Date().toISOString(),
      };
      setLocalConfig(updatedConfig);
      updateSection('marketData', updatedConfig);
    } else {
      const updatedConfig = {
        ...localConfig,
        [field]: value,
        lastUpdated: new Date().toISOString(),
      };
      setLocalConfig(updatedConfig);
      updateSection('marketData', updatedConfig);
    }
  };

  const handleSentimentChange = (
    sentiment: 'bullish' | 'neutral' | 'bearish',
  ) => {
    handleFieldChange('marketSentiment', sentiment);
  };

  const refreshMarketData = async () => {
    // Mock market data refresh - in real implementation, would fetch from APIs
    const mockData = {
      smiIndex: 11800 + (Math.random() - 0.5) * 1000,
      smiChange: (Math.random() - 0.5) * 4,
      spiIndex: 15200 + (Math.random() - 0.5) * 1200,
      spiYearReturn: 5.0 + (Math.random() - 0.5) * 6,
      bondYield10Y: 0.7 + (Math.random() - 0.5) * 0.8,
      bondYield2Y: 0.5 + (Math.random() - 0.5) * 0.6,
      volatilityIndex: 18.0 + (Math.random() - 0.5) * 10,
      lastUpdated: new Date().toISOString(),
    };

    const updatedConfig = {
      ...localConfig,
      ...mockData,
    };

    setLocalConfig(updatedConfig);
    updateSection('marketData', updatedConfig);
  };

  const inputClasses = `w-full px-3 py-2 rounded-lg border ${
    darkMode
      ? 'bg-gray-700 border-gray-600 text-white focus:border-blue-500'
      : 'bg-white border-gray-300 text-gray-900 focus:border-blue-500'
  } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50`;

  const labelClasses = `block text-sm font-medium mb-2 ${
    darkMode ? 'text-gray-300' : 'text-gray-700'
  }`;

  const cardClasses = `rounded-lg border p-6 ${
    darkMode ? 'bg-gray-800 border-gray-700' : 'bg-gray-50 border-gray-200'
  }`;

  const renderNumberInput = (
    field: string,
    label: string,
    suffix: string = '',
    step: number = 0.1,
    description?: string,
  ) => {
    const hasError = validationErrors[field];
    const value = field.includes('.')
      ? localConfig[field.split('.')[0] as keyof typeof localConfig][
          field.split('.')[1] as any
        ]
      : localConfig[field as keyof typeof localConfig];

    return (
      <div className='space-y-2'>
        <label className={labelClasses}>
          {label}
          {description && (
            <span
              className={`block text-xs mt-1 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}
            >
              {description}
            </span>
          )}
        </label>
        <div className='relative'>
          <input
            type='number'
            step={step}
            value={value as number}
            onChange={e => handleFieldChange(field, e.target.value)}
            className={`${inputClasses} ${hasError ? 'border-red-500' : ''} ${suffix ? 'pr-12' : ''}`}
          />
          {suffix && (
            <span
              className={`absolute right-3 top-1/2 transform -translate-y-1/2 text-sm ${
                darkMode ? 'text-gray-400' : 'text-gray-500'
              }`}
            >
              {suffix}
            </span>
          )}
        </div>
        {hasError && <p className='text-red-500 text-xs'>{hasError}</p>}
      </div>
    );
  };

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case 'bullish':
        return 'text-green-500';
      case 'bearish':
        return 'text-red-500';
      default:
        return darkMode ? 'text-gray-400' : 'text-gray-600';
    }
  };

  const getSentimentIcon = (sentiment: string) => {
    switch (sentiment) {
      case 'bullish':
        return '📈';
      case 'bearish':
        return '📉';
      default:
        return '➡️';
    }
  };

  return (
    <div className='space-y-8'>
      {/* Header */}
      <div>
        <h2 className='text-2xl font-bold mb-2'>
          📊 {t('admin.market.title', 'Market Data Configuration')}
        </h2>
        <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          {t(
            'admin.market.description',
            'Configure market indices, expected returns, and risk assumptions for investment calculations',
          )}
        </p>
      </div>

      {/* Data Source and Refresh */}
      <div className={cardClasses}>
        <div className='flex items-center justify-between mb-4'>
          <h3 className='text-lg font-semibold'>
            🔄 {t('admin.market.dataSource', 'Market Data Source')}
          </h3>
          <button
            onClick={refreshMarketData}
            className='px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors'
          >
            🔄 {t('admin.market.refresh', 'Refresh Data')}
          </button>
        </div>

        <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
          <div>
            <label className={labelClasses}>
              {t('admin.market.source', 'Data Source')}
            </label>
            <select
              value={localConfig.source}
              onChange={e => handleFieldChange('source', e.target.value)}
              className={inputClasses}
            >
              <option value='SIX Swiss Exchange'>SIX Swiss Exchange</option>
              <option value='Bloomberg'>Bloomberg</option>
              <option value='Reuters'>Reuters</option>
              <option value='Manual Entry'>Manual Entry</option>
            </select>
          </div>

          <div>
            <label className={labelClasses}>
              {t('admin.market.lastUpdated', 'Last Updated')}
            </label>
            <div
              className={`${inputClasses} bg-gray-100 ${darkMode ? 'bg-gray-600' : ''}`}
            >
              {new Date(localConfig.lastUpdated).toLocaleString()}
            </div>
          </div>
        </div>
      </div>

      {/* Swiss Market Indices */}
      <div className={cardClasses}>
        <h3 className='text-lg font-semibold mb-4'>
          🇨🇭 {t('admin.market.swissIndices', 'Swiss Market Indices')}
        </h3>

        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
          {renderNumberInput(
            'smiIndex',
            t('admin.market.smiIndex', 'SMI Index'),
            'pts',
            1,
            t('admin.market.smiIndexDesc', 'Swiss Market Index current level'),
          )}

          {renderNumberInput(
            'smiChange',
            t('admin.market.smiChange', 'SMI Change'),
            '%',
            0.01,
            t('admin.market.smiChangeDesc', 'Daily change in SMI'),
          )}

          {renderNumberInput(
            'spiIndex',
            t('admin.market.spiIndex', 'SPI Index'),
            'pts',
            1,
            t('admin.market.spiIndexDesc', 'Swiss Performance Index level'),
          )}

          {renderNumberInput(
            'spiYearReturn',
            t('admin.market.spiYearReturn', 'SPI YTD Return'),
            '%',
            0.1,
            t('admin.market.spiYearReturnDesc', 'Year-to-date SPI performance'),
          )}
        </div>
      </div>

      {/* Bond Market */}
      <div className={cardClasses}>
        <h3 className='text-lg font-semibold mb-4'>
          🏛️ {t('admin.market.bondMarket', 'Swiss Bond Market')}
        </h3>

        <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
          {renderNumberInput(
            'bondYield10Y',
            t('admin.market.bondYield10Y', '10-Year Bond Yield'),
            '%',
            0.01,
            t(
              'admin.market.bondYield10YDesc',
              'Swiss 10-year government bond yield',
            ),
          )}

          {renderNumberInput(
            'bondYield2Y',
            t('admin.market.bondYield2Y', '2-Year Bond Yield'),
            '%',
            0.01,
            t(
              'admin.market.bondYield2YDesc',
              'Swiss 2-year government bond yield',
            ),
          )}

          {renderNumberInput(
            'volatilityIndex',
            t('admin.market.volatilityIndex', 'Volatility Index'),
            '%',
            0.1,
            t('admin.market.volatilityIndexDesc', 'Market volatility indicator'),
          )}
        </div>
      </div>

      {/* Market Sentiment */}
      <div className={cardClasses}>
        <h3 className='text-lg font-semibold mb-4'>
          🎭 {t('admin.market.sentiment', 'Market Sentiment')}
        </h3>

        <div className='space-y-4'>
          <div>
            <label className={labelClasses}>
              {t('admin.market.currentSentiment', 'Current Market Sentiment')}
            </label>
            <div className='flex space-x-4'>
              {(['bullish', 'neutral', 'bearish'] as const).map(sentiment => (
                <button
                  key={sentiment}
                  onClick={() => handleSentimentChange(sentiment)}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${
                    localConfig.marketSentiment === sentiment
                      ? 'border-blue-500 bg-blue-500/20'
                      : darkMode
                        ? 'border-gray-600 hover:border-gray-500'
                        : 'border-gray-300 hover:border-gray-400'
                  }`}
                >
                  <span>{getSentimentIcon(sentiment)}</span>
                  <span
                    className={`capitalize ${getSentimentColor(sentiment)}`}
                  >
                    {sentiment}
                  </span>
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Expected Returns */}
      <div className={cardClasses}>
        <h3 className='text-lg font-semibold mb-4'>
          📈 {t('admin.market.expectedReturns', 'Expected Annual Returns')}
        </h3>

        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
          {renderNumberInput(
            'expectedReturns.stocks',
            t('admin.market.stockReturns', 'Stocks'),
            '%',
            0.1,
            t(
              'admin.market.stockReturnsDesc',
              'Expected annual stock market returns',
            ),
          )}

          {renderNumberInput(
            'expectedReturns.bonds',
            t('admin.market.bondReturns', 'Bonds'),
            '%',
            0.1,
            t('admin.market.bondReturnsDesc', 'Expected annual bond returns'),
          )}

          {renderNumberInput(
            'expectedReturns.cash',
            t('admin.market.cashReturns', 'Cash'),
            '%',
            0.01,
            t(
              'admin.market.cashReturnsDesc',
              'Expected cash and money market returns',
            ),
          )}

          {renderNumberInput(
            'expectedReturns.realEstate',
            t('admin.market.realEstateReturns', 'Real Estate'),
            '%',
            0.1,
            t(
              'admin.market.realEstateReturnsDesc',
              'Expected real estate returns',
            ),
          )}

          {renderNumberInput(
            'expectedReturns.commodities',
            t('admin.market.commodityReturns', 'Commodities'),
            '%',
            0.1,
            t('admin.market.commodityReturnsDesc', 'Expected commodity returns'),
          )}
        </div>
      </div>

      {/* Risk Premiums */}
      <div className={cardClasses}>
        <h3 className='text-lg font-semibold mb-4'>
          ⚠️ {t('admin.market.riskPremiums', 'Risk Premiums')}
        </h3>

        <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
          {renderNumberInput(
            'riskPremiums.equity',
            t('admin.market.equityPremium', 'Equity Risk Premium'),
            '%',
            0.1,
            t(
              'admin.market.equityPremiumDesc',
              'Additional return for equity risk',
            ),
          )}

          {renderNumberInput(
            'riskPremiums.credit',
            t('admin.market.creditPremium', 'Credit Risk Premium'),
            '%',
            0.1,
            t(
              'admin.market.creditPremiumDesc',
              'Additional return for credit risk',
            ),
          )}

          {renderNumberInput(
            'riskPremiums.liquidity',
            t('admin.market.liquidityPremium', 'Liquidity Risk Premium'),
            '%',
            0.1,
            t(
              'admin.market.liquidityPremiumDesc',
              'Additional return for liquidity risk',
            ),
          )}
        </div>
      </div>

      {/* Impact Summary */}
      <div className={cardClasses}>
        <h3 className='text-lg font-semibold mb-4'>
          💡 {t('admin.market.impact', 'Impact on Calculations')}
        </h3>

        <div className='space-y-4'>
          <div
            className={`p-4 rounded-lg ${darkMode ? 'bg-blue-900/30' : 'bg-blue-50'}`}
          >
            <h4 className='font-medium mb-2'>
              📊 {t('admin.market.portfolioImpact', 'Portfolio Projections')}
            </h4>
            <p
              className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
            >
              {t(
                'admin.market.portfolioImpactDesc',
                'Expected returns are used to project portfolio growth and calculate FIRE timelines based on asset allocation.',
              )}
            </p>
          </div>

          <div
            className={`p-4 rounded-lg ${darkMode ? 'bg-green-900/30' : 'bg-green-50'}`}
          >
            <h4 className='font-medium mb-2'>
              ⚖️ {t('admin.market.riskImpact', 'Risk Assessment')}
            </h4>
            <p
              className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
            >
              {t(
                'admin.market.riskImpactDesc',
                'Risk premiums and volatility data help assess portfolio risk and suggest appropriate asset allocations.',
              )}
            </p>
          </div>

          <div
            className={`p-4 rounded-lg ${darkMode ? 'bg-yellow-900/30' : 'bg-yellow-50'}`}
          >
            <h4 className='font-medium mb-2'>
              🎯{' '}
              {t(
                'admin.market.optimizationImpact',
                'Optimization Recommendations',
              )}
            </h4>
            <p
              className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
            >
              {t(
                'admin.market.optimizationImpactDesc',
                'Market sentiment and current conditions influence investment recommendations and rebalancing suggestions.',
              )}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
