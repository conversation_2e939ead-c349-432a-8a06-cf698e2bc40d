/**
 * Configuration Audit Log Panel for Swiss Budget Pro Admin
 * Displays audit trail of configuration changes
 */

import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  useAdminConfig,
  AuditLogEntry,
} from '../../contexts/AdminConfigContext';

interface ConfigAuditLogProps {
  darkMode?: boolean;
}

export const ConfigAuditLog: React.FC<ConfigAuditLogProps> = ({
  darkMode = true,
}) => {
  const { t } = useTranslation();
  const { getAuditLog } = useAdminConfig();
  const [filterAction, setFilterAction] = useState<string>('all');
  const [filterSection, setFilterSection] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [entriesPerPage] = useState<number>(20);

  const auditLog = getAuditLog();

  // Filter and search audit log entries
  const filteredEntries = useMemo(() => {
    return auditLog.filter(entry => {
      const matchesAction =
        filterAction === 'all' || entry.action === filterAction;
      const matchesSection =
        filterSection === 'all' || entry.section === filterSection;
      const matchesSearch =
        searchTerm === '' ||
        entry.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        entry.user.toLowerCase().includes(searchTerm.toLowerCase()) ||
        entry.section.toLowerCase().includes(searchTerm.toLowerCase());

      return matchesAction && matchesSection && matchesSearch;
    });
  }, [auditLog, filterAction, filterSection, searchTerm]);

  // Pagination
  const totalPages = Math.ceil(filteredEntries.length / entriesPerPage);
  const startIndex = (currentPage - 1) * entriesPerPage;
  const paginatedEntries = filteredEntries.slice(
    startIndex,
    startIndex + entriesPerPage
  );

  // Get unique actions and sections for filters
  const uniqueActions = [...new Set(auditLog.map(entry => entry.action))];
  const uniqueSections = [...new Set(auditLog.map(entry => entry.section))];

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'create':
        return '➕';
      case 'update':
        return '✏️';
      case 'delete':
        return '🗑️';
      case 'reset':
        return '🔄';
      case 'import':
        return '📥';
      case 'export':
        return '📤';
      default:
        return '📝';
    }
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case 'create':
        return 'text-green-500';
      case 'update':
        return 'text-blue-500';
      case 'delete':
        return 'text-red-500';
      case 'reset':
        return 'text-yellow-500';
      case 'import':
        return 'text-purple-500';
      case 'export':
        return 'text-indigo-500';
      default:
        return darkMode ? 'text-gray-400' : 'text-gray-600';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString(),
    };
  };

  const exportAuditLog = () => {
    const csvContent = [
      ['Timestamp', 'Action', 'Section', 'Field', 'User', 'Description'].join(
        ','
      ),
      ...filteredEntries.map(entry =>
        [
          entry.timestamp,
          entry.action,
          entry.section,
          entry.field || '',
          entry.user,
          `"${entry.description.replace(/"/g, '""')}"`, // Escape quotes in CSV
        ].join(',')
      ),
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `audit-log-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    URL.revokeObjectURL(url);
  };

  const inputClasses = `px-3 py-2 rounded-lg border ${
    darkMode
      ? 'bg-gray-700 border-gray-600 text-white focus:border-blue-500'
      : 'bg-white border-gray-300 text-gray-900 focus:border-blue-500'
  } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50`;

  const cardClasses = `rounded-lg border ${
    darkMode ? 'bg-gray-800 border-gray-700' : 'bg-gray-50 border-gray-200'
  }`;

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div>
        <h2 className='text-2xl font-bold mb-2'>
          📋 {t('admin.audit.title', 'Configuration Audit Log')}
        </h2>
        <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          {t(
            'admin.audit.description',
            'Track all configuration changes and system modifications'
          )}
        </p>
      </div>

      {/* Filters and Search */}
      <div className={`${cardClasses} p-6`}>
        <div className='flex flex-wrap items-center justify-between gap-4 mb-4'>
          <h3 className='text-lg font-semibold'>
            🔍 {t('admin.audit.filters', 'Filters & Search')}
          </h3>

          <button
            onClick={exportAuditLog}
            className='px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors'
          >
            📤 {t('admin.audit.export', 'Export Log')}
          </button>
        </div>

        <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
          <div>
            <label
              className={`block text-sm font-medium mb-2 ${
                darkMode ? 'text-gray-300' : 'text-gray-700'
              }`}
            >
              {t('admin.audit.filterAction', 'Filter by Action')}
            </label>
            <select
              value={filterAction}
              onChange={e => setFilterAction(e.target.value)}
              className={inputClasses}
            >
              <option value='all'>
                {t('admin.audit.allActions', 'All Actions')}
              </option>
              {uniqueActions.map(action => (
                <option key={action} value={action}>
                  {getActionIcon(action)}{' '}
                  {action.charAt(0).toUpperCase() + action.slice(1)}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label
              className={`block text-sm font-medium mb-2 ${
                darkMode ? 'text-gray-300' : 'text-gray-700'
              }`}
            >
              {t('admin.audit.filterSection', 'Filter by Section')}
            </label>
            <select
              value={filterSection}
              onChange={e => setFilterSection(e.target.value)}
              className={inputClasses}
            >
              <option value='all'>
                {t('admin.audit.allSections', 'All Sections')}
              </option>
              {uniqueSections.map(section => (
                <option key={section} value={section}>
                  {section.charAt(0).toUpperCase() + section.slice(1)}
                </option>
              ))}
            </select>
          </div>

          <div className='md:col-span-2'>
            <label
              className={`block text-sm font-medium mb-2 ${
                darkMode ? 'text-gray-300' : 'text-gray-700'
              }`}
            >
              {t('admin.audit.search', 'Search')}
            </label>
            <input
              type='text'
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              placeholder={t(
                'admin.audit.searchPlaceholder',
                'Search descriptions, users, sections...'
              )}
              className={inputClasses}
            />
          </div>
        </div>

        <div className='mt-4 flex items-center justify-between text-sm'>
          <span className={darkMode ? 'text-gray-400' : 'text-gray-600'}>
            {t('admin.audit.showing', 'Showing')} {filteredEntries.length}{' '}
            {t('admin.audit.of', 'of')} {auditLog.length}{' '}
            {t('admin.audit.entries', 'entries')}
          </span>

          {totalPages > 1 && (
            <div className='flex items-center space-x-2'>
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className={`px-3 py-1 rounded ${
                  currentPage === 1
                    ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700 text-white'
                }`}
              >
                ←
              </button>

              <span className={darkMode ? 'text-gray-400' : 'text-gray-600'}>
                {currentPage} / {totalPages}
              </span>

              <button
                onClick={() =>
                  setCurrentPage(Math.min(totalPages, currentPage + 1))
                }
                disabled={currentPage === totalPages}
                className={`px-3 py-1 rounded ${
                  currentPage === totalPages
                    ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700 text-white'
                }`}
              >
                →
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Audit Log Entries */}
      <div className={cardClasses}>
        {paginatedEntries.length === 0 ? (
          <div className='p-8 text-center'>
            <div className='text-4xl mb-4'>📝</div>
            <h3 className='text-lg font-medium mb-2'>
              {t('admin.audit.noEntries', 'No Audit Entries')}
            </h3>
            <p className={darkMode ? 'text-gray-400' : 'text-gray-600'}>
              {searchTerm || filterAction !== 'all' || filterSection !== 'all'
                ? t(
                    'admin.audit.noMatchingEntries',
                    'No entries match your current filters.'
                  )
                : t(
                    'admin.audit.noEntriesYet',
                    'No configuration changes have been logged yet.'
                  )}
            </p>
          </div>
        ) : (
          <div className='divide-y divide-gray-200 dark:divide-gray-700'>
            {paginatedEntries.map(entry => {
              const { date, time } = formatTimestamp(entry.timestamp);

              return (
                <div key={entry.id} className='p-6'>
                  <div className='flex items-start justify-between'>
                    <div className='flex items-start space-x-4 flex-1'>
                      <div
                        className={`text-xl ${getActionColor(entry.action)}`}
                      >
                        {getActionIcon(entry.action)}
                      </div>

                      <div className='flex-1 min-w-0'>
                        <div className='flex items-center space-x-2 mb-1'>
                          <span className='font-medium'>
                            {entry.action.charAt(0).toUpperCase() +
                              entry.action.slice(1)}
                          </span>
                          <span
                            className={`text-sm px-2 py-1 rounded ${
                              darkMode
                                ? 'bg-gray-700 text-gray-300'
                                : 'bg-gray-100 text-gray-700'
                            }`}
                          >
                            {entry.section}
                          </span>
                          {entry.field && (
                            <span
                              className={`text-sm px-2 py-1 rounded ${
                                darkMode
                                  ? 'bg-blue-900/30 text-blue-400'
                                  : 'bg-blue-100 text-blue-700'
                              }`}
                            >
                              {entry.field}
                            </span>
                          )}
                        </div>

                        <p
                          className={`text-sm mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
                        >
                          {entry.description}
                        </p>

                        <div
                          className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}
                        >
                          👤 {entry.user} • 📅 {date} • 🕐 {time}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Show old/new values if available */}
                  {(entry.oldValue !== undefined ||
                    entry.newValue !== undefined) && (
                    <div
                      className={`mt-4 p-3 rounded-lg ${
                        darkMode ? 'bg-gray-700/50' : 'bg-gray-50'
                      }`}
                    >
                      <div className='grid grid-cols-1 md:grid-cols-2 gap-4 text-xs'>
                        {entry.oldValue !== undefined && (
                          <div>
                            <span
                              className={`font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
                            >
                              {t('admin.audit.oldValue', 'Old Value')}:
                            </span>
                            <pre
                              className={`mt-1 p-2 rounded ${
                                darkMode
                                  ? 'bg-gray-800 text-gray-300'
                                  : 'bg-white text-gray-700'
                              } overflow-x-auto`}
                            >
                              {typeof entry.oldValue === 'object'
                                ? JSON.stringify(entry.oldValue, null, 2)
                                : String(entry.oldValue)}
                            </pre>
                          </div>
                        )}

                        {entry.newValue !== undefined && (
                          <div>
                            <span
                              className={`font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
                            >
                              {t('admin.audit.newValue', 'New Value')}:
                            </span>
                            <pre
                              className={`mt-1 p-2 rounded ${
                                darkMode
                                  ? 'bg-gray-800 text-gray-300'
                                  : 'bg-white text-gray-700'
                              } overflow-x-auto`}
                            >
                              {typeof entry.newValue === 'object'
                                ? JSON.stringify(entry.newValue, null, 2)
                                : String(entry.newValue)}
                            </pre>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Summary Statistics */}
      <div className={`${cardClasses} p-6`}>
        <h3 className='text-lg font-semibold mb-4'>
          📊 {t('admin.audit.statistics', 'Audit Statistics')}
        </h3>

        <div className='grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4'>
          {uniqueActions.map(action => {
            const count = auditLog.filter(
              entry => entry.action === action
            ).length;
            return (
              <div
                key={action}
                className={`p-3 rounded-lg text-center ${
                  darkMode ? 'bg-gray-700' : 'bg-gray-100'
                }`}
              >
                <div className={`text-lg ${getActionColor(action)}`}>
                  {getActionIcon(action)}
                </div>
                <div className='text-lg font-bold'>{count}</div>
                <div
                  className={`text-xs capitalize ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
                >
                  {action}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};
