/**
 * Admin Authentication Guard for Swiss Budget Pro
 * Protects admin routes with authentication and authorization
 */

import React, { useState, useEffect, ReactNode } from 'react';
import { useTranslation } from 'react-i18next';

interface AdminAuthGuardProps {
  children: ReactNode;
  darkMode?: boolean;
}

interface AdminUser {
  username: string;
  role: 'admin' | 'viewer';
  permissions: string[];
  lastLogin: string;
}

export const AdminAuthGuard: React.FC<AdminAuthGuardProps> = ({
  children,
  darkMode = true,
}) => {
  const { t } = useTranslation();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [loginForm, setLoginForm] = useState({ username: '', password: '' });
  const [loginError, setLoginError] = useState('');
  const [currentUser, setCurrentUser] = useState<AdminUser | null>(null);

  // Check authentication status on mount
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = () => {
    try {
      const authData = localStorage.getItem('swissBudgetPro_adminAuth');
      const sessionData = sessionStorage.getItem('swissBudgetPro_adminSession');

      if (authData && sessionData) {
        const auth = JSON.parse(authData);
        const session = JSON.parse(sessionData);

        // Check if session is still valid (24 hours)
        const sessionAge = Date.now() - session.timestamp;
        const maxSessionAge = 24 * 60 * 60 * 1000; // 24 hours

        if (sessionAge < maxSessionAge) {
          setCurrentUser(auth.user);
          setIsAuthenticated(true);
        } else {
          // Session expired
          logout();
        }
      }
    } catch (error) {
      console.error('Failed to check auth status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (username: string, password: string) => {
    setLoginError('');

    try {
      // In a real implementation, this would call an authentication API
      // For demo purposes, we'll use hardcoded credentials
      const validCredentials = [
        { username: 'admin', password: 'swiss2024!', role: 'admin' as const },
        { username: 'viewer', password: 'view2024!', role: 'viewer' as const },
        { username: 'demo', password: 'demo', role: 'admin' as const },
      ];

      const credential = validCredentials.find(
        cred => cred.username === username && cred.password === password,
      );

      if (!credential) {
        throw new Error('Invalid credentials');
      }

      const user: AdminUser = {
        username: credential.username,
        role: credential.role,
        permissions:
          credential.role === 'admin'
            ? ['read', 'write', 'delete', 'export', 'import', 'reset']
            : ['read'],
        lastLogin: new Date().toISOString(),
      };

      // Store authentication data
      const authData = {
        user,
        timestamp: Date.now(),
      };

      const sessionData = {
        sessionId: Date.now().toString(),
        timestamp: Date.now(),
      };

      localStorage.setItem(
        'swissBudgetPro_adminAuth',
        JSON.stringify(authData),
      );
      sessionStorage.setItem(
        'swissBudgetPro_adminSession',
        JSON.stringify(sessionData),
      );

      setCurrentUser(user);
      setIsAuthenticated(true);

      console.log('✅ Admin login successful:', user.username);
    } catch (error) {
      setLoginError(error instanceof Error ? error.message : 'Login failed');
      console.error('❌ Admin login failed:', error);
    }
  };

  const logout = () => {
    localStorage.removeItem('swissBudgetPro_adminAuth');
    sessionStorage.removeItem('swissBudgetPro_adminSession');
    setCurrentUser(null);
    setIsAuthenticated(false);
    setLoginForm({ username: '', password: '' });
    setLoginError('');
    console.log('✅ Admin logout successful');
  };

  const handleLoginSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await login(loginForm.username, loginForm.password);
  };

  const handleInputChange = (field: 'username' | 'password', value: string) => {
    setLoginForm(prev => ({ ...prev, [field]: value }));
    setLoginError(''); // Clear error when user types
  };

  // Loading state
  if (isLoading) {
    return (
      <div
        className={`min-h-screen flex items-center justify-center ${
          darkMode ? 'bg-gray-900 text-white' : 'bg-gray-100 text-gray-900'
        }`}
      >
        <div className='text-center'>
          <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4'></div>
          <p>{t('admin.auth.loading', 'Loading...')}</p>
        </div>
      </div>
    );
  }

  // Login form
  if (!isAuthenticated) {
    const cardClasses = `rounded-lg border p-8 max-w-md w-full mx-4 ${
      darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
    }`;

    const inputClasses = `w-full px-3 py-2 rounded-lg border ${
      darkMode
        ? 'bg-gray-700 border-gray-600 text-white focus:border-blue-500'
        : 'bg-white border-gray-300 text-gray-900 focus:border-blue-500'
    } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50`;

    return (
      <div
        className={`min-h-screen flex items-center justify-center ${
          darkMode ? 'bg-gray-900 text-white' : 'bg-gray-100 text-gray-900'
        }`}
      >
        <div className={cardClasses}>
          <div className='text-center mb-8'>
            <div className='text-4xl mb-4'>🔐</div>
            <h1 className='text-2xl font-bold mb-2'>
              {t('admin.auth.title', 'Admin Access')}
            </h1>
            <p
              className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
            >
              {t('admin.auth.subtitle', 'Swiss Budget Pro Administration')}
            </p>
          </div>

          <form onSubmit={handleLoginSubmit} className='space-y-6'>
            <div>
              <label
                className={`block text-sm font-medium mb-2 ${
                  darkMode ? 'text-gray-300' : 'text-gray-700'
                }`}
              >
                {t('admin.auth.username', 'Username')}
              </label>
              <input
                type='text'
                value={loginForm.username}
                onChange={e => handleInputChange('username', e.target.value)}
                className={inputClasses}
                placeholder={t(
                  'admin.auth.usernamePlaceholder',
                  'Enter username',
                )}
                required
                autoComplete='username'
              />
            </div>

            <div>
              <label
                className={`block text-sm font-medium mb-2 ${
                  darkMode ? 'text-gray-300' : 'text-gray-700'
                }`}
              >
                {t('admin.auth.password', 'Password')}
              </label>
              <input
                type='password'
                value={loginForm.password}
                onChange={e => handleInputChange('password', e.target.value)}
                className={inputClasses}
                placeholder={t(
                  'admin.auth.passwordPlaceholder',
                  'Enter password',
                )}
                required
                autoComplete='current-password'
              />
            </div>

            {loginError && (
              <div className='p-3 rounded-lg bg-red-500/20 border border-red-500/50'>
                <p className='text-red-500 text-sm'>❌ {loginError}</p>
              </div>
            )}

            <button
              type='submit'
              className='w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50'
            >
              {t('admin.auth.login', 'Login')}
            </button>
          </form>

          {/* Demo Credentials */}
          <div
            className={`mt-8 p-4 rounded-lg ${
              darkMode ? 'bg-gray-700/50' : 'bg-gray-50'
            }`}
          >
            <h3 className='text-sm font-medium mb-2'>
              {t('admin.auth.demoCredentials', 'Demo Credentials')}
            </h3>
            <div
              className={`text-xs space-y-1 ${
                darkMode ? 'text-gray-400' : 'text-gray-600'
              }`}
            >
              <div>
                👤 <strong>Admin:</strong> admin / swiss2024!
              </div>
              <div>
                👁️ <strong>Viewer:</strong> viewer / view2024!
              </div>
              <div>
                🎯 <strong>Demo:</strong> demo / demo
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Authenticated - render children with user context
  return (
    <div className='relative'>
      {/* User Info Bar */}
      <div
        className={`fixed top-0 right-0 z-50 m-4 p-3 rounded-lg shadow-lg ${
          darkMode
            ? 'bg-gray-800 border border-gray-700'
            : 'bg-white border border-gray-200'
        }`}
      >
        <div className='flex items-center space-x-3'>
          <div className='text-sm'>
            <div className='font-medium'>👤 {currentUser?.username}</div>
            <div
              className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
            >
              {currentUser?.role === 'admin' ? '🔧 Administrator' : '👁️ Viewer'}
            </div>
          </div>
          <button
            onClick={logout}
            className='px-3 py-1 text-xs bg-red-600 hover:bg-red-700 text-white rounded transition-colors'
            title={t('admin.auth.logout', 'Logout')}
          >
            🚪
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className='pt-16'>{children}</div>
    </div>
  );
};
