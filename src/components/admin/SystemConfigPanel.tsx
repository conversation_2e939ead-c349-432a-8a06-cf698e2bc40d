/**
 * System Configuration Panel for Swiss Budget Pro Admin
 * Allows configuration of system defaults, features, and performance settings
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAdminConfig } from '../../contexts/AdminConfigContext';

interface SystemConfigPanelProps {
  darkMode?: boolean;
}

export const SystemConfigPanel: React.FC<SystemConfigPanelProps> = ({
  darkMode = true,
}) => {
  const { t } = useTranslation();
  const { config, updateSection } = useAdminConfig();
  const [localConfig, setLocalConfig] = useState(config.system);
  const [validationErrors, setValidationErrors] = useState<
    Record<string, string>
  >({});

  // Update local config when global config changes
  useEffect(() => {
    setLocalConfig(config.system);
  }, [config.system]);

  const validatePercentage = (value: number): string | null => {
    if (value < 0 || value > 1) {
      return t(
        'admin.validation.percentageRange',
        'Value must be between 0% and 100%',
      );
    }
    return null;
  };

  const validateTimeout = (value: number): string | null => {
    if (value < 1000 || value > 300000) {
      return t(
        'admin.validation.timeoutRange',
        'Timeout must be between 1 and 300 seconds',
      );
    }
    return null;
  };

  const validateInterval = (value: number): string | null => {
    if (value < 5000 || value > 300000) {
      return t(
        'admin.validation.intervalRange',
        'Interval must be between 5 and 300 seconds',
      );
    }
    return null;
  };

  const handleFieldChange = (
    section: string,
    field: string,
    value: string | number | boolean,
  ) => {
    const numericValue = typeof value === 'string' ? parseFloat(value) : value;

    // Validate numeric fields
    if (typeof numericValue === 'number' && !isNaN(numericValue)) {
      let error: string | null = null;

      if (field.includes('Rate') && !field.includes('Return')) {
        error = validatePercentage(numericValue);
      } else if (field.includes('Timeout')) {
        error = validateTimeout(numericValue);
      } else if (field.includes('Interval')) {
        error = validateInterval(numericValue);
      }

      setValidationErrors(prev => ({
        ...prev,
        [`${section}.${field}`]: error || '',
      }));
    }

    const updatedConfig = { ...localConfig };

    // Handle nested object updates
    if (section === 'calculationDefaults') {
      updatedConfig.calculationDefaults = {
        ...updatedConfig.calculationDefaults,
        [field]: typeof value === 'number' ? value : numericValue,
      };
    } else if (section === 'userInterface') {
      if (field === 'numberFormat') {
        const [formatField, formatValue] = value.toString().split(':');
        updatedConfig.userInterface.numberFormat = {
          ...updatedConfig.userInterface.numberFormat,
          [formatField]: formatValue,
        };
      } else {
        updatedConfig.userInterface = {
          ...updatedConfig.userInterface,
          [field]: value,
        };
      }
    } else if (section === 'features') {
      updatedConfig.features = {
        ...updatedConfig.features,
        [field]: value,
      };
    } else if (section === 'performance') {
      updatedConfig.performance = {
        ...updatedConfig.performance,
        [field]: typeof value === 'number' ? value : numericValue,
      };
    }

    updatedConfig.lastUpdated = new Date().toISOString();
    setLocalConfig(updatedConfig);
    updateSection('system', updatedConfig);
  };

  const inputClasses = `w-full px-3 py-2 rounded-lg border ${
    darkMode
      ? 'bg-gray-700 border-gray-600 text-white focus:border-blue-500'
      : 'bg-white border-gray-300 text-gray-900 focus:border-blue-500'
  } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50`;

  const labelClasses = `block text-sm font-medium mb-2 ${
    darkMode ? 'text-gray-300' : 'text-gray-700'
  }`;

  const cardClasses = `rounded-lg border p-6 ${
    darkMode ? 'bg-gray-800 border-gray-700' : 'bg-gray-50 border-gray-200'
  }`;

  const renderNumberInput = (
    section: string,
    field: string,
    label: string,
    value: number,
    suffix: string = '',
    step: number = 0.001,
    description?: string,
  ) => {
    const fieldKey = `${section}.${field}`;
    const hasError = validationErrors[fieldKey];

    return (
      <div className='space-y-2'>
        <label className={labelClasses}>
          {label}
          {description && (
            <span
              className={`block text-xs mt-1 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}
            >
              {description}
            </span>
          )}
        </label>
        <div className='relative'>
          <input
            type='number'
            step={step}
            value={value}
            onChange={e => handleFieldChange(section, field, e.target.value)}
            className={`${inputClasses} ${hasError ? 'border-red-500' : ''} ${suffix ? 'pr-12' : ''}`}
          />
          {suffix && (
            <span
              className={`absolute right-3 top-1/2 transform -translate-y-1/2 text-sm ${
                darkMode ? 'text-gray-400' : 'text-gray-500'
              }`}
            >
              {suffix}
            </span>
          )}
        </div>
        {hasError && <p className='text-red-500 text-xs'>{hasError}</p>}
      </div>
    );
  };

  const renderToggle = (
    section: string,
    field: string,
    label: string,
    description?: string,
  ) => {
    const value =
      localConfig[section as keyof typeof localConfig][field as any];

    return (
      <div className='flex items-start space-x-3'>
        <input
          type='checkbox'
          id={`${section}-${field}`}
          checked={value}
          onChange={e => handleFieldChange(section, field, e.target.checked)}
          className='w-4 h-4 text-blue-600 rounded focus:ring-blue-500 mt-1'
        />
        <div className='flex-1'>
          <label
            htmlFor={`${section}-${field}`}
            className={`${labelClasses.replace('block', 'inline')} cursor-pointer`}
          >
            {label}
          </label>
          {description && (
            <p
              className={`text-xs mt-1 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}
            >
              {description}
            </p>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className='space-y-8'>
      {/* Header */}
      <div>
        <h2 className='text-2xl font-bold mb-2'>
          ⚙️ {t('admin.system.title', 'System Configuration')}
        </h2>
        <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          {t(
            'admin.system.description',
            'Configure system defaults, features, and performance settings',
          )}
        </p>
      </div>

      {/* Calculation Defaults */}
      <div className={cardClasses}>
        <h3 className='text-lg font-semibold mb-4'>
          🧮 {t('admin.system.calculationDefaults', 'Calculation Defaults')}
        </h3>

        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
          {renderNumberInput(
            'calculationDefaults',
            'withdrawalRate',
            t('admin.system.withdrawalRate', 'Standard Withdrawal Rate'),
            localConfig.calculationDefaults.withdrawalRate * 100,
            '%',
            0.1,
            t(
              'admin.system.withdrawalRateDesc',
              'Default FIRE withdrawal rate (4% rule)',
            ),
          )}

          {renderNumberInput(
            'calculationDefaults',
            'safeWithdrawalRate',
            t('admin.system.safeWithdrawalRate', 'Safe Withdrawal Rate'),
            localConfig.calculationDefaults.safeWithdrawalRate * 100,
            '%',
            0.1,
            t(
              'admin.system.safeWithdrawalRateDesc',
              'Conservative withdrawal rate for safety',
            ),
          )}

          {renderNumberInput(
            'calculationDefaults',
            'conservativeWithdrawalRate',
            t(
              'admin.system.conservativeWithdrawalRate',
              'Conservative Withdrawal Rate',
            ),
            localConfig.calculationDefaults.conservativeWithdrawalRate * 100,
            '%',
            0.1,
            t(
              'admin.system.conservativeWithdrawalRateDesc',
              'Very conservative withdrawal rate',
            ),
          )}

          {renderNumberInput(
            'calculationDefaults',
            'investmentReturn',
            t('admin.system.investmentReturn', 'Expected Investment Return'),
            localConfig.calculationDefaults.investmentReturn * 100,
            '%',
            0.1,
            t(
              'admin.system.investmentReturnDesc',
              'Default expected annual return',
            ),
          )}

          {renderNumberInput(
            'calculationDefaults',
            'bondReturn',
            t('admin.system.bondReturn', 'Expected Bond Return'),
            localConfig.calculationDefaults.bondReturn * 100,
            '%',
            0.1,
            t('admin.system.bondReturnDesc', 'Default expected bond return'),
          )}

          {renderNumberInput(
            'calculationDefaults',
            'cashReturn',
            t('admin.system.cashReturn', 'Expected Cash Return'),
            localConfig.calculationDefaults.cashReturn * 100,
            '%',
            0.01,
            t('admin.system.cashReturnDesc', 'Default expected cash return'),
          )}
        </div>
      </div>

      {/* User Interface Settings */}
      <div className={cardClasses}>
        <h3 className='text-lg font-semibold mb-4'>
          🎨 {t('admin.system.userInterface', 'User Interface Settings')}
        </h3>

        <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
          <div>
            <label className={labelClasses}>
              {t('admin.system.defaultLanguage', 'Default Language')}
            </label>
            <select
              value={localConfig.userInterface.defaultLanguage}
              onChange={e =>
                handleFieldChange(
                  'userInterface',
                  'defaultLanguage',
                  e.target.value,
                )
              }
              className={inputClasses}
            >
              <option value='de-CH'>Deutsch (Schweiz)</option>
              <option value='fr-CH'>Français (Suisse)</option>
              <option value='it-CH'>Italiano (Svizzera)</option>
              <option value='en-CH'>English (Switzerland)</option>
            </select>
          </div>

          <div>
            <label className={labelClasses}>
              {t('admin.system.defaultCurrency', 'Default Currency')}
            </label>
            <select
              value={localConfig.userInterface.defaultCurrency}
              onChange={e =>
                handleFieldChange(
                  'userInterface',
                  'defaultCurrency',
                  e.target.value,
                )
              }
              className={inputClasses}
            >
              <option value='CHF'>Swiss Franc (CHF)</option>
              <option value='EUR'>Euro (EUR)</option>
              <option value='USD'>US Dollar (USD)</option>
            </select>
          </div>

          <div>
            <label className={labelClasses}>
              {t('admin.system.decimalSeparator', 'Decimal Separator')}
            </label>
            <select
              value={localConfig.userInterface.numberFormat.decimalSeparator}
              onChange={e =>
                handleFieldChange(
                  'userInterface',
                  'numberFormat',
                  `decimalSeparator:${e.target.value}`,
                )
              }
              className={inputClasses}
            >
              <option value='.'>Period (.)</option>
              <option value=','>Comma (,)</option>
            </select>
          </div>

          <div>
            <label className={labelClasses}>
              {t('admin.system.thousandsSeparator', 'Thousands Separator')}
            </label>
            <select
              value={localConfig.userInterface.numberFormat.thousandsSeparator}
              onChange={e =>
                handleFieldChange(
                  'userInterface',
                  'numberFormat',
                  `thousandsSeparator:${e.target.value}`,
                )
              }
              className={inputClasses}
            >
              <option value="'">Apostrophe (')</option>
              <option value=','>Comma (,)</option>
              <option value=' '>Space ( )</option>
              <option value=''>None</option>
            </select>
          </div>

          <div>
            <label className={labelClasses}>
              {t('admin.system.dateFormat', 'Date Format')}
            </label>
            <select
              value={localConfig.userInterface.dateFormat}
              onChange={e =>
                handleFieldChange('userInterface', 'dateFormat', e.target.value)
              }
              className={inputClasses}
            >
              <option value='dd.mm.yyyy'>DD.MM.YYYY (Swiss)</option>
              <option value='dd/mm/yyyy'>DD/MM/YYYY</option>
              <option value='mm/dd/yyyy'>MM/DD/YYYY</option>
              <option value='yyyy-mm-dd'>YYYY-MM-DD (ISO)</option>
            </select>
          </div>
        </div>
      </div>

      {/* Feature Toggles */}
      <div className={cardClasses}>
        <h3 className='text-lg font-semibold mb-4'>
          🎛️ {t('admin.system.features', 'Feature Toggles')}
        </h3>

        <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
          {renderToggle(
            'features',
            'enableAdvancedCalculations',
            t(
              'admin.system.enableAdvancedCalculations',
              'Advanced Calculations',
            ),
            t(
              'admin.system.enableAdvancedCalculationsDesc',
              'Enable complex financial modeling and projections',
            ),
          )}

          {renderToggle(
            'features',
            'enableTaxOptimization',
            t('admin.system.enableTaxOptimization', 'Tax Optimization'),
            t(
              'admin.system.enableTaxOptimizationDesc',
              'Enable tax optimization recommendations and calculations',
            ),
          )}

          {renderToggle(
            'features',
            'enableHealthcareOptimization',
            t(
              'admin.system.enableHealthcareOptimization',
              'Healthcare Optimization',
            ),
            t(
              'admin.system.enableHealthcareOptimizationDesc',
              'Enable healthcare deductible optimization features',
            ),
          )}

          {renderToggle(
            'features',
            'enableCantonComparison',
            t('admin.system.enableCantonComparison', 'Canton Comparison'),
            t(
              'admin.system.enableCantonComparisonDesc',
              'Enable comparison tools across Swiss cantons',
            ),
          )}

          {renderToggle(
            'features',
            'enableExportFeatures',
            t('admin.system.enableExportFeatures', 'Export Features'),
            t(
              'admin.system.enableExportFeaturesDesc',
              'Enable PDF export and data download features',
            ),
          )}
        </div>
      </div>

      {/* Performance Settings */}
      <div className={cardClasses}>
        <h3 className='text-lg font-semibold mb-4'>
          ⚡ {t('admin.system.performance', 'Performance Settings')}
        </h3>

        <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
          {renderNumberInput(
            'performance',
            'autoSaveInterval',
            t('admin.system.autoSaveInterval', 'Auto-Save Interval'),
            localConfig.performance.autoSaveInterval / 1000,
            'sec',
            1,
            t(
              'admin.system.autoSaveIntervalDesc',
              'How often to automatically save user data',
            ),
          )}

          {renderNumberInput(
            'performance',
            'calculationTimeout',
            t('admin.system.calculationTimeout', 'Calculation Timeout'),
            localConfig.performance.calculationTimeout / 1000,
            'sec',
            1,
            t(
              'admin.system.calculationTimeoutDesc',
              'Maximum time for calculations to complete',
            ),
          )}

          {renderNumberInput(
            'performance',
            'maxHistoryEntries',
            t('admin.system.maxHistoryEntries', 'Max History Entries'),
            localConfig.performance.maxHistoryEntries,
            'entries',
            1,
            t(
              'admin.system.maxHistoryEntriesDesc',
              'Maximum number of calculation history entries to keep',
            ),
          )}
        </div>
      </div>

      {/* System Information */}
      <div className={cardClasses}>
        <h3 className='text-lg font-semibold mb-4'>
          ℹ️ {t('admin.system.information', 'System Information')}
        </h3>

        <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
          <div>
            <label className={labelClasses}>
              {t('admin.system.lastUpdated', 'Last Updated')}
            </label>
            <div
              className={`${inputClasses} bg-gray-100 ${darkMode ? 'bg-gray-600' : ''}`}
            >
              {new Date(localConfig.lastUpdated).toLocaleString()}
            </div>
          </div>

          <div>
            <label className={labelClasses}>
              {t('admin.system.configVersion', 'Configuration Version')}
            </label>
            <div
              className={`${inputClasses} bg-gray-100 ${darkMode ? 'bg-gray-600' : ''}`}
            >
              {config.version}
            </div>
          </div>
        </div>
      </div>

      {/* Impact Summary */}
      <div className={cardClasses}>
        <h3 className='text-lg font-semibold mb-4'>
          💡 {t('admin.system.impact', 'Configuration Impact')}
        </h3>

        <div className='space-y-4'>
          <div
            className={`p-4 rounded-lg ${darkMode ? 'bg-blue-900/30' : 'bg-blue-50'}`}
          >
            <h4 className='font-medium mb-2'>
              🧮 {t('admin.system.calculationImpact', 'Calculation Defaults')}
            </h4>
            <p
              className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
            >
              {t(
                'admin.system.calculationImpactDesc',
                'Default values are used as starting points for user calculations and can be overridden by individual users.',
              )}
            </p>
          </div>

          <div
            className={`p-4 rounded-lg ${darkMode ? 'bg-green-900/30' : 'bg-green-50'}`}
          >
            <h4 className='font-medium mb-2'>
              🎛️ {t('admin.system.featureImpact', 'Feature Toggles')}
            </h4>
            <p
              className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
            >
              {t(
                'admin.system.featureImpactDesc',
                'Disabling features will hide them from all users. Enable only features that are fully tested and ready for production.',
              )}
            </p>
          </div>

          <div
            className={`p-4 rounded-lg ${darkMode ? 'bg-yellow-900/30' : 'bg-yellow-50'}`}
          >
            <h4 className='font-medium mb-2'>
              ⚡ {t('admin.system.performanceImpact', 'Performance Settings')}
            </h4>
            <p
              className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
            >
              {t(
                'admin.system.performanceImpactDesc',
                'Performance settings affect user experience. Lower timeouts improve responsiveness but may cause failures for complex calculations.',
              )}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
