import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocalStorage } from '../hooks/useLocalStorage';
import type {
  AdditionalIncome,
  IncomeCategory,
  IncomeFrequency,
} from '../types/income';

interface AdditionalIncomeManagerProps {
  darkMode: boolean;
  onIncomeChange: (incomes: AdditionalIncome[]) => void;
}

const SWISS_INCOME_CATEGORIES: IncomeCategory[] = [
  {
    id: 'freelance',
    name: 'Freelance Work',
    icon: '💼',
    swissSpecific: false,
    description: 'Independent contractor and freelance income',
    taxImplications: 'Self-employment tax applies',
  },
  {
    id: 'rental',
    name: 'Rental Income',
    icon: '🏠',
    swissSpecific: false,
    description: 'Income from rental properties',
    taxImplications: 'Taxable as income, deductions available',
  },
  {
    id: 'dividends',
    name: 'Dividends',
    icon: '📈',
    swissSpecific: false,
    description: 'Dividend income from investments',
    taxImplications: 'Swiss withholding tax may apply',
  },
  {
    id: 'side_business',
    name: 'Side Business',
    icon: '🚀',
    swissSpecific: false,
    description: 'Income from side business or startup',
    taxImplications: 'Business income tax rules apply',
  },
  {
    id: 'consulting',
    name: 'Consulting',
    icon: '🎯',
    swissSpecific: false,
    description: 'Professional consulting services',
    taxImplications: 'Professional income, VAT may apply',
  },
  {
    id: 'royalties',
    name: 'Royalties',
    icon: '🎨',
    swissSpecific: false,
    description: 'Intellectual property royalties',
    taxImplications: 'Taxable as income',
  },
  {
    id: 'ahv_pension',
    name: 'AHV Pension',
    icon: '🇨🇭',
    swissSpecific: true,
    description: 'Swiss state pension (AHV/AVS)',
    taxImplications: 'Taxable pension income',
  },
  {
    id: 'pillar_2',
    name: 'Pillar 2 Pension',
    icon: '🏛️',
    swissSpecific: true,
    description: 'Occupational pension fund',
    taxImplications: 'Reduced tax rate for lump sum',
  },
  {
    id: 'other',
    name: 'Other Income',
    icon: '💰',
    swissSpecific: false,
    description: 'Other miscellaneous income sources',
    taxImplications: 'Varies by income type',
  },
];

const DEFAULT_INCOMES: AdditionalIncome[] = [
  {
    id: '1',
    name: 'Freelance Web Development',
    category: 'freelance',
    amount: 2000,
    frequency: 'monthly',
    isActive: true,
    startDate: '2024-01-01',
    taxRate: 0.25,
    notes: 'Part-time freelance work',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

const AdditionalIncomeManager: React.FC<AdditionalIncomeManagerProps> = ({
  darkMode,
  onIncomeChange,
}) => {
  const { t } = useTranslation();
  const [incomes, setIncomes] = useLocalStorage<AdditionalIncome[]>(
    'additional_incomes',
    DEFAULT_INCOMES,
  );
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingIncome, setEditingIncome] = useState<AdditionalIncome | null>(
    null,
  );
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [sortBy, setSortBy] = useState<
    'name' | 'amount' | 'category' | 'frequency'
  >('amount');

  useEffect(() => {
    onIncomeChange(incomes);
  }, [incomes, onIncomeChange]);

  const calculateMonthlyAmount = (income: AdditionalIncome): number => {
    if (!income.isActive) return 0;

    switch (income.frequency) {
      case 'weekly':
        return income.amount * 4.33; // Average weeks per month
      case 'monthly':
        return income.amount;
      case 'quarterly':
        return income.amount / 3;
      case 'annually':
        return income.amount / 12;
      case 'one_time':
        return 0; // One-time income doesn't contribute to monthly
      default:
        return 0;
    }
  };

  const totalMonthlyIncome = incomes
    .filter(income => income.isActive)
    .reduce((sum, income) => sum + calculateMonthlyAmount(income), 0);

  const totalAnnualIncome = incomes
    .filter(income => income.isActive)
    .reduce((sum, income) => {
      switch (income.frequency) {
        case 'weekly':
          return sum + income.amount * 52;
        case 'monthly':
          return sum + income.amount * 12;
        case 'quarterly':
          return sum + income.amount * 4;
        case 'annually':
          return sum + income.amount;
        case 'one_time':
          return sum + income.amount;
        default:
          return sum;
      }
    }, 0);

  const filteredIncomes = incomes
    .filter(
      income => filterCategory === 'all' || income.category === filterCategory,
    )
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'amount':
          return calculateMonthlyAmount(b) - calculateMonthlyAmount(a);
        case 'category':
          return a.category.localeCompare(b.category);
        case 'frequency':
          return a.frequency.localeCompare(b.frequency);
        default:
          return 0;
      }
    });

  const handleAddIncome = (
    income: Omit<AdditionalIncome, 'id' | 'createdAt' | 'updatedAt'>,
  ) => {
    const newIncome: AdditionalIncome = {
      ...income,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    setIncomes([...incomes, newIncome]);
    setShowAddForm(false);
  };

  const handleEditIncome = (income: AdditionalIncome) => {
    const updatedIncomes = incomes.map(inc =>
      inc.id === income.id
        ? { ...income, updatedAt: new Date().toISOString() }
        : inc,
    );
    setIncomes(updatedIncomes);
    setEditingIncome(null);
  };

  const handleDeleteIncome = (id: string) => {
    setIncomes(incomes.filter(inc => inc.id !== id));
  };

  const handleToggleActive = (id: string) => {
    const updatedIncomes = incomes.map(inc =>
      inc.id === id
        ? {
            ...inc,
            isActive: !inc.isActive,
            updatedAt: new Date().toISOString(),
          }
        : inc,
    );
    setIncomes(updatedIncomes);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('de-CH', {
      style: 'currency',
      currency: 'CHF',
    }).format(amount);
  };

  const getCategoryInfo = (categoryId: string) => {
    return (
      SWISS_INCOME_CATEGORIES.find(cat => cat.id === categoryId) ||
      SWISS_INCOME_CATEGORIES[8]
    ); // Default to 'other'
  };

  const getFrequencyLabel = (frequency: IncomeFrequency) => {
    const labels = {
      weekly: 'Weekly',
      monthly: 'Monthly',
      quarterly: 'Quarterly',
      annually: 'Annually',
      one_time: 'One-time',
    };
    return labels[frequency];
  };

  return (
    <div className='space-y-6'>
      {/* Income Overview */}
      <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
        <div
          className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
        >
          <h3 className='text-sm font-medium text-gray-500 mb-1'>
            Monthly Additional Income
          </h3>
          <p className='text-2xl font-bold text-green-600'>
            {formatCurrency(totalMonthlyIncome)}
          </p>
        </div>
        <div
          className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
        >
          <h3 className='text-sm font-medium text-gray-500 mb-1'>
            Annual Additional Income
          </h3>
          <p className='text-2xl font-bold text-blue-600'>
            {formatCurrency(totalAnnualIncome)}
          </p>
        </div>
        <div
          className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
        >
          <h3 className='text-sm font-medium text-gray-500 mb-1'>
            Active Income Sources
          </h3>
          <p className='text-2xl font-bold text-purple-600'>
            {incomes.filter(inc => inc.isActive).length}
          </p>
        </div>
      </div>

      {/* Controls */}
      <div className='flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between'>
        <div className='flex flex-col sm:flex-row gap-4'>
          <select
            value={filterCategory}
            onChange={e => setFilterCategory(e.target.value)}
            className={`px-3 py-2 rounded-md border ${
              darkMode
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            }`}
          >
            <option value='all'>All Categories</option>
            {SWISS_INCOME_CATEGORIES.map(category => (
              <option key={category.id} value={category.id}>
                {category.icon} {category.name}
              </option>
            ))}
          </select>

          <select
            value={sortBy}
            onChange={e => setSortBy(e.target.value as any)}
            className={`px-3 py-2 rounded-md border ${
              darkMode
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            }`}
          >
            <option value='amount'>Sort by Amount</option>
            <option value='name'>Sort by Name</option>
            <option value='category'>Sort by Category</option>
            <option value='frequency'>Sort by Frequency</option>
          </select>
        </div>

        <button
          onClick={() => setShowAddForm(true)}
          className='px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors'
        >
          + Add Income Source
        </button>
      </div>

      {/* Income List */}
      <div className='space-y-3'>
        {filteredIncomes.map(income => {
          const category = getCategoryInfo(income.category);
          const monthlyAmount = calculateMonthlyAmount(income);

          return (
            <div
              key={income.id}
              className={`p-4 rounded-lg border ${
                darkMode
                  ? 'bg-gray-800 border-gray-700'
                  : 'bg-white border-gray-200'
              } ${!income.isActive ? 'opacity-50' : ''}`}
            >
              <div className='flex items-center justify-between'>
                <div className='flex-1'>
                  <div className='flex items-center gap-3'>
                    <span className='text-2xl'>{category.icon}</span>
                    <div>
                      <h4 className='font-semibold'>{income.name}</h4>
                      <p className='text-sm text-gray-500'>
                        {category.name} • {getFrequencyLabel(income.frequency)}
                        {income.taxRate &&
                          ` • ${(income.taxRate * 100).toFixed(0)}% tax rate`}
                      </p>
                      {category.swissSpecific && (
                        <span className='inline-block px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full mt-1'>
                          🇨🇭 Swiss Specific
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                <div className='text-right'>
                  <p className='font-semibold'>
                    {formatCurrency(income.amount)}
                  </p>
                  <p className='text-sm text-gray-500'>
                    {getFrequencyLabel(income.frequency)}
                  </p>
                  {monthlyAmount > 0 && (
                    <p className='text-xs text-green-600'>
                      {formatCurrency(monthlyAmount)}/month
                    </p>
                  )}
                </div>

                <div className='flex items-center gap-2 ml-4'>
                  <button
                    onClick={() => handleToggleActive(income.id)}
                    className={`p-2 rounded ${
                      income.isActive
                        ? 'text-green-600 hover:bg-green-100'
                        : 'text-gray-400 hover:bg-gray-100'
                    }`}
                    title={income.isActive ? 'Deactivate' : 'Activate'}
                  >
                    {income.isActive ? '✓' : '○'}
                  </button>
                  <button
                    onClick={() => setEditingIncome(income)}
                    className='p-2 text-blue-600 hover:bg-blue-100 rounded'
                    title='Edit'
                  >
                    ✏️
                  </button>
                  <button
                    onClick={() => handleDeleteIncome(income.id)}
                    className='p-2 text-red-600 hover:bg-red-100 rounded'
                    title='Delete'
                  >
                    🗑️
                  </button>
                </div>
              </div>

              {income.notes && (
                <div className='mt-3 pt-3 border-t border-gray-200'>
                  <p className='text-sm text-gray-600'>{income.notes}</p>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {filteredIncomes.length === 0 && (
        <div
          className={`text-center py-8 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}
        >
          <p>
            No additional income sources found. Add your first income source to
            get started!
          </p>
        </div>
      )}

      {/* Add/Edit Income Modal */}
      {(showAddForm || editingIncome) && (
        <IncomeForm
          darkMode={darkMode}
          income={editingIncome}
          onSave={editingIncome ? handleEditIncome : handleAddIncome}
          onCancel={() => {
            setShowAddForm(false);
            setEditingIncome(null);
          }}
        />
      )}
    </div>
  );
};

// Income Form Component
interface IncomeFormProps {
  darkMode: boolean;
  income?: AdditionalIncome | null;
  onSave: (income: any) => void;
  onCancel: () => void;
}

const IncomeForm: React.FC<IncomeFormProps> = ({
  darkMode,
  income,
  onSave,
  onCancel,
}) => {
  const [formData, setFormData] = useState({
    name: income?.name || '',
    category: income?.category || 'freelance',
    amount: income?.amount || 0,
    frequency: income?.frequency || 'monthly',
    startDate: income?.startDate || '',
    endDate: income?.endDate || '',
    taxRate: income?.taxRate || 0,
    notes: income?.notes || '',
    isActive: income?.isActive ?? true,
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (income) {
      onSave({ ...income, ...formData });
    } else {
      onSave(formData);
    }
  };

  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50'>
      <div
        className={`max-w-2xl w-full max-h-[90vh] overflow-y-auto rounded-lg ${
          darkMode ? 'bg-gray-800' : 'bg-white'
        }`}
      >
        <div className='p-6'>
          <h2 className='text-xl font-bold mb-6'>
            {income ? 'Edit Income Source' : 'Add New Income Source'}
          </h2>

          <form onSubmit={handleSubmit} className='space-y-4'>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <div>
                <label className='block text-sm font-medium mb-1'>
                  Income Source Name *
                </label>
                <input
                  type='text'
                  value={formData.name}
                  onChange={e => handleChange('name', e.target.value)}
                  className={`w-full px-3 py-2 rounded-md border ${
                    darkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                  required
                />
              </div>

              <div>
                <label className='block text-sm font-medium mb-1'>
                  Category *
                </label>
                <select
                  value={formData.category}
                  onChange={e => handleChange('category', e.target.value)}
                  className={`w-full px-3 py-2 rounded-md border ${
                    darkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                  required
                >
                  {SWISS_INCOME_CATEGORIES.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.icon} {category.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className='block text-sm font-medium mb-1'>
                  Amount (CHF) *
                </label>
                <input
                  type='number'
                  step='0.01'
                  value={formData.amount}
                  onChange={e =>
                    handleChange('amount', parseFloat(e.target.value) || 0)
                  }
                  className={`w-full px-3 py-2 rounded-md border ${
                    darkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                  required
                />
              </div>

              <div>
                <label className='block text-sm font-medium mb-1'>
                  Frequency *
                </label>
                <select
                  value={formData.frequency}
                  onChange={e => handleChange('frequency', e.target.value)}
                  className={`w-full px-3 py-2 rounded-md border ${
                    darkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                  required
                >
                  <option value='weekly'>Weekly</option>
                  <option value='monthly'>Monthly</option>
                  <option value='quarterly'>Quarterly</option>
                  <option value='annually'>Annually</option>
                  <option value='one_time'>One-time</option>
                </select>
              </div>

              <div>
                <label className='block text-sm font-medium mb-1'>
                  Start Date
                </label>
                <input
                  type='date'
                  value={formData.startDate}
                  onChange={e => handleChange('startDate', e.target.value)}
                  className={`w-full px-3 py-2 rounded-md border ${
                    darkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                />
              </div>

              <div>
                <label className='block text-sm font-medium mb-1'>
                  End Date (Optional)
                </label>
                <input
                  type='date'
                  value={formData.endDate}
                  onChange={e => handleChange('endDate', e.target.value)}
                  className={`w-full px-3 py-2 rounded-md border ${
                    darkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                />
              </div>

              <div>
                <label className='block text-sm font-medium mb-1'>
                  Tax Rate (%)
                </label>
                <input
                  type='number'
                  step='0.01'
                  min='0'
                  max='100'
                  value={(formData.taxRate * 100).toFixed(2)}
                  onChange={e =>
                    handleChange(
                      'taxRate',
                      parseFloat(e.target.value) / 100 || 0,
                    )
                  }
                  className={`w-full px-3 py-2 rounded-md border ${
                    darkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                  placeholder='25.0'
                />
              </div>
            </div>

            <div>
              <label className='block text-sm font-medium mb-1'>Notes</label>
              <textarea
                value={formData.notes}
                onChange={e => handleChange('notes', e.target.value)}
                rows={3}
                className={`w-full px-3 py-2 rounded-md border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
                placeholder='Additional notes about this income source...'
              />
            </div>

            <div className='flex items-center gap-2'>
              <input
                type='checkbox'
                id='isActive'
                checked={formData.isActive}
                onChange={e => handleChange('isActive', e.target.checked)}
                className='rounded'
              />
              <label htmlFor='isActive' className='text-sm'>
                Active income source (include in calculations)
              </label>
            </div>

            <div className='flex gap-3 pt-4'>
              <button
                type='submit'
                className='flex-1 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors'
              >
                {income ? 'Update Income Source' : 'Add Income Source'}
              </button>
              <button
                type='button'
                onClick={onCancel}
                className={`flex-1 px-4 py-2 rounded-md border transition-colors ${
                  darkMode
                    ? 'border-gray-600 text-gray-300 hover:bg-gray-700'
                    : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default AdditionalIncomeManager;
