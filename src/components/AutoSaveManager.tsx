import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocalStorage } from '../hooks/useLocalStorage';

interface Snapshot {
  id: string;
  name: string;
  timestamp: string;
  data: {
    expenses: any[];
    savingsGoals: any[];
    investments: any[];
    additionalIncomes: any[];
    userInputs: any;
  };
  description?: string;
  isAutoSave: boolean;
}

interface AutoSaveManagerProps {
  darkMode: boolean;
  currentData: {
    expenses: any[];
    savingsGoals: any[];
    investments: any[];
    additionalIncomes: any[];
    userInputs: any;
  };
  onLoadSnapshot: (data: any) => void;
}

const AutoSaveManager: React.FC<AutoSaveManagerProps> = ({
  darkMode,
  currentData,
  onLoadSnapshot,
}) => {
  const { t } = useTranslation();
  const [snapshots, setSnapshots] = useLocalStorage<Snapshot[]>(
    'fire_snapshots',
    []
  );
  const [showManager, setShowManager] = useState(false);
  const [autoSaveEnabled, setAutoSaveEnabled] = useLocalStorage(
    'auto_save_enabled',
    true
  );
  const [autoSaveInterval, setAutoSaveInterval] = useLocalStorage(
    'auto_save_interval',
    5
  ); // minutes
  const [lastAutoSave, setLastAutoSave] = useLocalStorage('last_auto_save', '');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newSnapshotName, setNewSnapshotName] = useState('');
  const [newSnapshotDescription, setNewSnapshotDescription] = useState('');

  // Auto-save functionality
  useEffect(() => {
    if (!autoSaveEnabled) return;

    const interval = setInterval(
      () => {
        createAutoSave();
      },
      autoSaveInterval * 60 * 1000
    ); // Convert minutes to milliseconds

    return () => clearInterval(interval);
  }, [autoSaveEnabled, autoSaveInterval, currentData]);

  const createAutoSave = () => {
    const now = new Date();
    const autoSave: Snapshot = {
      id: `auto_${now.getTime()}`,
      name: `Auto-save ${now.toLocaleString('de-CH')}`,
      timestamp: now.toISOString(),
      data: { ...currentData },
      description: 'Automatic backup',
      isAutoSave: true,
    };

    // Keep only the last 10 auto-saves
    const updatedSnapshots = [
      autoSave,
      ...snapshots.filter(s => !s.isAutoSave).slice(0, 20),
      ...snapshots.filter(s => s.isAutoSave).slice(0, 9),
    ];
    setSnapshots(updatedSnapshots);
    setLastAutoSave(now.toISOString());
  };

  const createManualSnapshot = () => {
    if (!newSnapshotName.trim()) return;

    const now = new Date();
    const snapshot: Snapshot = {
      id: `manual_${now.getTime()}`,
      name: newSnapshotName.trim(),
      timestamp: now.toISOString(),
      data: { ...currentData },
      description: newSnapshotDescription.trim() || undefined,
      isAutoSave: false,
    };

    setSnapshots([snapshot, ...snapshots]);
    setNewSnapshotName('');
    setNewSnapshotDescription('');
    setShowCreateForm(false);
  };

  const loadSnapshot = (snapshot: Snapshot) => {
    if (
      window.confirm(
        `Load snapshot "${snapshot.name}"? This will replace your current data.`
      )
    ) {
      onLoadSnapshot(snapshot.data);
      setShowManager(false);
    }
  };

  const deleteSnapshot = (id: string) => {
    if (window.confirm('Delete this snapshot? This action cannot be undone.')) {
      setSnapshots(snapshots.filter(s => s.id !== id));
    }
  };

  const exportSnapshot = (snapshot: Snapshot) => {
    const dataStr = JSON.stringify(snapshot, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `fire_snapshot_${snapshot.name.replace(/[^a-z0-9]/gi, '_')}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const importSnapshot = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = e => {
      try {
        const snapshot = JSON.parse(e.target?.result as string) as Snapshot;
        snapshot.id = `imported_${Date.now()}`;
        snapshot.name = `${snapshot.name} (Imported)`;
        setSnapshots([snapshot, ...snapshots]);
      } catch (error) {
        alert('Invalid snapshot file format');
      }
    };
    reader.readAsText(file);
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('de-CH');
  };

  const getTimeSinceLastAutoSave = () => {
    if (!lastAutoSave) return 'Never';
    const now = new Date();
    const last = new Date(lastAutoSave);
    const diffMinutes = Math.floor(
      (now.getTime() - last.getTime()) / (1000 * 60)
    );

    if (diffMinutes < 1) return 'Just now';
    if (diffMinutes < 60) return `${diffMinutes} minutes ago`;
    const diffHours = Math.floor(diffMinutes / 60);
    if (diffHours < 24) return `${diffHours} hours ago`;
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays} days ago`;
  };

  return (
    <>
      {/* Auto-save Status Indicator */}
      <div
        className={`fixed bottom-4 right-4 p-3 rounded-lg shadow-lg ${
          darkMode
            ? 'bg-gray-800 border border-gray-700'
            : 'bg-white border border-gray-200'
        }`}
      >
        <div className='flex items-center gap-2 text-sm'>
          <div
            className={`w-2 h-2 rounded-full ${autoSaveEnabled ? 'bg-green-500' : 'bg-gray-400'}`}
          />
          <span>Auto-save: {autoSaveEnabled ? 'On' : 'Off'}</span>
          {autoSaveEnabled && (
            <span className='text-gray-500'>
              (Last: {getTimeSinceLastAutoSave()})
            </span>
          )}
        </div>
        <button
          onClick={() => setShowManager(true)}
          className='mt-2 text-xs text-blue-600 hover:text-blue-800'
        >
          Manage Snapshots
        </button>
      </div>

      {/* Snapshot Manager Modal */}
      {showManager && (
        <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50'>
          <div
            className={`max-w-4xl w-full max-h-[90vh] overflow-y-auto rounded-lg ${
              darkMode ? 'bg-gray-800' : 'bg-white'
            }`}
          >
            <div className='p-6'>
              <div className='flex items-center justify-between mb-6'>
                <h2 className='text-xl font-bold'>Snapshot Manager</h2>
                <button
                  onClick={() => setShowManager(false)}
                  className='text-gray-500 hover:text-gray-700'
                >
                  ✕
                </button>
              </div>

              {/* Auto-save Settings */}
              <div
                className={`p-4 rounded-lg mb-6 ${
                  darkMode ? 'bg-gray-700' : 'bg-gray-50'
                }`}
              >
                <h3 className='font-semibold mb-3'>Auto-save Settings</h3>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                  <div className='flex items-center gap-2'>
                    <input
                      type='checkbox'
                      id='autoSaveEnabled'
                      checked={autoSaveEnabled}
                      onChange={e => setAutoSaveEnabled(e.target.checked)}
                      className='rounded'
                    />
                    <label htmlFor='autoSaveEnabled'>Enable auto-save</label>
                  </div>
                  <div>
                    <label className='block text-sm font-medium mb-1'>
                      Auto-save interval (minutes)
                    </label>
                    <select
                      value={autoSaveInterval}
                      onChange={e =>
                        setAutoSaveInterval(parseInt(e.target.value))
                      }
                      className={`px-3 py-2 rounded-md border ${
                        darkMode
                          ? 'bg-gray-600 border-gray-500 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    >
                      <option value={1}>1 minute</option>
                      <option value={5}>5 minutes</option>
                      <option value={10}>10 minutes</option>
                      <option value={15}>15 minutes</option>
                      <option value={30}>30 minutes</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className='flex flex-wrap gap-3 mb-6'>
                <button
                  onClick={() => setShowCreateForm(true)}
                  className='px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700'
                >
                  Create Snapshot
                </button>
                <button
                  onClick={createAutoSave}
                  className='px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700'
                >
                  Save Now
                </button>
                <label className='px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 cursor-pointer'>
                  Import Snapshot
                  <input
                    type='file'
                    accept='.json'
                    onChange={importSnapshot}
                    className='hidden'
                  />
                </label>
              </div>

              {/* Create Snapshot Form */}
              {showCreateForm && (
                <div
                  className={`p-4 rounded-lg mb-6 border ${
                    darkMode
                      ? 'bg-gray-700 border-gray-600'
                      : 'bg-gray-50 border-gray-200'
                  }`}
                >
                  <h3 className='font-semibold mb-3'>Create New Snapshot</h3>
                  <div className='space-y-3'>
                    <div>
                      <label className='block text-sm font-medium mb-1'>
                        Snapshot Name *
                      </label>
                      <input
                        type='text'
                        value={newSnapshotName}
                        onChange={e => setNewSnapshotName(e.target.value)}
                        className={`w-full px-3 py-2 rounded-md border ${
                          darkMode
                            ? 'bg-gray-600 border-gray-500 text-white'
                            : 'bg-white border-gray-300 text-gray-900'
                        }`}
                        placeholder='e.g., Before tax optimization'
                      />
                    </div>
                    <div>
                      <label className='block text-sm font-medium mb-1'>
                        Description
                      </label>
                      <textarea
                        value={newSnapshotDescription}
                        onChange={e =>
                          setNewSnapshotDescription(e.target.value)
                        }
                        rows={2}
                        className={`w-full px-3 py-2 rounded-md border ${
                          darkMode
                            ? 'bg-gray-600 border-gray-500 text-white'
                            : 'bg-white border-gray-300 text-gray-900'
                        }`}
                        placeholder='Optional description...'
                      />
                    </div>
                    <div className='flex gap-2'>
                      <button
                        onClick={createManualSnapshot}
                        disabled={!newSnapshotName.trim()}
                        className='px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50'
                      >
                        Create
                      </button>
                      <button
                        onClick={() => setShowCreateForm(false)}
                        className='px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700'
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {/* Snapshots List */}
              <div className='space-y-3'>
                <h3 className='font-semibold'>
                  Saved Snapshots ({snapshots.length})
                </h3>
                {snapshots.length === 0 ? (
                  <p className='text-gray-500 text-center py-8'>
                    No snapshots yet. Create your first snapshot to save your
                    current progress.
                  </p>
                ) : (
                  snapshots.map(snapshot => (
                    <div
                      key={snapshot.id}
                      className={`p-4 rounded-lg border ${
                        darkMode
                          ? 'bg-gray-700 border-gray-600'
                          : 'bg-gray-50 border-gray-200'
                      }`}
                    >
                      <div className='flex items-start justify-between'>
                        <div className='flex-1'>
                          <div className='flex items-center gap-2'>
                            <h4 className='font-medium'>{snapshot.name}</h4>
                            {snapshot.isAutoSave && (
                              <span className='px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full'>
                                Auto-save
                              </span>
                            )}
                          </div>
                          <p className='text-sm text-gray-500 mt-1'>
                            {formatTimestamp(snapshot.timestamp)}
                          </p>
                          {snapshot.description && (
                            <p className='text-sm text-gray-600 mt-1'>
                              {snapshot.description}
                            </p>
                          )}
                        </div>
                        <div className='flex items-center gap-2 ml-4'>
                          <button
                            onClick={() => loadSnapshot(snapshot)}
                            className='px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700'
                          >
                            Load
                          </button>
                          <button
                            onClick={() => exportSnapshot(snapshot)}
                            className='px-3 py-1 text-sm bg-gray-600 text-white rounded hover:bg-gray-700'
                          >
                            Export
                          </button>
                          <button
                            onClick={() => deleteSnapshot(snapshot.id)}
                            className='px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700'
                          >
                            Delete
                          </button>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default AutoSaveManager;
