import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

interface CantonRelocationAnalysisProps {
  darkMode: boolean;
  userData: {
    monthlyIncome: number;
    currentSavings: number;
    familySize?: number;
  };
}

interface CantonData {
  code: string;
  name: string;
  incomeTaxRate: number; // Effective rate for given income
  wealthTaxRate: number; // Per mille
  costOfLivingIndex: number; // 100 = Swiss average
  qualityOfLifeScore: number; // 0-100
  housingCostIndex: number; // 100 = Swiss average
  employmentRate: number; // %
  averageSalary: number; // CHF
  languages: string[];
  highlights: string[];
  drawbacks: string[];
}

interface RelocationAnalysis {
  canton: CantonData;
  annualTaxSavings: number;
  costOfLivingDifference: number;
  netBenefit: number;
  paybackPeriod: number; // months
  lifetimeValue: number;
  recommendation:
    | 'highly_recommended'
    | 'recommended'
    | 'neutral'
    | 'not_recommended';
  score: number; // 0-100
}

const CantonRelocationAnalysis: React.FC<CantonRelocationAnalysisProps> = ({
  darkMode,
  userData,
}) => {
  const { t } = useTranslation();
  const [currentCanton, setCurrentCanton] = useState('ZH');
  const [analyses, setAnalyses] = useState<RelocationAnalysis[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [sortBy, setSortBy] = useState<'savings' | 'netBenefit' | 'score'>(
    'netBenefit',
  );
  const [showOnlyRecommended, setShowOnlyRecommended] = useState(false);

  // Swiss canton data (simplified for demo - in production would come from APIs)
  const cantonData: CantonData[] = [
    {
      code: 'ZG',
      name: 'Zug',
      incomeTaxRate: 14.2,
      wealthTaxRate: 0.3,
      costOfLivingIndex: 125,
      qualityOfLifeScore: 92,
      housingCostIndex: 140,
      employmentRate: 96.2,
      averageSalary: 95000,
      languages: ['German'],
      highlights: [
        'Lowest tax rates',
        'Crypto valley',
        'High salaries',
        'Beautiful lake',
      ],
      drawbacks: [
        'Very expensive housing',
        'Limited cultural scene',
        'Small size',
      ],
    },
    {
      code: 'SZ',
      name: 'Schwyz',
      incomeTaxRate: 15.8,
      wealthTaxRate: 0.4,
      costOfLivingIndex: 110,
      qualityOfLifeScore: 85,
      housingCostIndex: 120,
      employmentRate: 94.1,
      averageSalary: 78000,
      languages: ['German'],
      highlights: [
        'Low taxes',
        'Mountain scenery',
        'Good transport links',
        'Lower housing costs than ZG',
      ],
      drawbacks: [
        'Limited job market',
        'Rural setting',
        'Weather dependent tourism',
      ],
    },
    {
      code: 'NW',
      name: 'Nidwalden',
      incomeTaxRate: 16.5,
      wealthTaxRate: 0.5,
      costOfLivingIndex: 105,
      qualityOfLifeScore: 88,
      housingCostIndex: 115,
      employmentRate: 93.8,
      averageSalary: 75000,
      languages: ['German'],
      highlights: [
        'Low taxes',
        'Beautiful nature',
        'Good infrastructure',
        'Family friendly',
      ],
      drawbacks: [
        'Limited nightlife',
        'Small job market',
        'Conservative culture',
      ],
    },
    {
      code: 'ZH',
      name: 'Zurich',
      incomeTaxRate: 22.8,
      wealthTaxRate: 0.8,
      costOfLivingIndex: 130,
      qualityOfLifeScore: 95,
      housingCostIndex: 150,
      employmentRate: 95.5,
      averageSalary: 88000,
      languages: ['German', 'English'],
      highlights: [
        'Major financial center',
        'Excellent job market',
        'Cultural scene',
        'International',
      ],
      drawbacks: [
        'High taxes',
        'Expensive housing',
        'Crowded',
        'High cost of living',
      ],
    },
    {
      code: 'GE',
      name: 'Geneva',
      incomeTaxRate: 25.2,
      wealthTaxRate: 1.0,
      costOfLivingIndex: 135,
      qualityOfLifeScore: 90,
      housingCostIndex: 160,
      employmentRate: 92.3,
      averageSalary: 85000,
      languages: ['French', 'English'],
      highlights: [
        'International organizations',
        'Cultural diversity',
        'Lake Geneva',
        'High salaries',
      ],
      drawbacks: [
        'Highest taxes',
        'Very expensive',
        'Traffic congestion',
        'Language barrier',
      ],
    },
    {
      code: 'VD',
      name: 'Vaud',
      incomeTaxRate: 23.5,
      wealthTaxRate: 0.9,
      costOfLivingIndex: 115,
      qualityOfLifeScore: 87,
      housingCostIndex: 125,
      employmentRate: 91.7,
      averageSalary: 78000,
      languages: ['French'],
      highlights: [
        'Wine region',
        'Lake Geneva',
        'Good value',
        'Cultural scene',
      ],
      drawbacks: [
        'High taxes',
        'Language barrier',
        'Limited tech jobs',
        'Bureaucracy',
      ],
    },
    {
      code: 'BS',
      name: 'Basel-Stadt',
      incomeTaxRate: 24.1,
      wealthTaxRate: 0.7,
      costOfLivingIndex: 120,
      qualityOfLifeScore: 89,
      housingCostIndex: 135,
      employmentRate: 94.2,
      averageSalary: 82000,
      languages: ['German'],
      highlights: [
        'Pharma industry',
        'Cultural scene',
        'Good transport',
        'International',
      ],
      drawbacks: [
        'High taxes',
        'Expensive housing',
        'Industrial pollution',
        'Limited nature',
      ],
    },
    {
      code: 'TI',
      name: 'Ticino',
      incomeTaxRate: 21.8,
      wealthTaxRate: 0.6,
      costOfLivingIndex: 95,
      qualityOfLifeScore: 91,
      housingCostIndex: 110,
      employagementRate: 89.5,
      averageSalary: 68000,
      languages: ['Italian'],
      highlights: [
        'Mediterranean climate',
        'Low cost of living',
        'Beautiful scenery',
        'Relaxed lifestyle',
      ],
      drawbacks: [
        'Lower salaries',
        'Limited job market',
        'Language barrier',
        'Economic challenges',
      ],
    },
  ];

  // Calculate relocation analysis
  const analyzeRelocation = async () => {
    setIsAnalyzing(true);

    try {
      await new Promise(resolve => setTimeout(resolve, 2000));

      const currentCantonData = cantonData.find(c => c.code === currentCanton);
      if (!currentCantonData) return;

      const annualIncome = userData.monthlyIncome * 12;
      const familySize = userData.familySize || 1;

      const relocationAnalyses: RelocationAnalysis[] = cantonData
        .filter(canton => canton.code !== currentCanton)
        .map(canton => {
          // Calculate tax savings
          const currentTax =
            (annualIncome * currentCantonData.incomeTaxRate) / 100 +
            (userData.currentSavings * currentCantonData.wealthTaxRate) / 1000;
          const newTax =
            (annualIncome * canton.incomeTaxRate) / 100 +
            (userData.currentSavings * canton.wealthTaxRate) / 1000;
          const annualTaxSavings = currentTax - newTax;

          // Calculate cost of living difference
          const costDifference =
            ((canton.costOfLivingIndex - currentCantonData.costOfLivingIndex) /
              100) *
            annualIncome *
            0.7;

          // Estimate relocation costs
          const relocationCosts = 15000 * familySize; // Moving, deposits, etc.

          // Calculate net benefit
          const netBenefit = annualTaxSavings - costDifference;
          const paybackPeriod =
            netBenefit > 0
              ? Math.ceil(relocationCosts / (netBenefit / 12))
              : 999;
          const lifetimeValue = netBenefit * 20; // 20-year projection

          // Calculate recommendation score
          let score = 50; // Base score
          score += Math.min(30, annualTaxSavings / 1000); // Tax savings component
          score += Math.min(20, (100 - canton.costOfLivingIndex) / 5); // Cost of living component
          score += canton.qualityOfLifeScore / 5; // Quality of life component
          score -= Math.max(0, (paybackPeriod - 24) / 2); // Payback period penalty
          score = Math.max(0, Math.min(100, score));

          let recommendation: RelocationAnalysis['recommendation'] = 'neutral';
          if (score >= 80 && netBenefit > 5000)
            recommendation = 'highly_recommended';
          else if (score >= 65 && netBenefit > 2000)
            recommendation = 'recommended';
          else if (netBenefit < -2000 || paybackPeriod > 60)
            recommendation = 'not_recommended';

          return {
            canton,
            annualTaxSavings,
            costOfLivingDifference: costDifference,
            netBenefit,
            paybackPeriod,
            lifetimeValue,
            recommendation,
            score,
          };
        });

      // Sort by selected criteria
      relocationAnalyses.sort((a, b) => {
        switch (sortBy) {
          case 'savings':
            return b.annualTaxSavings - a.annualTaxSavings;
          case 'netBenefit':
            return b.netBenefit - a.netBenefit;
          case 'score':
            return b.score - a.score;
          default:
            return b.netBenefit - a.netBenefit;
        }
      });

      setAnalyses(relocationAnalyses);
    } catch (error) {
      console.error('Relocation analysis failed:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  useEffect(() => {
    if (userData.monthlyIncome > 0) {
      analyzeRelocation();
    }
  }, [currentCanton, userData, sortBy]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('de-CH', {
      style: 'currency',
      currency: 'CHF',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getRecommendationColor = (recommendation: string) => {
    switch (recommendation) {
      case 'highly_recommended':
        return 'border-green-500 bg-green-50 dark:bg-green-900/20';
      case 'recommended':
        return 'border-blue-500 bg-blue-50 dark:bg-blue-900/20';
      case 'neutral':
        return 'border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20';
      case 'not_recommended':
        return 'border-red-500 bg-red-50 dark:bg-red-900/20';
      default:
        return 'border-gray-500 bg-gray-50 dark:bg-gray-900/20';
    }
  };

  const getRecommendationLabel = (recommendation: string) => {
    switch (recommendation) {
      case 'highly_recommended':
        return '🌟 Highly Recommended';
      case 'recommended':
        return '👍 Recommended';
      case 'neutral':
        return '🤔 Neutral';
      case 'not_recommended':
        return '❌ Not Recommended';
      default:
        return '❓ Unknown';
    }
  };

  const filteredAnalyses = showOnlyRecommended
    ? analyses.filter(
        a =>
          a.recommendation === 'highly_recommended' ||
          a.recommendation === 'recommended',
      )
    : analyses;

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div>
        <h2 className='text-2xl font-bold mb-2'>
          🏛️ Canton Relocation Tax Optimizer
        </h2>
        <p className='text-gray-600 dark:text-gray-400'>
          Analyze potential tax savings and lifestyle impacts of relocating to
          different Swiss cantons
        </p>
      </div>

      {/* Configuration */}
      <div
        className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
      >
        <h3 className='text-lg font-semibold mb-4'>
          ⚙️ Analysis Configuration
        </h3>

        <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
          <div>
            <label className='block text-sm font-medium mb-2'>
              Current Canton
            </label>
            <select
              value={currentCanton}
              onChange={e => setCurrentCanton(e.target.value)}
              className={`w-full px-3 py-2 rounded-md border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
            >
              {cantonData.map(canton => (
                <option key={canton.code} value={canton.code}>
                  {canton.name} ({canton.code})
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className='block text-sm font-medium mb-2'>Sort By</label>
            <select
              value={sortBy}
              onChange={e => setSortBy(e.target.value as any)}
              className={`w-full px-3 py-2 rounded-md border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
            >
              <option value='netBenefit'>Net Benefit</option>
              <option value='savings'>Tax Savings</option>
              <option value='score'>Overall Score</option>
            </select>
          </div>

          <div className='flex items-end'>
            <label className='flex items-center'>
              <input
                type='checkbox'
                checked={showOnlyRecommended}
                onChange={e => setShowOnlyRecommended(e.target.checked)}
                className='mr-2'
              />
              <span className='text-sm'>Show only recommended</span>
            </label>
          </div>
        </div>
      </div>

      {/* Analysis Results */}
      {isAnalyzing ? (
        <div
          className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
        >
          <div className='flex items-center justify-center py-8'>
            <div className='animate-spin w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full mr-3'></div>
            <span>Analyzing canton relocation opportunities...</span>
          </div>
        </div>
      ) : (
        <div className='space-y-4'>
          {filteredAnalyses.map(analysis => (
            <div
              key={analysis.canton.code}
              className={`p-6 rounded-lg border-l-4 ${getRecommendationColor(analysis.recommendation)} ${
                darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'
              }`}
            >
              <div className='flex items-start justify-between mb-4'>
                <div>
                  <h3 className='text-xl font-bold flex items-center gap-2'>
                    {analysis.canton.name} ({analysis.canton.code})
                    <span className='text-sm font-normal px-2 py-1 rounded-full bg-gray-200 dark:bg-gray-700'>
                      Score: {analysis.score.toFixed(0)}/100
                    </span>
                  </h3>
                  <p className='text-sm font-medium mt-1'>
                    {getRecommendationLabel(analysis.recommendation)}
                  </p>
                </div>
                <div className='text-right'>
                  <div className='text-2xl font-bold text-green-600'>
                    {formatCurrency(analysis.netBenefit)}/year
                  </div>
                  <div className='text-sm text-gray-500'>Net Benefit</div>
                </div>
              </div>

              <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4'>
                <div className='text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg'>
                  <div className='text-sm text-gray-600 dark:text-gray-400'>
                    Tax Savings
                  </div>
                  <div className='text-lg font-bold text-green-600'>
                    {formatCurrency(analysis.annualTaxSavings)}
                  </div>
                </div>

                <div className='text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg'>
                  <div className='text-sm text-gray-600 dark:text-gray-400'>
                    Cost Difference
                  </div>
                  <div
                    className={`text-lg font-bold ${analysis.costOfLivingDifference > 0 ? 'text-red-600' : 'text-green-600'}`}
                  >
                    {analysis.costOfLivingDifference > 0 ? '+' : ''}
                    {formatCurrency(analysis.costOfLivingDifference)}
                  </div>
                </div>

                <div className='text-center p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg'>
                  <div className='text-sm text-gray-600 dark:text-gray-400'>
                    Payback Period
                  </div>
                  <div className='text-lg font-bold text-yellow-600'>
                    {analysis.paybackPeriod > 120
                      ? '10+ years'
                      : `${analysis.paybackPeriod} months`}
                  </div>
                </div>

                <div className='text-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg'>
                  <div className='text-sm text-gray-600 dark:text-gray-400'>
                    20-Year Value
                  </div>
                  <div className='text-lg font-bold text-purple-600'>
                    {formatCurrency(analysis.lifetimeValue)}
                  </div>
                </div>
              </div>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div>
                  <h4 className='font-semibold text-green-600 mb-2'>
                    ✅ Advantages
                  </h4>
                  <ul className='text-sm space-y-1'>
                    {analysis.canton.highlights.map((highlight, index) => (
                      <li key={index} className='flex items-center gap-2'>
                        <span className='text-green-500'>•</span>
                        {highlight}
                      </li>
                    ))}
                  </ul>
                </div>
                <div>
                  <h4 className='font-semibold text-red-600 mb-2'>
                    ⚠️ Considerations
                  </h4>
                  <ul className='text-sm space-y-1'>
                    {analysis.canton.drawbacks.map((drawback, index) => (
                      <li key={index} className='flex items-center gap-2'>
                        <span className='text-red-500'>•</span>
                        {drawback}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              <div className='mt-4 pt-4 border-t border-gray-200 dark:border-gray-600'>
                <div className='grid grid-cols-2 md:grid-cols-4 gap-4 text-sm'>
                  <div>
                    <span className='text-gray-600 dark:text-gray-400'>
                      Income Tax:
                    </span>
                    <span className='ml-2 font-medium'>
                      {analysis.canton.incomeTaxRate}%
                    </span>
                  </div>
                  <div>
                    <span className='text-gray-600 dark:text-gray-400'>
                      Quality of Life:
                    </span>
                    <span className='ml-2 font-medium'>
                      {analysis.canton.qualityOfLifeScore}/100
                    </span>
                  </div>
                  <div>
                    <span className='text-gray-600 dark:text-gray-400'>
                      Employment:
                    </span>
                    <span className='ml-2 font-medium'>
                      {analysis.canton.employmentRate}%
                    </span>
                  </div>
                  <div>
                    <span className='text-gray-600 dark:text-gray-400'>
                      Languages:
                    </span>
                    <span className='ml-2 font-medium'>
                      {analysis.canton.languages.join(', ')}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {filteredAnalyses.length === 0 && !isAnalyzing && (
        <div
          className={`p-8 rounded-lg text-center ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
        >
          <div className='text-4xl mb-4'>🏛️</div>
          <h3 className='text-lg font-semibold mb-2'>
            No relocation opportunities found
          </h3>
          <p className='text-gray-600 dark:text-gray-400'>
            {showOnlyRecommended
              ? 'Try showing all results or adjusting your current canton selection'
              : 'Configure your current canton and income to see relocation analysis'}
          </p>
        </div>
      )}

      {/* Refresh Analysis */}
      <div className='flex justify-center'>
        <button
          onClick={analyzeRelocation}
          disabled={isAnalyzing}
          className={`px-6 py-3 rounded-lg font-medium transition-colors ${
            isAnalyzing
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-blue-600 hover:bg-blue-700 text-white'
          }`}
        >
          {isAnalyzing ? 'Analyzing...' : '🔄 Refresh Analysis'}
        </button>
      </div>
    </div>
  );
};

export default CantonRelocationAnalysis;
