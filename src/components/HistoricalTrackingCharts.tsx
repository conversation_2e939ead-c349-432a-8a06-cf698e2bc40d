import React, { useState, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocalStorage } from '../hooks/useLocalStorage';

interface HistoricalTrackingChartsProps {
  darkMode: boolean;
  userData: {
    currentAge: number;
    retirementAge: number;
    currentSavings: number;
    monthlyIncome: number;
    monthlyExpenses: number;
    expectedReturn: number;
    inflationRate: number;
    safeWithdrawalRate: number;
  };
  expenses?: any[];
  investments?: any[];
  savingsGoals?: any[];
}

interface HistoricalDataPoint {
  date: string;
  age: number;
  netWorth: number;
  monthlyIncome: number;
  monthlyExpenses: number;
  monthlySavings: number;
  savingsRate: number;
  fireProgress: number;
  investmentValue: number;
  emergencyFund: number;
}

interface ChartConfig {
  type: 'line' | 'bar' | 'area';
  metric: keyof HistoricalDataPoint;
  label: string;
  color: string;
  format: 'currency' | 'percentage' | 'number';
}

const HistoricalTrackingCharts: React.FC<HistoricalTrackingChartsProps> = ({
  darkMode,
  userData,
  expenses = [],
  investments = [],
  savingsGoals = [],
}) => {
  const { t } = useTranslation();
  const [historicalData, setHistoricalData] = useLocalStorage<
    HistoricalDataPoint[]
  >('historical_data', []);
  const [selectedTimeframe, setSelectedTimeframe] = useState<
    '1M' | '3M' | '6M' | '1Y' | '2Y' | 'ALL'
  >('1Y');
  const [selectedMetrics, setSelectedMetrics] = useState<string[]>([
    'netWorth',
    'savingsRate',
    'fireProgress',
  ]);
  const [isGeneratingData, setIsGeneratingData] = useState(false);

  // Available chart configurations
  const chartConfigs: ChartConfig[] = [
    {
      type: 'area',
      metric: 'netWorth',
      label: 'Net Worth',
      color: '#10B981',
      format: 'currency',
    },
    {
      type: 'line',
      metric: 'monthlyIncome',
      label: 'Monthly Income',
      color: '#3B82F6',
      format: 'currency',
    },
    {
      type: 'line',
      metric: 'monthlyExpenses',
      label: 'Monthly Expenses',
      color: '#EF4444',
      format: 'currency',
    },
    {
      type: 'line',
      metric: 'monthlySavings',
      label: 'Monthly Savings',
      color: '#8B5CF6',
      format: 'currency',
    },
    {
      type: 'line',
      metric: 'savingsRate',
      label: 'Savings Rate',
      color: '#F59E0B',
      format: 'percentage',
    },
    {
      type: 'line',
      metric: 'fireProgress',
      label: 'FIRE Progress',
      color: '#06B6D4',
      format: 'percentage',
    },
    {
      type: 'area',
      metric: 'investmentValue',
      label: 'Investment Value',
      color: '#84CC16',
      format: 'currency',
    },
    {
      type: 'line',
      metric: 'emergencyFund',
      label: 'Emergency Fund',
      color: '#F97316',
      format: 'currency',
    },
  ];

  // Generate sample historical data for demonstration
  const generateSampleData = () => {
    setIsGeneratingData(true);

    setTimeout(() => {
      const sampleData: HistoricalDataPoint[] = [];
      const startDate = new Date();
      startDate.setFullYear(startDate.getFullYear() - 2); // 2 years of data

      const { currentAge, monthlyIncome, monthlyExpenses, expectedReturn } =
        userData;
      const monthlySavings = monthlyIncome - monthlyExpenses;
      const fireTarget = monthlyExpenses * 12 * 25; // 4% rule

      for (let i = 0; i < 24; i++) {
        // 24 months of data
        const date = new Date(startDate);
        date.setMonth(date.getMonth() + i);

        // Simulate growth and variations
        const growthFactor = Math.pow(1 + expectedReturn / 100 / 12, i);
        const randomVariation = 0.9 + Math.random() * 0.2; // ±10% variation

        const netWorth =
          userData.currentSavings * growthFactor * randomVariation;
        const currentMonthlySavings =
          monthlySavings * (0.95 + Math.random() * 0.1); // ±5% variation
        const currentSavingsRate =
          monthlyIncome > 0 ? (currentMonthlySavings / monthlyIncome) * 100 : 0;
        const currentFireProgress =
          fireTarget > 0 ? (netWorth / fireTarget) * 100 : 0;

        const investmentValue = netWorth * 0.7; // Assume 70% invested
        const emergencyFund = Math.min(netWorth * 0.2, monthlyExpenses * 6); // Max 6 months expenses

        sampleData.push({
          date: date.toISOString().split('T')[0],
          age: currentAge + i / 12,
          netWorth,
          monthlyIncome: monthlyIncome * (0.98 + Math.random() * 0.04), // ±2% variation
          monthlyExpenses: monthlyExpenses * (0.95 + Math.random() * 0.1), // ±5% variation
          monthlySavings: currentMonthlySavings,
          savingsRate: currentSavingsRate,
          fireProgress: Math.min(currentFireProgress, 100),
          investmentValue,
          emergencyFund,
        });
      }

      setHistoricalData(sampleData);
      setIsGeneratingData(false);
    }, 2000);
  };

  // Filter data based on selected timeframe
  const filteredData = useMemo(() => {
    if (historicalData.length === 0) return [];

    const now = new Date();
    const cutoffDate = new Date();

    switch (selectedTimeframe) {
      case '1M':
        cutoffDate.setMonth(now.getMonth() - 1);
        break;
      case '3M':
        cutoffDate.setMonth(now.getMonth() - 3);
        break;
      case '6M':
        cutoffDate.setMonth(now.getMonth() - 6);
        break;
      case '1Y':
        cutoffDate.setFullYear(now.getFullYear() - 1);
        break;
      case '2Y':
        cutoffDate.setFullYear(now.getFullYear() - 2);
        break;
      case 'ALL':
        return historicalData;
    }

    return historicalData.filter(point => new Date(point.date) >= cutoffDate);
  }, [historicalData, selectedTimeframe]);

  // Format values for display
  const formatValue = (value: number, format: string) => {
    switch (format) {
      case 'currency':
        return new Intl.NumberFormat('de-CH', {
          style: 'currency',
          currency: 'CHF',
          minimumFractionDigits: 0,
          maximumFractionDigits: 0,
        }).format(value);
      case 'percentage':
        return `${value.toFixed(1)}%`;
      case 'number':
        return value.toFixed(0);
      default:
        return value.toString();
    }
  };

  // Simple SVG chart component
  const SimpleChart: React.FC<{
    config: ChartConfig;
    data: HistoricalDataPoint[];
  }> = ({ config, data }) => {
    if (data.length === 0) return null;

    const values = data.map(d => d[config.metric] as number);
    const maxValue = Math.max(...values);
    const minValue = Math.min(...values);
    const range = maxValue - minValue || 1;

    const width = 400;
    const height = 200;
    const padding = 40;

    const points = data
      .map((d, i) => {
        const x = padding + (i / (data.length - 1)) * (width - 2 * padding);
        const y =
          height -
          padding -
          (((d[config.metric] as number) - minValue) / range) *
            (height - 2 * padding);
        return `${x},${y}`;
      })
      .join(' ');

    return (
      <div className='relative'>
        <svg
          width={width}
          height={height}
          className='border rounded-lg bg-white dark:bg-gray-800'
        >
          {/* Grid lines */}
          {[0, 0.25, 0.5, 0.75, 1].map(ratio => (
            <line
              key={ratio}
              x1={padding}
              y1={padding + ratio * (height - 2 * padding)}
              x2={width - padding}
              y2={padding + ratio * (height - 2 * padding)}
              stroke={darkMode ? '#374151' : '#E5E7EB'}
              strokeWidth='1'
            />
          ))}

          {/* Chart line/area */}
          {config.type === 'area' ? (
            <polygon
              points={`${padding},${height - padding} ${points} ${width - padding},${height - padding}`}
              fill={config.color}
              fillOpacity='0.2'
              stroke={config.color}
              strokeWidth='2'
            />
          ) : (
            <polyline
              points={points}
              fill='none'
              stroke={config.color}
              strokeWidth='2'
            />
          )}

          {/* Data points */}
          {data.map((d, i) => {
            const x = padding + (i / (data.length - 1)) * (width - 2 * padding);
            const y =
              height -
              padding -
              (((d[config.metric] as number) - minValue) / range) *
                (height - 2 * padding);
            return (
              <circle
                key={i}
                cx={x}
                cy={y}
                r='3'
                fill={config.color}
                className='hover:r-5 transition-all cursor-pointer'
              >
                <title>{`${config.label}: ${formatValue(d[config.metric] as number, config.format)}`}</title>
              </circle>
            );
          })}

          {/* Y-axis labels */}
          <text
            x='10'
            y={padding}
            className='text-xs fill-gray-600 dark:fill-gray-400'
          >
            {formatValue(maxValue, config.format)}
          </text>
          <text
            x='10'
            y={height - padding + 5}
            className='text-xs fill-gray-600 dark:fill-gray-400'
          >
            {formatValue(minValue, config.format)}
          </text>
        </svg>

        <div
          className='absolute top-2 left-2 text-sm font-semibold'
          style={{ color: config.color }}
        >
          {config.label}
        </div>
      </div>
    );
  };

  // Calculate summary statistics
  const summaryStats = useMemo(() => {
    if (filteredData.length === 0) return null;

    const latest = filteredData[filteredData.length - 1];
    const earliest = filteredData[0];

    const netWorthGrowth = latest.netWorth - earliest.netWorth;
    const netWorthGrowthPercent =
      earliest.netWorth > 0 ? (netWorthGrowth / earliest.netWorth) * 100 : 0;
    const avgSavingsRate =
      filteredData.reduce((sum, d) => sum + d.savingsRate, 0) /
      filteredData.length;
    const avgMonthlySavings =
      filteredData.reduce((sum, d) => sum + d.monthlySavings, 0) /
      filteredData.length;

    return {
      netWorthGrowth,
      netWorthGrowthPercent,
      avgSavingsRate,
      avgMonthlySavings,
      currentNetWorth: latest.netWorth,
      currentFireProgress: latest.fireProgress,
    };
  }, [filteredData]);

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div>
        <h2 className='text-2xl font-bold mb-2'>
          📈 Historical Tracking & Analytics
        </h2>
        <p className='text-gray-600 dark:text-gray-400'>
          Track your financial progress over time with comprehensive charts and
          analytics
        </p>
      </div>

      {/* Controls */}
      <div
        className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
      >
        <div className='flex flex-wrap items-center justify-between gap-4'>
          <div>
            <label className='block text-sm font-medium mb-2'>
              Time Period
            </label>
            <div className='flex gap-2'>
              {(['1M', '3M', '6M', '1Y', '2Y', 'ALL'] as const).map(period => (
                <button
                  key={period}
                  onClick={() => setSelectedTimeframe(period)}
                  className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                    selectedTimeframe === period
                      ? 'bg-blue-600 text-white'
                      : darkMode
                        ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                        : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  {period}
                </button>
              ))}
            </div>
          </div>

          <div>
            <label className='block text-sm font-medium mb-2'>
              Metrics to Display
            </label>
            <div className='flex flex-wrap gap-2'>
              {chartConfigs.slice(0, 4).map(config => (
                <label
                  key={config.metric}
                  className='flex items-center text-sm'
                >
                  <input
                    type='checkbox'
                    checked={selectedMetrics.includes(config.metric)}
                    onChange={e => {
                      if (e.target.checked) {
                        setSelectedMetrics([...selectedMetrics, config.metric]);
                      } else {
                        setSelectedMetrics(
                          selectedMetrics.filter(m => m !== config.metric),
                        );
                      }
                    }}
                    className='mr-2'
                  />
                  <span style={{ color: config.color }}>●</span>
                  <span className='ml-1'>{config.label}</span>
                </label>
              ))}
            </div>
          </div>

          <button
            onClick={generateSampleData}
            disabled={isGeneratingData}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              isGeneratingData
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-green-600 hover:bg-green-700 text-white'
            }`}
          >
            {isGeneratingData ? 'Generating...' : '📊 Generate Sample Data'}
          </button>
        </div>
      </div>

      {/* Summary Statistics */}
      {summaryStats && (
        <div
          className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
        >
          <h3 className='text-lg font-semibold mb-4'>
            📊 Summary Statistics ({selectedTimeframe})
          </h3>

          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
            <div className='text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg'>
              <div className='text-sm text-gray-600 dark:text-gray-400'>
                Net Worth Growth
              </div>
              <div className='text-2xl font-bold text-green-600'>
                {formatValue(summaryStats.netWorthGrowth, 'currency')}
              </div>
              <div className='text-sm text-green-600'>
                ({summaryStats.netWorthGrowthPercent > 0 ? '+' : ''}
                {summaryStats.netWorthGrowthPercent.toFixed(1)}%)
              </div>
            </div>

            <div className='text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg'>
              <div className='text-sm text-gray-600 dark:text-gray-400'>
                Current Net Worth
              </div>
              <div className='text-2xl font-bold text-blue-600'>
                {formatValue(summaryStats.currentNetWorth, 'currency')}
              </div>
              <div className='text-sm text-blue-600'>Latest Value</div>
            </div>

            <div className='text-center p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg'>
              <div className='text-sm text-gray-600 dark:text-gray-400'>
                Avg Savings Rate
              </div>
              <div className='text-2xl font-bold text-yellow-600'>
                {formatValue(summaryStats.avgSavingsRate, 'percentage')}
              </div>
              <div className='text-sm text-yellow-600'>Period Average</div>
            </div>

            <div className='text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg'>
              <div className='text-sm text-gray-600 dark:text-gray-400'>
                FIRE Progress
              </div>
              <div className='text-2xl font-bold text-purple-600'>
                {formatValue(summaryStats.currentFireProgress, 'percentage')}
              </div>
              <div className='text-sm text-purple-600'>Current Status</div>
            </div>
          </div>
        </div>
      )}

      {/* Charts */}
      {filteredData.length > 0 ? (
        <div
          className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
        >
          <h3 className='text-lg font-semibold mb-4'>📈 Financial Trends</h3>

          <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
            {chartConfigs
              .filter(config => selectedMetrics.includes(config.metric))
              .map(config => (
                <SimpleChart
                  key={config.metric}
                  config={config}
                  data={filteredData}
                />
              ))}
          </div>
        </div>
      ) : (
        <div
          className={`p-8 rounded-lg text-center ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
        >
          <div className='text-4xl mb-4'>📈</div>
          <h3 className='text-lg font-semibold mb-2'>
            No historical data available
          </h3>
          <p className='text-gray-600 dark:text-gray-400 mb-4'>
            Generate sample data to see your financial trends and progress over
            time
          </p>
          <button
            onClick={generateSampleData}
            disabled={isGeneratingData}
            className={`px-6 py-3 rounded-lg font-medium transition-colors ${
              isGeneratingData
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700 text-white'
            }`}
          >
            {isGeneratingData
              ? 'Generating Sample Data...'
              : '📊 Generate Sample Data'}
          </button>
        </div>
      )}

      {/* Data Table */}
      {filteredData.length > 0 && (
        <div
          className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
        >
          <h3 className='text-lg font-semibold mb-4'>
            📋 Historical Data Table
          </h3>

          <div className='overflow-x-auto'>
            <table className='w-full text-sm'>
              <thead>
                <tr
                  className={`border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
                >
                  <th className='text-left py-2'>Date</th>
                  <th className='text-right py-2'>Net Worth</th>
                  <th className='text-right py-2'>Monthly Income</th>
                  <th className='text-right py-2'>Monthly Expenses</th>
                  <th className='text-right py-2'>Savings Rate</th>
                  <th className='text-right py-2'>FIRE Progress</th>
                </tr>
              </thead>
              <tbody>
                {filteredData.slice(-10).map((point, index) => (
                  <tr
                    key={index}
                    className={`border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
                  >
                    <td className='py-2'>
                      {new Date(point.date).toLocaleDateString('de-CH')}
                    </td>
                    <td className='text-right py-2 font-medium'>
                      {formatValue(point.netWorth, 'currency')}
                    </td>
                    <td className='text-right py-2'>
                      {formatValue(point.monthlyIncome, 'currency')}
                    </td>
                    <td className='text-right py-2'>
                      {formatValue(point.monthlyExpenses, 'currency')}
                    </td>
                    <td className='text-right py-2'>
                      {formatValue(point.savingsRate, 'percentage')}
                    </td>
                    <td className='text-right py-2'>
                      {formatValue(point.fireProgress, 'percentage')}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            {filteredData.length > 10 && (
              <p className='text-center text-gray-500 mt-2 text-sm'>
                Showing last 10 entries of {filteredData.length} total records
              </p>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default HistoricalTrackingCharts;
