import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

interface DataExportImportProps {
  darkMode: boolean;
  userData: {
    currentAge: number;
    retirementAge: number;
    currentSavings: number;
    monthlyIncome: number;
    monthlyExpenses: number;
    expenses: any[];
    savingsGoals: any[];
    investments: any[];
    additionalIncomes: any[];
  };
  onImportData: (data: any) => void;
}

interface ExportFormat {
  id: string;
  name: string;
  description: string;
  extension: string;
  icon: string;
}

const DataExportImport: React.FC<DataExportImportProps> = ({
  darkMode,
  userData,
  onImportData,
}) => {
  const { t } = useTranslation();
  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [importError, setImportError] = useState<string | null>(null);
  const [importSuccess, setImportSuccess] = useState<string | null>(null);

  const exportFormats: ExportFormat[] = [
    {
      id: 'json',
      name: 'JSON Backup',
      description: 'Complete data backup with all settings and history',
      extension: 'json',
      icon: '📄',
    },
    {
      id: 'csv',
      name: 'CSV Summary',
      description: 'Financial summary in spreadsheet format',
      extension: 'csv',
      icon: '📊',
    },
    {
      id: 'pdf',
      name: 'PDF Report',
      description: 'Comprehensive financial report (coming soon)',
      extension: 'pdf',
      icon: '📋',
    },
    {
      id: 'excel',
      name: 'Excel Workbook',
      description: 'Detailed analysis with charts and tables (coming soon)',
      extension: 'xlsx',
      icon: '📈',
    },
  ];

  // Export to JSON
  const exportToJson = () => {
    const exportData = {
      metadata: {
        exportDate: new Date().toISOString(),
        version: '1.0',
        application: 'Swiss FIRE Calculator',
        description: 'Complete financial data backup',
      },
      userData: {
        ...userData,
        exportTimestamp: new Date().toISOString(),
      },
      settings: {
        darkMode,
        language: 'en', // Could be dynamic
        currency: 'CHF',
      },
      calculations: {
        savingsRate:
          ((userData.monthlyIncome - userData.monthlyExpenses) /
            userData.monthlyIncome) *
          100,
        fireTarget: userData.monthlyExpenses * 12 * 25,
        yearsToRetirement: userData.retirementAge - userData.currentAge,
        emergencyFundMonths: userData.currentSavings / userData.monthlyExpenses,
      },
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json',
    });
    downloadFile(
      blob,
      `swiss-fire-backup-${new Date().toISOString().split('T')[0]}.json`,
    );
  };

  // Export to CSV
  const exportToCsv = () => {
    const csvData = [
      ['Metric', 'Value', 'Unit'],
      ['Export Date', new Date().toLocaleDateString('de-CH'), ''],
      ['Current Age', userData.currentAge.toString(), 'years'],
      ['Retirement Age', userData.retirementAge.toString(), 'years'],
      [
        'Years to Retirement',
        (userData.retirementAge - userData.currentAge).toString(),
        'years',
      ],
      ['Current Savings', userData.currentSavings.toString(), 'CHF'],
      ['Monthly Income', userData.monthlyIncome.toString(), 'CHF'],
      ['Monthly Expenses', userData.monthlyExpenses.toString(), 'CHF'],
      [
        'Monthly Savings',
        (userData.monthlyIncome - userData.monthlyExpenses).toString(),
        'CHF',
      ],
      [
        'Savings Rate',
        (
          ((userData.monthlyIncome - userData.monthlyExpenses) /
            userData.monthlyIncome) *
          100
        ).toFixed(2),
        '%',
      ],
      ['FIRE Target', (userData.monthlyExpenses * 12 * 25).toString(), 'CHF'],
      [
        'Emergency Fund Coverage',
        (userData.currentSavings / userData.monthlyExpenses).toFixed(1),
        'months',
      ],
      ['', '', ''],
      ['Expense Categories', '', ''],
      ...userData.expenses.map(expense => [
        expense.name,
        expense.amount.toString(),
        expense.frequency === 'monthly' ? 'CHF/month' : 'CHF/year',
      ]),
      ['', '', ''],
      ['Savings Goals', '', ''],
      ...userData.savingsGoals.map(goal => [
        goal.name,
        goal.targetAmount?.toString() || '0',
        'CHF',
      ]),
      ['', '', ''],
      ['Investments', '', ''],
      ...userData.investments.map(investment => [
        investment.name,
        investment.currentValue?.toString() || '0',
        'CHF',
      ]),
      ['', '', ''],
      ['Additional Income', '', ''],
      ...userData.additionalIncomes.map(income => [
        income.name,
        income.amount?.toString() || '0',
        income.frequency === 'monthly' ? 'CHF/month' : 'CHF/year',
      ]),
    ];

    const csvContent = csvData
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    downloadFile(
      blob,
      `swiss-fire-summary-${new Date().toISOString().split('T')[0]}.csv`,
    );
  };

  // Download file helper
  const downloadFile = (blob: Blob, filename: string) => {
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  // Handle export
  const handleExport = async (format: string) => {
    setIsExporting(true);
    try {
      switch (format) {
        case 'json':
          exportToJson();
          break;
        case 'csv':
          exportToCsv();
          break;
        case 'pdf':
        case 'excel':
          alert('This format is coming soon!');
          break;
        default:
          throw new Error('Unsupported export format');
      }
    } catch (error) {
      console.error('Export failed:', error);
      alert('Export failed. Please try again.');
    } finally {
      setIsExporting(false);
    }
  };

  // Handle file import
  const handleFileImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsImporting(true);
    setImportError(null);
    setImportSuccess(null);

    const reader = new FileReader();
    reader.onload = e => {
      try {
        const content = e.target?.result as string;

        if (file.name.endsWith('.json')) {
          const data = JSON.parse(content);

          // Validate data structure
          if (!data.userData) {
            throw new Error('Invalid backup file: missing userData');
          }

          // Apply imported data
          onImportData(data.userData);
          setImportSuccess(`Successfully imported data from ${file.name}`);
        } else if (file.name.endsWith('.csv')) {
          // Basic CSV import (simplified)
          alert(
            'CSV import is not yet supported. Please use JSON backup files.',
          );
        } else {
          throw new Error(
            'Unsupported file format. Please use JSON backup files.',
          );
        }
      } catch (error) {
        console.error('Import failed:', error);
        setImportError(
          error instanceof Error ? error.message : 'Import failed',
        );
      } finally {
        setIsImporting(false);
        // Reset file input
        event.target.value = '';
      }
    };

    reader.onerror = () => {
      setImportError('Failed to read file');
      setIsImporting(false);
    };

    reader.readAsText(file);
  };

  // Generate sample data for testing
  const generateSampleData = () => {
    const sampleData = {
      currentAge: 30,
      retirementAge: 65,
      currentSavings: 100000,
      monthlyIncome: 8000,
      monthlyExpenses: 5000,
      expenses: [
        {
          id: '1',
          name: 'Rent',
          amount: 2000,
          frequency: 'monthly',
          category: 'housing',
          isActive: true,
        },
        {
          id: '2',
          name: 'Food',
          amount: 800,
          frequency: 'monthly',
          category: 'food',
          isActive: true,
        },
        {
          id: '3',
          name: 'Transport',
          amount: 300,
          frequency: 'monthly',
          category: 'transport',
          isActive: true,
        },
      ],
      savingsGoals: [
        {
          id: '1',
          name: 'Emergency Fund',
          targetAmount: 30000,
          currentAmount: 15000,
          isActive: true,
        },
        {
          id: '2',
          name: 'Pillar 3a',
          targetAmount: 7056,
          currentAmount: 5000,
          isActive: true,
        },
      ],
      investments: [
        {
          id: '1',
          name: 'Swiss ETF',
          currentValue: 50000,
          purchasePrice: 45000,
          isActive: true,
        },
        {
          id: '2',
          name: 'Global Stocks',
          currentValue: 30000,
          purchasePrice: 28000,
          isActive: true,
        },
      ],
      additionalIncomes: [
        {
          id: '1',
          name: 'Freelance Work',
          amount: 1000,
          frequency: 'monthly',
          isActive: true,
        },
      ],
    };

    onImportData(sampleData);
    setImportSuccess('Sample data loaded successfully!');
  };

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div>
        <h2 className='text-2xl font-bold mb-2'>💾 Data Export & Import</h2>
        <p className='text-gray-600 dark:text-gray-400'>
          Backup your financial data or import from other sources
        </p>
      </div>

      {/* Export Section */}
      <div
        className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
      >
        <h3 className='text-lg font-semibold mb-4'>📤 Export Data</h3>
        <p className='text-sm text-gray-600 dark:text-gray-400 mb-4'>
          Download your financial data in various formats for backup or analysis
        </p>

        <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
          {exportFormats.map(format => (
            <div
              key={format.id}
              className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                darkMode
                  ? 'border-gray-700 hover:border-gray-600 hover:bg-gray-700'
                  : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
              } ${format.id === 'pdf' || format.id === 'excel' ? 'opacity-50' : ''}`}
              onClick={() => handleExport(format.id)}
            >
              <div className='flex items-start gap-3'>
                <span className='text-2xl'>{format.icon}</span>
                <div className='flex-1'>
                  <h4 className='font-semibold'>{format.name}</h4>
                  <p className='text-sm text-gray-600 dark:text-gray-400'>
                    {format.description}
                  </p>
                  {(format.id === 'pdf' || format.id === 'excel') && (
                    <span className='inline-block mt-1 px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded'>
                      Coming Soon
                    </span>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Import Section */}
      <div
        className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
      >
        <h3 className='text-lg font-semibold mb-4'>📥 Import Data</h3>
        <p className='text-sm text-gray-600 dark:text-gray-400 mb-4'>
          Restore from backup or import data from other financial planning tools
        </p>

        {/* File Upload */}
        <div className='mb-4'>
          <label
            className={`block w-full p-6 border-2 border-dashed rounded-lg cursor-pointer transition-colors ${
              darkMode
                ? 'border-gray-600 hover:border-gray-500 hover:bg-gray-700'
                : 'border-gray-300 hover:border-gray-400 hover:bg-gray-50'
            }`}
          >
            <div className='text-center'>
              <span className='text-4xl mb-2 block'>📁</span>
              <span className='text-lg font-medium'>Choose file to import</span>
              <p className='text-sm text-gray-500 mt-1'>
                Supports JSON backup files (.json)
              </p>
            </div>
            <input
              type='file'
              accept='.json,.csv'
              onChange={handleFileImport}
              className='hidden'
              disabled={isImporting}
            />
          </label>
        </div>

        {/* Import Status */}
        {isImporting && (
          <div className='mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg'>
            <div className='flex items-center gap-2'>
              <div className='animate-spin w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full'></div>
              <span className='text-blue-800 dark:text-blue-200'>
                Importing data...
              </span>
            </div>
          </div>
        )}

        {importError && (
          <div className='mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg'>
            <div className='text-red-800 dark:text-red-200'>
              <strong>Import Error:</strong> {importError}
            </div>
          </div>
        )}

        {importSuccess && (
          <div className='mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg'>
            <div className='text-green-800 dark:text-green-200'>
              <strong>Success:</strong> {importSuccess}
            </div>
          </div>
        )}

        {/* Sample Data */}
        <div className='border-t pt-4'>
          <h4 className='font-medium mb-2'>🧪 Try Sample Data</h4>
          <p className='text-sm text-gray-600 dark:text-gray-400 mb-3'>
            Load sample financial data to explore the application features
          </p>
          <button
            onClick={generateSampleData}
            className='px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors'
          >
            Load Sample Data
          </button>
        </div>
      </div>

      {/* Data Summary */}
      <div
        className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
      >
        <h3 className='text-lg font-semibold mb-4'>📊 Current Data Summary</h3>

        <div className='grid grid-cols-2 md:grid-cols-4 gap-4 text-sm'>
          <div>
            <span className='text-gray-600 dark:text-gray-400'>Expenses:</span>
            <div className='font-semibold'>
              {userData.expenses?.length || 0} items
            </div>
          </div>
          <div>
            <span className='text-gray-600 dark:text-gray-400'>
              Savings Goals:
            </span>
            <div className='font-semibold'>
              {userData.savingsGoals?.length || 0} goals
            </div>
          </div>
          <div>
            <span className='text-gray-600 dark:text-gray-400'>
              Investments:
            </span>
            <div className='font-semibold'>
              {userData.investments?.length || 0} positions
            </div>
          </div>
          <div>
            <span className='text-gray-600 dark:text-gray-400'>
              Income Sources:
            </span>
            <div className='font-semibold'>
              {userData.additionalIncomes?.length || 0} sources
            </div>
          </div>
        </div>

        <div className='mt-4 text-xs text-gray-500'>
          Last updated: {new Date().toLocaleString('de-CH')}
        </div>
      </div>
    </div>
  );
};

export default DataExportImport;
