import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  SwissCanton,
  TaxCalculationInput,
  CANTON_TAX_DATA,
} from '../types/swiss-tax';
import {
  calculateSwissTax,
  compareCantonTaxes,
  findBestTaxCanton,
  calculateOptimalPillar3a,
} from '../utils/swiss-tax-calculator';
import { useLocalStorage } from '../hooks/useLocalStorage';

interface SwissTaxCalculatorProps {
  darkMode: boolean;
  initialIncome?: number;
  onTaxCalculationChange?: (result: any) => void;
}

const SwissTaxCalculator: React.FC<SwissTaxCalculatorProps> = ({
  darkMode,
  initialIncome = 100000,
  onTaxCalculationChange,
}) => {
  const { t } = useTranslation();

  // Tax calculation inputs with localStorage persistence
  const [taxInput, setTaxInput] = useLocalStorage<TaxCalculationInput>(
    'fire-tax-input',
    {
      grossIncome: initialIncome,
      canton: 'ZH',
      municipality: '',
      maritalStatus: 'single',
      children: 0,
      wealth: 200000,
      pillar3aContribution: 7056,
      professionalExpenses: 2000,
      churchMember: false,
      municipalMultiplier: undefined,
    },
  );

  const [showComparison, setShowComparison] = useState(false);
  const [selectedCantons, setSelectedCantons] = useState<SwissCanton[]>([
    'ZH',
    'ZG',
    'GE',
    'VD',
    'BE',
  ]);

  // Calculate tax result
  const taxResult = useMemo(() => {
    const result = calculateSwissTax(taxInput);
    onTaxCalculationChange?.(result);
    return result;
  }, [taxInput, onTaxCalculationChange]);

  // Calculate canton comparison
  const cantonComparison = useMemo(() => {
    if (!showComparison) return {};
    return compareCantonTaxes(taxInput, selectedCantons);
  }, [taxInput, selectedCantons, showComparison]);

  // Find best tax canton
  const bestCanton = useMemo(() => {
    return findBestTaxCanton(taxInput);
  }, [taxInput]);

  // Calculate optimal Pillar 3a
  const optimalPillar3a = useMemo(() => {
    return calculateOptimalPillar3a(taxInput);
  }, [taxInput]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('de-CH', {
      style: 'currency',
      currency: 'CHF',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (rate: number) => {
    return `${rate.toFixed(2)}%`;
  };

  const updateTaxInput = (updates: Partial<TaxCalculationInput>) => {
    setTaxInput(prev => ({ ...prev, ...updates }));
  };

  const cantonData = CANTON_TAX_DATA[taxInput.canton];

  return (
    <div className='space-y-6'>
      {/* Input Form */}
      <div
        className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800/60' : 'bg-white/80 shadow-sm'}`}
      >
        <h3 className='text-xl font-bold mb-4'>🇨🇭 Swiss Tax Calculator</h3>

        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
          {/* Gross Income */}
          <div>
            <label className='block text-sm font-medium mb-2'>
              Gross Annual Income (CHF)
            </label>
            <input
              type='number'
              value={taxInput.grossIncome}
              onChange={e =>
                updateTaxInput({ grossIncome: parseInt(e.target.value) || 0 })
              }
              className={`w-full px-3 py-2 rounded-lg border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              } focus:outline-none focus:ring-2 focus:ring-blue-500`}
            />
          </div>

          {/* Canton */}
          <div>
            <label className='block text-sm font-medium mb-2'>Canton</label>
            <select
              value={taxInput.canton}
              onChange={e =>
                updateTaxInput({ canton: e.target.value as SwissCanton })
              }
              className={`w-full px-3 py-2 rounded-lg border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              } focus:outline-none focus:ring-2 focus:ring-blue-500`}
            >
              {Object.entries(CANTON_TAX_DATA).map(([code, data]) => (
                <option key={code} value={code}>
                  {code} - {data.name}
                </option>
              ))}
            </select>
          </div>

          {/* Marital Status */}
          <div>
            <label className='block text-sm font-medium mb-2'>
              Marital Status
            </label>
            <select
              value={taxInput.maritalStatus}
              onChange={e =>
                updateTaxInput({ maritalStatus: e.target.value as any })
              }
              className={`w-full px-3 py-2 rounded-lg border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              } focus:outline-none focus:ring-2 focus:ring-blue-500`}
            >
              <option value='single'>Single</option>
              <option value='married'>Married</option>
              <option value='divorced'>Divorced</option>
              <option value='widowed'>Widowed</option>
            </select>
          </div>

          {/* Children */}
          <div>
            <label className='block text-sm font-medium mb-2'>
              Number of Children
            </label>
            <input
              type='number'
              min='0'
              value={taxInput.children}
              onChange={e =>
                updateTaxInput({ children: parseInt(e.target.value) || 0 })
              }
              className={`w-full px-3 py-2 rounded-lg border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              } focus:outline-none focus:ring-2 focus:ring-blue-500`}
            />
          </div>

          {/* Wealth */}
          <div>
            <label className='block text-sm font-medium mb-2'>
              Total Wealth (CHF)
            </label>
            <input
              type='number'
              value={taxInput.wealth}
              onChange={e =>
                updateTaxInput({ wealth: parseInt(e.target.value) || 0 })
              }
              className={`w-full px-3 py-2 rounded-lg border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              } focus:outline-none focus:ring-2 focus:ring-blue-500`}
            />
          </div>

          {/* Pillar 3a */}
          <div>
            <label className='block text-sm font-medium mb-2'>
              Pillar 3a Contribution (CHF)
            </label>
            <input
              type='number'
              max={cantonData.deductions.maxPillar3a}
              value={taxInput.pillar3aContribution}
              onChange={e =>
                updateTaxInput({
                  pillar3aContribution: parseInt(e.target.value) || 0,
                })
              }
              className={`w-full px-3 py-2 rounded-lg border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              } focus:outline-none focus:ring-2 focus:ring-blue-500`}
            />
            <p className='text-xs text-gray-500 mt-1'>
              Max: {formatCurrency(cantonData.deductions.maxPillar3a)}
            </p>
          </div>
        </div>

        {/* Church Member Toggle */}
        <div className='mt-4'>
          <label className='flex items-center space-x-2'>
            <input
              type='checkbox'
              checked={taxInput.churchMember}
              onChange={e => updateTaxInput({ churchMember: e.target.checked })}
              className='rounded'
            />
            <span className='text-sm'>
              Church member (subject to church tax)
            </span>
          </label>
        </div>
      </div>

      {/* Tax Results */}
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
        <div
          className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}
        >
          <h4 className='text-sm font-medium text-gray-500 dark:text-gray-400'>
            Total Tax
          </h4>
          <p className='text-2xl font-bold text-red-600'>
            {formatCurrency(taxResult.totalTax)}
          </p>
          <p className='text-sm text-gray-500'>
            {formatPercentage(taxResult.effectiveTaxRate)} effective
          </p>
        </div>

        <div
          className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}
        >
          <h4 className='text-sm font-medium text-gray-500 dark:text-gray-400'>
            Net Income
          </h4>
          <p className='text-2xl font-bold text-green-600'>
            {formatCurrency(taxResult.netIncome)}
          </p>
          <p className='text-sm text-gray-500'>After all taxes</p>
        </div>

        <div
          className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}
        >
          <h4 className='text-sm font-medium text-gray-500 dark:text-gray-400'>
            Marginal Rate
          </h4>
          <p className='text-2xl font-bold text-orange-600'>
            {formatPercentage(taxResult.marginalTaxRate)}
          </p>
          <p className='text-sm text-gray-500'>Next CHF 1,000</p>
        </div>

        <div
          className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}
        >
          <h4 className='text-sm font-medium text-gray-500 dark:text-gray-400'>
            Wealth Tax
          </h4>
          <p className='text-2xl font-bold text-purple-600'>
            {formatCurrency(taxResult.wealthTax)}
          </p>
          <p className='text-sm text-gray-500'>
            {cantonData.wealthTaxRate}‰ rate
          </p>
        </div>
      </div>

      {/* Tax Breakdown */}
      <div
        className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800/60' : 'bg-white/80 shadow-sm'}`}
      >
        <h4 className='text-lg font-semibold mb-4'>Tax Breakdown</h4>

        <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
          {/* Tax Components */}
          <div>
            <h5 className='font-medium mb-3'>Tax Components</h5>
            <div className='space-y-2'>
              <div className='flex justify-between'>
                <span>Federal Tax:</span>
                <span className='font-medium'>
                  {formatCurrency(taxResult.federalTax)}
                </span>
              </div>
              <div className='flex justify-between'>
                <span>Cantonal Tax:</span>
                <span className='font-medium'>
                  {formatCurrency(taxResult.cantonalTax)}
                </span>
              </div>
              <div className='flex justify-between'>
                <span>Municipal Tax:</span>
                <span className='font-medium'>
                  {formatCurrency(taxResult.municipalTax)}
                </span>
              </div>
              {taxResult.churchTax > 0 && (
                <div className='flex justify-between'>
                  <span>Church Tax:</span>
                  <span className='font-medium'>
                    {formatCurrency(taxResult.churchTax)}
                  </span>
                </div>
              )}
              <div className='flex justify-between'>
                <span>Wealth Tax:</span>
                <span className='font-medium'>
                  {formatCurrency(taxResult.wealthTax)}
                </span>
              </div>
              <div className='flex justify-between border-t pt-2 font-semibold'>
                <span>Total:</span>
                <span>{formatCurrency(taxResult.totalTax)}</span>
              </div>
            </div>
          </div>

          {/* Deductions */}
          <div>
            <h5 className='font-medium mb-3'>Deductions</h5>
            <div className='space-y-2'>
              <div className='flex justify-between'>
                <span>Personal Deduction:</span>
                <span className='font-medium'>
                  {formatCurrency(taxResult.breakdown.deductions.personal)}
                </span>
              </div>
              {taxResult.breakdown.deductions.married > 0 && (
                <div className='flex justify-between'>
                  <span>Married Deduction:</span>
                  <span className='font-medium'>
                    {formatCurrency(taxResult.breakdown.deductions.married)}
                  </span>
                </div>
              )}
              {taxResult.breakdown.deductions.children > 0 && (
                <div className='flex justify-between'>
                  <span>Children Deduction:</span>
                  <span className='font-medium'>
                    {formatCurrency(taxResult.breakdown.deductions.children)}
                  </span>
                </div>
              )}
              <div className='flex justify-between'>
                <span>Pillar 3a:</span>
                <span className='font-medium'>
                  {formatCurrency(taxResult.breakdown.deductions.pillar3a)}
                </span>
              </div>
              <div className='flex justify-between'>
                <span>Professional Expenses:</span>
                <span className='font-medium'>
                  {formatCurrency(taxResult.breakdown.deductions.professional)}
                </span>
              </div>
              <div className='flex justify-between border-t pt-2 font-semibold'>
                <span>Total Deductions:</span>
                <span>
                  {formatCurrency(taxResult.breakdown.deductions.total)}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Optimization Recommendations */}
      <div
        className={`p-6 rounded-xl ${darkMode ? 'bg-blue-900/20 border-blue-600' : 'bg-blue-50 border-blue-300'} border`}
      >
        <h4 className='text-lg font-semibold mb-4 text-blue-800 dark:text-blue-200'>
          💡 Tax Optimization
        </h4>

        <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
          {/* Pillar 3a Optimization */}
          <div>
            <h5 className='font-medium mb-2'>Pillar 3a Optimization</h5>
            <p className='text-sm text-blue-700 dark:text-blue-300 mb-2'>
              Optimal contribution:{' '}
              {formatCurrency(optimalPillar3a.optimalContribution)}
            </p>
            <p className='text-sm text-blue-700 dark:text-blue-300 mb-2'>
              Tax savings: {formatCurrency(optimalPillar3a.taxSavings)}
            </p>
            <p className='text-sm text-blue-700 dark:text-blue-300'>
              Net benefit: {formatCurrency(optimalPillar3a.netBenefit)}
            </p>
          </div>

          {/* Best Canton */}
          <div>
            <h5 className='font-medium mb-2'>Best Tax Canton</h5>
            <p className='text-sm text-blue-700 dark:text-blue-300 mb-2'>
              Lowest taxes: {bestCanton.canton} -{' '}
              {CANTON_TAX_DATA[bestCanton.canton].name}
            </p>
            <p className='text-sm text-blue-700 dark:text-blue-300 mb-2'>
              Total tax: {formatCurrency(bestCanton.result.totalTax)}
            </p>
            <p className='text-sm text-blue-700 dark:text-blue-300'>
              Potential savings: {formatCurrency(bestCanton.savings)}
            </p>
          </div>
        </div>
      </div>

      {/* Canton Comparison Toggle */}
      <div className='flex justify-center'>
        <button
          onClick={() => setShowComparison(!showComparison)}
          className='px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors'
        >
          {showComparison ? 'Hide' : 'Show'} Canton Comparison
        </button>
      </div>

      {/* Canton Comparison Table */}
      {showComparison && (
        <div
          className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800/60' : 'bg-white/80 shadow-sm'}`}
        >
          <h4 className='text-lg font-semibold mb-4'>Canton Tax Comparison</h4>

          <div className='overflow-x-auto'>
            <table className='w-full text-sm'>
              <thead>
                <tr
                  className={`border-b ${darkMode ? 'border-gray-600' : 'border-gray-300'}`}
                >
                  <th className='text-left py-2'>Canton</th>
                  <th className='text-right py-2'>Total Tax</th>
                  <th className='text-right py-2'>Effective Rate</th>
                  <th className='text-right py-2'>Net Income</th>
                  <th className='text-right py-2'>Difference</th>
                </tr>
              </thead>
              <tbody>
                {Object.entries(cantonComparison)
                  .sort(([, a], [, b]) => a.totalTax - b.totalTax)
                  .map(([canton, result]) => (
                    <tr
                      key={canton}
                      className={`border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
                    >
                      <td className='py-2 font-medium'>
                        {canton} - {CANTON_TAX_DATA[canton as SwissCanton].name}
                      </td>
                      <td className='text-right py-2'>
                        {formatCurrency(result.totalTax)}
                      </td>
                      <td className='text-right py-2'>
                        {formatPercentage(result.effectiveTaxRate)}
                      </td>
                      <td className='text-right py-2'>
                        {formatCurrency(result.netIncome)}
                      </td>
                      <td className='text-right py-2'>
                        <span
                          className={
                            result.totalTax ===
                            Math.min(
                              ...Object.values(cantonComparison).map(
                                r => r.totalTax,
                              ),
                            )
                              ? 'text-green-600'
                              : 'text-red-600'
                          }
                        >
                          {result.totalTax ===
                          Math.min(
                            ...Object.values(cantonComparison).map(
                              r => r.totalTax,
                            ),
                          )
                            ? 'Best'
                            : `+${formatCurrency(result.totalTax - Math.min(...Object.values(cantonComparison).map(r => r.totalTax)))}`}
                        </span>
                      </td>
                    </tr>
                  ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};

export default SwissTaxCalculator;
