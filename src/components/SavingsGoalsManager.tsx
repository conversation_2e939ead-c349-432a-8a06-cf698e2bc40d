import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocalStorage } from '../hooks/useLocalStorage';
import {
  calculateMonthsToCompletion,
  calculateProgress,
  calculateProjectedCompletionDate,
  generateSavingsRecommendations,
  getGoalTypeInfo,
  SavingsGoal,
  SavingsGoalPriority,
  SavingsGoalStatus,
  SavingsGoalType,
  SWISS_SAVINGS_GOAL_TYPES,
} from '../types/savings';

interface SavingsGoalsManagerProps {
  darkMode: boolean;
  monthlyIncome: number;
  monthlyExpenses: number;
  onGoalsChange?: (goals: SavingsGoal[]) => void;
}

const SavingsGoalsManager: React.FC<SavingsGoalsManagerProps> = ({
  darkMode,
  monthlyIncome,
  monthlyExpenses,
  onGoalsChange,
}) => {
  const { t } = useTranslation();

  // Load savings goals from localStorage
  const [savingsGoals, setSavingsGoals] = useLocalStorage<SavingsGoal[]>(
    'fire-savings-goals',
    [
      {
        id: '1',
        type: 'emergency_fund',
        name: 'Emergency Fund',
        targetAmount: 15000,
        currentAmount: 5000,
        monthlyContribution: 500,
        priority: 'critical',
        status: 'active',
        autoContribute: true,
        swissSpecific: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: '2',
        type: 'pillar_3a',
        name: 'Pillar 3a Retirement',
        targetAmount: 7056,
        currentAmount: 2000,
        monthlyContribution: 588,
        priority: 'critical',
        status: 'active',
        autoContribute: true,
        swissSpecific: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    ]
  );

  const [showAddForm, setShowAddForm] = useState(false);
  const [editingGoal, setEditingGoal] = useState<SavingsGoal | null>(null);
  const [filterStatus, setFilterStatus] = useState<SavingsGoalStatus | 'all'>(
    'all'
  );
  const [filterPriority, setFilterPriority] = useState<
    SavingsGoalPriority | 'all'
  >('all');

  // Form state
  const [formData, setFormData] = useState({
    type: 'other' as SavingsGoalType,
    name: '',
    description: '',
    targetAmount: '',
    currentAmount: '',
    monthlyContribution: '',
    priority: 'medium' as SavingsGoalPriority,
    targetDate: '',
    autoContribute: true,
  });

  // Calculate savings analysis
  const savingsAnalysis = useMemo(() => {
    const activeGoals = savingsGoals.filter(g => g.status === 'active');

    const totalMonthlyContributions = activeGoals.reduce(
      (sum, goal) => sum + goal.monthlyContribution,
      0
    );

    const totalTargetAmount = activeGoals.reduce(
      (sum, goal) => sum + goal.targetAmount,
      0
    );

    const totalCurrentAmount = activeGoals.reduce(
      (sum, goal) => sum + goal.currentAmount,
      0
    );

    const overallProgress =
      totalTargetAmount > 0
        ? (totalCurrentAmount / totalTargetAmount) * 100
        : 0;

    const availableForSavings = monthlyIncome - monthlyExpenses;
    const monthlyShortfall = Math.max(
      0,
      totalMonthlyContributions - availableForSavings
    );

    const priorityBreakdown = activeGoals.reduce(
      (acc, goal) => {
        acc[goal.priority] =
          (acc[goal.priority] || 0) + goal.monthlyContribution;
        return acc;
      },
      {} as Record<SavingsGoalPriority, number>
    );

    const typeBreakdown = activeGoals.reduce(
      (acc, goal) => {
        acc[goal.type] = (acc[goal.type] || 0) + goal.monthlyContribution;
        return acc;
      },
      {} as Record<SavingsGoalType, number>
    );

    const recommendations = generateSavingsRecommendations(
      savingsGoals,
      monthlyIncome,
      monthlyExpenses
    );

    // Calculate projected completion date (earliest goal completion)
    const completionDates = activeGoals
      .map(goal => calculateProjectedCompletionDate(goal))
      .filter(date => date !== null)
      .sort((a, b) => a!.getTime() - b!.getTime());

    const projectedCompletionDate =
      completionDates.length > 0 ? completionDates[0]!.toISOString() : null;

    return {
      totalMonthlyContributions,
      totalTargetAmount,
      totalCurrentAmount,
      overallProgress,
      projectedCompletionDate,
      monthlyShortfall,
      priorityBreakdown,
      typeBreakdown,
      recommendations,
    };
  }, [savingsGoals, monthlyIncome, monthlyExpenses]);

  // Filtered goals
  const filteredGoals = useMemo(() => {
    return savingsGoals.filter(goal => {
      const statusMatch =
        filterStatus === 'all' || goal.status === filterStatus;
      const priorityMatch =
        filterPriority === 'all' || goal.priority === filterPriority;
      return statusMatch && priorityMatch;
    });
  }, [savingsGoals, filterStatus, filterPriority]);

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const goalData: SavingsGoal = {
      id: editingGoal?.id || Date.now().toString(),
      type: formData.type,
      name: formData.name,
      description: formData.description || undefined,
      targetAmount: parseFloat(formData.targetAmount) || 0,
      currentAmount: parseFloat(formData.currentAmount) || 0,
      monthlyContribution: parseFloat(formData.monthlyContribution) || 0,
      priority: formData.priority,
      status: 'active',
      targetDate: formData.targetDate || undefined,
      autoContribute: formData.autoContribute,
      swissSpecific: getGoalTypeInfo(formData.type).swissSpecific,
      createdAt: editingGoal?.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    if (editingGoal) {
      const updatedGoals = savingsGoals.map(g =>
        g.id === editingGoal.id ? goalData : g
      );
      setSavingsGoals(updatedGoals);
      onGoalsChange?.(updatedGoals);
    } else {
      const newGoals = [...savingsGoals, goalData];
      setSavingsGoals(newGoals);
      onGoalsChange?.(newGoals);
    }

    // Reset form
    setFormData({
      type: 'other',
      name: '',
      description: '',
      targetAmount: '',
      currentAmount: '',
      monthlyContribution: '',
      priority: 'medium',
      targetDate: '',
      autoContribute: true,
    });
    setShowAddForm(false);
    setEditingGoal(null);
  };

  // Handle edit
  const handleEdit = (goal: SavingsGoal) => {
    setEditingGoal(goal);
    setFormData({
      type: goal.type,
      name: goal.name,
      description: goal.description || '',
      targetAmount: goal.targetAmount.toString(),
      currentAmount: goal.currentAmount.toString(),
      monthlyContribution: goal.monthlyContribution.toString(),
      priority: goal.priority,
      targetDate: goal.targetDate || '',
      autoContribute: goal.autoContribute,
    });
    setShowAddForm(true);
  };

  // Handle delete
  const handleDelete = (goalId: string) => {
    const updatedGoals = savingsGoals.filter(g => g.id !== goalId);
    setSavingsGoals(updatedGoals);
    onGoalsChange?.(updatedGoals);
  };

  // Handle status change
  const handleStatusChange = (goalId: string, newStatus: SavingsGoalStatus) => {
    const updatedGoals = savingsGoals.map(g =>
      g.id === goalId
        ? {
            ...g,
            status: newStatus,
            completedAt:
              newStatus === 'completed' ? new Date().toISOString() : undefined,
            updatedAt: new Date().toISOString(),
          }
        : g
    );
    setSavingsGoals(updatedGoals);
    onGoalsChange?.(updatedGoals);
  };

  // Handle contribution update
  const handleContributionUpdate = (goalId: string, newAmount: number) => {
    const updatedGoals = savingsGoals.map(g =>
      g.id === goalId
        ? {
            ...g,
            currentAmount: newAmount,
            updatedAt: new Date().toISOString(),
          }
        : g
    );
    setSavingsGoals(updatedGoals);
    onGoalsChange?.(updatedGoals);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('de-CH', {
      style: 'currency',
      currency: 'CHF',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getPriorityColor = (priority: SavingsGoalPriority) => {
    switch (priority) {
      case 'critical':
        return 'text-red-600 bg-red-100 dark:bg-red-900/30';
      case 'high':
        return 'text-orange-600 bg-orange-100 dark:bg-orange-900/30';
      case 'medium':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30';
      case 'low':
        return 'text-green-600 bg-green-100 dark:bg-green-900/30';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30';
    }
  };

  const getStatusColor = (status: SavingsGoalStatus) => {
    switch (status) {
      case 'active':
        return 'text-blue-600 bg-blue-100 dark:bg-blue-900/30';
      case 'completed':
        return 'text-green-600 bg-green-100 dark:bg-green-900/30';
      case 'paused':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30';
      case 'cancelled':
        return 'text-red-600 bg-red-100 dark:bg-red-900/30';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30';
    }
  };

  return (
    <div className='space-y-6'>
      {/* Summary Cards */}
      <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
        <div
          className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}
        >
          <h3 className='text-sm font-medium text-gray-500 dark:text-gray-400'>
            Total Target
          </h3>
          <p className='text-2xl font-bold text-blue-600'>
            {formatCurrency(savingsAnalysis.totalTargetAmount)}
          </p>
        </div>
        <div
          className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}
        >
          <h3 className='text-sm font-medium text-gray-500 dark:text-gray-400'>
            Current Saved
          </h3>
          <p className='text-2xl font-bold text-green-600'>
            {formatCurrency(savingsAnalysis.totalCurrentAmount)}
          </p>
        </div>
        <div
          className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}
        >
          <h3 className='text-sm font-medium text-gray-500 dark:text-gray-400'>
            Monthly Contributions
          </h3>
          <p className='text-2xl font-bold text-purple-600'>
            {formatCurrency(savingsAnalysis.totalMonthlyContributions)}
          </p>
        </div>
        <div
          className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}
        >
          <h3 className='text-sm font-medium text-gray-500 dark:text-gray-400'>
            Overall Progress
          </h3>
          <p className='text-2xl font-bold text-orange-600'>
            {savingsAnalysis.overallProgress.toFixed(1)}%
          </p>
        </div>
      </div>

      {/* Recommendations */}
      {savingsAnalysis.recommendations.length > 0 && (
        <div
          className={`p-4 rounded-lg ${darkMode ? 'bg-yellow-900/20 border-yellow-600' : 'bg-yellow-50 border-yellow-300'} border`}
        >
          <h3 className='text-lg font-semibold mb-3 text-yellow-800 dark:text-yellow-200'>
            💡 Recommendations
          </h3>
          <div className='space-y-2'>
            {savingsAnalysis.recommendations.slice(0, 3).map(rec => (
              <div key={rec.id} className='flex items-start space-x-3'>
                <span className='text-yellow-600'>•</span>
                <div>
                  <p className='font-medium text-yellow-800 dark:text-yellow-200'>
                    {rec.title}
                  </p>
                  <p className='text-sm text-yellow-700 dark:text-yellow-300'>
                    {rec.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Monthly Shortfall Warning */}
      {savingsAnalysis.monthlyShortfall > 0 && (
        <div
          className={`p-4 rounded-lg ${darkMode ? 'bg-red-900/20 border-red-600' : 'bg-red-50 border-red-300'} border`}
        >
          <h3 className='text-lg font-semibold mb-2 text-red-800 dark:text-red-200'>
            ⚠️ Budget Alert
          </h3>
          <p className='text-red-700 dark:text-red-300'>
            Your savings goals require{' '}
            {formatCurrency(savingsAnalysis.monthlyShortfall)} more than your
            available budget. Consider adjusting your goals or increasing your
            income.
          </p>
        </div>
      )}

      {/* Controls */}
      <div className='flex flex-wrap gap-4 items-center justify-between'>
        <div className='flex gap-4'>
          {/* Status Filter */}
          <select
            value={filterStatus}
            onChange={e =>
              setFilterStatus(e.target.value as SavingsGoalStatus | 'all')
            }
            className={`px-3 py-2 rounded-lg border ${
              darkMode
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            } focus:outline-none focus:ring-2 focus:ring-blue-500`}
          >
            <option value='all'>All Status</option>
            <option value='active'>Active</option>
            <option value='completed'>Completed</option>
            <option value='paused'>Paused</option>
            <option value='cancelled'>Cancelled</option>
          </select>

          {/* Priority Filter */}
          <select
            value={filterPriority}
            onChange={e =>
              setFilterPriority(e.target.value as SavingsGoalPriority | 'all')
            }
            className={`px-3 py-2 rounded-lg border ${
              darkMode
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            } focus:outline-none focus:ring-2 focus:ring-blue-500`}
          >
            <option value='all'>All Priorities</option>
            <option value='critical'>Critical</option>
            <option value='high'>High</option>
            <option value='medium'>Medium</option>
            <option value='low'>Low</option>
          </select>
        </div>

        <button
          onClick={() => setShowAddForm(true)}
          className='px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors'
        >
          + Add Savings Goal
        </button>
      </div>

      {/* Add/Edit Form */}
      {showAddForm && (
        <div
          className={`p-6 rounded-lg border ${
            darkMode
              ? 'bg-gray-800 border-gray-600'
              : 'bg-white border-gray-300'
          }`}
        >
          <h3 className='text-lg font-semibold mb-4'>
            {editingGoal ? 'Edit Savings Goal' : 'Add New Savings Goal'}
          </h3>

          <form
            onSubmit={handleSubmit}
            className='grid grid-cols-1 md:grid-cols-2 gap-4'
          >
            {/* Goal Type */}
            <div>
              <label className='block text-sm font-medium mb-2'>
                Goal Type
              </label>
              <select
                value={formData.type}
                onChange={e => {
                  const newType = e.target.value as SavingsGoalType;
                  const typeInfo = getGoalTypeInfo(newType);
                  setFormData({
                    ...formData,
                    type: newType,
                    name: formData.name || typeInfo.name,
                    targetAmount:
                      formData.targetAmount ||
                      typeInfo.suggestedAmount?.toString() ||
                      '',
                    priority: typeInfo.defaultPriority,
                  });
                }}
                className={`w-full px-3 py-2 rounded-lg border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                required
              >
                {SWISS_SAVINGS_GOAL_TYPES.map(type => (
                  <option key={type.id} value={type.id}>
                    {type.icon} {type.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Goal Name */}
            <div>
              <label className='block text-sm font-medium mb-2'>
                Goal Name
              </label>
              <input
                type='text'
                value={formData.name}
                onChange={e =>
                  setFormData({ ...formData, name: e.target.value })
                }
                className={`w-full px-3 py-2 rounded-lg border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                required
              />
            </div>

            {/* Target Amount */}
            <div>
              <label className='block text-sm font-medium mb-2'>
                Target Amount (CHF)
              </label>
              <input
                type='number'
                step='0.01'
                value={formData.targetAmount}
                onChange={e =>
                  setFormData({ ...formData, targetAmount: e.target.value })
                }
                className={`w-full px-3 py-2 rounded-lg border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                required
              />
            </div>

            {/* Current Amount */}
            <div>
              <label className='block text-sm font-medium mb-2'>
                Current Amount (CHF)
              </label>
              <input
                type='number'
                step='0.01'
                value={formData.currentAmount}
                onChange={e =>
                  setFormData({ ...formData, currentAmount: e.target.value })
                }
                className={`w-full px-3 py-2 rounded-lg border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                required
              />
            </div>

            {/* Monthly Contribution */}
            <div>
              <label className='block text-sm font-medium mb-2'>
                Monthly Contribution (CHF)
              </label>
              <input
                type='number'
                step='0.01'
                value={formData.monthlyContribution}
                onChange={e =>
                  setFormData({
                    ...formData,
                    monthlyContribution: e.target.value,
                  })
                }
                className={`w-full px-3 py-2 rounded-lg border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                required
              />
            </div>

            {/* Priority */}
            <div>
              <label className='block text-sm font-medium mb-2'>Priority</label>
              <select
                value={formData.priority}
                onChange={e =>
                  setFormData({
                    ...formData,
                    priority: e.target.value as SavingsGoalPriority,
                  })
                }
                className={`w-full px-3 py-2 rounded-lg border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                required
              >
                <option value='critical'>Critical</option>
                <option value='high'>High</option>
                <option value='medium'>Medium</option>
                <option value='low'>Low</option>
              </select>
            </div>

            {/* Form Actions */}
            <div className='md:col-span-2 flex gap-4 pt-4'>
              <button
                type='submit'
                className='px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors'
              >
                {editingGoal ? 'Update' : 'Add'} Goal
              </button>
              <button
                type='button'
                onClick={() => {
                  setShowAddForm(false);
                  setEditingGoal(null);
                  setFormData({
                    type: 'other',
                    name: '',
                    description: '',
                    targetAmount: '',
                    currentAmount: '',
                    monthlyContribution: '',
                    priority: 'medium',
                    targetDate: '',
                    autoContribute: true,
                  });
                }}
                className={`px-6 py-2 rounded-lg font-medium transition-colors ${
                  darkMode
                    ? 'bg-gray-700 hover:bg-gray-600 text-white'
                    : 'bg-gray-200 hover:bg-gray-300 text-gray-800'
                }`}
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Goals List */}
      <div className='space-y-4'>
        {filteredGoals.map(goal => {
          const typeInfo = getGoalTypeInfo(goal.type);
          const progress = calculateProgress(goal);
          const monthsToCompletion = calculateMonthsToCompletion(goal);

          return (
            <div
              key={goal.id}
              className={`p-6 rounded-lg border ${
                darkMode
                  ? 'bg-gray-800 border-gray-600'
                  : 'bg-white border-gray-300'
              }`}
            >
              <div className='flex items-start justify-between mb-4'>
                <div className='flex items-center space-x-4'>
                  <span className='text-3xl'>{typeInfo.icon}</span>
                  <div>
                    <h4 className='text-lg font-semibold'>{goal.name}</h4>
                    <p className='text-sm text-gray-500'>{typeInfo.name}</p>
                    {goal.description && (
                      <p className='text-sm text-gray-600 dark:text-gray-400 mt-1'>
                        {goal.description}
                      </p>
                    )}
                  </div>
                </div>

                <div className='flex items-center space-x-2'>
                  <span
                    className={`px-3 py-1 rounded-full text-xs font-medium ${getPriorityColor(goal.priority)}`}
                  >
                    {goal.priority}
                  </span>
                  <span
                    className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(goal.status)}`}
                  >
                    {goal.status}
                  </span>
                </div>
              </div>

              {/* Progress Bar */}
              <div className='mb-4'>
                <div className='flex justify-between text-sm mb-2'>
                  <span>Progress: {progress.toFixed(1)}%</span>
                  <span>
                    {formatCurrency(goal.currentAmount)} /{' '}
                    {formatCurrency(goal.targetAmount)}
                  </span>
                </div>
                <div className='w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3'>
                  <div
                    className='bg-blue-600 h-3 rounded-full transition-all duration-300'
                    style={{ width: `${Math.min(100, progress)}%` }}
                  ></div>
                </div>
              </div>

              {/* Goal Details */}
              <div className='grid grid-cols-2 md:grid-cols-4 gap-4 text-sm'>
                <div>
                  <span className='text-gray-500'>Monthly Contribution:</span>
                  <p className='font-semibold'>
                    {formatCurrency(goal.monthlyContribution)}
                  </p>
                </div>
                <div>
                  <span className='text-gray-500'>Remaining:</span>
                  <p className='font-semibold'>
                    {formatCurrency(
                      Math.max(0, goal.targetAmount - goal.currentAmount)
                    )}
                  </p>
                </div>
                <div>
                  <span className='text-gray-500'>Time to Complete:</span>
                  <p className='font-semibold'>
                    {monthsToCompletion !== null
                      ? `${monthsToCompletion} months`
                      : 'N/A'}
                  </p>
                </div>
                <div>
                  <span className='text-gray-500'>Auto Contribute:</span>
                  <p className='font-semibold'>
                    {goal.autoContribute ? 'Yes' : 'No'}
                  </p>
                </div>
              </div>

              {/* Actions */}
              <div className='flex justify-between items-center mt-4 pt-4 border-t border-gray-200 dark:border-gray-600'>
                <div className='flex space-x-2'>
                  {goal.status === 'active' && (
                    <>
                      <button
                        onClick={() => handleStatusChange(goal.id, 'completed')}
                        className='px-3 py-1 text-sm bg-green-600 hover:bg-green-700 text-white rounded transition-colors'
                        disabled={progress < 100}
                      >
                        Mark Complete
                      </button>
                      <button
                        onClick={() => handleStatusChange(goal.id, 'paused')}
                        className='px-3 py-1 text-sm bg-yellow-600 hover:bg-yellow-700 text-white rounded transition-colors'
                      >
                        Pause
                      </button>
                    </>
                  )}
                  {goal.status === 'paused' && (
                    <button
                      onClick={() => handleStatusChange(goal.id, 'active')}
                      className='px-3 py-1 text-sm bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors'
                    >
                      Resume
                    </button>
                  )}
                </div>

                <div className='flex space-x-2'>
                  <button
                    onClick={() => handleEdit(goal)}
                    className='p-2 text-blue-600 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-lg transition-colors'
                    title='Edit'
                  >
                    ✏️
                  </button>
                  <button
                    onClick={() => handleDelete(goal.id)}
                    className='p-2 text-red-600 hover:bg-red-100 dark:hover:bg-red-900/30 rounded-lg transition-colors'
                    title='Delete'
                  >
                    🗑️
                  </button>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {filteredGoals.length === 0 && (
        <div className='text-center py-8 text-gray-500'>
          No savings goals found. Add your first goal to start tracking your
          progress!
        </div>
      )}
    </div>
  );
};

export default SavingsGoalsManager;
