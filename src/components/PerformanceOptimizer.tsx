import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

interface PerformanceOptimizerProps {
  darkMode: boolean;
}

interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  memoryUsage: number;
  bundleSize: number;
  cacheHitRate: number;
  errorRate: number;
}

interface OptimizationRecommendation {
  category: 'performance' | 'memory' | 'network' | 'rendering';
  priority: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  impact: string;
  implementation: string;
  estimatedImprovement: string;
}

const PerformanceOptimizer: React.FC<PerformanceOptimizerProps> = ({
  darkMode,
}) => {
  const { t } = useTranslation();
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [recommendations, setRecommendations] = useState<
    OptimizationRecommendation[]
  >([]);

  // Performance monitoring and analysis
  const analyzePerformance = async () => {
    setIsAnalyzing(true);

    try {
      // Simulate performance analysis
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Get real performance metrics where possible
      const navigation = performance.getEntriesByType(
        'navigation',
      )[0] as PerformanceNavigationTiming;
      const memory = (performance as any).memory;

      const performanceMetrics: PerformanceMetrics = {
        loadTime: navigation
          ? navigation.loadEventEnd - navigation.navigationStart
          : 1200,
        renderTime: navigation
          ? navigation.domContentLoadedEventEnd -
            navigation.domContentLoadedEventStart
          : 300,
        memoryUsage: memory ? memory.usedJSHeapSize / 1024 / 1024 : 25, // MB
        bundleSize: 2.1, // MB (estimated)
        cacheHitRate: 85, // %
        errorRate: 0.02, // %
      };

      setMetrics(performanceMetrics);

      // Generate optimization recommendations based on metrics
      const optimizations: OptimizationRecommendation[] = [];

      if (performanceMetrics.loadTime > 2000) {
        optimizations.push({
          category: 'performance',
          priority: 'high',
          title: 'Optimize Initial Load Time',
          description:
            'Application load time exceeds 2 seconds, impacting user experience',
          impact: 'Reduce bounce rate by 15-20%',
          implementation:
            'Implement code splitting, lazy loading, and bundle optimization',
          estimatedImprovement: '40-60% faster load times',
        });
      }

      if (performanceMetrics.bundleSize > 2.0) {
        optimizations.push({
          category: 'network',
          priority: 'high',
          title: 'Reduce Bundle Size',
          description: 'Large bundle size affects download and parse times',
          impact: 'Improve load performance on slower connections',
          implementation:
            'Tree shaking, dynamic imports, and dependency optimization',
          estimatedImprovement: '30-50% smaller bundle size',
        });
      }

      if (performanceMetrics.memoryUsage > 30) {
        optimizations.push({
          category: 'memory',
          priority: 'medium',
          title: 'Optimize Memory Usage',
          description: 'High memory consumption may cause performance issues',
          impact: 'Better performance on low-end devices',
          implementation:
            'Implement React.memo, useMemo, and cleanup unused objects',
          estimatedImprovement: '20-30% memory reduction',
        });
      }

      if (performanceMetrics.renderTime > 500) {
        optimizations.push({
          category: 'rendering',
          priority: 'medium',
          title: 'Optimize Rendering Performance',
          description: 'Slow rendering affects user interaction responsiveness',
          impact: 'Smoother user interactions and animations',
          implementation:
            'Virtual scrolling, component optimization, and render batching',
          estimatedImprovement: '50-70% faster rendering',
        });
      }

      if (performanceMetrics.cacheHitRate < 90) {
        optimizations.push({
          category: 'network',
          priority: 'medium',
          title: 'Improve Caching Strategy',
          description:
            'Low cache hit rate increases unnecessary network requests',
          impact: 'Faster subsequent page loads',
          implementation:
            'Implement service worker, optimize cache headers, and localStorage caching',
          estimatedImprovement: '25-40% faster repeat visits',
        });
      }

      // Always include some general optimizations
      optimizations.push(
        {
          category: 'performance',
          priority: 'low',
          title: 'Implement Progressive Web App Features',
          description: 'Add PWA capabilities for better user experience',
          impact: 'Offline functionality and app-like experience',
          implementation:
            'Service worker, web app manifest, and offline caching',
          estimatedImprovement: 'Enhanced user engagement',
        },
        {
          category: 'rendering',
          priority: 'low',
          title: 'Optimize Component Re-renders',
          description: 'Reduce unnecessary component re-renders',
          impact: 'Improved UI responsiveness',
          implementation: 'React.memo, useCallback, and state optimization',
          estimatedImprovement: '15-25% fewer re-renders',
        },
      );

      setRecommendations(optimizations);
    } catch (error) {
      console.error('Performance analysis failed:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  useEffect(() => {
    analyzePerformance();
  }, []);

  const formatBytes = (bytes: number) => {
    return `${bytes.toFixed(1)} MB`;
  };

  const formatTime = (ms: number) => {
    return `${(ms / 1000).toFixed(2)}s`;
  };

  const getMetricColor = (
    value: number,
    thresholds: { good: number; fair: number },
  ) => {
    if (value <= thresholds.good) return 'text-green-600';
    if (value <= thresholds.fair) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'border-red-500 bg-red-50 dark:bg-red-900/20';
      case 'medium':
        return 'border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20';
      case 'low':
        return 'border-blue-500 bg-blue-50 dark:bg-blue-900/20';
      default:
        return 'border-gray-500 bg-gray-50 dark:bg-gray-900/20';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'performance':
        return '⚡';
      case 'memory':
        return '🧠';
      case 'network':
        return '🌐';
      case 'rendering':
        return '🎨';
      default:
        return '🔧';
    }
  };

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div>
        <h2 className='text-2xl font-bold mb-2'>⚡ Performance Optimizer</h2>
        <p className='text-gray-600 dark:text-gray-400'>
          Analyze and optimize application performance for better user
          experience
        </p>
      </div>

      {/* Performance Metrics */}
      {isAnalyzing ? (
        <div
          className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
        >
          <div className='flex items-center justify-center py-8'>
            <div className='animate-spin w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full mr-3'></div>
            <span>Analyzing application performance...</span>
          </div>
        </div>
      ) : (
        metrics && (
          <div
            className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
          >
            <h3 className='text-lg font-semibold mb-4'>
              📊 Performance Metrics
            </h3>

            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
              <div className='text-center p-4 rounded-lg bg-gray-50 dark:bg-gray-700'>
                <div className='text-sm text-gray-600 dark:text-gray-400'>
                  Load Time
                </div>
                <div
                  className={`text-2xl font-bold ${getMetricColor(metrics.loadTime, { good: 1500, fair: 3000 })}`}
                >
                  {formatTime(metrics.loadTime)}
                </div>
                <div className='text-xs text-gray-500'>Target: &lt;2s</div>
              </div>

              <div className='text-center p-4 rounded-lg bg-gray-50 dark:bg-gray-700'>
                <div className='text-sm text-gray-600 dark:text-gray-400'>
                  Render Time
                </div>
                <div
                  className={`text-2xl font-bold ${getMetricColor(metrics.renderTime, { good: 300, fair: 500 })}`}
                >
                  {formatTime(metrics.renderTime)}
                </div>
                <div className='text-xs text-gray-500'>Target: &lt;300ms</div>
              </div>

              <div className='text-center p-4 rounded-lg bg-gray-50 dark:bg-gray-700'>
                <div className='text-sm text-gray-600 dark:text-gray-400'>
                  Memory Usage
                </div>
                <div
                  className={`text-2xl font-bold ${getMetricColor(metrics.memoryUsage, { good: 20, fair: 40 })}`}
                >
                  {formatBytes(metrics.memoryUsage)}
                </div>
                <div className='text-xs text-gray-500'>Target: &lt;20MB</div>
              </div>

              <div className='text-center p-4 rounded-lg bg-gray-50 dark:bg-gray-700'>
                <div className='text-sm text-gray-600 dark:text-gray-400'>
                  Bundle Size
                </div>
                <div
                  className={`text-2xl font-bold ${getMetricColor(metrics.bundleSize, { good: 1.5, fair: 2.5 })}`}
                >
                  {formatBytes(metrics.bundleSize)}
                </div>
                <div className='text-xs text-gray-500'>Target: &lt;1.5MB</div>
              </div>

              <div className='text-center p-4 rounded-lg bg-gray-50 dark:bg-gray-700'>
                <div className='text-sm text-gray-600 dark:text-gray-400'>
                  Cache Hit Rate
                </div>
                <div
                  className={`text-2xl font-bold ${getMetricColor(100 - metrics.cacheHitRate, { good: 5, fair: 15 })}`}
                >
                  {metrics.cacheHitRate.toFixed(1)}%
                </div>
                <div className='text-xs text-gray-500'>Target: &gt;95%</div>
              </div>

              <div className='text-center p-4 rounded-lg bg-gray-50 dark:bg-gray-700'>
                <div className='text-sm text-gray-600 dark:text-gray-400'>
                  Error Rate
                </div>
                <div
                  className={`text-2xl font-bold ${getMetricColor(metrics.errorRate, { good: 0.01, fair: 0.05 })}`}
                >
                  {(metrics.errorRate * 100).toFixed(2)}%
                </div>
                <div className='text-xs text-gray-500'>Target: &lt;0.1%</div>
              </div>
            </div>
          </div>
        )
      )}

      {/* Optimization Recommendations */}
      {recommendations.length > 0 && (
        <div
          className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
        >
          <h3 className='text-lg font-semibold mb-4'>
            🎯 Optimization Recommendations
          </h3>

          <div className='space-y-4'>
            {recommendations.map((rec, index) => (
              <div
                key={index}
                className={`p-4 rounded-lg border-l-4 ${getPriorityColor(rec.priority)}`}
              >
                <div className='flex items-start justify-between mb-2'>
                  <div className='flex items-center gap-2'>
                    <span className='text-lg'>
                      {getCategoryIcon(rec.category)}
                    </span>
                    <h4 className='font-semibold'>{rec.title}</h4>
                    <span
                      className={`px-2 py-1 rounded-full text-xs font-medium ${
                        rec.priority === 'high'
                          ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                          : rec.priority === 'medium'
                            ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
                            : 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
                      }`}
                    >
                      {rec.priority.toUpperCase()}
                    </span>
                  </div>
                </div>

                <p className='text-sm text-gray-600 dark:text-gray-400 mb-3'>
                  {rec.description}
                </p>

                <div className='grid grid-cols-1 md:grid-cols-3 gap-3 text-sm'>
                  <div>
                    <span className='font-medium text-green-600 dark:text-green-400'>
                      Impact:
                    </span>
                    <p className='text-gray-600 dark:text-gray-400'>
                      {rec.impact}
                    </p>
                  </div>
                  <div>
                    <span className='font-medium text-blue-600 dark:text-blue-400'>
                      Implementation:
                    </span>
                    <p className='text-gray-600 dark:text-gray-400'>
                      {rec.implementation}
                    </p>
                  </div>
                  <div>
                    <span className='font-medium text-purple-600 dark:text-purple-400'>
                      Improvement:
                    </span>
                    <p className='text-gray-600 dark:text-gray-400'>
                      {rec.estimatedImprovement}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Performance Tips */}
      <div
        className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
      >
        <h3 className='text-lg font-semibold mb-4'>
          💡 Performance Best Practices
        </h3>

        <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
          <div className='space-y-3'>
            <h4 className='font-medium text-blue-600 dark:text-blue-400'>
              Frontend Optimization
            </h4>
            <ul className='text-sm space-y-2 text-gray-600 dark:text-gray-400'>
              <li>• Use React.memo for expensive components</li>
              <li>• Implement code splitting with React.lazy</li>
              <li>• Optimize images with WebP format</li>
              <li>• Minimize bundle size with tree shaking</li>
              <li>• Use service workers for caching</li>
            </ul>
          </div>

          <div className='space-y-3'>
            <h4 className='font-medium text-green-600 dark:text-green-400'>
              Runtime Optimization
            </h4>
            <ul className='text-sm space-y-2 text-gray-600 dark:text-gray-400'>
              <li>• Debounce expensive calculations</li>
              <li>• Use virtual scrolling for large lists</li>
              <li>• Implement proper error boundaries</li>
              <li>• Optimize re-renders with useCallback</li>
              <li>• Monitor memory leaks and cleanup</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Refresh Analysis */}
      <div className='flex justify-center'>
        <button
          onClick={analyzePerformance}
          disabled={isAnalyzing}
          className={`px-6 py-3 rounded-lg font-medium transition-colors ${
            isAnalyzing
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-blue-600 hover:bg-blue-700 text-white'
          }`}
        >
          {isAnalyzing ? 'Analyzing...' : '🔄 Re-analyze Performance'}
        </button>
      </div>
    </div>
  );
};

export default PerformanceOptimizer;
