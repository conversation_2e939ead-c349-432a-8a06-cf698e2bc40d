import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocalStorage } from '../hooks/useLocalStorage';

interface RealEstateInvestmentTrackerProps {
  darkMode: boolean;
}

interface RealEstateProperty {
  id: string;
  name: string;
  type: 'apartment' | 'house' | 'commercial' | 'land';
  location: string;
  canton: string;
  purchasePrice: number;
  currentValue: number;
  purchaseDate: string;
  mortgageAmount: number;
  mortgageRate: number;
  monthlyRent: number;
  monthlyExpenses: number;
  notes: string;
}

interface PropertyAnalysis {
  equity: number;
  monthlyNetIncome: number;
  annualNetIncome: number;
  grossYield: number;
  netYield: number;
  capitalGain: number;
  capitalGainPercentage: number;
  totalReturn: number;
  totalReturnPercentage: number;
}

interface PortfolioSummary {
  totalValue: number;
  totalEquity: number;
  totalMortgage: number;
  totalMonthlyRent: number;
  totalMonthlyExpenses: number;
  totalNetIncome: number;
  averageYield: number;
  totalCapitalGain: number;
}

const RealEstateInvestmentTracker: React.FC<
  RealEstateInvestmentTrackerProps
> = ({ darkMode }) => {
  const { t } = useTranslation();
  const [properties, setProperties] = useLocalStorage<RealEstateProperty[]>(
    'real_estate_properties',
    [],
  );
  const [newProperty, setNewProperty] = useState<Partial<RealEstateProperty>>({
    name: '',
    type: 'apartment',
    location: '',
    canton: 'ZH',
    purchasePrice: 0,
    currentValue: 0,
    purchaseDate: new Date().toISOString().split('T')[0],
    mortgageAmount: 0,
    mortgageRate: 2.5,
    monthlyRent: 0,
    monthlyExpenses: 0,
    notes: '',
  });
  const [portfolioSummary, setPortfolioSummary] =
    useState<PortfolioSummary | null>(null);

  // Swiss cantons
  const swissCantons = [
    'AG',
    'AI',
    'AR',
    'BE',
    'BL',
    'BS',
    'FR',
    'GE',
    'GL',
    'GR',
    'JU',
    'LU',
    'NE',
    'NW',
    'OW',
    'SG',
    'SH',
    'SO',
    'SZ',
    'TG',
    'TI',
    'UR',
    'VD',
    'VS',
    'ZG',
    'ZH',
  ];

  // Property types
  const propertyTypes = [
    { value: 'apartment', label: '🏠 Apartment' },
    { value: 'house', label: '🏡 House' },
    { value: 'commercial', label: '🏢 Commercial' },
    { value: 'land', label: '🌍 Land' },
  ];

  // Calculate property analysis
  const analyzeProperty = (property: RealEstateProperty): PropertyAnalysis => {
    const equity = property.currentValue - property.mortgageAmount;
    const monthlyNetIncome = property.monthlyRent - property.monthlyExpenses;
    const annualNetIncome = monthlyNetIncome * 12;
    const grossYield =
      property.currentValue > 0
        ? ((property.monthlyRent * 12) / property.currentValue) * 100
        : 0;
    const netYield =
      property.currentValue > 0
        ? (annualNetIncome / property.currentValue) * 100
        : 0;
    const capitalGain = property.currentValue - property.purchasePrice;
    const capitalGainPercentage =
      property.purchasePrice > 0
        ? (capitalGain / property.purchasePrice) * 100
        : 0;

    // Calculate years owned
    const purchaseDate = new Date(property.purchaseDate);
    const currentDate = new Date();
    const yearsOwned =
      (currentDate.getTime() - purchaseDate.getTime()) /
      (1000 * 60 * 60 * 24 * 365.25);

    const totalReturn = capitalGain + annualNetIncome * yearsOwned;
    const totalReturnPercentage =
      property.purchasePrice > 0
        ? (totalReturn / property.purchasePrice) * 100
        : 0;

    return {
      equity,
      monthlyNetIncome,
      annualNetIncome,
      grossYield,
      netYield,
      capitalGain,
      capitalGainPercentage,
      totalReturn,
      totalReturnPercentage,
    };
  };

  // Calculate portfolio summary
  useEffect(() => {
    if (properties.length === 0) {
      setPortfolioSummary(null);
      return;
    }

    const totalValue = properties.reduce(
      (sum, prop) => sum + prop.currentValue,
      0,
    );
    const totalMortgage = properties.reduce(
      (sum, prop) => sum + prop.mortgageAmount,
      0,
    );
    const totalEquity = totalValue - totalMortgage;
    const totalMonthlyRent = properties.reduce(
      (sum, prop) => sum + prop.monthlyRent,
      0,
    );
    const totalMonthlyExpenses = properties.reduce(
      (sum, prop) => sum + prop.monthlyExpenses,
      0,
    );
    const totalNetIncome = (totalMonthlyRent - totalMonthlyExpenses) * 12;
    const averageYield =
      totalValue > 0 ? (totalNetIncome / totalValue) * 100 : 0;
    const totalCapitalGain = properties.reduce(
      (sum, prop) => sum + (prop.currentValue - prop.purchasePrice),
      0,
    );

    setPortfolioSummary({
      totalValue,
      totalEquity,
      totalMortgage,
      totalMonthlyRent,
      totalMonthlyExpenses,
      totalNetIncome,
      averageYield,
      totalCapitalGain,
    });
  }, [properties]);

  const addProperty = () => {
    if (!newProperty.name || !newProperty.purchasePrice) {
      return;
    }

    const property: RealEstateProperty = {
      id: Date.now().toString(),
      name: newProperty.name || '',
      type: (newProperty.type as any) || 'apartment',
      location: newProperty.location || '',
      canton: newProperty.canton || 'ZH',
      purchasePrice: newProperty.purchasePrice || 0,
      currentValue: newProperty.currentValue || newProperty.purchasePrice || 0,
      purchaseDate:
        newProperty.purchaseDate || new Date().toISOString().split('T')[0],
      mortgageAmount: newProperty.mortgageAmount || 0,
      mortgageRate: newProperty.mortgageRate || 2.5,
      monthlyRent: newProperty.monthlyRent || 0,
      monthlyExpenses: newProperty.monthlyExpenses || 0,
      notes: newProperty.notes || '',
    };

    setProperties([...properties, property]);
    setNewProperty({
      name: '',
      type: 'apartment',
      location: '',
      canton: 'ZH',
      purchasePrice: 0,
      currentValue: 0,
      purchaseDate: new Date().toISOString().split('T')[0],
      mortgageAmount: 0,
      mortgageRate: 2.5,
      monthlyRent: 0,
      monthlyExpenses: 0,
      notes: '',
    });
  };

  const removeProperty = (id: string) => {
    setProperties(properties.filter(prop => prop.id !== id));
  };

  const updateProperty = (id: string, updates: Partial<RealEstateProperty>) => {
    setProperties(
      properties.map(prop => (prop.id === id ? { ...prop, ...updates } : prop)),
    );
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('de-CH', {
      style: 'currency',
      currency: 'CHF',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (percentage: number) => {
    const color = percentage >= 0 ? 'text-green-600' : 'text-red-600';
    const sign = percentage >= 0 ? '+' : '';
    return (
      <span className={color}>
        {sign}
        {percentage.toFixed(2)}%
      </span>
    );
  };

  const getPropertyTypeIcon = (type: string) => {
    switch (type) {
      case 'apartment':
        return '🏠';
      case 'house':
        return '🏡';
      case 'commercial':
        return '🏢';
      case 'land':
        return '🌍';
      default:
        return '🏠';
    }
  };

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div>
        <h2 className='text-2xl font-bold mb-2'>
          🏠 Real Estate Investment Tracker
        </h2>
        <p className='text-gray-600 dark:text-gray-400'>
          Track and analyze your Swiss real estate investments with yield
          calculations and performance metrics
        </p>
      </div>

      {/* Portfolio Summary */}
      {portfolioSummary && (
        <div
          className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
        >
          <h3 className='text-lg font-semibold mb-4'>📊 Portfolio Summary</h3>

          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
            <div className='text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg'>
              <div className='text-sm text-gray-600 dark:text-gray-400'>
                Total Value
              </div>
              <div className='text-2xl font-bold text-blue-600'>
                {formatCurrency(portfolioSummary.totalValue)}
              </div>
            </div>

            <div className='text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg'>
              <div className='text-sm text-gray-600 dark:text-gray-400'>
                Total Equity
              </div>
              <div className='text-2xl font-bold text-green-600'>
                {formatCurrency(portfolioSummary.totalEquity)}
              </div>
            </div>

            <div className='text-center p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg'>
              <div className='text-sm text-gray-600 dark:text-gray-400'>
                Annual Net Income
              </div>
              <div className='text-2xl font-bold text-yellow-600'>
                {formatCurrency(portfolioSummary.totalNetIncome)}
              </div>
            </div>

            <div className='text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg'>
              <div className='text-sm text-gray-600 dark:text-gray-400'>
                Average Yield
              </div>
              <div className='text-2xl font-bold text-purple-600'>
                {portfolioSummary.averageYield.toFixed(2)}%
              </div>
            </div>
          </div>

          <div className='grid grid-cols-1 md:grid-cols-3 gap-4 mt-4'>
            <div className='text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg'>
              <div className='text-sm text-gray-600 dark:text-gray-400'>
                Total Mortgage
              </div>
              <div className='text-lg font-bold'>
                {formatCurrency(portfolioSummary.totalMortgage)}
              </div>
            </div>

            <div className='text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg'>
              <div className='text-sm text-gray-600 dark:text-gray-400'>
                Monthly Rent
              </div>
              <div className='text-lg font-bold'>
                {formatCurrency(portfolioSummary.totalMonthlyRent)}
              </div>
            </div>

            <div className='text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg'>
              <div className='text-sm text-gray-600 dark:text-gray-400'>
                Capital Gain
              </div>
              <div
                className={`text-lg font-bold ${portfolioSummary.totalCapitalGain >= 0 ? 'text-green-600' : 'text-red-600'}`}
              >
                {formatCurrency(portfolioSummary.totalCapitalGain)}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Add New Property */}
      <div
        className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
      >
        <h3 className='text-lg font-semibold mb-4'>➕ Add New Property</h3>

        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
          <div>
            <label className='block text-sm font-medium mb-2'>
              Property Name
            </label>
            <input
              type='text'
              value={newProperty.name}
              onChange={e =>
                setNewProperty(prev => ({ ...prev, name: e.target.value }))
              }
              className={`w-full px-3 py-2 rounded-md border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
              placeholder='e.g., Apartment Zurich'
            />
          </div>

          <div>
            <label className='block text-sm font-medium mb-2'>
              Property Type
            </label>
            <select
              value={newProperty.type}
              onChange={e =>
                setNewProperty(prev => ({
                  ...prev,
                  type: e.target.value as any,
                }))
              }
              className={`w-full px-3 py-2 rounded-md border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
            >
              {propertyTypes.map(type => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className='block text-sm font-medium mb-2'>Location</label>
            <input
              type='text'
              value={newProperty.location}
              onChange={e =>
                setNewProperty(prev => ({ ...prev, location: e.target.value }))
              }
              className={`w-full px-3 py-2 rounded-md border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
              placeholder='e.g., Zurich City Center'
            />
          </div>

          <div>
            <label className='block text-sm font-medium mb-2'>Canton</label>
            <select
              value={newProperty.canton}
              onChange={e =>
                setNewProperty(prev => ({ ...prev, canton: e.target.value }))
              }
              className={`w-full px-3 py-2 rounded-md border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
            >
              {swissCantons.map(canton => (
                <option key={canton} value={canton}>
                  {canton}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className='block text-sm font-medium mb-2'>
              Purchase Price (CHF)
            </label>
            <input
              type='number'
              value={newProperty.purchasePrice || ''}
              onChange={e =>
                setNewProperty(prev => ({
                  ...prev,
                  purchasePrice: parseFloat(e.target.value) || 0,
                }))
              }
              className={`w-full px-3 py-2 rounded-md border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
              placeholder='0'
            />
          </div>

          <div>
            <label className='block text-sm font-medium mb-2'>
              Current Value (CHF)
            </label>
            <input
              type='number'
              value={newProperty.currentValue || ''}
              onChange={e =>
                setNewProperty(prev => ({
                  ...prev,
                  currentValue: parseFloat(e.target.value) || 0,
                }))
              }
              className={`w-full px-3 py-2 rounded-md border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
              placeholder='0'
            />
          </div>

          <div>
            <label className='block text-sm font-medium mb-2'>
              Mortgage Amount (CHF)
            </label>
            <input
              type='number'
              value={newProperty.mortgageAmount || ''}
              onChange={e =>
                setNewProperty(prev => ({
                  ...prev,
                  mortgageAmount: parseFloat(e.target.value) || 0,
                }))
              }
              className={`w-full px-3 py-2 rounded-md border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
              placeholder='0'
            />
          </div>

          <div>
            <label className='block text-sm font-medium mb-2'>
              Monthly Rent (CHF)
            </label>
            <input
              type='number'
              value={newProperty.monthlyRent || ''}
              onChange={e =>
                setNewProperty(prev => ({
                  ...prev,
                  monthlyRent: parseFloat(e.target.value) || 0,
                }))
              }
              className={`w-full px-3 py-2 rounded-md border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
              placeholder='0'
            />
          </div>

          <div>
            <label className='block text-sm font-medium mb-2'>
              Monthly Expenses (CHF)
            </label>
            <input
              type='number'
              value={newProperty.monthlyExpenses || ''}
              onChange={e =>
                setNewProperty(prev => ({
                  ...prev,
                  monthlyExpenses: parseFloat(e.target.value) || 0,
                }))
              }
              className={`w-full px-3 py-2 rounded-md border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
              placeholder='0'
            />
          </div>
        </div>

        <div className='mt-4 flex justify-end'>
          <button
            onClick={addProperty}
            className='px-6 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors'
          >
            Add Property
          </button>
        </div>
      </div>

      {/* Properties List */}
      {properties.length > 0 && (
        <div
          className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
        >
          <h3 className='text-lg font-semibold mb-4'>🏘️ Your Properties</h3>

          <div className='space-y-4'>
            {properties.map(property => {
              const analysis = analyzeProperty(property);

              return (
                <div
                  key={property.id}
                  className={`p-4 border rounded-lg ${darkMode ? 'border-gray-700 bg-gray-700' : 'border-gray-200 bg-gray-50'}`}
                >
                  <div className='flex items-start justify-between mb-3'>
                    <div>
                      <h4 className='text-lg font-semibold flex items-center gap-2'>
                        {getPropertyTypeIcon(property.type)}
                        {property.name}
                      </h4>
                      <p className='text-sm text-gray-600 dark:text-gray-400'>
                        {property.location}, {property.canton}
                      </p>
                    </div>
                    <button
                      onClick={() => removeProperty(property.id)}
                      className='text-red-600 hover:text-red-800 transition-colors'
                      title='Remove property'
                    >
                      🗑️
                    </button>
                  </div>

                  <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
                    <div className='text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg'>
                      <div className='text-xs text-gray-600 dark:text-gray-400'>
                        Current Value
                      </div>
                      <div className='text-lg font-bold text-blue-600'>
                        {formatCurrency(property.currentValue)}
                      </div>
                    </div>

                    <div className='text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg'>
                      <div className='text-xs text-gray-600 dark:text-gray-400'>
                        Equity
                      </div>
                      <div className='text-lg font-bold text-green-600'>
                        {formatCurrency(analysis.equity)}
                      </div>
                    </div>

                    <div className='text-center p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg'>
                      <div className='text-xs text-gray-600 dark:text-gray-400'>
                        Net Yield
                      </div>
                      <div className='text-lg font-bold text-yellow-600'>
                        {analysis.netYield.toFixed(2)}%
                      </div>
                    </div>

                    <div className='text-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg'>
                      <div className='text-xs text-gray-600 dark:text-gray-400'>
                        Capital Gain
                      </div>
                      <div
                        className={`text-lg font-bold ${analysis.capitalGain >= 0 ? 'text-green-600' : 'text-red-600'}`}
                      >
                        {formatPercentage(analysis.capitalGainPercentage)}
                      </div>
                    </div>
                  </div>

                  <div className='grid grid-cols-1 md:grid-cols-3 gap-4 mt-3 text-sm'>
                    <div>
                      <span className='text-gray-600 dark:text-gray-400'>
                        Monthly Net Income:
                      </span>
                      <span className='ml-2 font-medium'>
                        {formatCurrency(analysis.monthlyNetIncome)}
                      </span>
                    </div>
                    <div>
                      <span className='text-gray-600 dark:text-gray-400'>
                        Gross Yield:
                      </span>
                      <span className='ml-2 font-medium'>
                        {analysis.grossYield.toFixed(2)}%
                      </span>
                    </div>
                    <div>
                      <span className='text-gray-600 dark:text-gray-400'>
                        Total Return:
                      </span>
                      <span className='ml-2 font-medium'>
                        {formatPercentage(analysis.totalReturnPercentage)}
                      </span>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {properties.length === 0 && (
        <div
          className={`p-8 rounded-lg text-center ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
        >
          <div className='text-4xl mb-4'>🏠</div>
          <h3 className='text-lg font-semibold mb-2'>
            No real estate investments yet
          </h3>
          <p className='text-gray-600 dark:text-gray-400'>
            Add your first property to start tracking your real estate portfolio
          </p>
        </div>
      )}
    </div>
  );
};

export default RealEstateInvestmentTracker;
