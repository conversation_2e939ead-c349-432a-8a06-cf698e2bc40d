import React, { useState, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

interface RiskAssessmentMetricsProps {
  darkMode: boolean;
  userData: {
    currentAge: number;
    retirementAge: number;
    currentSavings: number;
    monthlyIncome: number;
    monthlyExpenses: number;
    expectedReturn: number;
    inflationRate: number;
    safeWithdrawalRate: number;
  };
  investments?: any[];
  expenses?: any[];
}

interface RiskMetric {
  id: string;
  name: string;
  value: number;
  score: number; // 0-100 (100 = lowest risk)
  category: 'financial' | 'market' | 'personal' | 'economic';
  description: string;
  recommendation: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

interface RiskProfile {
  overallScore: number;
  riskLevel: 'conservative' | 'moderate' | 'aggressive' | 'very_aggressive';
  metrics: RiskMetric[];
  recommendations: string[];
  strengths: string[];
  weaknesses: string[];
}

const RiskAssessmentMetrics: React.FC<RiskAssessmentMetricsProps> = ({
  darkMode,
  userData,
  investments = [],
  expenses = [],
}) => {
  const { t } = useTranslation();
  const [riskProfile, setRiskProfile] = useState<RiskProfile | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // Risk categories
  const riskCategories = [
    { value: 'all', label: '🎯 All Risks', icon: '🎯' },
    { value: 'financial', label: '💰 Financial', icon: '💰' },
    { value: 'market', label: '📈 Market', icon: '📈' },
    { value: 'personal', label: '👤 Personal', icon: '👤' },
    { value: 'economic', label: '🌍 Economic', icon: '🌍' },
  ];

  // Calculate comprehensive risk assessment
  const calculateRiskAssessment = async () => {
    setIsAnalyzing(true);

    try {
      await new Promise(resolve => setTimeout(resolve, 2000));

      const {
        currentAge,
        retirementAge,
        currentSavings,
        monthlyIncome,
        monthlyExpenses,
        expectedReturn,
        inflationRate,
        safeWithdrawalRate,
      } = userData;

      const yearsToRetirement = Math.max(0, retirementAge - currentAge);
      const monthlySavings = monthlyIncome - monthlyExpenses;
      const savingsRate =
        monthlyIncome > 0 ? (monthlySavings / monthlyIncome) * 100 : 0;
      const fireTarget = monthlyExpenses * 12 * (100 / safeWithdrawalRate);
      const totalInvestments = investments.reduce(
        (sum, inv) => sum + (inv.currentValue || 0),
        0
      );
      const totalExpenses = expenses.reduce(
        (sum, exp) => sum + (exp.amount || 0),
        0
      );

      const metrics: RiskMetric[] = [];

      // 1. Emergency Fund Risk
      const emergencyFundTarget = monthlyExpenses * 6;
      const emergencyFundRatio =
        emergencyFundTarget > 0
          ? (currentSavings * 0.2) / emergencyFundTarget
          : 0;
      const emergencyFundScore = Math.min(100, emergencyFundRatio * 100);

      metrics.push({
        id: 'emergency_fund',
        name: 'Emergency Fund Coverage',
        value: emergencyFundRatio * 6, // Months covered
        score: emergencyFundScore,
        category: 'financial',
        description:
          'Measures your ability to cover unexpected expenses without touching investments',
        recommendation:
          emergencyFundScore < 70
            ? 'Build emergency fund to 6 months of expenses'
            : 'Emergency fund is adequate',
        severity:
          emergencyFundScore < 50
            ? 'critical'
            : emergencyFundScore < 70
              ? 'high'
              : 'low',
      });

      // 2. Savings Rate Risk
      const savingsRateScore = Math.min(
        100,
        Math.max(0, (savingsRate - 10) * 2)
      ); // 10% = 0, 60% = 100

      metrics.push({
        id: 'savings_rate',
        name: 'Savings Rate Sustainability',
        value: savingsRate,
        score: savingsRateScore,
        category: 'financial',
        description:
          'Evaluates the sustainability and adequacy of your current savings rate',
        recommendation:
          savingsRateScore < 60
            ? 'Increase savings rate to at least 20% for FIRE goals'
            : 'Savings rate is strong',
        severity:
          savingsRateScore < 40
            ? 'critical'
            : savingsRateScore < 60
              ? 'high'
              : 'low',
      });

      // 3. Investment Concentration Risk
      const investmentDiversification =
        investments.length > 0 ? Math.min(100, investments.length * 20) : 0;
      const concentrationScore = Math.min(100, investmentDiversification);

      metrics.push({
        id: 'concentration',
        name: 'Investment Concentration',
        value: investments.length,
        score: concentrationScore,
        category: 'market',
        description:
          'Assesses diversification across different investment types and asset classes',
        recommendation:
          concentrationScore < 60
            ? 'Diversify across more asset classes and investments'
            : 'Good diversification',
        severity:
          concentrationScore < 40
            ? 'high'
            : concentrationScore < 60
              ? 'medium'
              : 'low',
      });

      // 4. Time Horizon Risk
      const timeHorizonScore =
        yearsToRetirement > 30
          ? 100
          : yearsToRetirement > 20
            ? 80
            : yearsToRetirement > 10
              ? 60
              : 30;

      metrics.push({
        id: 'time_horizon',
        name: 'Time Horizon Risk',
        value: yearsToRetirement,
        score: timeHorizonScore,
        category: 'personal',
        description:
          'Evaluates the time available to recover from market downturns',
        recommendation:
          timeHorizonScore < 60
            ? 'Consider more conservative allocation due to shorter time horizon'
            : 'Sufficient time for growth investments',
        severity:
          timeHorizonScore < 40
            ? 'high'
            : timeHorizonScore < 60
              ? 'medium'
              : 'low',
      });

      // 5. Income Stability Risk
      const incomeStabilityScore =
        monthlyIncome > 0 ? Math.min(100, (monthlyIncome / 5000) * 50 + 50) : 0; // Simplified scoring

      metrics.push({
        id: 'income_stability',
        name: 'Income Stability',
        value: monthlyIncome,
        score: incomeStabilityScore,
        category: 'personal',
        description:
          'Assesses the stability and adequacy of your primary income source',
        recommendation:
          incomeStabilityScore < 60
            ? 'Consider diversifying income sources or building larger emergency fund'
            : 'Income appears stable',
        severity:
          incomeStabilityScore < 40
            ? 'high'
            : incomeStabilityScore < 60
              ? 'medium'
              : 'low',
      });

      // 6. Inflation Risk
      const realReturn = expectedReturn - inflationRate;
      const inflationScore =
        realReturn > 4 ? 100 : realReturn > 2 ? 80 : realReturn > 0 ? 60 : 20;

      metrics.push({
        id: 'inflation',
        name: 'Inflation Protection',
        value: realReturn,
        score: inflationScore,
        category: 'economic',
        description:
          "Measures your portfolio's ability to maintain purchasing power over time",
        recommendation:
          inflationScore < 60
            ? 'Consider inflation-protected investments like stocks or TIPS'
            : 'Good inflation protection',
        severity:
          inflationScore < 40 ? 'high' : inflationScore < 60 ? 'medium' : 'low',
      });

      // 7. Withdrawal Rate Risk
      const withdrawalRateScore =
        safeWithdrawalRate <= 4
          ? 100
          : safeWithdrawalRate <= 5
            ? 80
            : safeWithdrawalRate <= 6
              ? 60
              : 30;

      metrics.push({
        id: 'withdrawal_rate',
        name: 'Withdrawal Rate Safety',
        value: safeWithdrawalRate,
        score: withdrawalRateScore,
        category: 'financial',
        description:
          'Evaluates the sustainability of your planned withdrawal rate in retirement',
        recommendation:
          withdrawalRateScore < 60
            ? 'Consider reducing withdrawal rate or increasing savings'
            : 'Withdrawal rate is conservative',
        severity:
          withdrawalRateScore < 40
            ? 'high'
            : withdrawalRateScore < 60
              ? 'medium'
              : 'low',
      });

      // 8. Sequence of Returns Risk
      const sequenceRiskScore =
        yearsToRetirement > 10 ? 90 : yearsToRetirement > 5 ? 70 : 40;

      metrics.push({
        id: 'sequence_risk',
        name: 'Sequence of Returns Risk',
        value: yearsToRetirement,
        score: sequenceRiskScore,
        category: 'market',
        description:
          'Risk of poor market returns early in retirement affecting long-term sustainability',
        recommendation:
          sequenceRiskScore < 60
            ? 'Consider bond tent strategy as you approach retirement'
            : 'Sufficient time buffer',
        severity:
          sequenceRiskScore < 50
            ? 'high'
            : sequenceRiskScore < 70
              ? 'medium'
              : 'low',
      });

      // Calculate overall score
      const overallScore =
        metrics.reduce((sum, metric) => sum + metric.score, 0) / metrics.length;

      // Determine risk level
      let riskLevel: RiskProfile['riskLevel'] = 'moderate';
      if (overallScore >= 80) riskLevel = 'conservative';
      else if (overallScore >= 60) riskLevel = 'moderate';
      else if (overallScore >= 40) riskLevel = 'aggressive';
      else riskLevel = 'very_aggressive';

      // Generate recommendations
      const recommendations = [
        overallScore < 60
          ? 'Focus on building emergency fund and reducing high-risk exposures'
          : 'Maintain current risk management practices',
        savingsRate < 20
          ? 'Increase savings rate to accelerate FIRE timeline and reduce risk'
          : 'Continue strong savings discipline',
        investments.length < 3
          ? 'Diversify investments across asset classes and geographies'
          : 'Review and rebalance portfolio regularly',
        yearsToRetirement < 10
          ? 'Consider gradually shifting to more conservative allocations'
          : 'Take advantage of long time horizon for growth',
      ];

      // Identify strengths and weaknesses
      const strengths = metrics.filter(m => m.score >= 70).map(m => m.name);
      const weaknesses = metrics.filter(m => m.score < 50).map(m => m.name);

      const profile: RiskProfile = {
        overallScore,
        riskLevel,
        metrics,
        recommendations,
        strengths,
        weaknesses,
      };

      setRiskProfile(profile);
    } catch (error) {
      console.error('Risk assessment failed:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  useEffect(() => {
    calculateRiskAssessment();
  }, [userData, investments, expenses]);

  // Filter metrics by category
  const filteredMetrics =
    riskProfile?.metrics.filter(metric => {
      if (selectedCategory === 'all') return true;
      return metric.category === selectedCategory;
    }) || [];

  const formatValue = (metric: RiskMetric) => {
    switch (metric.id) {
      case 'emergency_fund':
        return `${metric.value.toFixed(1)} months`;
      case 'savings_rate':
        return `${metric.value.toFixed(1)}%`;
      case 'concentration':
        return `${metric.value} investments`;
      case 'time_horizon':
        return `${metric.value} years`;
      case 'income_stability':
        return `CHF ${metric.value.toLocaleString()}`;
      case 'inflation':
        return `${metric.value.toFixed(1)}% real return`;
      case 'withdrawal_rate':
        return `${metric.value.toFixed(1)}%`;
      case 'sequence_risk':
        return `${metric.value} years to retirement`;
      default:
        return metric.value.toString();
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    if (score >= 40) return 'text-orange-600';
    return 'text-red-600';
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low':
        return 'border-green-500 bg-green-50 dark:bg-green-900/20';
      case 'medium':
        return 'border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20';
      case 'high':
        return 'border-orange-500 bg-orange-50 dark:bg-orange-900/20';
      case 'critical':
        return 'border-red-500 bg-red-50 dark:bg-red-900/20';
      default:
        return 'border-gray-500 bg-gray-50 dark:bg-gray-900/20';
    }
  };

  const getRiskLevelColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'conservative':
        return 'text-green-600';
      case 'moderate':
        return 'text-blue-600';
      case 'aggressive':
        return 'text-orange-600';
      case 'very_aggressive':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div>
        <h2 className='text-2xl font-bold mb-2'>⚠️ Risk Assessment Metrics</h2>
        <p className='text-gray-600 dark:text-gray-400'>
          Comprehensive analysis of financial risks and recommendations for risk
          mitigation
        </p>
      </div>

      {/* Overall Risk Profile */}
      {isAnalyzing ? (
        <div
          className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
        >
          <div className='flex items-center justify-center py-8'>
            <div className='animate-spin w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full mr-3'></div>
            <span>Analyzing risk profile...</span>
          </div>
        </div>
      ) : (
        riskProfile && (
          <div
            className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
          >
            <h3 className='text-lg font-semibold mb-4'>
              📊 Overall Risk Profile
            </h3>

            <div className='grid grid-cols-1 md:grid-cols-3 gap-4 mb-6'>
              <div className='text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg'>
                <div className='text-sm text-gray-600 dark:text-gray-400'>
                  Risk Score
                </div>
                <div
                  className={`text-3xl font-bold ${getScoreColor(riskProfile.overallScore)}`}
                >
                  {riskProfile.overallScore.toFixed(0)}/100
                </div>
                <div className='text-xs text-gray-500'>Higher = Lower Risk</div>
              </div>

              <div className='text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg'>
                <div className='text-sm text-gray-600 dark:text-gray-400'>
                  Risk Level
                </div>
                <div
                  className={`text-2xl font-bold ${getRiskLevelColor(riskProfile.riskLevel)}`}
                >
                  {riskProfile.riskLevel.replace('_', ' ').toUpperCase()}
                </div>
                <div className='text-xs text-gray-500'>Investment Profile</div>
              </div>

              <div className='text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg'>
                <div className='text-sm text-gray-600 dark:text-gray-400'>
                  Risk Factors
                </div>
                <div className='text-2xl font-bold text-gray-600'>
                  {
                    riskProfile.metrics.filter(
                      m => m.severity === 'high' || m.severity === 'critical'
                    ).length
                  }
                </div>
                <div className='text-xs text-gray-500'>High/Critical Risks</div>
              </div>
            </div>

            {/* Strengths and Weaknesses */}
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <div>
                <h4 className='font-semibold text-green-600 mb-2'>
                  ✅ Strengths
                </h4>
                {riskProfile.strengths.length > 0 ? (
                  <ul className='text-sm space-y-1'>
                    {riskProfile.strengths.map((strength, index) => (
                      <li key={index} className='flex items-center gap-2'>
                        <span className='text-green-500'>•</span>
                        {strength}
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p className='text-sm text-gray-500'>
                    Focus on building financial strengths
                  </p>
                )}
              </div>
              <div>
                <h4 className='font-semibold text-red-600 mb-2'>
                  ⚠️ Areas for Improvement
                </h4>
                {riskProfile.weaknesses.length > 0 ? (
                  <ul className='text-sm space-y-1'>
                    {riskProfile.weaknesses.map((weakness, index) => (
                      <li key={index} className='flex items-center gap-2'>
                        <span className='text-red-500'>•</span>
                        {weakness}
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p className='text-sm text-gray-500'>
                    Strong risk profile across all areas
                  </p>
                )}
              </div>
            </div>
          </div>
        )
      )}

      {/* Risk Category Filter */}
      {riskProfile && (
        <div
          className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
        >
          <div className='flex items-center justify-between mb-4'>
            <h3 className='text-lg font-semibold'>🎯 Risk Metrics Analysis</h3>
            <select
              value={selectedCategory}
              onChange={e => setSelectedCategory(e.target.value)}
              className={`px-3 py-2 rounded-md border text-sm ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
            >
              {riskCategories.map(category => (
                <option key={category.value} value={category.value}>
                  {category.label}
                </option>
              ))}
            </select>
          </div>

          <div className='space-y-4'>
            {filteredMetrics.map(metric => (
              <div
                key={metric.id}
                className={`p-4 rounded-lg border-l-4 ${getSeverityColor(metric.severity)}`}
              >
                <div className='flex items-start justify-between mb-2'>
                  <div>
                    <h4 className='font-semibold'>{metric.name}</h4>
                    <p className='text-sm text-gray-600 dark:text-gray-400'>
                      {metric.description}
                    </p>
                  </div>
                  <div className='text-right'>
                    <div
                      className={`text-lg font-bold ${getScoreColor(metric.score)}`}
                    >
                      {metric.score.toFixed(0)}/100
                    </div>
                    <div className='text-sm text-gray-500'>
                      {formatValue(metric)}
                    </div>
                  </div>
                </div>

                <div className='mt-3'>
                  <span className='font-medium text-blue-600 dark:text-blue-400'>
                    Recommendation:
                  </span>
                  <p className='text-sm text-gray-600 dark:text-gray-400 mt-1'>
                    {metric.recommendation}
                  </p>
                </div>

                <div className='flex items-center justify-between mt-3 pt-3 border-t border-gray-200 dark:border-gray-600'>
                  <span className='text-xs text-gray-500 capitalize'>
                    Category: {metric.category}
                  </span>
                  <span
                    className={`px-2 py-1 rounded-full text-xs font-medium ${
                      metric.severity === 'critical'
                        ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                        : metric.severity === 'high'
                          ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300'
                          : metric.severity === 'medium'
                            ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
                            : 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                    }`}
                  >
                    {metric.severity.toUpperCase()} RISK
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Recommendations */}
      {riskProfile && (
        <div
          className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
        >
          <h3 className='text-lg font-semibold mb-4'>
            💡 Risk Management Recommendations
          </h3>

          <div className='space-y-3'>
            {riskProfile.recommendations.map((recommendation, index) => (
              <div key={index} className='flex items-start gap-3'>
                <div className='flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold'>
                  {index + 1}
                </div>
                <p className='text-sm'>{recommendation}</p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Refresh Analysis */}
      <div className='flex justify-center'>
        <button
          onClick={calculateRiskAssessment}
          disabled={isAnalyzing}
          className={`px-6 py-3 rounded-lg font-medium transition-colors ${
            isAnalyzing
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-red-600 hover:bg-red-700 text-white'
          }`}
        >
          {isAnalyzing ? 'Analyzing...' : '🔄 Refresh Risk Analysis'}
        </button>
      </div>
    </div>
  );
};

export default RiskAssessmentMetrics;
