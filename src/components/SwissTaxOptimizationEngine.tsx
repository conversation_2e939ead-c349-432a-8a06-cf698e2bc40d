import React, { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  calculateSwissIncomeTax,
  calculateWealthTax,
  compareCantonTaxes,
  type CantonCode,
  type CivilStatus,
} from '../utils/swiss-tax-calculations';

interface SwissTaxOptimizationEngineProps {
  darkMode: boolean;
  userData: {
    currentAge: number;
    monthlyIncome: number;
    currentSavings: number;
    canton: CantonCode;
    maritalStatus: CivilStatus;
    children: number;
    pillar3aContribution: number;
  };
}

interface TaxOptimizationStrategy {
  id: string;
  name: string;
  description: string;
  annualSavings: number;
  implementation: string[];
  riskLevel: 'Low' | 'Medium' | 'High';
  timeToImplement: string;
  legalCompliance: boolean;
}

interface TaxScenario {
  name: string;
  grossIncome: number;
  deductions: number;
  canton: CantonCode;
  civilStatus: CivilStatus;
  netWorth: number;
  totalTax: number;
  effectiveRate: number;
  savings: number;
}

const SwissTaxOptimizationEngine: React.FC<SwissTaxOptimizationEngineProps> = ({
  darkMode,
  userData,
}) => {
  const { t } = useTranslation();
  const [isAnalyzing, setIsAnalyzing] = useState(true);
  const [selectedScenario, setSelectedScenario] = useState<string>('current');
  const [optimizationStrategies, setOptimizationStrategies] = useState<
    TaxOptimizationStrategy[]
  >([]);
  const [taxScenarios, setTaxScenarios] = useState<TaxScenario[]>([]);
  const [cantonComparison, setCantonComparison] = useState<any[]>([]);

  const annualIncome = userData.monthlyIncome * 12;
  const currentNetWorth = userData.currentSavings;

  // Calculate current tax situation
  const currentTaxSituation = useMemo(() => {
    const incomeTax = calculateSwissIncomeTax(
      annualIncome,
      userData.canton,
      userData.maritalStatus,
      1.0,
      userData.pillar3aContribution + 2000 // Basic professional expenses
    );

    const wealthTax = calculateWealthTax(
      currentNetWorth,
      userData.canton,
      userData.maritalStatus
    );

    return {
      incomeTax: incomeTax.total,
      wealthTax: wealthTax.total,
      totalTax: incomeTax.total + wealthTax.total,
      effectiveRate: ((incomeTax.total + wealthTax.total) / annualIncome) * 100,
    };
  }, [
    annualIncome,
    currentNetWorth,
    userData.canton,
    userData.maritalStatus,
    userData.pillar3aContribution,
  ]);

  // Generate optimization strategies
  useEffect(() => {
    const generateOptimizationStrategies = () => {
      const strategies: TaxOptimizationStrategy[] = [];

      // Pillar 3a Optimization
      const maxPillar3a = 7056; // 2024 limit for employees
      const currentPillar3a = userData.pillar3aContribution;
      if (currentPillar3a < maxPillar3a) {
        const additionalContribution = maxPillar3a - currentPillar3a;
        const taxSavings =
          additionalContribution * (currentTaxSituation.effectiveRate / 100);

        strategies.push({
          id: 'pillar3a-max',
          name: 'Maximize Pillar 3a Contributions',
          description: `Increase your Pillar 3a contribution to the maximum CHF ${maxPillar3a.toLocaleString()}`,
          annualSavings: taxSavings,
          implementation: [
            `Increase monthly Pillar 3a contribution by CHF ${Math.round(additionalContribution / 12)}`,
            'Set up automatic transfer to Pillar 3a account',
            'Choose investment-based Pillar 3a for higher returns',
            'Review and optimize Pillar 3a provider fees',
          ],
          riskLevel: 'Low',
          timeToImplement: '1 month',
          legalCompliance: true,
        });
      }

      // Professional Expenses Optimization
      const maxProfessionalExpenses = Math.min(annualIncome * 0.2, 4000); // 20% up to CHF 4,000
      const currentProfessionalExpenses = 2000; // Assumed current
      if (currentProfessionalExpenses < maxProfessionalExpenses) {
        const additionalDeduction =
          maxProfessionalExpenses - currentProfessionalExpenses;
        const taxSavings =
          additionalDeduction * (currentTaxSituation.effectiveRate / 100);

        strategies.push({
          id: 'professional-expenses',
          name: 'Optimize Professional Expense Deductions',
          description: 'Maximize legitimate professional expense deductions',
          annualSavings: taxSavings,
          implementation: [
            'Document all work-related expenses (transport, meals, equipment)',
            'Claim home office expenses if applicable',
            'Deduct professional development and training costs',
            'Keep detailed records and receipts',
          ],
          riskLevel: 'Low',
          timeToImplement: '3 months',
          legalCompliance: true,
        });
      }

      // Canton Relocation Strategy
      const cantonComparison = compareCantonTaxes(
        annualIncome,
        userData.maritalStatus,
        currentNetWorth
      );
      const currentCantonTax = cantonComparison.find(
        c => c.canton === userData.canton
      );
      const bestCantonTax = cantonComparison[0]; // Lowest tax canton

      if (
        currentCantonTax &&
        bestCantonTax &&
        currentCantonTax.canton !== bestCantonTax.canton
      ) {
        const potentialSavings =
          currentCantonTax.totalTax - bestCantonTax.totalTax;

        if (potentialSavings > 5000) {
          // Only suggest if savings > CHF 5,000
          strategies.push({
            id: 'canton-relocation',
            name: `Relocate to ${bestCantonTax.name}`,
            description: `Consider relocating to ${bestCantonTax.name} for significant tax savings`,
            annualSavings: potentialSavings,
            implementation: [
              'Research cost of living differences',
              'Evaluate job market and opportunities',
              'Consider family and lifestyle impact',
              'Plan relocation timeline and costs',
            ],
            riskLevel: 'High',
            timeToImplement: '6-12 months',
            legalCompliance: true,
          });
        }
      }

      // Income Splitting Strategy (for married couples)
      if (userData.maritalStatus === 'married') {
        const incomeSplittingSavings = annualIncome * 0.02; // Estimated 2% savings

        strategies.push({
          id: 'income-splitting',
          name: 'Income Splitting Optimization',
          description:
            'Optimize income distribution between spouses for tax efficiency',
          annualSavings: incomeSplittingSavings,
          implementation: [
            'Review employment contracts and salary structures',
            'Consider spouse employment in family business',
            'Optimize investment income distribution',
            'Consult tax advisor for complex situations',
          ],
          riskLevel: 'Medium',
          timeToImplement: '3-6 months',
          legalCompliance: true,
        });
      }

      // Wealth Tax Optimization
      if (currentNetWorth > 200000) {
        const wealthTaxOptimization = currentTaxSituation.wealthTax * 0.15; // Estimated 15% reduction

        strategies.push({
          id: 'wealth-tax-optimization',
          name: 'Wealth Tax Structure Optimization',
          description: 'Optimize asset structure to minimize wealth tax burden',
          annualSavings: wealthTaxOptimization,
          implementation: [
            'Review asset allocation and structure',
            'Consider pension fund buybacks',
            'Optimize real estate holdings',
            'Evaluate debt structuring strategies',
          ],
          riskLevel: 'Medium',
          timeToImplement: '6 months',
          legalCompliance: true,
        });
      }

      return strategies.sort((a, b) => b.annualSavings - a.annualSavings);
    };

    const generateTaxScenarios = () => {
      const scenarios: TaxScenario[] = [];

      // Current scenario
      scenarios.push({
        name: 'Current Situation',
        grossIncome: annualIncome,
        deductions: userData.pillar3aContribution + 2000,
        canton: userData.canton,
        civilStatus: userData.maritalStatus,
        netWorth: currentNetWorth,
        totalTax: currentTaxSituation.totalTax,
        effectiveRate: currentTaxSituation.effectiveRate,
        savings: 0,
      });

      // Optimized scenario
      const optimizedDeductions = 7056 + Math.min(annualIncome * 0.2, 4000); // Max Pillar 3a + professional expenses
      const optimizedTax = calculateSwissIncomeTax(
        annualIncome,
        userData.canton,
        userData.maritalStatus,
        1.0,
        optimizedDeductions
      );
      const optimizedWealthTax = calculateWealthTax(
        currentNetWorth,
        userData.canton,
        userData.maritalStatus
      );
      const optimizedTotal = optimizedTax.total + optimizedWealthTax.total;

      scenarios.push({
        name: 'Optimized (Same Canton)',
        grossIncome: annualIncome,
        deductions: optimizedDeductions,
        canton: userData.canton,
        civilStatus: userData.maritalStatus,
        netWorth: currentNetWorth,
        totalTax: optimizedTotal,
        effectiveRate: (optimizedTotal / annualIncome) * 100,
        savings: currentTaxSituation.totalTax - optimizedTotal,
      });

      // Best canton scenario
      const cantonComparison = compareCantonTaxes(
        annualIncome,
        userData.maritalStatus,
        currentNetWorth
      );
      const bestCanton = cantonComparison[0];

      scenarios.push({
        name: `Best Canton (${bestCanton.name})`,
        grossIncome: annualIncome,
        deductions: optimizedDeductions,
        canton: bestCanton.canton,
        civilStatus: userData.maritalStatus,
        netWorth: currentNetWorth,
        totalTax: bestCanton.totalTax,
        effectiveRate: bestCanton.effectiveRate * 100,
        savings: currentTaxSituation.totalTax - bestCanton.totalTax,
      });

      return scenarios;
    };

    setIsAnalyzing(true);

    setTimeout(() => {
      setOptimizationStrategies(generateOptimizationStrategies());
      setTaxScenarios(generateTaxScenarios());
      setCantonComparison(
        compareCantonTaxes(
          annualIncome,
          userData.maritalStatus,
          currentNetWorth
        ).slice(0, 10)
      );
      setIsAnalyzing(false);
    }, 2000);
  }, [userData, annualIncome, currentNetWorth, currentTaxSituation]);

  if (isAnalyzing) {
    return (
      <div
        className={`p-6 rounded-lg border ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}
      >
        <div className='text-center'>
          <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4'></div>
          <h3
            className={`text-lg font-semibold mb-2 ${darkMode ? 'text-white' : 'text-gray-900'}`}
          >
            🏛️ Swiss Tax Optimization Engine
          </h3>
          <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
            Analyzing Swiss tax optimization opportunities across all 26
            cantons...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div
        className={`p-6 rounded-lg border ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}
      >
        <h3
          className={`text-xl font-bold mb-2 ${darkMode ? 'text-white' : 'text-gray-900'}`}
        >
          🏛️ Swiss Tax Optimization Engine
        </h3>
        <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
          Comprehensive analysis of Swiss tax optimization strategies and canton
          comparison
        </p>
      </div>

      {/* Current Tax Situation */}
      <div
        className={`p-6 rounded-lg border ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}
      >
        <h4
          className={`text-lg font-semibold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}
        >
          📊 Current Tax Situation
        </h4>
        <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
          <div
            className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}
          >
            <div
              className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
            >
              Annual Income Tax
            </div>
            <div
              className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}
            >
              CHF {currentTaxSituation.incomeTax.toLocaleString()}
            </div>
          </div>
          <div
            className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}
          >
            <div
              className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
            >
              Annual Wealth Tax
            </div>
            <div
              className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}
            >
              CHF {currentTaxSituation.wealthTax.toLocaleString()}
            </div>
          </div>
          <div
            className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}
          >
            <div
              className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
            >
              Total Annual Tax
            </div>
            <div
              className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}
            >
              CHF {currentTaxSituation.totalTax.toLocaleString()}
            </div>
          </div>
          <div
            className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}
          >
            <div
              className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
            >
              Effective Tax Rate
            </div>
            <div
              className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}
            >
              {currentTaxSituation.effectiveRate.toFixed(1)}%
            </div>
          </div>
        </div>
      </div>

      {/* Optimization Strategies */}
      <div
        className={`p-6 rounded-lg border ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}
      >
        <h4
          className={`text-lg font-semibold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}
        >
          💡 Tax Optimization Strategies
        </h4>
        <div className='space-y-4'>
          {optimizationStrategies.map(strategy => (
            <div
              key={strategy.id}
              className={`p-4 rounded-lg border ${darkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'}`}
            >
              <div className='flex justify-between items-start mb-2'>
                <h5
                  className={`font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}
                >
                  {strategy.name}
                </h5>
                <div className='flex items-center space-x-2'>
                  <span
                    className={`px-2 py-1 rounded text-xs font-medium ${
                      strategy.riskLevel === 'Low'
                        ? 'bg-green-100 text-green-800'
                        : strategy.riskLevel === 'Medium'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-red-100 text-red-800'
                    }`}
                  >
                    {strategy.riskLevel} Risk
                  </span>
                  <span
                    className={`text-lg font-bold ${darkMode ? 'text-green-400' : 'text-green-600'}`}
                  >
                    CHF {strategy.annualSavings.toLocaleString()}
                  </span>
                </div>
              </div>
              <p
                className={`text-sm mb-3 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
              >
                {strategy.description}
              </p>
              <div className='mb-3'>
                <h6
                  className={`text-sm font-medium mb-2 ${darkMode ? 'text-white' : 'text-gray-900'}`}
                >
                  Implementation Steps:
                </h6>
                <ul
                  className={`text-sm space-y-1 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
                >
                  {strategy.implementation.map((step, index) => (
                    <li key={index} className='flex items-start'>
                      <span className='mr-2'>•</span>
                      <span>{step}</span>
                    </li>
                  ))}
                </ul>
              </div>
              <div className='flex justify-between text-sm'>
                <span
                  className={`${darkMode ? 'text-gray-400' : 'text-gray-500'}`}
                >
                  Time to implement: {strategy.timeToImplement}
                </span>
                <span
                  className={`${strategy.legalCompliance ? 'text-green-600' : 'text-red-600'}`}
                >
                  {strategy.legalCompliance
                    ? '✓ Legally compliant'
                    : '⚠ Requires legal review'}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Tax Scenarios Comparison */}
      <div
        className={`p-6 rounded-lg border ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}
      >
        <h4
          className={`text-lg font-semibold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}
        >
          📈 Tax Scenarios Comparison
        </h4>
        <div className='overflow-x-auto'>
          <table className='w-full'>
            <thead>
              <tr
                className={`border-b ${darkMode ? 'border-gray-600' : 'border-gray-200'}`}
              >
                <th
                  className={`text-left py-2 px-3 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
                >
                  Scenario
                </th>
                <th
                  className={`text-right py-2 px-3 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
                >
                  Total Tax
                </th>
                <th
                  className={`text-right py-2 px-3 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
                >
                  Effective Rate
                </th>
                <th
                  className={`text-right py-2 px-3 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
                >
                  Annual Savings
                </th>
              </tr>
            </thead>
            <tbody>
              {taxScenarios.map((scenario, index) => (
                <tr
                  key={index}
                  className={`border-b ${darkMode ? 'border-gray-700' : 'border-gray-100'}`}
                >
                  <td
                    className={`py-3 px-3 font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}
                  >
                    {scenario.name}
                  </td>
                  <td
                    className={`py-3 px-3 text-right ${darkMode ? 'text-white' : 'text-gray-900'}`}
                  >
                    CHF {scenario.totalTax.toLocaleString()}
                  </td>
                  <td
                    className={`py-3 px-3 text-right ${darkMode ? 'text-white' : 'text-gray-900'}`}
                  >
                    {scenario.effectiveRate.toFixed(1)}%
                  </td>
                  <td
                    className={`py-3 px-3 text-right font-bold ${
                      scenario.savings > 0
                        ? darkMode
                          ? 'text-green-400'
                          : 'text-green-600'
                        : darkMode
                          ? 'text-gray-400'
                          : 'text-gray-500'
                    }`}
                  >
                    {scenario.savings > 0
                      ? `CHF ${scenario.savings.toLocaleString()}`
                      : '-'}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Canton Tax Comparison */}
      <div
        className={`p-6 rounded-lg border ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}
      >
        <h4
          className={`text-lg font-semibold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}
        >
          🏛️ Canton Tax Comparison (Top 10 Lowest Tax)
        </h4>
        <div className='overflow-x-auto'>
          <table className='w-full'>
            <thead>
              <tr
                className={`border-b ${darkMode ? 'border-gray-600' : 'border-gray-200'}`}
              >
                <th
                  className={`text-left py-2 px-3 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
                >
                  Canton
                </th>
                <th
                  className={`text-right py-2 px-3 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
                >
                  Income Tax
                </th>
                <th
                  className={`text-right py-2 px-3 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
                >
                  Wealth Tax
                </th>
                <th
                  className={`text-right py-2 px-3 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
                >
                  Total Tax
                </th>
                <th
                  className={`text-right py-2 px-3 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
                >
                  Effective Rate
                </th>
                <th
                  className={`text-right py-2 px-3 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
                >
                  vs Current
                </th>
              </tr>
            </thead>
            <tbody>
              {cantonComparison.map((canton, index) => {
                const isCurrentCanton = canton.canton === userData.canton;
                const savings = currentTaxSituation.totalTax - canton.totalTax;

                return (
                  <tr
                    key={canton.canton}
                    className={`border-b ${darkMode ? 'border-gray-700' : 'border-gray-100'} ${
                      isCurrentCanton
                        ? darkMode
                          ? 'bg-blue-900/20'
                          : 'bg-blue-50'
                        : ''
                    }`}
                  >
                    <td
                      className={`py-3 px-3 font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}
                    >
                      {canton.name} ({canton.canton})
                      {isCurrentCanton && (
                        <span className='ml-2 text-xs bg-blue-600 text-white px-2 py-1 rounded'>
                          Current
                        </span>
                      )}
                    </td>
                    <td
                      className={`py-3 px-3 text-right ${darkMode ? 'text-white' : 'text-gray-900'}`}
                    >
                      CHF {canton.incomeTax.toLocaleString()}
                    </td>
                    <td
                      className={`py-3 px-3 text-right ${darkMode ? 'text-white' : 'text-gray-900'}`}
                    >
                      CHF {canton.wealthTax.toLocaleString()}
                    </td>
                    <td
                      className={`py-3 px-3 text-right ${darkMode ? 'text-white' : 'text-gray-900'}`}
                    >
                      CHF {canton.totalTax.toLocaleString()}
                    </td>
                    <td
                      className={`py-3 px-3 text-right ${darkMode ? 'text-white' : 'text-gray-900'}`}
                    >
                      {(canton.effectiveRate * 100).toFixed(1)}%
                    </td>
                    <td
                      className={`py-3 px-3 text-right font-bold ${
                        savings > 0
                          ? darkMode
                            ? 'text-green-400'
                            : 'text-green-600'
                          : savings < 0
                            ? darkMode
                              ? 'text-red-400'
                              : 'text-red-600'
                            : darkMode
                              ? 'text-gray-400'
                              : 'text-gray-500'
                      }`}
                    >
                      {savings !== 0
                        ? `${savings > 0 ? '+' : ''}CHF ${savings.toLocaleString()}`
                        : '-'}
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* Tax Planning Recommendations */}
      <div
        className={`p-6 rounded-lg border ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}
      >
        <h4
          className={`text-lg font-semibold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}
        >
          🎯 Tax Planning Recommendations
        </h4>
        <div className='space-y-4'>
          <div
            className={`p-4 rounded-lg ${darkMode ? 'bg-blue-900/20 border border-blue-700' : 'bg-blue-50 border border-blue-200'}`}
          >
            <h5
              className={`font-semibold mb-2 ${darkMode ? 'text-blue-300' : 'text-blue-800'}`}
            >
              💡 Immediate Actions (0-3 months)
            </h5>
            <ul
              className={`space-y-1 text-sm ${darkMode ? 'text-blue-200' : 'text-blue-700'}`}
            >
              <li>
                • Maximize Pillar 3a contributions for immediate tax savings
              </li>
              <li>• Document and claim all legitimate professional expenses</li>
              <li>• Review and optimize current tax withholdings</li>
              <li>• Set up automatic savings for tax-efficient investments</li>
            </ul>
          </div>

          <div
            className={`p-4 rounded-lg ${darkMode ? 'bg-yellow-900/20 border border-yellow-700' : 'bg-yellow-50 border border-yellow-200'}`}
          >
            <h5
              className={`font-semibold mb-2 ${darkMode ? 'text-yellow-300' : 'text-yellow-800'}`}
            >
              📅 Medium-term Planning (3-12 months)
            </h5>
            <ul
              className={`space-y-1 text-sm ${darkMode ? 'text-yellow-200' : 'text-yellow-700'}`}
            >
              <li>
                • Consider canton relocation if savings exceed CHF 10,000
                annually
              </li>
              <li>• Optimize investment structure for wealth tax efficiency</li>
              <li>• Plan pension fund buybacks for tax optimization</li>
              <li>• Review family tax planning strategies</li>
            </ul>
          </div>

          <div
            className={`p-4 rounded-lg ${darkMode ? 'bg-green-900/20 border border-green-700' : 'bg-green-50 border border-green-200'}`}
          >
            <h5
              className={`font-semibold mb-2 ${darkMode ? 'text-green-300' : 'text-green-800'}`}
            >
              🚀 Long-term Strategy (1+ years)
            </h5>
            <ul
              className={`space-y-1 text-sm ${darkMode ? 'text-green-200' : 'text-green-700'}`}
            >
              <li>• Develop comprehensive estate planning strategy</li>
              <li>• Consider business structure optimization</li>
              <li>• Plan for retirement tax efficiency</li>
              <li>• Regular annual tax strategy reviews and adjustments</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Refresh Analysis */}
      <div className='text-center'>
        <button
          onClick={() => window.location.reload()}
          className={`px-6 py-3 rounded-lg font-medium transition-colors ${
            darkMode
              ? 'bg-blue-600 hover:bg-blue-700 text-white'
              : 'bg-blue-600 hover:bg-blue-700 text-white'
          }`}
        >
          🔄 Refresh Tax Analysis
        </button>
      </div>
    </div>
  );
};

export default SwissTaxOptimizationEngine;
