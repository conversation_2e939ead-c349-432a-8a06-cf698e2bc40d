import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Expense,
  ExpenseCategory,
  ExpensePriority,
  ExpenseFrequency,
  SWISS_EXPENSE_CATEGORIES,
  calculateMonthlyAmount,
  calculateAnnualAmount,
  getCategoryInfo,
} from '../types/expenses';
import { useLocalStorage } from '../hooks/useLocalStorage';

interface ExpenseManagerProps {
  darkMode: boolean;
  onExpensesChange?: (expenses: Expense[]) => void;
}

const ExpenseManager: React.FC<ExpenseManagerProps> = ({
  darkMode,
  onExpensesChange,
}) => {
  const { t } = useTranslation();

  // Load expenses from localStorage
  const [expenses, setExpenses] = useLocalStorage<Expense[]>('fire-expenses', [
    {
      id: '1',
      category: 'housing',
      name: 'Rent/Mortgage',
      amount: 2500,
      priority: 'essential',
      frequency: 'monthly',
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: '2',
      category: 'food',
      name: 'Groceries',
      amount: 600,
      priority: 'essential',
      frequency: 'monthly',
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: '3',
      category: 'transportation',
      name: 'Public Transport',
      amount: 150,
      priority: 'essential',
      frequency: 'monthly',
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
  ]);

  const [showAddForm, setShowAddForm] = useState(false);
  const [editingExpense, setEditingExpense] = useState<Expense | null>(null);
  const [filterCategory, setFilterCategory] = useState<ExpenseCategory | 'all'>(
    'all',
  );
  const [filterPriority, setFilterPriority] = useState<ExpensePriority | 'all'>(
    'all',
  );

  // Form state for adding/editing expenses
  const [formData, setFormData] = useState({
    category: 'other' as ExpenseCategory,
    subcategory: '',
    name: '',
    amount: '',
    priority: 'important' as ExpensePriority,
    frequency: 'monthly' as ExpenseFrequency,
    description: '',
    tags: '',
  });

  // Calculate expense summaries
  const expenseSummary = useMemo(() => {
    const activeExpenses = expenses.filter(e => e.isActive);

    const totalMonthly = activeExpenses.reduce(
      (sum, expense) => sum + calculateMonthlyAmount(expense),
      0,
    );

    const totalAnnual = activeExpenses.reduce(
      (sum, expense) => sum + calculateAnnualAmount(expense),
      0,
    );

    const byPriority = activeExpenses.reduce(
      (acc, expense) => {
        const monthly = calculateMonthlyAmount(expense);
        acc[expense.priority] = (acc[expense.priority] || 0) + monthly;
        return acc;
      },
      {} as Record<ExpensePriority, number>,
    );

    const byCategory = activeExpenses.reduce(
      (acc, expense) => {
        const monthly = calculateMonthlyAmount(expense);
        acc[expense.category] = (acc[expense.category] || 0) + monthly;
        return acc;
      },
      {} as Record<ExpenseCategory, number>,
    );

    const essentialExpenses = byPriority.essential || 0;
    const nonEssentialExpenses =
      (byPriority.important || 0) + (byPriority['nice-to-have'] || 0);

    return {
      totalMonthly,
      totalAnnual,
      byCategory,
      byPriority,
      essentialExpenses,
      nonEssentialExpenses,
      swissSpecificExpenses: activeExpenses
        .filter(e => getCategoryInfo(e.category).swissSpecific)
        .reduce((sum, e) => sum + calculateMonthlyAmount(e), 0),
    };
  }, [expenses]);

  // Filtered expenses
  const filteredExpenses = useMemo(() => {
    return expenses.filter(expense => {
      const categoryMatch =
        filterCategory === 'all' || expense.category === filterCategory;
      const priorityMatch =
        filterPriority === 'all' || expense.priority === filterPriority;
      return categoryMatch && priorityMatch;
    });
  }, [expenses, filterCategory, filterPriority]);

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const expenseData: Expense = {
      id: editingExpense?.id || Date.now().toString(),
      category: formData.category,
      subcategory: formData.subcategory || undefined,
      name: formData.name,
      amount: parseFloat(formData.amount) || 0,
      priority: formData.priority,
      frequency: formData.frequency,
      description: formData.description || undefined,
      isActive: true,
      tags: formData.tags
        ? formData.tags.split(',').map(t => t.trim())
        : undefined,
      createdAt: editingExpense?.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    if (editingExpense) {
      // Update existing expense
      const updatedExpenses = expenses.map(e =>
        e.id === editingExpense.id ? expenseData : e,
      );
      setExpenses(updatedExpenses);
      onExpensesChange?.(updatedExpenses);
    } else {
      // Add new expense
      const newExpenses = [...expenses, expenseData];
      setExpenses(newExpenses);
      onExpensesChange?.(newExpenses);
    }

    // Reset form
    setFormData({
      category: 'other',
      subcategory: '',
      name: '',
      amount: '',
      priority: 'important',
      frequency: 'monthly',
      description: '',
      tags: '',
    });
    setShowAddForm(false);
    setEditingExpense(null);
  };

  // Handle edit
  const handleEdit = (expense: Expense) => {
    setEditingExpense(expense);
    setFormData({
      category: expense.category,
      subcategory: expense.subcategory || '',
      name: expense.name,
      amount: expense.amount.toString(),
      priority: expense.priority,
      frequency: expense.frequency,
      description: expense.description || '',
      tags: expense.tags?.join(', ') || '',
    });
    setShowAddForm(true);
  };

  // Handle delete
  const handleDelete = (expenseId: string) => {
    const updatedExpenses = expenses.filter(e => e.id !== expenseId);
    setExpenses(updatedExpenses);
    onExpensesChange?.(updatedExpenses);
  };

  // Handle toggle active
  const handleToggleActive = (expenseId: string) => {
    const updatedExpenses = expenses.map(e =>
      e.id === expenseId ? { ...e, isActive: !e.isActive } : e,
    );
    setExpenses(updatedExpenses);
    onExpensesChange?.(updatedExpenses);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('de-CH', {
      style: 'currency',
      currency: 'CHF',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getPriorityColor = (priority: ExpensePriority) => {
    switch (priority) {
      case 'essential':
        return 'text-red-600 bg-red-100 dark:bg-red-900/30';
      case 'important':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30';
      case 'nice-to-have':
        return 'text-green-600 bg-green-100 dark:bg-green-900/30';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30';
    }
  };

  return (
    <div className='space-y-6'>
      {/* Summary Cards */}
      <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
        <div
          className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}
        >
          <h3 className='text-sm font-medium text-gray-500 dark:text-gray-400'>
            Monthly Total
          </h3>
          <p className='text-2xl font-bold text-blue-600'>
            {formatCurrency(expenseSummary.totalMonthly)}
          </p>
        </div>
        <div
          className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}
        >
          <h3 className='text-sm font-medium text-gray-500 dark:text-gray-400'>
            Essential
          </h3>
          <p className='text-2xl font-bold text-red-600'>
            {formatCurrency(expenseSummary.essentialExpenses)}
          </p>
        </div>
        <div
          className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}
        >
          <h3 className='text-sm font-medium text-gray-500 dark:text-gray-400'>
            Non-Essential
          </h3>
          <p className='text-2xl font-bold text-green-600'>
            {formatCurrency(expenseSummary.nonEssentialExpenses)}
          </p>
        </div>
      </div>

      {/* Controls */}
      <div className='flex flex-wrap gap-4 items-center justify-between'>
        <div className='flex gap-4'>
          {/* Category Filter */}
          <select
            value={filterCategory}
            onChange={e =>
              setFilterCategory(e.target.value as ExpenseCategory | 'all')
            }
            className={`px-3 py-2 rounded-lg border ${
              darkMode
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            } focus:outline-none focus:ring-2 focus:ring-blue-500`}
          >
            <option value='all'>All Categories</option>
            {SWISS_EXPENSE_CATEGORIES.map(cat => (
              <option key={cat.id} value={cat.id}>
                {cat.icon} {cat.name}
              </option>
            ))}
          </select>

          {/* Priority Filter */}
          <select
            value={filterPriority}
            onChange={e =>
              setFilterPriority(e.target.value as ExpensePriority | 'all')
            }
            className={`px-3 py-2 rounded-lg border ${
              darkMode
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            } focus:outline-none focus:ring-2 focus:ring-blue-500`}
          >
            <option value='all'>All Priorities</option>
            <option value='essential'>Essential</option>
            <option value='important'>Important</option>
            <option value='nice-to-have'>Nice to Have</option>
          </select>
        </div>

        <button
          onClick={() => setShowAddForm(true)}
          className='px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors'
        >
          + Add Expense
        </button>
      </div>

      {/* Add/Edit Form */}
      {showAddForm && (
        <div
          className={`p-6 rounded-lg border ${
            darkMode
              ? 'bg-gray-800 border-gray-600'
              : 'bg-white border-gray-300'
          }`}
        >
          <h3 className='text-lg font-semibold mb-4'>
            {editingExpense ? 'Edit Expense' : 'Add New Expense'}
          </h3>

          <form
            onSubmit={handleSubmit}
            className='grid grid-cols-1 md:grid-cols-2 gap-4'
          >
            {/* Category */}
            <div>
              <label className='block text-sm font-medium mb-2'>Category</label>
              <select
                value={formData.category}
                onChange={e =>
                  setFormData({
                    ...formData,
                    category: e.target.value as ExpenseCategory,
                  })
                }
                className={`w-full px-3 py-2 rounded-lg border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                required
              >
                {SWISS_EXPENSE_CATEGORIES.map(cat => (
                  <option key={cat.id} value={cat.id}>
                    {cat.icon} {cat.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Name */}
            <div>
              <label className='block text-sm font-medium mb-2'>Name</label>
              <input
                type='text'
                value={formData.name}
                onChange={e =>
                  setFormData({ ...formData, name: e.target.value })
                }
                className={`w-full px-3 py-2 rounded-lg border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                required
              />
            </div>

            {/* Amount */}
            <div>
              <label className='block text-sm font-medium mb-2'>
                Amount (CHF)
              </label>
              <input
                type='number'
                step='0.01'
                value={formData.amount}
                onChange={e =>
                  setFormData({ ...formData, amount: e.target.value })
                }
                className={`w-full px-3 py-2 rounded-lg border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                required
              />
            </div>

            {/* Priority */}
            <div>
              <label className='block text-sm font-medium mb-2'>Priority</label>
              <select
                value={formData.priority}
                onChange={e =>
                  setFormData({
                    ...formData,
                    priority: e.target.value as ExpensePriority,
                  })
                }
                className={`w-full px-3 py-2 rounded-lg border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                required
              >
                <option value='essential'>Essential</option>
                <option value='important'>Important</option>
                <option value='nice-to-have'>Nice to Have</option>
              </select>
            </div>

            {/* Frequency */}
            <div>
              <label className='block text-sm font-medium mb-2'>
                Frequency
              </label>
              <select
                value={formData.frequency}
                onChange={e =>
                  setFormData({
                    ...formData,
                    frequency: e.target.value as ExpenseFrequency,
                  })
                }
                className={`w-full px-3 py-2 rounded-lg border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                required
              >
                <option value='monthly'>Monthly</option>
                <option value='quarterly'>Quarterly</option>
                <option value='annually'>Annually</option>
                <option value='one-time'>One-time</option>
              </select>
            </div>

            {/* Subcategory */}
            <div>
              <label className='block text-sm font-medium mb-2'>
                Subcategory (Optional)
              </label>
              <input
                type='text'
                value={formData.subcategory}
                onChange={e =>
                  setFormData({ ...formData, subcategory: e.target.value })
                }
                className={`w-full px-3 py-2 rounded-lg border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                } focus:outline-none focus:ring-2 focus:ring-blue-500`}
              />
            </div>

            {/* Form Actions */}
            <div className='md:col-span-2 flex gap-4 pt-4'>
              <button
                type='submit'
                className='px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors'
              >
                {editingExpense ? 'Update' : 'Add'} Expense
              </button>
              <button
                type='button'
                onClick={() => {
                  setShowAddForm(false);
                  setEditingExpense(null);
                  setFormData({
                    category: 'other',
                    subcategory: '',
                    name: '',
                    amount: '',
                    priority: 'important',
                    frequency: 'monthly',
                    description: '',
                    tags: '',
                  });
                }}
                className={`px-6 py-2 rounded-lg font-medium transition-colors ${
                  darkMode
                    ? 'bg-gray-700 hover:bg-gray-600 text-white'
                    : 'bg-gray-200 hover:bg-gray-300 text-gray-800'
                }`}
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Expenses List */}
      <div className='space-y-3'>
        {filteredExpenses.map(expense => {
          const categoryInfo = getCategoryInfo(expense.category);
          return (
            <div
              key={expense.id}
              className={`p-4 rounded-lg border ${
                darkMode
                  ? 'bg-gray-800 border-gray-600'
                  : 'bg-white border-gray-300'
              } ${!expense.isActive ? 'opacity-50' : ''}`}
            >
              <div className='flex items-center justify-between'>
                <div className='flex items-center space-x-4'>
                  <span className='text-2xl'>{categoryInfo.icon}</span>
                  <div>
                    <h4 className='font-medium'>{expense.name}</h4>
                    <p className='text-sm text-gray-500'>
                      {categoryInfo.name}
                      {expense.subcategory && ` • ${expense.subcategory}`}
                    </p>
                  </div>
                </div>

                <div className='flex items-center space-x-4'>
                  <div className='text-right'>
                    <p className='font-semibold'>
                      {formatCurrency(calculateMonthlyAmount(expense))}/month
                    </p>
                    <p className='text-sm text-gray-500'>
                      {formatCurrency(expense.amount)}/{expense.frequency}
                    </p>
                  </div>

                  <span
                    className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(expense.priority)}`}
                  >
                    {expense.priority}
                  </span>

                  <div className='flex space-x-2'>
                    <button
                      onClick={() => handleToggleActive(expense.id)}
                      className={`p-2 rounded-lg transition-colors ${
                        expense.isActive
                          ? 'text-green-600 hover:bg-green-100 dark:hover:bg-green-900/30'
                          : 'text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700'
                      }`}
                      title={expense.isActive ? 'Deactivate' : 'Activate'}
                    >
                      {expense.isActive ? '✓' : '○'}
                    </button>
                    <button
                      onClick={() => handleEdit(expense)}
                      className='p-2 text-blue-600 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-lg transition-colors'
                      title='Edit'
                    >
                      ✏️
                    </button>
                    <button
                      onClick={() => handleDelete(expense.id)}
                      className='p-2 text-red-600 hover:bg-red-100 dark:hover:bg-red-900/30 rounded-lg transition-colors'
                      title='Delete'
                    >
                      🗑️
                    </button>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {filteredExpenses.length === 0 && (
        <div className='text-center py-8 text-gray-500'>
          No expenses found. Add your first expense to get started!
        </div>
      )}
    </div>
  );
};

export default ExpenseManager;
