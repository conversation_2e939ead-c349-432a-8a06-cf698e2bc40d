import React, { useRef, useEffect, useCallback, useMemo } from 'react';
import * as d3 from 'd3';

interface ChartData {
  date: Date;
  value: number;
  label?: string;
  category?: string;
}

interface ChartConfig {
  type: 'line' | 'area' | 'bar' | 'scatter';
  width?: number;
  height?: number;
  margin?: { top: number; right: number; bottom: number; left: number };
  color?: string;
  gradient?: boolean;
  animated?: boolean;
  interactive?: boolean;
  responsive?: boolean;
}

interface EnhancedD3ChartProps {
  data: ChartData[];
  config: ChartConfig;
  darkMode: boolean;
  onDataPointClick?: (data: ChartData) => void;
  onDataPointHover?: (data: ChartData | null) => void;
  className?: string;
}

const EnhancedD3Chart: React.FC<EnhancedD3ChartProps> = ({
  data,
  config,
  darkMode,
  onDataPointClick,
  onDataPointHover,
  className = '',
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Default configuration
  const defaultConfig: Required<ChartConfig> = {
    type: 'line',
    width: 600,
    height: 400,
    margin: { top: 20, right: 30, bottom: 40, left: 50 },
    color: '#3B82F6',
    gradient: false,
    animated: true,
    interactive: true,
    responsive: true,
  };

  const chartConfig = useMemo(() => ({ ...defaultConfig, ...config }), [config]);

  // Responsive width calculation
  const getResponsiveWidth = useCallback(() => {
    if (!chartConfig.responsive || !containerRef.current) {
      return chartConfig.width;
    }
    const containerWidth = containerRef.current.clientWidth;
    return Math.max(300, Math.min(containerWidth - 20, chartConfig.width));
  }, [chartConfig.responsive, chartConfig.width]);

  // Format currency for Swiss locale
  const formatCurrency = useCallback((value: number) => {
    return new Intl.NumberFormat('de-CH', {
      style: 'currency',
      currency: 'CHF',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  }, []);

  // Format date for Swiss locale
  const formatDate = useCallback((date: Date) => {
    return new Intl.DateTimeFormat('de-CH', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(date);
  }, []);

  // Create scales
  const createScales = useCallback((width: number, height: number) => {
    const { margin } = chartConfig;
    const innerWidth = width - margin.left - margin.right;
    const innerHeight = height - margin.top - margin.bottom;

    const xScale = d3
      .scaleTime()
      .domain(d3.extent(data, d => d.date) as [Date, Date])
      .range([0, innerWidth]);

    const yScale = d3
      .scaleLinear()
      .domain(d3.extent(data, d => d.value) as [number, number])
      .nice()
      .range([innerHeight, 0]);

    return { xScale, yScale, innerWidth, innerHeight };
  }, [data, chartConfig]);

  // Create line generator
  const createLineGenerator = useCallback((xScale: d3.ScaleTime<number, number>, yScale: d3.ScaleLinear<number, number>) => {
    return d3
      .line<ChartData>()
      .x(d => xScale(d.date))
      .y(d => yScale(d.value))
      .curve(d3.curveMonotoneX);
  }, []);

  // Create area generator
  const createAreaGenerator = useCallback((xScale: d3.ScaleTime<number, number>, yScale: d3.ScaleLinear<number, number>) => {
    return d3
      .area<ChartData>()
      .x(d => xScale(d.date))
      .y0(yScale(0))
      .y1(d => yScale(d.value))
      .curve(d3.curveMonotoneX);
  }, []);

  // Show tooltip
  const showTooltip = useCallback((event: MouseEvent, d: ChartData) => {
    if (!tooltipRef.current) return;

    const tooltip = d3.select(tooltipRef.current);
    tooltip
      .style('opacity', 1)
      .style('left', `${event.pageX + 10}px`)
      .style('top', `${event.pageY - 10}px`)
      .html(`
        <div class="p-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded shadow-lg">
          <div class="font-semibold">${formatDate(d.date)}</div>
          <div class="text-sm">${formatCurrency(d.value)}</div>
          ${d.label ? `<div class="text-xs text-gray-500">${d.label}</div>` : ''}
        </div>
      `);

    onDataPointHover?.(d);
  }, [formatDate, formatCurrency, onDataPointHover]);

  // Hide tooltip
  const hideTooltip = useCallback(() => {
    if (!tooltipRef.current) return;

    d3.select(tooltipRef.current).style('opacity', 0);
    onDataPointHover?.(null);
  }, [onDataPointHover]);

  // Main chart rendering effect
  useEffect(() => {
    if (!svgRef.current || !data.length) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    const width = getResponsiveWidth();
    const height = chartConfig.height;
    const { margin } = chartConfig;

    // Update SVG dimensions
    svg.attr('width', width).attr('height', height);

    const { xScale, yScale, innerWidth, innerHeight } = createScales(width, height);

    // Create main group
    const g = svg
      .append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    // Add gradient definition if needed
    if (chartConfig.gradient) {
      const gradient = svg
        .append('defs')
        .append('linearGradient')
        .attr('id', 'chart-gradient')
        .attr('gradientUnits', 'userSpaceOnUse')
        .attr('x1', 0).attr('y1', 0)
        .attr('x2', 0).attr('y2', innerHeight);

      gradient
        .append('stop')
        .attr('offset', '0%')
        .attr('stop-color', chartConfig.color)
        .attr('stop-opacity', 0.8);

      gradient
        .append('stop')
        .attr('offset', '100%')
        .attr('stop-color', chartConfig.color)
        .attr('stop-opacity', 0.1);
    }

    // Add axes
    const xAxis = d3.axisBottom(xScale).tickFormat(d3.timeFormat('%b %Y'));
    const yAxis = d3.axisLeft(yScale).tickFormat(d => formatCurrency(d as number));

    g.append('g')
      .attr('class', 'x-axis')
      .attr('transform', `translate(0,${innerHeight})`)
      .call(xAxis)
      .selectAll('text')
      .style('fill', darkMode ? '#9CA3AF' : '#6B7280');

    g.append('g')
      .attr('class', 'y-axis')
      .call(yAxis)
      .selectAll('text')
      .style('fill', darkMode ? '#9CA3AF' : '#6B7280');

    // Style axis lines
    g.selectAll('.domain, .tick line')
      .style('stroke', darkMode ? '#4B5563' : '#E5E7EB');

    // Render chart based on type
    if (chartConfig.type === 'line') {
      const line = createLineGenerator(xScale, yScale);
      const path = g
        .append('path')
        .datum(data)
        .attr('fill', 'none')
        .attr('stroke', chartConfig.color)
        .attr('stroke-width', 2)
        .attr('d', line);

      if (chartConfig.animated) {
        const totalLength = path.node()?.getTotalLength() || 0;
        path
          .attr('stroke-dasharray', `${totalLength} ${totalLength}`)
          .attr('stroke-dashoffset', totalLength)
          .transition()
          .duration(1500)
          .ease(d3.easeLinear)
          .attr('stroke-dashoffset', 0);
      }
    } else if (chartConfig.type === 'area') {
      const area = createAreaGenerator(xScale, yScale);
      const path = g
        .append('path')
        .datum(data)
        .attr('fill', chartConfig.gradient ? 'url(#chart-gradient)' : chartConfig.color)
        .attr('fill-opacity', 0.6)
        .attr('stroke', chartConfig.color)
        .attr('stroke-width', 2)
        .attr('d', area);

      if (chartConfig.animated) {
        path
          .attr('fill-opacity', 0)
          .transition()
          .duration(1000)
          .attr('fill-opacity', 0.6);
      }
    }

    // Add interactive data points
    if (chartConfig.interactive) {
      g.selectAll('.data-point')
        .data(data)
        .enter()
        .append('circle')
        .attr('class', 'data-point')
        .attr('cx', d => xScale(d.date))
        .attr('cy', d => yScale(d.value))
        .attr('r', 4)
        .attr('fill', chartConfig.color)
        .attr('stroke', 'white')
        .attr('stroke-width', 2)
        .style('cursor', 'pointer')
        .on('mouseover', function(event, d) {
          d3.select(this).attr('r', 6);
          showTooltip(event, d);
        })
        .on('mouseout', function() {
          d3.select(this).attr('r', 4);
          hideTooltip();
        })
        .on('click', (event, d) => {
          onDataPointClick?.(d);
        });

      if (chartConfig.animated) {
        g.selectAll('.data-point')
          .attr('r', 0)
          .transition()
          .duration(800)
          .delay((d, i) => i * 50)
          .attr('r', 4);
      }
    }

  }, [data, chartConfig, darkMode, getResponsiveWidth, createScales, createLineGenerator, createAreaGenerator, showTooltip, hideTooltip, onDataPointClick, formatCurrency]);

  // Handle window resize for responsive charts
  useEffect(() => {
    if (!chartConfig.responsive) return;

    const handleResize = () => {
      // Trigger re-render on resize
      if (svgRef.current) {
        const event = new CustomEvent('resize');
        svgRef.current.dispatchEvent(event);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [chartConfig.responsive]);

  return (
    <div ref={containerRef} className={`relative ${className}`}>
      <svg
        ref={svgRef}
        className={`${darkMode ? 'bg-gray-900/30' : 'bg-white/30'} rounded-lg`}
      />
      <div
        ref={tooltipRef}
        className="absolute pointer-events-none opacity-0 transition-opacity duration-200 z-10"
      />
    </div>
  );
};

export default EnhancedD3Chart;
