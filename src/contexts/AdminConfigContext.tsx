/**
 * Admin Configuration Context for Swiss Budget Pro
 * Manages system-wide configuration settings including inflation, market data, and defaults
 */

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from 'react';

// Configuration interfaces
export interface InflationConfig {
  currentCPI: number;
  coreCPI: number;
  housingCosts: number;
  healthcareCosts: number;
  energyCosts: number;
  foodCosts: number;
  forecast12M: number;
  forecast24M: number;
  historicalAverage: number;
  targetInflation: number;
  lastUpdated: string;
  source: string;
  autoUpdate: boolean;
}

export interface MarketDataConfig {
  smiIndex: number;
  smiChange: number;
  spiIndex: number;
  spiYearReturn: number;
  bondYield10Y: number;
  bondYield2Y: number;
  volatilityIndex: number;
  marketSentiment: 'bullish' | 'neutral' | 'bearish';
  expectedReturns: {
    stocks: number;
    bonds: number;
    cash: number;
    realEstate: number;
    commodities: number;
  };
  riskPremiums: {
    equity: number;
    credit: number;
    liquidity: number;
  };
  lastUpdated: string;
  source: string;
  autoUpdate: boolean;
}

export interface TaxConfig {
  federalRates: Array<{
    min: number;
    max: number;
    rate: number;
  }>;
  cantonalRates: Record<
    string,
    {
      rate: number;
      wealthTax: number;
      wealthTaxThreshold: number;
    }
  >;
  socialInsurance: {
    ahv: { rate: number; maxIncome: number };
    alv: {
      rate: number;
      maxIncome: number;
      additionalRate: number;
      additionalThreshold: number;
    };
    nbv: { rate: number };
  };
  pillar3a: {
    maxContribution: number;
    maxContributionSelfEmployed: number;
    withdrawalAge: number;
    earlyWithdrawalPenalty: number;
  };
  lastUpdated: string;
  source: string;
}

export interface HealthcareConfig {
  deductibles: number[];
  premiumsByCanton: Record<
    string,
    {
      average: number;
      range: [number, number];
    }
  >;
  subsidyThresholds: Record<
    string,
    {
      single: number;
      couple: number;
      family: number;
    }
  >;
  costInflation: number;
  lastUpdated: string;
  source: string;
}

export interface SystemConfig {
  calculationDefaults: {
    withdrawalRate: number;
    safeWithdrawalRate: number;
    conservativeWithdrawalRate: number;
    investmentReturn: number;
    bondReturn: number;
    cashReturn: number;
  };
  userInterface: {
    defaultLanguage: string;
    defaultCurrency: string;
    numberFormat: {
      decimalSeparator: string;
      thousandsSeparator: string;
    };
    dateFormat: string;
  };
  features: {
    enableAdvancedCalculations: boolean;
    enableTaxOptimization: boolean;
    enableHealthcareOptimization: boolean;
    enableCantonComparison: boolean;
    enableExportFeatures: boolean;
  };
  performance: {
    autoSaveInterval: number;
    calculationTimeout: number;
    maxHistoryEntries: number;
  };
  lastUpdated: string;
}

export interface AdminConfig {
  inflation: InflationConfig;
  marketData: MarketDataConfig;
  tax: TaxConfig;
  healthcare: HealthcareConfig;
  system: SystemConfig;
  version: string;
  lastModified: string;
  modifiedBy: string;
}

export interface AuditLogEntry {
  id: string;
  timestamp: string;
  action: 'create' | 'update' | 'delete' | 'reset' | 'import' | 'export';
  section: string;
  field?: string;
  oldValue?: any;
  newValue?: any;
  user: string;
  description: string;
}

interface AdminConfigContextType {
  config: AdminConfig;
  updateConfig: (newConfig: Partial<AdminConfig>) => Promise<void>;
  updateSection: <K extends keyof AdminConfig>(
    section: K,
    data: Partial<AdminConfig[K]>
  ) => Promise<void>;
  resetToDefaults: () => Promise<void>;
  exportConfig: () => string;
  importConfig: (configJson: string) => Promise<void>;
  getAuditLog: () => AuditLogEntry[];
  isLoading: boolean;
  lastSaved: string | null;
  hasUnsavedChanges: boolean;
}

const AdminConfigContext = createContext<AdminConfigContextType | undefined>(
  undefined,
);

// Default configuration values
const getDefaultConfig = (): AdminConfig => ({
  inflation: {
    currentCPI: 1.5,
    coreCPI: 1.3,
    housingCosts: 2.0,
    healthcareCosts: 3.0,
    energyCosts: 0.0,
    foodCosts: 1.0,
    forecast12M: 1.7,
    forecast24M: 1.9,
    historicalAverage: 1.8,
    targetInflation: 2.0,
    lastUpdated: new Date().toISOString(),
    source: 'Swiss National Bank',
    autoUpdate: false,
  },
  marketData: {
    smiIndex: 11800,
    smiChange: 0.0,
    spiIndex: 15200,
    spiYearReturn: 5.0,
    bondYield10Y: 0.7,
    bondYield2Y: 0.5,
    volatilityIndex: 18.0,
    marketSentiment: 'neutral',
    expectedReturns: {
      stocks: 7.0,
      bonds: 2.5,
      cash: 1.0,
      realEstate: 4.0,
      commodities: 3.0,
    },
    riskPremiums: {
      equity: 5.5,
      credit: 1.5,
      liquidity: 0.5,
    },
    lastUpdated: new Date().toISOString(),
    source: 'SIX Swiss Exchange',
    autoUpdate: false,
  },
  tax: {
    federalRates: [
      { min: 0, max: 14500, rate: 0 },
      { min: 14500, max: 31600, rate: 0.077 },
      { min: 31600, max: 41400, rate: 0.088 },
      { min: 41400, max: 55200, rate: 0.11 },
      { min: 55200, max: 72500, rate: 0.132 },
      { min: 72500, max: 78100, rate: 0.154 },
      { min: 78100, max: *********, rate: 0.165 },
    ],
    cantonalRates: {
      ZH: { rate: 0.12, wealthTax: 0.002, wealthTaxThreshold: 100000 },
      GE: { rate: 0.15, wealthTax: 0.003, wealthTaxThreshold: 75000 },
      VD: { rate: 0.14, wealthTax: 0.0025, wealthTaxThreshold: 80000 },
      BE: { rate: 0.13, wealthTax: 0.002, wealthTaxThreshold: 90000 },
      ZG: { rate: 0.08, wealthTax: 0.001, wealthTaxThreshold: 150000 },
      BS: { rate: 0.16, wealthTax: 0.003, wealthTaxThreshold: 70000 },
      BL: { rate: 0.11, wealthTax: 0.002, wealthTaxThreshold: 95000 },
      AG: { rate: 0.1, wealthTax: 0.0015, wealthTaxThreshold: 100000 },
    },
    socialInsurance: {
      ahv: { rate: 0.0525, maxIncome: 88200 },
      alv: {
        rate: 0.011,
        maxIncome: 148200,
        additionalRate: 0.005,
        additionalThreshold: 148200,
      },
      nbv: { rate: 0.0125 },
    },
    pillar3a: {
      maxContribution: 7056,
      maxContributionSelfEmployed: 35280,
      withdrawalAge: 60,
      earlyWithdrawalPenalty: 0.05,
    },
    lastUpdated: new Date().toISOString(),
    source: 'Swiss Federal Tax Administration',
  },
  healthcare: {
    deductibles: [300, 500, 1000, 1500, 2000, 2500],
    premiumsByCanton: {
      ZH: { average: 450, range: [380, 520] },
      GE: { average: 520, range: [450, 590] },
      VD: { average: 480, range: [410, 550] },
      BE: { average: 420, range: [360, 480] },
      ZG: { average: 380, range: [320, 440] },
      BS: { average: 500, range: [430, 570] },
      BL: { average: 440, range: [380, 500] },
      AG: { average: 400, range: [340, 460] },
    },
    subsidyThresholds: {
      ZH: { single: 35000, couple: 55000, family: 75000 },
      GE: { single: 32000, couple: 50000, family: 70000 },
      VD: { single: 33000, couple: 52000, family: 72000 },
      BE: { single: 34000, couple: 54000, family: 74000 },
      ZG: { single: 40000, couple: 60000, family: 80000 },
      BS: { single: 31000, couple: 49000, family: 69000 },
      BL: { single: 35000, couple: 55000, family: 75000 },
      AG: { single: 36000, couple: 56000, family: 76000 },
    },
    costInflation: 3.0,
    lastUpdated: new Date().toISOString(),
    source: 'Federal Office of Public Health',
  },
  system: {
    calculationDefaults: {
      withdrawalRate: 0.04,
      safeWithdrawalRate: 0.035,
      conservativeWithdrawalRate: 0.03,
      investmentReturn: 0.07,
      bondReturn: 0.025,
      cashReturn: 0.01,
    },
    userInterface: {
      defaultLanguage: 'de-CH',
      defaultCurrency: 'CHF',
      numberFormat: {
        decimalSeparator: '.',
        thousandsSeparator: "'",
      },
      dateFormat: 'dd.mm.yyyy',
    },
    features: {
      enableAdvancedCalculations: true,
      enableTaxOptimization: true,
      enableHealthcareOptimization: true,
      enableCantonComparison: true,
      enableExportFeatures: true,
    },
    performance: {
      autoSaveInterval: 30000, // 30 seconds
      calculationTimeout: 10000, // 10 seconds
      maxHistoryEntries: 100,
    },
    lastUpdated: new Date().toISOString(),
  },
  version: '1.0.0',
  lastModified: new Date().toISOString(),
  modifiedBy: 'system',
});

export const AdminConfigProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [config, setConfig] = useState<AdminConfig>(getDefaultConfig());
  const [isLoading, setIsLoading] = useState(false);
  const [lastSaved, setLastSaved] = useState<string | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [auditLog, setAuditLog] = useState<AuditLogEntry[]>([]);

  // Load configuration from localStorage on mount
  useEffect(() => {
    const loadConfig = () => {
      try {
        const savedConfig = localStorage.getItem('swissBudgetPro_adminConfig');
        const savedAuditLog = localStorage.getItem(
          'swissBudgetPro_adminAuditLog',
        );
        const savedLastSaved = localStorage.getItem(
          'swissBudgetPro_adminLastSaved',
        );

        if (savedConfig) {
          const parsedConfig = JSON.parse(savedConfig);
          setConfig({ ...getDefaultConfig(), ...parsedConfig });
        }

        if (savedAuditLog) {
          setAuditLog(JSON.parse(savedAuditLog));
        }

        if (savedLastSaved) {
          setLastSaved(savedLastSaved);
        }
      } catch (error) {
        console.error('Failed to load admin configuration:', error);
        addAuditEntry(
          'create',
          'system',
          undefined,
          undefined,
          undefined,
          'Failed to load configuration, using defaults',
        );
      }
    };

    loadConfig();
  }, []);

  const addAuditEntry = (
    action: AuditLogEntry['action'],
    section: string,
    field?: string,
    oldValue?: any,
    newValue?: any,
    description?: string,
  ) => {
    const entry: AuditLogEntry = {
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
      action,
      section,
      field,
      oldValue,
      newValue,
      user: 'admin', // In a real app, this would come from authentication
      description:
        description || `${action} ${section}${field ? `.${field}` : ''}`,
    };

    setAuditLog(prev => {
      const newLog = [entry, ...prev].slice(0, 1000); // Keep last 1000 entries
      localStorage.setItem(
        'swissBudgetPro_adminAuditLog',
        JSON.stringify(newLog),
      );
      return newLog;
    });
  };

  const saveConfig = async (newConfig: AdminConfig) => {
    setIsLoading(true);
    try {
      const configToSave = {
        ...newConfig,
        lastModified: new Date().toISOString(),
        modifiedBy: 'admin',
      };

      localStorage.setItem(
        'swissBudgetPro_adminConfig',
        JSON.stringify(configToSave),
      );
      const timestamp = new Date().toISOString();
      localStorage.setItem('swissBudgetPro_adminLastSaved', timestamp);

      setConfig(configToSave);
      setLastSaved(timestamp);
      setHasUnsavedChanges(false);

      addAuditEntry(
        'update',
        'configuration',
        undefined,
        undefined,
        undefined,
        'Configuration saved successfully',
      );
    } catch (error) {
      console.error('Failed to save configuration:', error);
      addAuditEntry(
        'update',
        'configuration',
        undefined,
        undefined,
        undefined,
        `Failed to save configuration: ${error}`,
      );
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateConfig = async (newConfig: Partial<AdminConfig>) => {
    const updatedConfig = { ...config, ...newConfig };
    await saveConfig(updatedConfig);
  };

  const updateSection = async <K extends keyof AdminConfig>(
    section: K,
    data: Partial<AdminConfig[K]>,
  ) => {
    const oldValue = config[section];
    const newValue = { ...config[section], ...data };

    addAuditEntry('update', section as string, undefined, oldValue, newValue);

    const updatedConfig = {
      ...config,
      [section]: newValue,
    };

    setConfig(updatedConfig);
    setHasUnsavedChanges(true);
  };

  const resetToDefaults = async () => {
    const defaultConfig = getDefaultConfig();
    addAuditEntry(
      'reset',
      'all',
      undefined,
      config,
      defaultConfig,
      'Reset all configuration to defaults',
    );
    await saveConfig(defaultConfig);
  };

  const exportConfig = (): string => {
    addAuditEntry(
      'export',
      'configuration',
      undefined,
      undefined,
      undefined,
      'Configuration exported',
    );
    return JSON.stringify(config, null, 2);
  };

  const importConfig = async (configJson: string) => {
    try {
      const importedConfig = JSON.parse(configJson);
      const mergedConfig = { ...getDefaultConfig(), ...importedConfig };

      addAuditEntry(
        'import',
        'configuration',
        undefined,
        config,
        mergedConfig,
        'Configuration imported',
      );
      await saveConfig(mergedConfig);
    } catch (error) {
      addAuditEntry(
        'import',
        'configuration',
        undefined,
        undefined,
        undefined,
        `Failed to import configuration: ${error}`,
      );
      throw new Error('Invalid configuration format');
    }
  };

  const getAuditLog = (): AuditLogEntry[] => {
    return auditLog;
  };

  const value: AdminConfigContextType = {
    config,
    updateConfig,
    updateSection,
    resetToDefaults,
    exportConfig,
    importConfig,
    getAuditLog,
    isLoading,
    lastSaved,
    hasUnsavedChanges,
  };

  return (
    <AdminConfigContext.Provider value={value}>
      {children}
    </AdminConfigContext.Provider>
  );
};

export const useAdminConfig = (): AdminConfigContextType => {
  const context = useContext(AdminConfigContext);
  if (!context) {
    throw new Error(
      'useAdminConfig must be used within an AdminConfigProvider',
    );
  }
  return context;
};
