/**
 * Example: Swiss Budget Component with Full Debug Integration
 * This shows how to integrate all debugging tools into an existing component
 */

import React, { useState, useEffect, useCallback } from 'react';
import { debugLogger } from '../utils/debug-logger';
import { performanceMonitor } from '../utils/performance-monitor';
import { errorReporter } from '../utils/error-reporter';
import { errorDiagnostics } from '../utils/error-diagnostics';
import {
  usePerformanceMonitoring,
  useSwissCalculationPerformance,
  useUserInteractionTracking,
} from '../hooks/usePerformanceMonitoring';

interface SwissBudgetData {
  monthlyIncome: number;
  monthlyExpenses: number;
  currentSavings: number;
  canton: string;
  retirementAge: number;
}

interface CalculationResults {
  fireYears: number;
  fireNumber: number;
  savingsRate: number;
  totalTax: number;
  netIncome: number;
}

const DebuggedSwissBudgetComponent: React.FC = () => {
  // State
  const [data, setData] = useState<SwissBudgetData>({
    monthlyIncome: 0,
    monthlyExpenses: 0,
    currentSavings: 0,
    canton: 'ZH',
    retirementAge: 65,
  });
  const [results, setResults] = useState<CalculationResults | null>(null);
  const [isCalculating, setIsCalculating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Debug hooks
  const { trackCalculation, trackAsyncOperation, getComponentStats } =
    usePerformanceMonitoring({
      componentName: 'SwissBudgetComponent',
      trackRenders: true,
      trackEffects: true,
      trackCalculations: true,
      slowRenderThreshold: 16,
    });

  const { trackTaxCalculation, trackFIRECalculation, trackDataPersistence } =
    useSwissCalculationPerformance('SwissBudgetComponent');

  const { trackFormSubmission, trackCalculationTrigger, trackTabChange } =
    useUserInteractionTracking('SwissBudgetComponent');

  // Component mount effect with debugging
  useEffect(() => {
    debugLogger.log('info', 'component', 'SwissBudgetComponent mounted');

    // Start error reporting for this component
    errorReporter.startTracking();

    // Load persisted data
    const loadData = async () => {
      try {
        await trackDataPersistence('load', async () => {
          const stored = localStorage.getItem('swissBudgetPro_data');
          if (stored) {
            const parsedData = JSON.parse(stored);
            setData(parsedData);
            debugLogger.log(
              'info',
              'data-persistence',
              'Data loaded from localStorage',
              {
                dataKeys: Object.keys(parsedData),
              }
            );
          }
        });
      } catch (error) {
        debugLogger.log('error', 'data-persistence', 'Failed to load data', {
          error,
        });
        setError('Failed to load saved data');
      }
    };

    loadData();

    return () => {
      debugLogger.log('info', 'component', 'SwissBudgetComponent unmounting');

      // Log component performance stats
      const stats = getComponentStats();
      debugLogger.log(
        'info',
        'performance',
        'Component performance summary',
        stats
      );
    };
  }, [trackDataPersistence, getComponentStats]);

  // Swiss tax calculation with debugging
  const calculateTax = useCallback(
    (income: number, canton: string) => {
      return trackTaxCalculation(canton, income, () => {
        // Simulate tax calculation
        const federalTax = income * 0.08; // Simplified
        const cantonalTax = income * 0.12; // Simplified
        const totalTax = federalTax + cantonalTax;

        debugLogger.log('debug', 'tax-calculation', 'Tax calculation details', {
          income,
          canton,
          federalTax,
          cantonalTax,
          totalTax,
          effectiveRate: totalTax / income,
        });

        return { federalTax, cantonalTax, totalTax };
      });
    },
    [trackTaxCalculation]
  );

  // FIRE calculation with debugging
  const calculateFIRE = useCallback(
    (scenario: SwissBudgetData) => {
      return trackFIRECalculation(scenario, () => {
        const annualIncome = scenario.monthlyIncome * 12;
        const annualExpenses = scenario.monthlyExpenses * 12;
        const annualSavings = annualIncome - annualExpenses;
        const savingsRate = annualSavings / annualIncome;

        // Calculate tax
        const taxResult = calculateTax(annualIncome, scenario.canton);
        const netIncome = annualIncome - taxResult.totalTax;
        const netSavings = netIncome - annualExpenses;

        // FIRE calculation
        const fireNumber = annualExpenses * 25; // 4% rule
        const yearsToFIRE = (fireNumber - scenario.currentSavings) / netSavings;

        const result = {
          fireYears: Math.max(0, yearsToFIRE),
          fireNumber,
          savingsRate,
          totalTax: taxResult.totalTax,
          netIncome,
        };

        debugLogger.log(
          'info',
          'fire-calculation',
          'FIRE calculation completed',
          {
            scenario: {
              income: scenario.monthlyIncome,
              expenses: scenario.monthlyExpenses,
              canton: scenario.canton,
            },
            result,
            assumptions: {
              withdrawalRate: 0.04,
              taxRate: taxResult.totalTax / annualIncome,
            },
          }
        );

        return result;
      });
    },
    [trackFIRECalculation, calculateTax]
  );

  // Main calculation function with comprehensive debugging
  const performCalculations = useCallback(async () => {
    if (data.monthlyIncome <= 0 || data.monthlyExpenses <= 0) {
      debugLogger.log(
        'warn',
        'validation',
        'Invalid input data for calculations',
        { data }
      );
      return;
    }

    setIsCalculating(true);
    setError(null);

    const endTracking = trackCalculationTrigger('full_calculation', {
      income: data.monthlyIncome,
      expenses: data.monthlyExpenses,
      canton: data.canton,
    });

    try {
      const calculationResult = await trackAsyncOperation(
        'full_calculation',
        async () => {
          // Simulate async calculation delay
          await new Promise(resolve => setTimeout(resolve, 100));

          return calculateFIRE(data);
        }
      );

      setResults(calculationResult);

      // Save results
      await trackDataPersistence('save', async () => {
        const dataToSave = {
          ...data,
          lastCalculation: new Date().toISOString(),
        };
        localStorage.setItem('swissBudgetPro_data', JSON.stringify(dataToSave));
      });

      debugLogger.log(
        'info',
        'calculation',
        'All calculations completed successfully',
        {
          results: calculationResult,
          processingTime: performance.now(),
        }
      );
    } catch (error) {
      const errorMessage = (error as Error).message;
      setError(errorMessage);

      debugLogger.log('error', 'calculation', 'Calculation failed', {
        error: errorMessage,
        data,
        stack: (error as Error).stack,
      });

      // Generate comprehensive error report
      const errorReport = await errorReporter.generateErrorReport(
        error as Error,
        {
          componentName: 'SwissBudgetComponent',
          type: 'calculation',
          data,
        }
      );

      debugLogger.log('error', 'error-reporting', 'Error report generated', {
        reportId: errorReport.id,
        severity: errorReport.reproduction.severity,
      });

      // Run diagnostics
      const diagnostics = errorDiagnostics.diagnoseError(error as Error);
      diagnostics.forEach(diagnostic => {
        debugLogger.log('warn', 'diagnostics', diagnostic.issue, diagnostic);
      });
    } finally {
      setIsCalculating(false);
      endTracking();
    }
  }, [
    data,
    calculateFIRE,
    trackAsyncOperation,
    trackCalculationTrigger,
    trackDataPersistence,
  ]);

  // Input change handler with debugging
  const handleInputChange = useCallback(
    (field: keyof SwissBudgetData, value: number | string) => {
      debugLogger.log('debug', 'user-input', `Input changed: ${field}`, {
        field,
        value,
        previousValue: data[field],
      });

      setData(prev => ({ ...prev, [field]: value }));

      // Trigger recalculation after input change
      if (
        field === 'monthlyIncome' ||
        field === 'monthlyExpenses' ||
        field === 'canton'
      ) {
        setTimeout(performCalculations, 500); // Debounce
      }
    },
    [data, performCalculations]
  );

  // Form submission with debugging
  const handleSubmit = useCallback(
    (e: React.FormEvent) => {
      e.preventDefault();

      const endTracking = trackFormSubmission('budget_form', data);

      debugLogger.log('info', 'user-interaction', 'Form submitted', { data });

      performCalculations().finally(() => {
        endTracking();
      });
    },
    [data, performCalculations, trackFormSubmission]
  );

  // Error boundary integration
  const handleError = useCallback(
    (error: Error) => {
      debugLogger.log('error', 'component', 'Component error caught', {
        error: error.message,
        stack: error.stack,
        componentState: { data, results, isCalculating },
      });
    },
    [data, results, isCalculating]
  );

  // Render with debug information
  debugLogger.log('debug', 'render', 'SwissBudgetComponent rendering', {
    renderCount: performance.now(),
    hasResults: !!results,
    isCalculating,
    hasError: !!error,
  });

  return (
    <div className='max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg'>
      <h1 className='text-3xl font-bold text-gray-800 mb-6'>
        🇨🇭 Swiss Budget Pro (Debug Mode)
      </h1>

      {/* Debug Info Panel */}
      {process.env.NODE_ENV === 'development' && (
        <div className='mb-6 p-4 bg-gray-100 rounded border-l-4 border-blue-500'>
          <h3 className='font-bold text-gray-700 mb-2'>🐛 Debug Information</h3>
          <div className='grid grid-cols-2 gap-4 text-sm'>
            <div>
              <strong>Component Stats:</strong>
              <pre className='text-xs mt-1 bg-gray-200 p-2 rounded'>
                {JSON.stringify(getComponentStats(), null, 2)}
              </pre>
            </div>
            <div>
              <strong>Performance Metrics:</strong>
              <div className='text-xs mt-1'>
                <div>
                  Slow Operations:{' '}
                  {performanceMonitor.getSlowOperations().length}
                </div>
                <div>
                  Total Metrics: {performanceMonitor.getMetrics().length}
                </div>
                <div>
                  Memory Usage:{' '}
                  {(performance as any).memory
                    ? `${Math.round((performance as any).memory.usedJSHeapSize / 1024 / 1024)}MB`
                    : 'N/A'}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className='mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded'>
          <h3 className='font-bold'>Error:</h3>
          <p>{error}</p>
          <button
            onClick={() => {
              debugLogger.downloadLogs();
              debugLogger.log(
                'info',
                'user-action',
                'User downloaded error logs'
              );
            }}
            className='mt-2 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700'
          >
            Download Debug Logs
          </button>
        </div>
      )}

      {/* Main Form */}
      <form onSubmit={handleSubmit} className='space-y-6'>
        <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
          <div>
            <label className='block text-sm font-medium text-gray-700 mb-2'>
              Monthly Income (CHF)
            </label>
            <input
              type='number'
              value={data.monthlyIncome}
              onChange={e =>
                handleInputChange(
                  'monthlyIncome',
                  parseFloat(e.target.value) || 0
                )
              }
              className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
              placeholder='8000'
            />
          </div>

          <div>
            <label className='block text-sm font-medium text-gray-700 mb-2'>
              Monthly Expenses (CHF)
            </label>
            <input
              type='number'
              value={data.monthlyExpenses}
              onChange={e =>
                handleInputChange(
                  'monthlyExpenses',
                  parseFloat(e.target.value) || 0
                )
              }
              className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
              placeholder='5000'
            />
          </div>

          <div>
            <label className='block text-sm font-medium text-gray-700 mb-2'>
              Current Savings (CHF)
            </label>
            <input
              type='number'
              value={data.currentSavings}
              onChange={e =>
                handleInputChange(
                  'currentSavings',
                  parseFloat(e.target.value) || 0
                )
              }
              className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
              placeholder='100000'
            />
          </div>

          <div>
            <label className='block text-sm font-medium text-gray-700 mb-2'>
              Canton
            </label>
            <select
              value={data.canton}
              onChange={e => handleInputChange('canton', e.target.value)}
              className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            >
              <option value='ZH'>Zurich</option>
              <option value='GE'>Geneva</option>
              <option value='VD'>Vaud</option>
              <option value='BE'>Bern</option>
              <option value='ZG'>Zug</option>
            </select>
          </div>
        </div>

        <button
          type='submit'
          disabled={isCalculating}
          className='w-full px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors'
        >
          {isCalculating ? '🔄 Calculating...' : '📊 Calculate FIRE Plan'}
        </button>
      </form>

      {/* Results */}
      {results && (
        <div className='mt-8 p-6 bg-green-50 rounded-lg border border-green-200'>
          <h2 className='text-2xl font-bold text-green-800 mb-4'>
            📈 Your FIRE Plan
          </h2>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
            <div className='bg-white p-4 rounded border'>
              <div className='text-sm text-gray-600'>Years to FIRE</div>
              <div className='text-3xl font-bold text-green-600'>
                {results.fireYears.toFixed(1)}
              </div>
            </div>
            <div className='bg-white p-4 rounded border'>
              <div className='text-sm text-gray-600'>FIRE Number</div>
              <div className='text-3xl font-bold text-blue-600'>
                CHF {results.fireNumber.toLocaleString()}
              </div>
            </div>
            <div className='bg-white p-4 rounded border'>
              <div className='text-sm text-gray-600'>Savings Rate</div>
              <div className='text-3xl font-bold text-purple-600'>
                {(results.savingsRate * 100).toFixed(1)}%
              </div>
            </div>
            <div className='bg-white p-4 rounded border'>
              <div className='text-sm text-gray-600'>Annual Tax</div>
              <div className='text-3xl font-bold text-red-600'>
                CHF {results.totalTax.toLocaleString()}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Debug Actions */}
      {process.env.NODE_ENV === 'development' && (
        <div className='mt-8 p-4 bg-gray-100 rounded'>
          <h3 className='font-bold text-gray-700 mb-3'>🛠️ Debug Actions</h3>
          <div className='flex flex-wrap gap-2'>
            <button
              onClick={() => debugLogger.downloadLogs()}
              className='px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700'
            >
              📥 Export Logs
            </button>
            <button
              onClick={() => {
                const report = performanceMonitor.exportPerformanceReport();
                const blob = new Blob([report], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'performance-report.json';
                a.click();
                URL.revokeObjectURL(url);
              }}
              className='px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700'
            >
              ⚡ Export Performance
            </button>
            <button
              onClick={() => {
                throw new Error('Test error for debugging');
              }}
              className='px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700'
            >
              🚨 Trigger Test Error
            </button>
            <button
              onClick={() => {
                debugLogger.clearLogs();
                performanceMonitor.clearMetrics();
                errorReporter.clearReports();
              }}
              className='px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700'
            >
              🗑️ Clear All Debug Data
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default DebuggedSwissBudgetComponent;
