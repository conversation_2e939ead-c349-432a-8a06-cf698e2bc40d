// Advanced Monte Carlo Simulation Engine
export const MonteCarloEngine = {
  // Generate random returns based on historical volatility
  generateRandomReturn(
    meanReturn: number,
    volatility: number,
    distribution: string = 'normal',
  ): number {
    if (distribution === 'normal') {
      // Box-Muller transformation for normal distribution
      const u1 = Math.random();
      const u2 = Math.random();
      const z0 = Math.sqrt(-2 * Math.log(u1)) * Math.cos(2 * Math.PI * u2);
      return meanReturn + volatility * z0;
    } else if (distribution === 'lognormal') {
      // Log-normal distribution for more realistic market returns
      const normal = this.generateRandomReturn(0, 1, 'normal');
      return Math.exp(Math.log(meanReturn) + volatility * normal);
    }
    return meanReturn;
  },

  // Run Monte Carlo simulation for FIRE projections
  runSimulation(params: any, iterations = 1000) {
    const {
      currentAge,
      retirementAge,
      currentSavings,
      monthlyContribution,
      targetAmount,
      expectedReturn,
      volatility,
      inflationRate,
      inflationVolatility,
      economicShocks,
    } = params;

    const results: any[] = [];
    const yearsToRetirement = retirementAge - currentAge;

    for (let i = 0; i < iterations; i++) {
      let balance = currentSavings;
      let realBalance = currentSavings;
      let monthlyContrib = monthlyContribution;
      let cumulativeInflation = 1;

      const yearlyResults: any[] = [];

      for (let year = 0; year < yearsToRetirement; year++) {
        // Generate random returns and inflation
        const annualReturn = this.generateRandomReturn(
          expectedReturn / 100,
          volatility / 100,
          'lognormal',
        );
        const annualInflation = this.generateRandomReturn(
          inflationRate / 100,
          inflationVolatility / 100,
          'normal',
        );

        // Apply economic shocks (market crashes, recessions)
        let shockMultiplier = 1;
        if (economicShocks && Math.random() < economicShocks.probability) {
          shockMultiplier = 1 + economicShocks.severity; // Negative severity for crashes
        }

        const adjustedReturn = annualReturn * shockMultiplier;

        // Calculate year-end balance
        const yearStartBalance = balance;
        const annualContributions = monthlyContrib * 12;

        // Apply returns to average balance throughout year
        const avgBalance = yearStartBalance + annualContributions / 2;
        const investmentGrowth = avgBalance * adjustedReturn;

        balance = yearStartBalance + annualContributions + investmentGrowth;

        // Track real purchasing power
        cumulativeInflation *= 1 + annualInflation;
        realBalance = balance / cumulativeInflation;

        // Adjust future contributions for inflation
        monthlyContrib *= 1 + annualInflation;

        yearlyResults.push({
          year: currentAge + year + 1,
          nominalBalance: balance,
          realBalance: realBalance,
          annualReturn: adjustedReturn * 100,
          inflation: annualInflation * 100,
          monthlyContribution: monthlyContrib,
        });
      }

      const finalBalance = balance;
      const finalRealBalance = realBalance;
      const success = finalRealBalance >= targetAmount;
      const shortfall = success ? 0 : targetAmount - finalRealBalance;

      results.push({
        iteration: i + 1,
        finalBalance,
        finalRealBalance,
        success,
        shortfall,
        yearlyResults,
      });
    }

    return this.analyzeResults(results, targetAmount);
  },

  // Analyze Monte Carlo results
  analyzeResults(results: any[], targetAmount: number) {
    const successfulRuns = results.filter(r => r.success);
    const successRate = (successfulRuns.length / results.length) * 100;

    const finalBalances = results.map(r => r.finalRealBalance);
    finalBalances.sort((a, b) => a - b);

    const percentiles = {
      p10: finalBalances[Math.floor(results.length * 0.1)],
      p25: finalBalances[Math.floor(results.length * 0.25)],
      p50: finalBalances[Math.floor(results.length * 0.5)],
      p75: finalBalances[Math.floor(results.length * 0.75)],
      p90: finalBalances[Math.floor(results.length * 0.9)],
    };

    const averageBalance =
      finalBalances.reduce((sum, bal) => sum + bal, 0) / finalBalances.length;
    const shortfalls = results.filter(r => !r.success).map(r => r.shortfall);
    const averageShortfall =
      shortfalls.length > 0
        ? shortfalls.reduce((sum, s) => sum + s, 0) / shortfalls.length
        : 0;

    // Risk metrics
    const worstCase = finalBalances[0];
    const bestCase = finalBalances[finalBalances.length - 1];
    const volatilityOfOutcomes = this.calculateStandardDeviation(finalBalances);

    return {
      successRate,
      percentiles,
      averageBalance,
      averageShortfall,
      worstCase,
      bestCase,
      volatilityOfOutcomes,
      targetAmount,
      iterations: results.length,
      detailedResults: results,
      riskMetrics: {
        probabilityOfRuin:
          ((results.length - successfulRuns.length) / results.length) * 100,
        expectedShortfall: averageShortfall,
        valueAtRisk: targetAmount - percentiles.p10, // 10% chance of losing this much
        conditionalValueAtRisk: targetAmount - percentiles.p10, // Use p10 instead of p5
      },
    };
  },

  // Calculate standard deviation
  calculateStandardDeviation(values: number[]) {
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
    const avgSquaredDiff =
      squaredDiffs.reduce((sum, diff) => sum + diff, 0) / values.length;
    return Math.sqrt(avgSquaredDiff);
  },

  // Stress testing scenarios
  generateStressTestScenarios() {
    return {
      baseCase: {
        name: 'Base Case',
        expectedReturn: 6.0,
        volatility: 15.0,
        inflationRate: 2.0,
        inflationVolatility: 1.0,
        economicShocks: null,
      },
      bearMarket: {
        name: 'Bear Market',
        expectedReturn: 4.0,
        volatility: 25.0,
        inflationRate: 1.0,
        inflationVolatility: 1.5,
        economicShocks: { probability: 0.15, severity: -0.3 },
      },
      highInflation: {
        name: 'High Inflation',
        expectedReturn: 7.0,
        volatility: 20.0,
        inflationRate: 4.0,
        inflationVolatility: 2.0,
        economicShocks: { probability: 0.1, severity: -0.2 },
      },
      recession: {
        name: 'Recession Scenario',
        expectedReturn: 3.0,
        volatility: 30.0,
        inflationRate: 0.5,
        inflationVolatility: 2.0,
        economicShocks: { probability: 0.25, severity: -0.4 },
      },
      stagflation: {
        name: 'Stagflation',
        expectedReturn: 2.0,
        volatility: 25.0,
        inflationRate: 5.0,
        inflationVolatility: 3.0,
        economicShocks: { probability: 0.2, severity: -0.25 },
      },
    };
  },

  // Safe withdrawal rate analysis
  calculateSafeWithdrawalRate(balance: number, years = 30, successRate = 95) {
    const scenarios = this.generateStressTestScenarios();
    const withdrawalRates: any[] = [];

    for (let rate = 0.02; rate <= 0.06; rate += 0.002) {
      let totalSuccess = 0;
      let totalRuns = 0;

      Object.values(scenarios).forEach((scenario: any) => {
        const results = this.runWithdrawalSimulation(
          {
            initialBalance: balance,
            withdrawalRate: rate,
            years: years,
            expectedReturn: scenario.expectedReturn,
            volatility: scenario.volatility,
            inflationRate: scenario.inflationRate,
          },
          200,
        );

        totalSuccess += results.successfulYears;
        totalRuns += results.totalRuns;
      });

      const overallSuccessRate = (totalSuccess / totalRuns) * 100;
      withdrawalRates.push({
        rate: rate * 100,
        successRate: overallSuccessRate,
      });

      if (overallSuccessRate >= successRate) {
        return {
          safeWithdrawalRate: rate * 100,
          confidence: overallSuccessRate,
          analysis: withdrawalRates,
        };
      }
    }

    return {
      safeWithdrawalRate: 2.0, // Conservative fallback
      confidence: 90,
      analysis: withdrawalRates,
    };
  },

  // Withdrawal phase simulation
  runWithdrawalSimulation(params: any, iterations = 500) {
    const {
      initialBalance,
      withdrawalRate,
      years,
      expectedReturn,
      volatility,
      inflationRate,
    } = params;

    let successfulRuns = 0;

    for (let i = 0; i < iterations; i++) {
      let balance = initialBalance;
      let annualWithdrawal = initialBalance * withdrawalRate;
      let success = true;

      for (let year = 0; year < years; year++) {
        // Generate random return
        const annualReturn = this.generateRandomReturn(
          expectedReturn / 100,
          volatility / 100,
          'lognormal',
        );

        // Apply withdrawal at beginning of year
        balance -= annualWithdrawal;

        // Apply investment returns
        balance *= 1 + annualReturn;

        // Adjust withdrawal for inflation
        annualWithdrawal *= 1 + inflationRate / 100;

        // Check if we've run out of money
        if (balance <= 0) {
          success = false;
          break;
        }
      }

      if (success) successfulRuns++;
    }

    return {
      successfulYears: successfulRuns,
      totalRuns: iterations,
      successRate: (successfulRuns / iterations) * 100,
    };
  },
};
